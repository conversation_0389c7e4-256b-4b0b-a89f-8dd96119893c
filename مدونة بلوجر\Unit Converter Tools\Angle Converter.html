<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Angle Converter - Convert Degrees, Radians, Gradians & More</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Angle Converter - Convert Degrees, Radians, Gradians & More",
        "description": "Instantly convert between various angle units like degrees, radians, gradians, and arcseconds. A free online tool for mathematics, engineering, and navigation.",
        "url": "https://www.webtoolskit.org/p/angle-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-12",
        "dateModified": "2025-06-20",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Angle Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Angle Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to do angle conversion?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Angle conversion is done using established relationships between units. The most fundamental relationship is that a full circle is 360 degrees (deg), 2π radians (rad), or 400 gradians (grad). To convert from one unit to another, you use a conversion factor derived from these relationships. For example, to convert degrees to radians, you multiply by (π / 180)."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert degrees to directions?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Degrees are converted to compass directions based on a 360-degree circle where 0° is North. The main cardinal directions are: North (0° or 360°), East (90°), South (180°), and West (270°). Intermediate directions fall in between, such as Northeast (45°), Southeast (135°), Southwest (225°), and Northwest (315°)."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert degrees to coordinates?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You cannot directly convert an angle to a single coordinate; you need a distance or radius (r) as well. An angle (θ) and a radius (r) define a point on a circle in a polar coordinate system. To convert this to Cartesian coordinates (x, y), you use trigonometry: x = r * cos(θ) and y = r * sin(θ). Ensure your angle is in radians for these formulas or convert it from degrees first."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert numbers to angles?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A plain number can represent an angle if you know the unit (e.g., the number 90 could mean 90 degrees or 90 radians). If the number is a ratio from a trigonometric function (like the ratio of a triangle's sides), you use an inverse trigonometric function (arcsin, arccos, arctan) to find the angle. For example, if the sine of an angle is 0.5, the angle is arcsin(0.5) = 30°."
          }
        },
        {
          "@type": "Question",
          "name": "What is the formula for the change of an angle?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The change of an angle, known as angular displacement, is the difference between the final angle and the initial angle. The formula is: Δθ = θ_final - θ_initial. The result (Δθ) represents the amount of rotation that has occurred. For example, if an object moves from a 30° position to a 75° position, the change of angle is 75° - 30° = 45°."
          }
        }
      ]
    }
    </script>

    <style>
        /* Angle Converter Widget - Simplified & Template Compatible */
        .angle-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .angle-converter-widget-container * { box-sizing: border-box; }

        .angle-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .angle-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .angle-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .angle-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .angle-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .angle-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .angle-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .angle-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .angle-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .angle-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .angle-converter-btn:hover { transform: translateY(-2px); }

        .angle-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .angle-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .angle-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .angle-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .angle-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .angle-converter-btn-success:hover {
            background-color: #059669;
        }

        .angle-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .angle-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .angle-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .angle-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .angle-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .angle-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .angle-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .angle-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .angle-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .angle-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .angle-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="length-converter"] .angle-converter-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="frequency-converter"] .angle-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="speed-converter"] .angle-converter-related-tool-icon { background: linear-gradient(145deg, #4F46E5, #4338CA); }

        .angle-converter-related-tool-item:hover .angle-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="length-converter"]:hover .angle-converter-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="frequency-converter"]:hover .angle-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        a[href*="speed-converter"]:hover .angle-converter-related-tool-icon { background: linear-gradient(145deg, #5b52f6, #4f46e5); }
        
        .angle-converter-related-tool-item { box-shadow: none; border: none; }
        .angle-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .angle-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .angle-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .angle-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .angle-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .angle-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .angle-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .angle-converter-related-tool-item:hover .angle-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .angle-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .angle-converter-widget-title { font-size: 1.875rem; }
            .angle-converter-buttons { flex-direction: column; }
            .angle-converter-btn { flex: none; }
            .angle-converter-input-group { grid-template-columns: 1fr; }
            .angle-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .angle-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .angle-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .angle-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .angle-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .angle-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .angle-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .angle-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .angle-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .angle-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .angle-converter-output::selection { background-color: var(--primary-color); color: white; }
        .angle-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .angle-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="angle-converter-widget-container">
        <h1 class="angle-converter-widget-title">Angle Converter</h1>
        <p class="angle-converter-widget-description">
            A precise tool for converting angle measurements between degrees, radians, gradians, and other common units.
        </p>
        
        <div class="angle-converter-input-group">
            <label for="angleFromInput" class="angle-converter-label">From:</label>
            <input 
                type="number" 
                id="angleFromInput" 
                class="angle-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="angleFromUnit" class="angle-converter-select">
                <option value="degree" selected>Degree (°)</option>
                <option value="radian">Radian (rad)</option>
                <option value="gradian">Gradian (grad)</option>
                <option value="milliradian">Milliradian (mrad)</option>
                <option value="arcminute">Arcminute (')</option>
                <option value="arcsecond">Arcsecond (")</option>
            </select>
        </div>

        <div class="angle-converter-input-group">
            <label for="angleToInput" class="angle-converter-label">To:</label>
            <input 
                type="number" 
                id="angleToInput" 
                class="angle-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="angleToUnit" class="angle-converter-select">
                <option value="degree">Degree (°)</option>
                <option value="radian" selected>Radian (rad)</option>
                <option value="gradian">Gradian (grad)</option>
                <option value="milliradian">Milliradian (mrad)</option>
                <option value="arcminute">Arcminute (')</option>
                <option value="arcsecond">Arcsecond (")</option>
            </select>
        </div>

        <div class="angle-converter-buttons">
            <button class="angle-converter-btn angle-converter-btn-primary" onclick="AngleConverter.convert()">
                Convert Angle
            </button>
            <button class="angle-converter-btn angle-converter-btn-secondary" onclick="AngleConverter.clear()">
                Clear All
            </button>
            <button class="angle-converter-btn angle-converter-btn-success" onclick="AngleConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="angle-converter-result">
            <h3 class="angle-converter-result-title">Conversion Result:</h3>
            <div class="angle-converter-output" id="angleConverterOutput">
                Your converted angle will appear here...
            </div>
        </div>

        <div class="angle-converter-related-tools">
            <h3 class="angle-converter-related-tools-title">Related Tools</h3>
            <div class="angle-converter-related-tools-grid">
                <a href="/p/length-converter.html" class="angle-converter-related-tool-item" rel="noopener">
                    <div class="angle-converter-related-tool-icon">
                        <i class="fas fa-ruler-horizontal"></i>
                    </div>
                    <div class="angle-converter-related-tool-name">Length Converter</div>
                </a>
                <a href="/p/frequency-converter.html" class="angle-converter-related-tool-item" rel="noopener">
                    <div class="angle-converter-related-tool-icon">
                        <i class="fas fa-wave-square"></i>
                    </div>
                    <div class="angle-converter-related-tool-name">Frequency Converter</div>
                </a>
                <a href="/p/speed-converter.html" class="angle-converter-related-tool-item" rel="noopener">
                    <div class="angle-converter-related-tool-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="angle-converter-related-tool-name">Speed Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Instant and Accurate Angle Unit Conversion</h2>
            <p>Angles are a fundamental part of geometry, trigonometry, engineering, and navigation. While degrees are the most common unit, many scientific and mathematical applications rely on radians, gradians, or other precise measurements. Our free <strong>Angle Converter</strong> provides a simple and reliable way to convert between these units, ensuring your calculations are always accurate. Whether you're a student solving a trigonometry problem, an engineer designing a part, or a navigator plotting a course, this tool is designed to help.</p>
            <p>This converter supports the most widely used angle units, including Degrees, Radians, Gradians, Milliradians, Arcminutes, and Arcseconds. Eliminate the hassle of remembering complex conversion formulas and get instant results for any application.</p>

            <h3>How to Use the Angle Converter</h3>
            <ol>
                <li><strong>Enter Your Angle:</strong> Type the numeric value of the angle you want to convert into the "From" input field.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., Degrees) and your target unit (e.g., Radians) from the dropdown lists.</li>
                <li><strong>Convert:</strong> Click the "Convert Angle" button to perform the calculation and see the result instantly.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to easily save the converted value for your work.</li>
            </ol>

            <h3>Frequently Asked Questions About Angle Conversion</h3>
            
            <h4>How to do angle conversion?</h4>
            <p>Angle conversion is done using established relationships between units. The most fundamental relationship is that a full circle is 360 degrees (deg), 2π radians (rad), or 400 gradians (grad). To convert from one unit to another, you use a conversion factor derived from these relationships. For example, to convert degrees to radians, you multiply by (π / 180).</p>

            <h4>How do you convert degrees to directions?</h4>
            <p>Degrees are converted to compass directions based on a 360-degree circle where 0° is North. The main cardinal directions are: North (0° or 360°), East (90°), South (180°), and West (270°). Intermediate directions fall in between, such as Northeast (45°), Southeast (135°), Southwest (225°), and Northwest (315°).</p>

            <h4>How to convert degrees to coordinates?</h4>
            <p>You cannot directly convert an angle to a single coordinate; you need a distance or radius (r) as well. An angle (θ) and a radius (r) define a point on a circle in a polar coordinate system. To convert this to Cartesian coordinates (x, y), you use trigonometry: x = r * cos(θ) and y = r * sin(θ). Ensure your angle is in radians for these formulas or convert it from degrees first.</p>

            <h4>How do you convert numbers to angles?</h4>
            <p>A plain number can represent an angle if you know the unit (e.g., the number 90 could mean 90 degrees or 90 radians). If the number is a ratio from a trigonometric function (like the ratio of a triangle's sides), you use an inverse trigonometric function (arcsin, arccos, arctan) to find the angle. For example, if the sine of an angle is 0.5, the angle is arcsin(0.5) = 30°.</p>

            <h4>What is the formula for the change of an angle?</h4>
            <p>The change of an angle, known as angular displacement, is the difference between the final angle and the initial angle. The formula is: Δθ = θ_final - θ_initial. The result (Δθ) represents the amount of rotation that has occurred. For example, if an object moves from a 30° position to a 75° position, the change of angle is 75° - 30° = 45°.</p>
        </div>

        <div class="angle-converter-features">
            <h3 class="angle-converter-features-title">Key Features:</h3>
            <ul class="angle-converter-features-list">
                <li class="angle-converter-features-item" style="margin-bottom: 0.3em;">Supports 6 common angle units</li>
                <li class="angle-converter-features-item" style="margin-bottom: 0.3em;">Includes degrees, radians, grads</li>
                <li class="angle-converter-features-item" style="margin-bottom: 0.3em;">High-precision calculations</li>
                <li class="angle-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="angle-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side processing</li>
                <li class="angle-converter-features-item" style="margin-bottom: 0.3em;">Ideal for math & engineering</li>
                <li class="angle-converter-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="angle-converter-notification" id="angleConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Angle Converter
        (function() {
            'use strict';

            // Conversion factors to Radians
            const conversionFactors = {
                'degree': Math.PI / 180,
                'radian': 1,
                'gradian': Math.PI / 200,
                'milliradian': 0.001,
                'arcminute': Math.PI / (180 * 60),
                'arcsecond': Math.PI / (180 * 3600)
            };

            const elements = {
                fromInput: () => document.getElementById('angleFromInput'),
                toInput: () => document.getElementById('angleToInput'),
                fromUnit: () => document.getElementById('angleFromUnit'),
                toUnit: () => document.getElementById('angleToUnit'),
                output: () => document.getElementById('angleConverterOutput'),
                notification: () => document.getElementById('angleConverterNotification')
            };

            window.AngleConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to base unit (Radians) first, then to target unit
                    const valueInRadians = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInRadians / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-9 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(12)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 
                        'degree': '°', 'radian': 'rad', 'gradian': 'grad', 
                        'milliradian': 'mrad', 'arcminute': "'", 'arcsecond': '"'
                    };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted angle will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        AngleConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>