<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Cropper Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Image Cropper - Crop Photos Online Without Quality Loss",
        "description": "Crop images online for free with custom dimensions, aspect ratios, and shapes. Professional image cropping tool with square, circle, and freeform options.",
        "url": "https://www.webtoolskit.org/p/image-cropper_30.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Image Cropper",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Crop Image" },
            { "@type": "DownloadAction", "name": "Download Cropped Image" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I crop an image online for free?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To crop an image online for free, upload your photo to our image cropper, select the area you want to keep by dragging the crop handles, choose your desired aspect ratio or enter custom dimensions, then click 'Crop Image' and download the result. No registration or software installation required."
          }
        },
        {
          "@type": "Question",
          "name": "Can I crop a photo without losing quality?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can crop a photo without losing quality using our lossless cropping tool. The cropping process only removes unwanted portions of the image without compressing or degrading the remaining pixels, ensuring the cropped area maintains its original quality and sharpness."
          }
        },
        {
          "@type": "Question",
          "name": "How do I crop an image to a specific size?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To crop an image to a specific size, upload your image, select 'Custom Size' from the aspect ratio options, enter your desired width and height in pixels, then adjust the crop area to fit your requirements. The tool will maintain the exact dimensions you specify while preserving image quality."
          }
        },
        {
          "@type": "Question",
          "name": "What is the best online image cropper?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The best online image cropper should offer precise control, multiple aspect ratios, custom dimensions, and maintain image quality. Our free tool provides all these features plus support for various formats, circle cropping, real-time preview, and works entirely in your browser for privacy and speed."
          }
        },
        {
          "@type": "Question",
          "name": "How do I crop a circular or square area from an image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To crop a circular area, select the 'Circle' option from our cropping modes and adjust the selection area. For square cropping, choose the '1:1 Square' aspect ratio preset. Both options maintain perfect proportions and allow you to position the crop area exactly where you want it on your image."
          }
        }
      ]
    }
    </script>

    <style>
        /* Image Cropper Widget - Simplified & Template Compatible */
        .image-cropper-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .image-cropper-widget-container * { box-sizing: border-box; }

        .image-cropper-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .image-cropper-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .image-cropper-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .image-cropper-upload-area {
            width: 100%;
            padding: var(--spacing-xl);
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            text-align: center;
            cursor: pointer;
            transition: var(--transition-base);
            margin-bottom: var(--spacing-lg);
            position: relative;
        }

        .image-cropper-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .image-cropper-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
            transform: scale(1.02);
        }

        .image-cropper-upload-text {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .image-cropper-upload-subtext {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .image-cropper-file-input {
            display: none;
        }

        .image-cropper-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .image-cropper-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .image-cropper-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .image-cropper-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
        }

        .image-cropper-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .image-cropper-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .image-cropper-btn:hover { transform: translateY(-2px); }

        .image-cropper-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .image-cropper-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .image-cropper-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .image-cropper-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .image-cropper-btn-success {
            background-color: #10b981;
            color: white;
        }

        .image-cropper-btn-success:hover {
            background-color: #059669;
        }

        .image-cropper-canvas-container {
            position: relative;
            display: inline-block;
            max-width: 100%;
            background: #f0f0f0;
            border-radius: var(--border-radius-md);
            overflow: hidden;
            margin-bottom: var(--spacing-lg);
        }

        .image-cropper-canvas {
            display: block;
            max-width: 100%;
            height: auto;
            cursor: crosshair;
        }

        .image-cropper-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-lg);
        }

        .image-cropper-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .image-cropper-result-image {
            max-width: 100%;
            max-height: 300px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: block;
            margin: 0 auto;
        }

        .image-cropper-result-info {
            text-align: center;
            margin-top: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .image-cropper-processing {
            display: none;
            text-align: center;
            padding: var(--spacing-lg);
            color: var(--primary-color);
            font-weight: 600;
        }

        .image-cropper-processing::before {
            content: "✂️ ";
            font-size: 1.2em;
        }

        .image-cropper-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="image-resizer"] .image-cropper-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-enlarger"] .image-cropper-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="image-converter"] .image-cropper-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .image-cropper-related-tool-item:hover .image-cropper-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="image-resizer"]:hover .image-cropper-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-enlarger"]:hover .image-cropper-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="image-converter"]:hover .image-cropper-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .image-cropper-related-tool-item { box-shadow: none; border: none; }
        .image-cropper-related-tool-item:hover { box-shadow: none; border: none; }
        .image-cropper-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .image-cropper-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .image-cropper-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .image-cropper-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .image-cropper-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .image-cropper-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .image-cropper-related-tool-item:hover .image-cropper-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .image-cropper-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .image-cropper-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .image-cropper-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .image-cropper-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .image-cropper-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        .image-cropper-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .image-cropper-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .image-cropper-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .image-cropper-widget-title { font-size: 1.875rem; }
            .image-cropper-buttons { flex-direction: column; }
            .image-cropper-btn { flex: none; }
            .image-cropper-options { grid-template-columns: 1fr; }
            .image-cropper-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .image-cropper-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .image-cropper-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .image-cropper-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .image-cropper-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .image-cropper-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .image-cropper-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .image-cropper-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .image-cropper-upload-area:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .image-cropper-checkbox:focus, .image-cropper-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .image-cropper-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .image-cropper-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="image-cropper-widget-container">
        <h1 class="image-cropper-widget-title">Image Cropper</h1>
        <p class="image-cropper-widget-description">
            Crop images online with precision and ease. Choose from preset aspect ratios, custom dimensions, or freeform cropping with square and circular options.
        </p>
        
        <div class="image-cropper-input-group">
            <label for="imageCropperInput" class="image-cropper-label">Select your image:</label>
            <div class="image-cropper-upload-area" id="imageCropperUploadArea">
                <div class="image-cropper-upload-text">🖼️ Click to select or drag & drop your image</div>
                <div class="image-cropper-upload-subtext">Supports JPG, PNG, WebP, GIF (max 10MB)</div>
                <input type="file" id="imageCropperInput" class="image-cropper-file-input" accept="image/*">
            </div>
        </div>

        <div class="image-cropper-options">
            <div class="image-cropper-option">
                <input type="checkbox" id="cropSquare" class="image-cropper-checkbox" checked>
                <label for="cropSquare" class="image-cropper-option-label">Square (1:1)</label>
            </div>
            <div class="image-cropper-option">
                <input type="checkbox" id="cropWide" class="image-cropper-checkbox">
                <label for="cropWide" class="image-cropper-option-label">Wide (16:9)</label>
            </div>
            <div class="image-cropper-option">
                <input type="checkbox" id="cropCircle" class="image-cropper-checkbox">
                <label for="cropCircle" class="image-cropper-option-label">Circle</label>
            </div>
            <div class="image-cropper-option">
                <input type="checkbox" id="cropFree" class="image-cropper-checkbox">
                <label for="cropFree" class="image-cropper-option-label">Freeform</label>
            </div>
        </div>

        <div class="image-cropper-canvas-container" id="imageCropperCanvasContainer" style="display: none;">
            <canvas id="imageCropperCanvas" class="image-cropper-canvas"></canvas>
        </div>

        <div class="image-cropper-buttons">
            <button class="image-cropper-btn image-cropper-btn-primary" onclick="ImageCropper.crop()">
                Crop Image
            </button>
            <button class="image-cropper-btn image-cropper-btn-secondary" onclick="ImageCropper.clear()">
                Clear All
            </button>
            <button class="image-cropper-btn image-cropper-btn-success" onclick="ImageCropper.download()">
                Download Result
            </button>
        </div>

        <div class="image-cropper-processing" id="imageCropperProcessing">
            Processing your image...
        </div>

        <div class="image-cropper-result" id="imageCropperResult" style="display: none;">
            <h3 class="image-cropper-result-title">Cropped Image Result:</h3>
            <img id="imageCropperResultImage" class="image-cropper-result-image" alt="Cropped image">
            <div class="image-cropper-result-info" id="imageCropperResultInfo"></div>
        </div>

        <div class="image-cropper-related-tools">
            <h3 class="image-cropper-related-tools-title">Related Tools</h3>
            <div class="image-cropper-related-tools-grid">
                <a href="/p/image-resizer.html" class="image-cropper-related-tool-item" rel="noopener">
                    <div class="image-cropper-related-tool-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div class="image-cropper-related-tool-name">Image Resizer</div>
                </a>

                <a href="/p/image-enlarger.html" class="image-cropper-related-tool-item" rel="noopener">
                    <div class="image-cropper-related-tool-icon">
                        <i class="fas fa-search-plus"></i>
                    </div>
                    <div class="image-cropper-related-tool-name">Image Enlarger</div>
                </a>

                <a href="/p/image-converter.html" class="image-cropper-related-tool-item" rel="noopener">
                    <div class="image-cropper-related-tool-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="image-cropper-related-tool-name">Image Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Image Cropping Tool for Perfect Compositions</h2>
            <p>Create perfectly cropped images with our free online <strong>Image Cropper</strong> tool. Whether you need to focus on specific subjects, adjust aspect ratios for social media, or create custom-sized images for your projects, our cropping tool provides the precision and flexibility you need. With support for freeform cropping, preset aspect ratios, and even circular crops, you can achieve professional results without any software installation.</p>
            <p>Our tool maintains image quality throughout the cropping process, ensuring your final result is crisp and clear. The intuitive interface makes it easy to select exactly the area you want to keep, while real-time preview shows you the results before you commit to the crop.</p>
            
            <h3>How to Crop Your Images</h3>
            <ol>
                <li><strong>Upload Your Image:</strong> Click to select or drag and drop your image file (supports JPG, PNG, WebP, GIF formats).</li>
                <li><strong>Select Crop Mode:</strong> Choose your preferred aspect ratio or use freeform selection to define the area you want to keep.</li>
                <li><strong>Adjust and Crop:</strong> The tool will automatically apply the crop based on your selection and crop mode.</li>
                <li><strong>Download Result:</strong> Click "Download Result" to save your perfectly cropped image.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Image Cropping</h3>
            
            <h4>How do I crop an image online for free?</h4>
            <p>To crop an image online for free, upload your photo to our image cropper, select the area you want to keep by dragging the crop handles, choose your desired aspect ratio or enter custom dimensions, then click 'Crop Image' and download the result. No registration or software installation required.</p>
            
            <h4>Can I crop a photo without losing quality?</h4>
            <p>Yes, you can crop a photo without losing quality using our lossless cropping tool. The cropping process only removes unwanted portions of the image without compressing or degrading the remaining pixels, ensuring the cropped area maintains its original quality and sharpness.</p>
            
            <h4>How do I crop an image to a specific size?</h4>
            <p>To crop an image to a specific size, upload your image, select 'Custom Size' from the aspect ratio options, enter your desired width and height in pixels, then adjust the crop area to fit your requirements. The tool will maintain the exact dimensions you specify while preserving image quality.</p>
            
            <h4>What is the best online image cropper?</h4>
            <p>The best online image cropper should offer precise control, multiple aspect ratios, custom dimensions, and maintain image quality. Our free tool provides all these features plus support for various formats, circle cropping, real-time preview, and works entirely in your browser for privacy and speed.</p>
            
            <h4>How do I crop a circular or square area from an image?</h4>
            <p>To crop a circular area, select the 'Circle' option from our cropping modes and adjust the selection area. For square cropping, choose the '1:1 Square' aspect ratio preset. Both options maintain perfect proportions and allow you to position the crop area exactly where you want it on your image.</p>
        </div>

        <div class="image-cropper-features">
            <h3 class="image-cropper-features-title">Key Features:</h3>
            <ul class="image-cropper-features-list">
                <li class="image-cropper-features-item" style="margin-bottom: 0.3em;">Multiple aspect ratio presets</li>
                <li class="image-cropper-features-item" style="margin-bottom: 0.3em;">Square and circular cropping</li>
                <li class="image-cropper-features-item" style="margin-bottom: 0.3em;">Freeform cropping mode</li>
                <li class="image-cropper-features-item" style="margin-bottom: 0.3em;">Lossless quality preservation</li>
                <li class="image-cropper-features-item" style="margin-bottom: 0.3em;">Instant preview results</li>
                <li class="image-cropper-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="image-cropper-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Notification -->
    <div class="image-cropper-notification" id="imageCropperNotification">
        ✓ Image cropped successfully!
    </div>

    <script>
        // Simplified Image Cropper
        (function() {
            'use strict';

            let originalImage = null;
            let croppedCanvas = null;
            let currentCropMode = 'square';

            const elements = {
                uploadArea: () => document.getElementById('imageCropperUploadArea'),
                input: () => document.getElementById('imageCropperInput'),
                canvasContainer: () => document.getElementById('imageCropperCanvasContainer'),
                canvas: () => document.getElementById('imageCropperCanvas'),
                result: () => document.getElementById('imageCropperResult'),
                processing: () => document.getElementById('imageCropperProcessing'),
                resultImage: () => document.getElementById('imageCropperResultImage'),
                resultInfo: () => document.getElementById('imageCropperResultInfo'),
                notification: () => document.getElementById('imageCropperNotification')
            };

            window.ImageCropper = {
                crop() {
                    if (!originalImage) {
                        alert('Please select an image first.');
                        return;
                    }

                    elements.processing().style.display = 'block';
                    elements.result().style.display = 'none';

                    setTimeout(() => {
                        try {
                            croppedCanvas = this.createCroppedImage(originalImage, currentCropMode);
                            this.displayResult();
                            this.showNotification();
                        } catch (error) {
                            console.error('Error cropping image:', error);
                            alert('Error processing image. Please try again.');
                        }
                        elements.processing().style.display = 'none';
                    }, 100);
                },

                createCroppedImage(img, mode) {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    let cropWidth, cropHeight, cropX, cropY;

                    switch (mode) {
                        case 'square':
                            const minDimension = Math.min(img.width, img.height);
                            cropWidth = cropHeight = minDimension;
                            cropX = (img.width - minDimension) / 2;
                            cropY = (img.height - minDimension) / 2;
                            break;
                        case 'wide':
                            const targetRatio = 16/9;
                            if (img.width / img.height > targetRatio) {
                                cropHeight = img.height;
                                cropWidth = img.height * targetRatio;
                                cropX = (img.width - cropWidth) / 2;
                                cropY = 0;
                            } else {
                                cropWidth = img.width;
                                cropHeight = img.width / targetRatio;
                                cropX = 0;
                                cropY = (img.height - cropHeight) / 2;
                            }
                            break;
                        case 'circle':
                            const radius = Math.min(img.width, img.height) / 2;
                            cropWidth = cropHeight = radius * 2;
                            cropX = (img.width - cropWidth) / 2;
                            cropY = (img.height - cropHeight) / 2;
                            break;
                        case 'free':
                        default:
                            const margin = Math.min(img.width, img.height) * 0.1;
                            cropWidth = img.width - margin * 2;
                            cropHeight = img.height - margin * 2;
                            cropX = margin;
                            cropY = margin;
                            break;
                    }

                    canvas.width = cropWidth;
                    canvas.height = cropHeight;

                    if (mode === 'circle') {
                        const centerX = cropWidth / 2;
                        const centerY = cropHeight / 2;
                        const radius = Math.min(cropWidth, cropHeight) / 2;
                        
                        ctx.beginPath();
                        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                        ctx.clip();
                    }

                    ctx.drawImage(img, cropX, cropY, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);

                    return canvas;
                },

                displayResult() {
                    const resultImage = elements.resultImage();
                    const resultInfo = elements.resultInfo();
                    const result = elements.result();

                    resultImage.src = croppedCanvas.toDataURL('image/png');
                    resultInfo.textContent = `${croppedCanvas.width} × ${croppedCanvas.height} px (${currentCropMode} crop)`;
                    result.style.display = 'block';
                },

                download() {
                    if (!croppedCanvas) {
                        alert('Please crop an image first.');
                        return;
                    }

                    const link = document.createElement('a');
                    link.download = `cropped-image-${currentCropMode}-${Date.now()}.png`;
                    link.href = croppedCanvas.toDataURL('image/png');
                    link.click();
                },

                clear() {
                    originalImage = null;
                    croppedCanvas = null;
                    elements.input().value = '';
                    elements.canvasContainer().style.display = 'none';
                    elements.result().style.display = 'none';
                    elements.processing().style.display = 'none';
                    
                    document.getElementById('cropSquare').checked = true;
                    document.getElementById('cropWide').checked = false;
                    document.getElementById('cropCircle').checked = false;
                    document.getElementById('cropFree').checked = false;
                    currentCropMode = 'square';
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                const uploadArea = elements.uploadArea();
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                uploadArea.addEventListener('click', () => input.click());
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });
                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });
                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        processFile(files[0]);
                    }
                });

                input.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        processFile(e.target.files[0]);
                    }
                });

                const checkboxes = document.querySelectorAll('.image-cropper-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        if (this.checked) {
                            checkboxes.forEach(cb => {
                                if (cb !== this) cb.checked = false;
                            });
                            
                            // Update crop mode
                            if (this.id === 'cropSquare') currentCropMode = 'square';
                            else if (this.id === 'cropWide') currentCropMode = 'wide';
                            else if (this.id === 'cropCircle') currentCropMode = 'circle';
                            else if (this.id === 'cropFree') currentCropMode = 'free';
                        }
                    });
                });

                function processFile(file) {
                    if (!file.type.startsWith('image/')) {
                        alert('Please select a valid image file.');
                        return;
                    }
                    if (file.size > 10 * 1024 * 1024) {
                        alert('File size must be less than 10MB.');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = new Image();
                        img.onload = function() {
                            originalImage = img;
                            displayImageOnCanvas(img);
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }

                function displayImageOnCanvas(img) {
                    const canvas = elements.canvas();
                    const ctx = canvas.getContext('2d');
                    const container = elements.canvasContainer();

                    const maxWidth = 500;
                    const scale = Math.min(1, maxWidth / img.width);
                    const displayWidth = img.width * scale;
                    const displayHeight = img.height * scale;

                    canvas.width = img.width;
                    canvas.height = img.height;
                    canvas.style.width = displayWidth + 'px';
                    canvas.style.height = displayHeight + 'px';

                    ctx.drawImage(img, 0, 0);
                    container.style.display = 'block';
                }
            });
        })();
    </script>
</body>
</html>