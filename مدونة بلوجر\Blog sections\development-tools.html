<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Developer Tools – Format & Convert JSON, XML, CSV Easily Online</title>
  <meta name="description" content="Clean, format, validate, and convert JSON, XML, and CSV data online. Perfect for developers and data analysts working with structured content.">
  <meta name="keywords" content="developer tools, json formatter, json validator, xml to json, csv to json, json editor, json minify, development utilities">
  <link rel="canonical" href="https://www.webtoolskit.org/p/development-tools.html">

  <!-- Page-specific Open Graph Meta Tags -->
  <meta property="og:url" content="https://www.webtoolskit.org/p/development-tools.html">
  <meta property="og:title" content="Developer Tools - Format & Convert JSON, XML, CSV Online">
  <meta property="og:description" content="Clean, format, validate, and convert JSON, XML, and CSV data online. Perfect for developers and data analysts working with structured content.">
  <meta property="og:image" content="https://www.webtoolskit.org/images/dev-tools-og.jpg">

  <!-- Page-specific Twitter Card Meta Tags -->
  <meta name="twitter:url" content="https://www.webtoolskit.org/p/development-tools.html">
  <meta name="twitter:title" content="Developer Tools - Format & Convert JSON, XML, CSV Online">
  <meta name="twitter:description" content="Clean, format, validate, and convert JSON, XML, and CSV data online. Perfect for developers and data analysts working with structured content.">
  <meta name="twitter:image" content="https://www.webtoolskit.org/images/dev-tools-og.jpg">
  
  <!-- Enhanced Schema.org structured data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": ["WebPage", "CollectionPage"],
    "name": "Developer Tools – Format & Convert JSON, XML, CSV Easily Online",
    "description": "Clean, format, validate, and convert JSON, XML, and CSV data online. Perfect for developers and data analysts working with structured content.",
    "url": "https://www.webtoolskit.org/p/development-tools.html",
    "isAccessibleForFree": true,
    "datePublished": "2025-06-20",
    "dateModified": "2025-06-20",
    "author": {
      "@type": "Organization",
      "name": "WebToolsKit",
      "url": "https://www.webtoolskit.org",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.webtoolskit.org/images/logo.png"
      }
    },
    "publisher": {
      "@type": "Organization",
      "name": "WebToolsKit",
      "url": "https://www.webtoolskit.org"
    },
    "mainEntity": {
      "@type": "ItemList",
      "name": "Developer Tools Collection",
      "description": "Comprehensive collection of free online tools for JSON, XML, and CSV data manipulation.",
      "numberOfItems": 12,
      "itemListElement": [
        {
          "@type": "WebApplication",
          "position": 1,
          "name": "JSON Viewer",
          "description": "View and explore JSON data in a structured, tree-like format for easy analysis.",
          "url": "https://www.webtoolskit.org/p/json-viewer.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["Tree view", "Data exploration", "Syntax highlighting"]
        },
        {
          "@type": "WebApplication",
          "position": 2,
          "name": "JSON Formatter",
          "description": "Format and beautify JSON data with proper indentation and syntax highlighting.",
          "url": "https://www.webtoolskit.org/p/json-formatter.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["Beautify JSON", "Pretty print", "Indentation control"]
        },
        {
          "@type": "WebApplication",
          "position": 3,
          "name": "JSON Validator",
          "description": "Validate JSON syntax and structure to ensure your data is properly formatted.",
          "url": "https://www.webtoolskit.org/p/json-validator.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["Syntax checking", "Error highlighting", "Real-time validation"]
        },
        {
          "@type": "WebApplication",
          "position": 4,
          "name": "JSON Editor",
          "description": "Edit JSON data with syntax highlighting and real-time validation features.",
          "url": "https://www.webtoolskit.org/p/json-editor.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["Live editing", "Syntax validation", "Tree and code views"]
        },
        {
          "@type": "WebApplication",
          "position": 5,
          "name": "JSON Minify",
          "description": "Compress JSON data by removing whitespace and formatting for smaller file sizes.",
          "url": "https://www.webtoolskit.org/p/json-minify.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["Compress JSON", "Remove whitespace", "File size reduction"]
        },
        {
          "@type": "WebApplication",
          "position": 6,
          "name": "XML to JSON Converter",
          "description": "Convert XML data to JSON format for modern web applications and APIs.",
          "url": "https://www.webtoolskit.org/p/xml-to-json.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["XML to JSON conversion", "Attribute handling", "Structured output"]
        },
        {
          "@type": "WebApplication",
          "position": 7,
          "name": "CSV to JSON Converter",
          "description": "Transform CSV spreadsheet data into JSON format for web development projects.",
          "url": "https://www.webtoolskit.org/p/csv-to-json.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["CSV to JSON array", "Header detection", "Data mapping"]
        },
        {
          "@type": "WebApplication",
          "position": 8,
          "name": "JSON to CSV Converter",
          "description": "Convert JSON data to CSV format for spreadsheet applications and data analysis.",
          "url": "https://www.webtoolskit.org/p/json-to-csv.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["JSON to CSV conversion", "Spreadsheet format", "Data export"]
        },
        {
          "@type": "WebApplication",
          "position": 9,
          "name": "TSV to JSON Converter",
          "description": "Convert Tab-Separated Values (TSV) data to JSON format for web applications.",
          "url": "https://www.webtoolskit.org/p/tsv-to-json.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["TSV to JSON array", "Tab-delimited data", "Web format conversion"]
        },
        {
          "@type": "WebApplication",
          "position": 10,
          "name": "JSON to XML Converter",
          "description": "Convert JSON data to XML format for legacy systems and enterprise applications.",
          "url": "https://www.webtoolskit.org/p/json-to-xml.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["JSON to XML conversion", "Element and attribute creation", "Legacy system integration"]
        },
        {
          "@type": "WebApplication",
          "position": 11,
          "name": "JSON to Text Converter",
          "description": "Extract and convert JSON data to plain text format for documentation and reports.",
          "url": "https://www.webtoolskit.org/p/json-to-text.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["Extract text values", "Plain text conversion", "Data stripping"]
        },
        {
          "@type": "WebApplication",
          "position": 12,
          "name": "JSON to TSV Converter",
          "description": "Convert JSON data to Tab-Separated Values (TSV) format for data processing.",
          "url": "https://www.webtoolskit.org/p/json-to-tsv.html",
          "applicationCategory": "DeveloperTool",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
          "featureList": ["JSON to TSV conversion", "Tab-separated output", "Data export"]
        }
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://www.webtoolskit.org"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Developer Tools"
        }
      ]
    }
  }
  </script>

  <style>
    /* Duplicate CSS variables and base styles removed - inherit from main template */

    /* Page-specific styles only */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 8px 16px;
    }

    .page-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .page-title {
      font-size: 36px;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 10px;
      line-height: 1.2;
    }

    .page-description {
      font-size: 1.1rem;
      color: var(--text-color-light);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.5;
    }

    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
      justify-items: center;
    }

    .tool-card {
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 18px;
      text-align: center;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }

    .tool-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      opacity: 0;
      transition: opacity 0.4s ease;
    }

    .tool-card::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .tool-card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 12px 35px rgba(0,0,0,0.2);
      border-color: var(--primary-color);
    }

    .tool-card:hover::before {
      opacity: 1;
    }

    .tool-card:hover::after {
      left: 100%;
    }

    .tool-icon {
      width: 56px;
      height: 56px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      font-size: 22px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .tool-icon::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .tool-card:hover .tool-icon {
      transform: scale(1.1) rotate(5deg);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .tool-card:hover .tool-icon::before {
      opacity: 1;
    }

    /* Distinctive Icon Colors for Development Tools */
    .icon-json-viewer { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; }
    .icon-json-formatter { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-json-validator { background: linear-gradient(135deg, #EC4899, #DB2777); color: white; }
    .icon-json-editor { background: linear-gradient(135deg, #10B981, #059669); color: white; }
    .icon-json-minify { background: linear-gradient(135deg, #F59E0B, #D97706); color: white; }
    .icon-xml-json { background: linear-gradient(135deg, #6366F1, #4F46E5); color: white; }
    .icon-csv-json { background: linear-gradient(135deg, #0EA5E9, #0284C7); color: white; }
    .icon-tsv-json { background: linear-gradient(135deg, #4F46E5, #4338CA); color: white; }
    .icon-json-xml { background: linear-gradient(135deg, #EF4444, #DC2626); color: white; }
    .icon-json-csv { background: linear-gradient(135deg, #14B8A6, #0D9488); color: white; }
    .icon-json-text { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-json-tsv { background: linear-gradient(135deg, #F97316, #EA580C); color: white; }

    .tool-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 8px;
    }

    .tool-description {
      color: var(--text-color-light);
      font-size: 14px;
      margin-bottom: 12px;
      line-height: 1.4;
    }

    .tool-link {
      display: inline-block;
      background: var(--primary-color);
      color: #ffffff !important;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid var(--primary-color);
      box-shadow: 0 2px 8px rgba(0, 71, 171, 0.2);
      position: relative;
      overflow: hidden;
    }

    .tool-link::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    .tool-link:hover {
      background: #003d96;
      border-color: #003d96;
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
      color: #ffffff !important;
    }

    .tool-link:hover::before {
      left: 100%;
    }

    [data-theme="dark"] .tool-link {
      background: #60a5fa;
      border-color: #60a5fa;
      color: #ffffff !important;
      box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
    }

    [data-theme="dark"] .tool-link:hover {
      background: #3b82f6;
      border-color: #3b82f6;
      box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
      color: #ffffff !important;
    }

    /* Mobile Optimization with Scrollable Icons */
    @media (max-width: 768px) {
      .container { padding: 6px 12px; }
      .page-header { margin-bottom: 16px; }
      .page-title { font-size: 28px; margin-bottom: 8px; }
      .page-description { font-size: 1rem; padding: 0 8px; }

      /* Mobile single column layout */
      .tools-grid {
        display: block;
        overflow-x: unset;
        padding: 8px 0;
      }
      .tool-card {
        width: 100%;
        margin: 0 0 12px 0;
        border-radius: 14px;
        padding: 16px;
        min-height: 80px;
        box-sizing: border-box;
        /* Remove all transitions except for description for stability */
      }
      .tool-card .tool-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 8px;
        font-size: 20px;
      }
      .tool-card .tool-title,
      .tool-card .tool-link {
        opacity: 1;
        transform: none;
        pointer-events: auto;
      }
      .tool-card .tool-description {
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        margin: 0;
        transition: opacity 0.3s, max-height 0.3s;
        will-change: opacity, max-height;
        display: block;
      }
      .tool-card.show-description .tool-description {
        opacity: 1;
        max-height: 100px;
        margin-bottom: 10px;
      }
    }
    @media (max-width: 480px) {
      .tool-card { min-width: 200px; max-width: 95vw; padding: 10px; }
      .tool-card .tool-icon { width: 40px; height: 40px; font-size: 18px; }
    }
    @media (max-width: 320px) {
      .tool-card { min-width: 140px; padding: 6px; }
      .tool-card .tool-icon { width: 32px; height: 32px; font-size: 15px; }
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="page-header">
      <h1 id="main-title" class="page-title">Developer Tools – JSON, XML, CSV, Format &amp; Validate Online</h1>
      <p class="page-description">Clean, format, validate, and convert JSON, XML, and CSV data online. Perfect for developers and data analysts working with structured content.</p>
    </header>

    <main id="main-content" role="main" aria-labelledby="main-title">
      <section class="tools-section" aria-labelledby="tools-section-title">
        <h2 id="tools-section-title" class="sr-only">Available Developer Tools</h2>
        <div class="tools-grid" role="list">
          <!-- JSON Viewer -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-json-viewer" aria-hidden="true">
              <i class="fas fa-eye"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">JSON Viewer</h3>
              <p class="tool-description">View and explore JSON data in a structured, tree-like format for easy analysis.</p>
              <a class="tool-link" href="/p/json-viewer.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JSON Formatter -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-json-formatter" aria-hidden="true">
              <i class="fas fa-code"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">JSON Formatter</h3>
              <p class="tool-description">Format and beautify JSON data with proper indentation and syntax highlighting.</p>
              <a class="tool-link" href="/p/json-formatter.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JSON Validator -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-json-validator" aria-hidden="true">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">JSON Validator</h3>
              <p class="tool-description">Validate JSON syntax and structure to ensure your data is properly formatted.</p>
              <a class="tool-link" href="/p/json-validator.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JSON Editor -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-json-editor" aria-hidden="true">
              <i class="fas fa-edit"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">JSON Editor</h3>
              <p class="tool-description">Edit JSON data with syntax highlighting and real-time validation features.</p>
              <a class="tool-link" href="/p/json-editor.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JSON Minify -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-json-minify" aria-hidden="true">
              <i class="fas fa-compress-alt"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">JSON Minify</h3>
              <p class="tool-description">Compress JSON data by removing whitespace and formatting for smaller file sizes.</p>
              <a class="tool-link" href="/p/json-minify.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- XML to JSON -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-xml-json" aria-hidden="true">
              <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">XML to JSON</h3>
              <p class="tool-description">Convert XML data to JSON format for modern web applications and APIs.</p>
              <a class="tool-link" href="/p/xml-to-json.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- CSV to JSON -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-csv-json" aria-hidden="true">
              <i class="fas fa-table"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">CSV to JSON</h3>
              <p class="tool-description">Transform CSV spreadsheet data into JSON format for web development projects.</p>
              <a class="tool-link" href="/p/csv-to-json.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JSON to CSV -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-json-csv" aria-hidden="true">
              <i class="fas fa-file-csv"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">JSON to CSV</h3>
              <p class="tool-description">Convert JSON data to CSV format for spreadsheet applications and data analysis.</p>
              <a class="tool-link" href="/p/json-to-csv.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- TSV to JSON -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-tsv-json" aria-hidden="true">
              <i class="fas fa-columns"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">TSV to JSON</h3>
              <p class="tool-description">Convert Tab-Separated Values (TSV) data to JSON format for web applications.</p>
              <a class="tool-link" href="/p/tsv-to-json.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JSON to XML -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-json-xml" aria-hidden="true">
              <i class="fas fa-code"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">JSON to XML</h3>
              <p class="tool-description">Convert JSON data to XML format for legacy systems and enterprise applications.</p>
              <a class="tool-link" href="/p/json-to-xml.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JSON to Text -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-json-text" aria-hidden="true">
              <i class="fas fa-file-alt"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">JSON to Text</h3>
              <p class="tool-description">Extract and convert JSON data to plain text format for documentation and reports.</p>
              <a class="tool-link" href="/p/json-to-text.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
          <!-- JSON to TSV -->
          <article class="tool-card" role="listitem">
            <div class="tool-icon icon-json-tsv" aria-hidden="true">
              <i class="fas fa-table"></i>
            </div>
            <div class="tool-content">
              <h3 class="tool-title">JSON to TSV</h3>
              <p class="tool-description">Convert JSON data to Tab-Separated Values (TSV) format for data processing.</p>
              <a class="tool-link" href="/p/json-to-tsv.html" rel="noopener">Try this tool →</a>
            </div>
          </article>
        </div>
      </section>
    </main>
  </div>
  
  <script>
    // --- SIMPLIFIED: Expand card on tap for mobile ---
    document.addEventListener('DOMContentLoaded', function() {
      if (window.innerWidth > 768) return;
      const toolCards = document.querySelectorAll('.tool-card');
      toolCards.forEach(card => {
        card.addEventListener('click', function() {
          toolCards.forEach(c => c.classList.remove('expanded'));
          this.classList.add('expanded');
        });
      });
    });

    // Mobile: Description toggles on click/tap, always works instantly
    document.addEventListener('DOMContentLoaded', function() {
      if (window.innerWidth > 768) return;
      const toolCards = document.querySelectorAll('.tool-card');
      // Prevent double event firing on touch devices
      let lastTouch = 0;
      toolCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.classList.add('show-description');
        });
        card.addEventListener('mouseleave', function() {
          this.classList.remove('show-description');
        });
        card.addEventListener('touchend', function(e) {
          e.preventDefault();
          lastTouch = Date.now();
          if (this.classList.contains('show-description')) {
            this.classList.remove('show-description');
          } else {
            toolCards.forEach(c => c.classList.remove('show-description'));
            this.classList.add('show-description');
          }
        }, { passive: false });
        card.addEventListener('click', function(e) {
          // Ignore click if just handled by touch
          if (Date.now() - lastTouch < 500) return;
          if (this.classList.contains('show-description')) {
            this.classList.remove('show-description');
          } else {
            toolCards.forEach(c => c.classList.remove('show-description'));
            this.classList.add('show-description');
          }
        });
      });
    });
  </script>

</body>
</html>