<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text to Decimal Converter - Free Online Tool</title>
    <meta name="description" content="Convert any text or string into its decimal (ASCII/Unicode) values instantly. Our free online Text to Decimal converter is perfect for developers and data analysis.">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Text to Decimal Converter - Get ASCII/Unicode Values",
        "description": "Convert any text or string into its decimal (ASCII/Unicode) values instantly. Our free online Text to Decimal converter is perfect for developers and data analysis.",
        "url": "https://www.webtoolskit.org/p/text-to-decimal.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-19",
        "dateModified": "2025-06-19",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Text to Decimal Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Text to Decimal" },
            { "@type": "CopyAction", "name": "Copy Decimal Values" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert text to decimal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert text to decimal, each character in the text is replaced by its numerical value from a character encoding standard like ASCII or Unicode. For example, using the ASCII standard, the letter 'A' is assigned the decimal value 65."
          }
        },
        {
          "@type": "Question",
          "name": "What is the decimal value of the letter A?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The decimal value of the uppercase letter 'A' is 65 in the ASCII character set. The lowercase letter 'a' has a decimal value of 97."
          }
        },
        {
          "@type": "Question",
          "name": "What is text to decimal ASCII conversion?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Text to decimal ASCII conversion is the process of taking a string of text and converting each character into its corresponding decimal number as defined by the ASCII (American Standard Code for Information Interchange) standard. This standard assigns a unique number from 0 to 127 to each English letter, digit, and common symbol."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert a string to a decimal number?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "If you mean converting a string of text (like 'Hello') to decimal values, you convert each character to its ASCII/Unicode decimal code. If you mean converting a string of digits (like '123') to a number, programming languages have built-in functions like parseInt() or Number() to do this. This tool focuses on the first case, converting each character to its code."
          }
        },
        {
          "@type": "Question",
          "name": "What is 'Hello' in decimal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The text 'Hello' converted to decimal ASCII values is '72 101 108 108 111'. Each number represents one character: 72 = 'H', 101 = 'e', 108 = 'l', 108 = 'l', and 111 = 'o'."
          }
        }
      ]
    }
    </script>


    <style>
        /* Text to Decimal Widget - Simplified & Template Compatible */
        .text-to-decimal-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .text-to-decimal-widget-container * { box-sizing: border-box; }

        .text-to-decimal-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-to-decimal-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .text-to-decimal-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .text-to-decimal-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .text-to-decimal-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }
        
        .text-to-decimal-options {
            margin-bottom: var(--spacing-xl);
        }
        
        .text-to-decimal-separator-input {
            width: 100%;
            max-width: 200px;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            font-size: var(--font-size-base);
        }

        .text-to-decimal-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .text-to-decimal-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .text-to-decimal-btn:hover { transform: translateY(-2px); }

        .text-to-decimal-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .text-to-decimal-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .text-to-decimal-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .text-to-decimal-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .text-to-decimal-btn-success {
            background-color: #10b981;
            color: white;
        }

        .text-to-decimal-btn-success:hover {
            background-color: #059669;
        }

        .text-to-decimal-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .text-to-decimal-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-to-decimal-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .text-to-decimal-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .text-to-decimal-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .text-to-decimal-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .text-to-decimal-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-to-decimal-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .text-to-decimal-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .text-to-decimal-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .text-to-decimal-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="text-to-ascii"] .text-to-decimal-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="decimal-to-text"] .text-to-decimal-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="text-to-binary"] .text-to-decimal-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .text-to-decimal-related-tool-item:hover .text-to-decimal-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="text-to-ascii"]:hover .text-to-decimal-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="decimal-to-text"]:hover .text-to-decimal-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="text-to-binary"]:hover .text-to-decimal-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .text-to-decimal-related-tool-item { box-shadow: none; border: none; }
        .text-to-decimal-related-tool-item:hover { box-shadow: none; border: none; }
        .text-to-decimal-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .text-to-decimal-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .text-to-decimal-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .text-to-decimal-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .text-to-decimal-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .text-to-decimal-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .text-to-decimal-related-tool-item:hover .text-to-decimal-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .text-to-decimal-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .text-to-decimal-widget-title { font-size: 1.875rem; }
            .text-to-decimal-buttons { flex-direction: column; }
            .text-to-decimal-btn { flex: none; }
            .text-to-decimal-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .text-to-decimal-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .text-to-decimal-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .text-to-decimal-related-tool-name { font-size: 0.875rem; }
            .text-to-decimal-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .text-to-decimal-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .text-to-decimal-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .text-to-decimal-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .text-to-decimal-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .text-to-decimal-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .text-to-decimal-btn:focus, .text-to-decimal-separator-input:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .text-to-decimal-output::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="text-to-decimal-widget-container">
        <h1 class="text-to-decimal-widget-title">Text to Decimal Converter</h1>
        <p class="text-to-decimal-widget-description">
            Convert any text into a sequence of decimal numbers based on ASCII or Unicode character codes. A simple, fast, and free encoding tool.
        </p>
        
        <div class="text-to-decimal-input-group">
            <label for="textToDecimalInput" class="text-to-decimal-label">Enter your text:</label>
            <textarea 
                id="textToDecimalInput" 
                class="text-to-decimal-textarea"
                placeholder="Type or paste your text here..."
                rows="4"
            ></textarea>
        </div>

        <div class="text-to-decimal-options">
            <label for="textToDecimalSeparator" class="text-to-decimal-label">Separator:</label>
            <input 
                type="text" 
                id="textToDecimalSeparator" 
                class="text-to-decimal-separator-input" 
                value=" "
                placeholder="e.g., space, comma..."
            >
        </div>

        <div class="text-to-decimal-buttons">
            <button class="text-to-decimal-btn text-to-decimal-btn-primary" onclick="TextToDecimalConverter.convert()">
                Convert to Decimal
            </button>
            <button class="text-to-decimal-btn text-to-decimal-btn-secondary" onclick="TextToDecimalConverter.clear()">
                Clear All
            </button>
            <button class="text-to-decimal-btn text-to-decimal-btn-success" onclick="TextToDecimalConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="text-to-decimal-result">
            <h3 class="text-to-decimal-result-title">Decimal Output:</h3>
            <div class="text-to-decimal-output" id="textToDecimalOutput">
                Your decimal values will appear here...
            </div>
        </div>

        <div class="text-to-decimal-related-tools">
            <h3 class="text-to-decimal-related-tools-title">Related Tools</h3>
            <div class="text-to-decimal-related-tools-grid">
                <a href="/p/text-to-ascii.html" class="text-to-decimal-related-tool-item" rel="noopener">
                    <div class="text-to-decimal-related-tool-icon">
                        <i class="fas fa-keyboard"></i>
                    </div>
                    <div class="text-to-decimal-related-tool-name">Text to ASCII</div>
                </a>
                
                <a href="/p/decimal-to-text.html" class="text-to-decimal-related-tool-item" rel="noopener">
                    <div class="text-to-decimal-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="text-to-decimal-related-tool-name">Decimal to Text</div>
                </a>

                <a href="/p/text-to-binary.html" class="text-to-decimal-related-tool-item" rel="noopener">
                    <div class="text-to-decimal-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="text-to-decimal-related-tool-name">Text to Binary</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Understanding Text to Decimal Conversion</h2>
            <p>Our <strong>Text to Decimal Converter</strong> is a straightforward tool that reveals the numeric foundation of digital text. Every character on your screen, from letters and numbers to symbols and emojis, is represented by a unique number in the computer's memory. This tool takes your text and converts each character into its corresponding decimal (base-10) value according to standards like ASCII and Unicode. This process is fundamental to understanding how computers handle and process text data.</p>
            <p>This converter is especially useful for programmers who need to work with character codes, students learning about data representation, and anyone curious about the inner workings of digital information. It provides a clear, immediate look at the decimal values that form the words you write.</p>
            
            <h3>How to Use the Text to Decimal Converter</h3>
            <ol>
                <li><strong>Enter Text:</strong> Type or paste any string of characters into the input box.</li>
                <li><strong>Choose Your Separator:</strong> You can define how each decimal value is separated in the output. A space is used by default, but you can use a comma or any other character.</li>
                <li><strong>Click Convert:</strong> Press the "Convert to Decimal" button. The tool will instantly process your text.</li>
                <li><strong>Get the Result:</strong> The decimal values for your text will appear in the output box, ready to be copied.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Text to Decimal Conversion</h3>
            
            <h4>How do you convert text to decimal?</h4>
            <p>To convert text to decimal, each character in the text is replaced by its numerical value from a character encoding standard like ASCII or Unicode. For example, using the ASCII standard, the letter 'A' is assigned the decimal value 65.</p>
            
            <h4>What is the decimal value of the letter A?</h4>
            <p>The decimal value of the uppercase letter 'A' is 65 in the ASCII character set. The lowercase letter 'a' has a decimal value of 97.</p>
            
            <h4>What is text to decimal ASCII conversion?</h4>
            <p>Text to decimal ASCII conversion is the process of taking a string of text and converting each character into its corresponding decimal number as defined by the ASCII (American Standard Code for Information Interchange) standard. This standard assigns a unique number from 0 to 127 to each English letter, digit, and common symbol.</p>
            
            <h4>How do you convert a string to a decimal number?</h4>
            <p>If you mean converting a string of text (like 'Hello') to decimal values, you convert each character to its ASCII/Unicode decimal code. If you mean converting a string of digits (like '123') to a number, programming languages have built-in functions like <code>parseInt()</code> or <code>Number()</code> to do this. This tool focuses on the first case, converting each character to its code.</p>
            
            <h4>What is "Hello" in decimal?</h4>
            <p>The text 'Hello' converted to decimal ASCII values is <code>72 101 108 108 111</code>. Each number represents one character: 72 = 'H', 101 = 'e', 108 = 'l', 108 = 'l', and 111 = 'o'.</p>
        </div>


        <div class="text-to-decimal-features">
            <h3 class="text-to-decimal-features-title">Key Features:</h3>
            <ul class="text-to-decimal-features-list">
                <li class="text-to-decimal-features-item">Instant text to decimal conversion</li>
                <li class="text-to-decimal-features-item">Supports all Unicode characters</li>
                <li class="text-to-decimal-features-item">Customizable output separator</li>
                <li class="text-to-decimal-features-item">Clean, simple, and fast interface</li>
                <li class="text-to-decimal-features-item">One-click copy to clipboard</li>
                <li class="text-to-decimal-features-item">Useful for educational purposes</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="text-to-decimal-notification" id="textToDecimalNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Text to Decimal Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('textToDecimalInput'),
                output: () => document.getElementById('textToDecimalOutput'),
                notification: () => document.getElementById('textToDecimalNotification'),
                separator: () => document.getElementById('textToDecimalSeparator')
            };

            window.TextToDecimalConverter = {
                convert() {
                    const inputEl = elements.input();
                    const outputEl = elements.output();
                    const separator = elements.separator().value;
                    const text = inputEl.value;

                    if (!text.trim()) {
                        outputEl.textContent = 'Please enter text to convert.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }

                    outputEl.style.color = '';
                    
                    try {
                        const decimalCodes = [];
                        for (let i = 0; i < text.length; i++) {
                            const charCode = text.charCodeAt(i);
                            decimalCodes.push(charCode);
                        }
                        outputEl.textContent = decimalCodes.join(separator);
                    } catch (error) {
                        outputEl.textContent = `Error: An unexpected error occurred.`;
                        outputEl.style.color = '#dc2626';
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your decimal values will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your decimal values will appear here...', 'Please enter text to convert.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        TextToDecimalConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>