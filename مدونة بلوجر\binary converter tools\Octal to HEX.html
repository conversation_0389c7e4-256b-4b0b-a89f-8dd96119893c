<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octal to HEX Converter - Free Online Tool</title>
    <meta name="description" content="Convert octal (base-8) numbers to hexadecimal (base-16) instantly with our free online tool. Fast, accurate, and easy to use for developers, students, and enthusiasts.">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Octal to HEX Converter - Convert Base-8 to Base-16 Online",
        "description": "Convert octal (base-8) numbers to hexadecimal (base-16) instantly with our free online tool. Fast, accurate, and easy to use for developers, students, and enthusiasts.",
        "url": "https://www.webtoolskit.org/p/octal-to-hex.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-19",
        "dateModified": "2025-06-19",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Octal to HEX Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Octal to HEX" },
            { "@type": "CopyAction", "name": "Copy HEX Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert Octal to HEX?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The standard method is to use binary as an intermediate base. First, convert each octal digit to its 3-bit binary equivalent. After you have the full binary string, regroup the bits into sets of 4, starting from the right. Finally, convert each 4-bit group into its corresponding hexadecimal digit."
          }
        },
        {
          "@type": "Question",
          "name": "What is the hex equivalent of the octal number 77?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The hexadecimal equivalent of the octal number 77 is 3F. Here's how: each '7' in octal is '111' in binary. So, 77 in octal is 111111 in binary. Regrouping these 6 bits into a 4-bit group and a 2-bit group (padded to 4 bits) gives 0011 and 1111. 0011 is '3' in hex, and 1111 is 'F' in hex. This results in 3F."
          }
        },
        {
          "@type": "Question",
          "name": "What is the easiest way to convert Octal to HEX?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The easiest and most reliable way is to use an online Octal to HEX converter like this one. It automates the multi-step conversion process, eliminates the chance of human error, and provides the result instantly, which is ideal for both learning and professional work."
          }
        },
        {
          "@type": "Question",
          "name": "Is it better to use Octal or Hexadecimal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "In modern computing, hexadecimal is far more common. Because computers work with bytes (8 bits), hex is a natural fit as one hex digit represents exactly half a byte (4 bits). Octal, where one digit represents 3 bits, doesn't align as neatly with byte-based architecture and is now mostly seen in legacy systems or specific applications like file permissions in Unix/Linux."
          }
        },
        {
          "@type": "Question",
          "name": "Why is binary used to convert between Octal and HEX?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Binary is used as an intermediary because both octal (base-8) and hexadecimal (base-16) are powers of two (8 = 2^3 and 16 = 2^4). This means there's a direct and simple relationship between their digits and groups of binary bits. Converting through binary is much more straightforward than trying to convert directly using complex division or multiplication."
          }
        }
      ]
    }
    </script>


    <style>
        /* Octal to HEX Widget - Simplified & Template Compatible */
        .octal-to-hex-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .octal-to-hex-widget-container * { box-sizing: border-box; }

        .octal-to-hex-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .octal-to-hex-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .octal-to-hex-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .octal-to-hex-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .octal-to-hex-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .octal-to-hex-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .octal-to-hex-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .octal-to-hex-btn:hover { transform: translateY(-2px); }

        .octal-to-hex-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .octal-to-hex-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .octal-to-hex-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .octal-to-hex-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .octal-to-hex-btn-success {
            background-color: #10b981;
            color: white;
        }

        .octal-to-hex-btn-success:hover {
            background-color: #059669;
        }

        .octal-to-hex-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .octal-to-hex-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .octal-to-hex-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .octal-to-hex-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .octal-to-hex-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .octal-to-hex-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .octal-to-hex-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .octal-to-hex-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .octal-to-hex-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .octal-to-hex-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .octal-to-hex-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="hex-to-octal"] .octal-to-hex-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="octal-to-decimal"] .octal-to-hex-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="octal-to-binary"] .octal-to-hex-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .octal-to-hex-related-tool-item:hover .octal-to-hex-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="hex-to-octal"]:hover .octal-to-hex-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="octal-to-decimal"]:hover .octal-to-hex-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="octal-to-binary"]:hover .octal-to-hex-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .octal-to-hex-related-tool-item { box-shadow: none; border: none; }
        .octal-to-hex-related-tool-item:hover { box-shadow: none; border: none; }
        .octal-to-hex-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .octal-to-hex-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .octal-to-hex-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .octal-to-hex-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .octal-to-hex-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .octal-to-hex-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .octal-to-hex-related-tool-item:hover .octal-to-hex-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .octal-to-hex-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .octal-to-hex-widget-title { font-size: 1.875rem; }
            .octal-to-hex-buttons { flex-direction: column; }
            .octal-to-hex-btn { flex: none; }
            .octal-to-hex-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .octal-to-hex-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .octal-to-hex-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .octal-to-hex-related-tool-name { font-size: 0.875rem; }
            .octal-to-hex-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .octal-to-hex-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .octal-to-hex-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .octal-to-hex-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .octal-to-hex-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .octal-to-hex-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .octal-to-hex-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .octal-to-hex-output::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="octal-to-hex-widget-container">
        <h1 class="octal-to-hex-widget-title">Octal to HEX Converter</h1>
        <p class="octal-to-hex-widget-description">
            A fast and free tool to convert octal (base-8) numbers to hexadecimal (base-16) with complete accuracy.
        </p>
        
        <div class="octal-to-hex-input-group">
            <label for="octalToHexInput" class="octal-to-hex-label">Enter an octal number:</label>
            <textarea 
                id="octalToHexInput" 
                class="octal-to-hex-textarea"
                placeholder="Enter an octal number here (e.g., 377)..."
                rows="4"
            ></textarea>
        </div>

        <div class="octal-to-hex-buttons">
            <button class="octal-to-hex-btn octal-to-hex-btn-primary" onclick="OctalToHexConverter.convert()">
                Convert to HEX
            </button>
            <button class="octal-to-hex-btn octal-to-hex-btn-secondary" onclick="OctalToHexConverter.clear()">
                Clear All
            </button>
            <button class="octal-to-hex-btn octal-to-hex-btn-success" onclick="OctalToHexConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="octal-to-hex-result">
            <h3 class="octal-to-hex-result-title">HEX Result:</h3>
            <div class="octal-to-hex-output" id="octalToHexOutput">
                Your HEX result will appear here...
            </div>
        </div>

        <div class="octal-to-hex-related-tools">
            <h3 class="octal-to-hex-related-tools-title">Related Tools</h3>
            <div class="octal-to-hex-related-tools-grid">
                <a href="/p/hex-to-octal.html" class="octal-to-hex-related-tool-item" rel="noopener">
                    <div class="octal-to-hex-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="octal-to-hex-related-tool-name">HEX to Octal</div>
                </a>

                <a href="/p/octal-to-decimal.html" class="octal-to-hex-related-tool-item" rel="noopener">
                    <div class="octal-to-hex-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="octal-to-hex-related-tool-name">Octal to Decimal</div>
                </a>

                <a href="/p/octal-to-binary.html" class="octal-to-hex-related-tool-item" rel="noopener">
                    <div class="octal-to-hex-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="octal-to-hex-related-tool-name">Octal to Binary</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Instant Octal to HEX Conversion</h2>
            <p>Our <strong>Octal to HEX Converter</strong> simplifies the process of translating numbers from the octal (base-8) system to the hexadecimal (base-16) system. This conversion is a common requirement in computer science and programming, especially when working with different layers of system architecture. While octal is known for its use in file permissions, hexadecimal is dominant in memory addressing and data representation. This tool seamlessly bridges the two, eliminating complex and error-prone manual calculations.</p>
            
            <h3>How Our Converter Works</h3>
            <p>Manual conversion between octal and hex requires multiple steps, usually involving binary as an intermediate stage. Our tool automates this for you instantly.</p>
            <ol>
                <li><strong>Enter Octal Number:</strong> Input your base-8 number (using digits 0-7) into the text area.</li>
                <li><strong>Click Convert:</strong> Hit the "Convert to HEX" button.</li>
                <li><strong>Get HEX Result:</strong> The tool instantly calculates and displays the equivalent base-16 number in the result box, ready to be copied and used.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Octal to HEX Conversion</h3>
            
            <h4>How do you convert Octal to HEX?</h4>
            <p>The standard method is to use binary as an intermediate base. First, convert each octal digit to its 3-bit binary equivalent. After you have the full binary string, regroup the bits into sets of 4, starting from the right. Finally, convert each 4-bit group into its corresponding hexadecimal digit.</p>
            
            <h4>What is the hex equivalent of the octal number 77?</h4>
            <p>The hexadecimal equivalent of the octal number <code>77</code> is <code>3F</code>. Here's how: each '7' in octal is '111' in binary. So, 77 in octal is <code>111111</code> in binary. Regrouping these 6 bits into a 4-bit group and a 2-bit group (padded to 4 bits) gives <code>0011</code> and <code>1111</code>. <code>0011</code> is '3' in hex, and <code>1111</code> is 'F' in hex. This results in 3F.</p>
            
            <h4>What is the easiest way to convert Octal to HEX?</h4>
            <p>The easiest and most reliable way is to use an online Octal to HEX converter like this one. It automates the multi-step conversion process, eliminates the chance of human error, and provides the result instantly, which is ideal for both learning and professional work.</p>
            
            <h4>Is it better to use Octal or Hexadecimal?</h4>
            <p>In modern computing, hexadecimal is far more common. Because computers work with bytes (8 bits), hex is a natural fit as one hex digit represents exactly half a byte (4 bits). Octal, where one digit represents 3 bits, doesn't align as neatly with byte-based architecture and is now mostly seen in legacy systems or specific applications like file permissions in Unix/Linux.</p>
            
            <h4>Why is binary used to convert between Octal and HEX?</h4>
            <p>Binary is used as an intermediary because both octal (base-8) and hexadecimal (base-16) are powers of two (8 = 2³ and 16 = 2⁴). This means there's a direct and simple relationship between their digits and groups of binary bits. Converting through binary is much more straightforward than trying to convert directly using complex division or multiplication.</p>
        </div>


        <div class="octal-to-hex-features">
            <h3 class="octal-to-hex-features-title">Key Features:</h3>
            <ul class="octal-to-hex-features-list">
                <li class="octal-to-hex-features-item">Instant and accurate conversion</li>
                <li class="octal-to-hex-features-item">Supports large octal numbers</li>
                <li class="octal-to-hex-features-item">Clean, modern, and easy-to-use UI</li>
                <li class="octal-to-hex-features-item">Validates input to prevent errors</li>
                <li class="octal-to-hex-features-item">One-click copy for results</li>
                <li class="octal-to-hex-features-item">Fully responsive for all devices</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="octal-to-hex-notification" id="octalToHexNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Octal to HEX Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('octalToHexInput'),
                output: () => document.getElementById('octalToHexOutput'),
                notification: () => document.getElementById('octalToHexNotification')
            };

            window.OctalToHexConverter = {
                convert() {
                    const inputEl = elements.input();
                    const outputEl = elements.output();
                    let octalValue = inputEl.value.trim();

                    if (!octalValue) {
                        outputEl.textContent = 'Please enter an octal number.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }

                    // Sanitize input to only allow valid octal characters (0-7)
                    const sanitizedOctal = octalValue.replace(/[^0-7]/g, '');
                    if (sanitizedOctal !== octalValue) {
                        outputEl.textContent = 'Invalid input. Please enter a valid octal number (0-7).';
                        outputEl.style.color = '#dc2626';
                        return;
                    }
                    if (!sanitizedOctal) {
                        outputEl.textContent = 'Please enter an octal number.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }

                    outputEl.style.color = '';
                    
                    try {
                        // Use BigInt for large number support. Prepend '0o' for BigInt to recognize it as octal.
                        const decimalBigInt = BigInt('0o' + sanitizedOctal);
                        const hexResult = decimalBigInt.toString(16).toUpperCase();
                        outputEl.textContent = hexResult;
                    } catch (error) {
                        outputEl.textContent = `Error: Input is invalid or too large to process.`;
                        outputEl.style.color = '#dc2626';
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your HEX result will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your HEX result will appear here...', 'Please enter an octal number.', 'Invalid input. Please enter a valid octal number (0-7).'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        OctalToHexConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>