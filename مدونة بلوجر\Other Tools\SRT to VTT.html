<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free SRT to VTT Converter - Convert SubRip to WebVTT Online</title>
    <meta name="description" content="Convert SRT subtitle files to VTT format instantly with our free SRT to VTT Converter. Perfect for HTML5 video players, web development, and online streaming.">
    <meta name="keywords" content="srt to vtt, srt to vtt converter, subrip to webvtt, subtitle converter, html5 video subtitles, webvtt converter">
    <link rel="canonical" href="https://www.webtoolskit.org/p/srt-to-vtt.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free SRT to VTT Converter - Convert SubRip to WebVTT Online",
        "description": "Convert SRT subtitle files to VTT format instantly with our free SRT to VTT Converter. Perfect for HTML5 video players, web development, and online streaming.",
        "url": "https://www.webtoolskit.org/p/srt-to-vtt.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "SRT to VTT Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "SRT to VTT conversion",
                "SubRip subtitle processing",
                "HTML5 video compatibility",
                "Web development utility",
                "WebVTT format generation"
            ]
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert SRT to VTT" },
            { "@type": "DownloadAction", "name": "Download VTT File" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I convert SRT to VTT subtitle format?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Paste your SRT subtitle content into the input field above and click 'Convert to VTT'. Our tool will instantly convert the SubRip format to WebVTT format, which you can then copy or download for use with HTML5 video players and web applications."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between SRT and VTT files?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "SRT (SubRip) is a simple subtitle format with numbered sequences and comma-separated timestamps. VTT (WebVTT) is designed for web browsers with dot-separated timestamps, WEBVTT header, and support for styling and positioning features."
          }
        },
        {
          "@type": "Question",
          "name": "Can I use VTT files for HTML5 video players?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, VTT is the preferred subtitle format for HTML5 video players and web browsers. It's specifically designed for web use and supports advanced features like styling, positioning, and metadata that SRT doesn't offer."
          }
        },
        {
          "@type": "Question",
          "name": "How do I convert SubRip subtitles to WebVTT?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our converter handles the technical conversion automatically. It adds the WEBVTT header, converts timestamp formats (00:00:00,000 to 00:00.000), removes sequence numbers, and ensures proper WebVTT formatting for web compatibility."
          }
        },
        {
          "@type": "Question",
          "name": "Which subtitle format is better for web videos SRT or VTT?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "VTT is better for web videos as it's specifically designed for HTML5 video players and browsers. It supports advanced features like styling, positioning, and metadata. SRT is better for desktop video players and editing software with broader compatibility."
          }
        }
      ]
    }
    </script>

    <style>
        /* SRT to VTT Converter Widget - Simplified & Template Compatible */
        .srt-vtt-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .srt-vtt-widget-container * { box-sizing: border-box; }

        .srt-vtt-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .srt-vtt-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .srt-vtt-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .srt-vtt-field {
            display: flex;
            flex-direction: column;
        }

        .srt-vtt-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .srt-vtt-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            resize: vertical;
            min-height: 200px;
        }

        .srt-vtt-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .srt-vtt-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .srt-vtt-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .srt-vtt-btn:hover { transform: translateY(-2px); }

        .srt-vtt-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .srt-vtt-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .srt-vtt-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .srt-vtt-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .srt-vtt-btn-success {
            background-color: #10b981;
            color: white;
        }

        .srt-vtt-btn-success:hover {
            background-color: #059669;
        }

        .srt-vtt-btn-download {
            background-color: #8b5cf6;
            color: white;
        }

        .srt-vtt-btn-download:hover {
            background-color: #7c3aed;
        }

        .srt-vtt-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .srt-vtt-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .srt-vtt-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 400px;
        }

        .srt-vtt-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .srt-vtt-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        @media (max-width: 768px) {
            .srt-vtt-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .srt-vtt-widget-title { font-size: 1.875rem; }
            .srt-vtt-buttons { flex-direction: column; }
            .srt-vtt-btn { flex: none; }
        }

        [data-theme="dark"] .srt-vtt-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .srt-vtt-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .srt-vtt-output::selection { background-color: var(--primary-color); color: white; }

        .srt-vtt-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="vtt-to-srt"] .srt-vtt-related-tool-icon { background: linear-gradient(145deg, #3B82F6, #2563EB); }
        a[href*="youtube-thumbnail-downloader"] .srt-vtt-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="base64-decode"] .srt-vtt-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }

        .srt-vtt-related-tool-item:hover .srt-vtt-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="vtt-to-srt"]:hover .srt-vtt-related-tool-icon { background: linear-gradient(145deg, #60a5fa, #3b82f6); }
        a[href*="youtube-thumbnail-downloader"]:hover .srt-vtt-related-tool-icon { background: linear-gradient(145deg, #38d9a9, #20c997); }
        a[href*="base64-decode"]:hover .srt-vtt-related-tool-icon { background: linear-gradient(145deg, #34d399, #10b981); }

        .srt-vtt-related-tool-item { box-shadow: none; border: none; }
        .srt-vtt-related-tool-item:hover { box-shadow: none; border: none; }
        .srt-vtt-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .srt-vtt-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .srt-vtt-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .srt-vtt-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .srt-vtt-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .srt-vtt-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .srt-vtt-related-tool-item:hover .srt-vtt-related-tool-name { color: var(--primary-color); }

        .srt-vtt-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .srt-vtt-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .srt-vtt-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .srt-vtt-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .srt-vtt-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .srt-vtt-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .srt-vtt-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .srt-vtt-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .srt-vtt-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .srt-vtt-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .srt-vtt-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .srt-vtt-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .srt-vtt-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .srt-vtt-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="srt-vtt-widget-container">
        <h1 class="srt-vtt-widget-title">SRT to VTT Converter</h1>
        <p class="srt-vtt-widget-description">
            Convert SubRip subtitle files to WebVTT format instantly. Perfect for HTML5 video players, web development, and online streaming platforms.
        </p>

        <form class="srt-vtt-form">
            <div class="srt-vtt-field">
                <label for="srtInput" class="srt-vtt-label">Paste SRT Content:</label>
                <textarea
                    id="srtInput"
                    class="srt-vtt-textarea"
                    placeholder="Paste your SRT subtitle content here...

Example:
1
00:00:00,000 --> 00:00:02,000
Hello, welcome to our video!

2
00:00:02,000 --> 00:00:05,000
This is a sample subtitle."
                ></textarea>
            </div>
        </form>

        <div class="srt-vtt-buttons">
            <button class="srt-vtt-btn srt-vtt-btn-primary" onclick="SRTtoVTTConverter.convert()">
                Convert to VTT
            </button>
            <button class="srt-vtt-btn srt-vtt-btn-secondary" onclick="SRTtoVTTConverter.clear()">
                Clear All
            </button>
            <button class="srt-vtt-btn srt-vtt-btn-success" onclick="SRTtoVTTConverter.copy()">
                Copy VTT
            </button>
            <button class="srt-vtt-btn srt-vtt-btn-download" onclick="SRTtoVTTConverter.download()">
                Download VTT
            </button>
        </div>

        <div class="srt-vtt-result">
            <h3 class="srt-vtt-result-title">Converted VTT Content:</h3>
            <div class="srt-vtt-output" id="vttOutput">Your converted WebVTT subtitle content will appear here...</div>
        </div>

        <div class="srt-vtt-related-tools">
            <h3 class="srt-vtt-related-tools-title">Related Tools</h3>
            <div class="srt-vtt-related-tools-grid">
                <a href="/p/vtt-to-srt.html" class="srt-vtt-related-tool-item" rel="noopener">
                    <div class="srt-vtt-related-tool-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="srt-vtt-related-tool-name">VTT to SRT</div>
                </a>

                <a href="/p/youtube-thumbnail-downloader.html" class="srt-vtt-related-tool-item" rel="noopener">
                    <div class="srt-vtt-related-tool-icon">
                        <i class="fab fa-youtube"></i>
                    </div>
                    <div class="srt-vtt-related-tool-name">YouTube Thumbnail Downloader</div>
                </a>

                <a href="/p/base64-decode.html" class="srt-vtt-related-tool-item" rel="noopener">
                    <div class="srt-vtt-related-tool-icon">
                        <i class="fas fa-unlock-alt"></i>
                    </div>
                    <div class="srt-vtt-related-tool-name">Base64 Decode</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional SRT to VTT Converter for Web Development</h2>
            <p>Our <strong>SRT to VTT Converter</strong> transforms SubRip subtitle files into WebVTT format instantly, making your subtitles compatible with HTML5 video players and modern web browsers. Whether you're developing web applications, creating online courses, or managing video content, our tool ensures seamless subtitle format conversion for web platforms.</p>
            <p>Perfect for web developers, content creators, and video streaming platforms who need to convert traditional SRT subtitles to the web-optimized VTT format. The tool handles all technical formatting requirements automatically, ensuring your subtitles work perfectly with HTML5 video elements and modern web standards.</p>

            <h3>How to Use the SRT to VTT Converter</h3>
            <ol>
                <li><strong>Paste SRT Content:</strong> Copy your SubRip subtitle content and paste it into the input field above.</li>
                <li><strong>Convert Format:</strong> Click "Convert to VTT" to transform the SRT format to WebVTT format instantly.</li>
                <li><strong>Copy or Download:</strong> Use "Copy VTT" to copy the result or "Download VTT" to save as a file.</li>
                <li><strong>Use in Web Projects:</strong> Import the converted VTT file into your HTML5 video player or web application.</li>
            </ol>

            <h3>Frequently Asked Questions About SRT to VTT Conversion</h3>

            <h4>How do I convert SRT to VTT subtitle format?</h4>
            <p>Paste your SRT subtitle content into the input field above and click 'Convert to VTT'. Our tool will instantly convert the SubRip format to WebVTT format, which you can then copy or download for use with HTML5 video players and web applications.</p>

            <h4>What is the difference between SRT and VTT files?</h4>
            <p>SRT (SubRip) is a simple subtitle format with numbered sequences and comma-separated timestamps. VTT (WebVTT) is designed for web browsers with dot-separated timestamps, WEBVTT header, and support for styling and positioning features.</p>

            <h4>Can I use VTT files for HTML5 video players?</h4>
            <p>Yes, VTT is the preferred subtitle format for HTML5 video players and web browsers. It's specifically designed for web use and supports advanced features like styling, positioning, and metadata that SRT doesn't offer.</p>

            <h4>How do I convert SubRip subtitles to WebVTT?</h4>
            <p>Our converter handles the technical conversion automatically. It adds the WEBVTT header, converts timestamp formats (00:00:00,000 to 00:00.000), removes sequence numbers, and ensures proper WebVTT formatting for web compatibility.</p>

            <h4>Which subtitle format is better for web videos SRT or VTT?</h4>
            <p>VTT is better for web videos as it's specifically designed for HTML5 video players and browsers. It supports advanced features like styling, positioning, and metadata. SRT is better for desktop video players and editing software with broader compatibility.</p>
        </div>

        <div class="srt-vtt-features">
            <h3 class="srt-vtt-features-title">Key Features:</h3>
            <ul class="srt-vtt-features-list">
                <li class="srt-vtt-features-item" style="margin-bottom: 0.3em;">Instant SRT to VTT Conversion</li>
                <li class="srt-vtt-features-item" style="margin-bottom: 0.3em;">SubRip Format Processing</li>
                <li class="srt-vtt-features-item" style="margin-bottom: 0.3em;">HTML5 Video Compatibility</li>
                <li class="srt-vtt-features-item" style="margin-bottom: 0.3em;">Web Browser Support</li>
                <li class="srt-vtt-features-item" style="margin-bottom: 0.3em;">Download VTT Files</li>
                <li class="srt-vtt-features-item" style="margin-bottom: 0.3em;">Mobile-Friendly Interface</li>
                <li class="srt-vtt-features-item">100% Free and Accurate</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="srt-vtt-notification" id="srtVttNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                srtInput: () => document.getElementById('srtInput'),
                vttOutput: () => document.getElementById('vttOutput'),
                notification: () => document.getElementById('srtVttNotification')
            };

            function convertSRTtoVTT(srtContent) {
                if (!srtContent || !srtContent.trim()) {
                    throw new Error('Please provide SRT content to convert');
                }

                let lines = srtContent.trim().split('\n');
                let vttContent = 'WEBVTT\n\n';
                let i = 0;

                while (i < lines.length) {
                    // Skip empty lines
                    while (i < lines.length && lines[i].trim() === '') {
                        i++;
                    }

                    if (i >= lines.length) break;

                    // Skip subtitle index number (first line of each subtitle block)
                    if (/^\d+$/.test(lines[i].trim())) {
                        i++;
                    }

                    // Skip empty lines after index
                    while (i < lines.length && lines[i].trim() === '') {
                        i++;
                    }

                    if (i >= lines.length) break;

                    // Look for timestamp line (contains -->)
                    if (lines[i].includes('-->')) {
                        // Convert timestamp format from SRT to VTT
                        let timestampLine = lines[i].trim();
                        // Convert from 00:00:00,000 --> 00:00:02,000 to 00:00.000 --> 00:02.000
                        timestampLine = timestampLine.replace(/(\d{2}):(\d{2}):(\d{2}),(\d{3})/g, '$1:$2.$4');
                        // Handle cases where we might have hours that need to be preserved
                        timestampLine = timestampLine.replace(/(\d{2}):(\d{2}):(\d{2}),(\d{3})/g, '$1:$2:$3.$4');

                        vttContent += timestampLine + '\n';
                        i++;

                        // Collect subtitle text lines
                        let subtitleText = '';
                        while (i < lines.length && lines[i].trim() !== '' && !lines[i].includes('-->') && !/^\d+$/.test(lines[i].trim())) {
                            if (subtitleText !== '') {
                                subtitleText += '\n';
                            }
                            subtitleText += lines[i].trim();
                            i++;
                        }

                        vttContent += subtitleText + '\n\n';
                    } else {
                        // Skip unexpected lines
                        i++;
                    }
                }

                return vttContent.trim();
            }

            window.SRTtoVTTConverter = {
                convert() {
                    const srtContent = elements.srtInput().value;
                    const output = elements.vttOutput();

                    try {
                        const vttContent = convertSRTtoVTT(srtContent);
                        output.textContent = vttContent;
                        output.style.color = '';
                    } catch (error) {
                        output.textContent = `Error: ${error.message}`;
                        output.style.color = '#dc2626';
                    }
                },

                clear() {
                    elements.srtInput().value = '';
                    elements.vttOutput().textContent = 'Your converted WebVTT subtitle content will appear here...';
                    elements.vttOutput().style.color = '';
                },

                copy() {
                    const text = elements.vttOutput().textContent;
                    if (text === 'Your converted WebVTT subtitle content will appear here...' || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                download() {
                    const text = elements.vttOutput().textContent;
                    if (text === 'Your converted WebVTT subtitle content will appear here...' || text.startsWith('Error:')) return;

                    const blob = new Blob([text], { type: 'text/vtt' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'subtitles.vtt';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    this.showNotification('Downloaded!');
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message = '✓ Copied to clipboard!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Auto-convert when user pastes content
                elements.srtInput().addEventListener('paste', function() {
                    setTimeout(() => {
                        if (this.value.trim()) {
                            SRTtoVTTConverter.convert();
                        }
                    }, 100);
                });

                // Enter key shortcut
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        SRTtoVTTConverter.convert();
                    }
                });

                // Sample SRT content for demonstration
                const sampleSRT = `1
00:00:00,000 --> 00:00:02,000
Hello, welcome to our video!

2
00:00:02,000 --> 00:00:05,000
This is a sample subtitle.

3
00:00:05,000 --> 00:00:08,000
You can paste your SRT content above.`;

                // Uncomment to load sample content on page load
                // elements.srtInput().value = sampleSRT;
                // SRTtoVTTConverter.convert();
            });
        })();
    </script>
</body>
</html>
