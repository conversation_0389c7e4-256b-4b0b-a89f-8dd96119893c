<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PNG to BMP Converter - Free Online Image Format Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free PNG to BMP Converter - Convert Images Online",
        "description": "Convert PNG images to BMP format instantly. Free online tool with uncompressed output, legacy system compatibility, and high-quality conversion.",
        "url": "https://www.webtoolskit.org/p/png-to-bmp.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-22",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "PNG to BMP Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert PNG to BMP" },
            { "@type": "DownloadAction", "name": "Download Converted BMP" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Can PNG be converted to BMP?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, PNG can be easily converted to BMP using our free online converter. The conversion process preserves image quality while creating an uncompressed BMP file compatible with older systems and legacy software that may not support PNG format."
          }
        },
        {
          "@type": "Question",
          "name": "Why use BMP instead of PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use BMP instead of PNG for compatibility with older systems, legacy software, and applications that don't support PNG. BMP is also preferred for certain professional workflows, embedded systems, and when you need uncompressed raw image data."
          }
        },
        {
          "@type": "Question",
          "name": "Is a PNG file a bitmap?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, PNG is technically a bitmap format that stores image data as pixels, but it uses compression and advanced features. BMP (bitmap) specifically refers to the uncompressed Windows bitmap format. Both are raster/bitmap images, but PNG is compressed while BMP is typically uncompressed."
          }
        },
        {
          "@type": "Question",
          "name": "What are the disadvantages of BMP file?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "BMP disadvantages include very large file sizes due to no compression, limited web browser support, no transparency support, and inefficient storage. BMP files can be 10-50 times larger than PNG equivalents, making them impractical for web use or storage-limited applications."
          }
        },
        {
          "@type": "Question",
          "name": "Is BMP larger than PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, BMP files are significantly larger than PNG files. BMP uses no compression, while PNG uses lossless compression. A BMP file can be 10-50 times larger than the same image in PNG format, making PNG much more efficient for storage and web use."
          }
        }
      ]
    }
    </script>

    <style>
        /* PNG to BMP Widget - Simplified & Template Compatible */
        .png-bmp-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .png-bmp-widget-container * { box-sizing: border-box; }

        .png-bmp-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .png-bmp-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .png-bmp-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-lg);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
        }

        .png-bmp-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .png-bmp-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .png-bmp-upload-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .png-bmp-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .png-bmp-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .png-bmp-file-input {
            display: none;
        }

        .png-bmp-preview {
            display: none;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .png-bmp-preview-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .png-bmp-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: flex-start;
        }

        .png-bmp-preview-item {
            flex: 1;
            text-align: center;
        }

        .png-bmp-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .png-bmp-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-sm);
        }

        .png-bmp-file-info {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .png-bmp-size-warning {
            background-color: #fef3cd;
            border: 1px solid #fecaca;
            color: #92400e;
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-lg);
            font-size: 0.875rem;
            display: none;
        }

        .png-bmp-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .png-bmp-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .png-bmp-btn:hover { transform: translateY(-2px); }

        .png-bmp-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .png-bmp-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .png-bmp-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .png-bmp-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .png-bmp-btn-success {
            background-color: #10b981;
            color: white;
        }

        .png-bmp-btn-success:hover {
            background-color: #059669;
        }

        .png-bmp-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .png-bmp-btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .png-bmp-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="bmp-to-png"] .png-bmp-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .png-bmp-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="png-to-jpg"] .png-bmp-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .png-bmp-related-tool-item:hover .png-bmp-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="bmp-to-png"]:hover .png-bmp-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .png-bmp-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="png-to-jpg"]:hover .png-bmp-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .png-bmp-related-tool-item { box-shadow: none; border: none; }
        .png-bmp-related-tool-item:hover { box-shadow: none; border: none; }
        .png-bmp-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .png-bmp-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .png-bmp-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .png-bmp-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .png-bmp-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .png-bmp-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .png-bmp-related-tool-item:hover .png-bmp-related-tool-name { color: var(--primary-color); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .png-bmp-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .png-bmp-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .png-bmp-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .png-bmp-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .png-bmp-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .png-bmp-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .png-bmp-widget-title { font-size: 1.875rem; }
            .png-bmp-buttons { flex-direction: column; }
            .png-bmp-btn { flex: none; }
            .png-bmp-preview-content { flex-direction: column; }
            .png-bmp-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .png-bmp-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .png-bmp-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .png-bmp-related-tool-name { font-size: 0.875rem; }
            .png-bmp-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .png-bmp-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .png-bmp-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .png-bmp-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .png-bmp-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .png-bmp-upload-area:hover { background-color: var(--card-bg); }
        .png-bmp-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        [data-theme="dark"] .png-bmp-size-warning { background-color: #451a03; border-color: #92400e; color: #fbbf24; }
    </style>
</head>
<body>
    <div class="png-bmp-widget-container">
        <h1 class="png-bmp-widget-title">PNG to BMP Converter</h1>
        <p class="png-bmp-widget-description">
            Convert PNG images to BMP format for legacy system compatibility and uncompressed output. Free, secure, and optimized for professional workflows.
        </p>
        
        <div class="png-bmp-upload-area" id="uploadArea">
            <div class="png-bmp-upload-icon">📁</div>
            <div class="png-bmp-upload-text">Click to select PNG image or drag & drop</div>
            <div class="png-bmp-upload-subtext">Supports PNG files (Max 10MB)</div>
            <input type="file" id="fileInput" class="png-bmp-file-input" accept=".png">
        </div>

        <div class="png-bmp-size-warning" id="sizeWarning">
            <strong>File Size Notice:</strong> BMP files are uncompressed and will be significantly larger than PNG. The converted file may be 10-50 times larger than the original.
        </div>

        <div class="png-bmp-preview" id="previewSection">
            <h3 class="png-bmp-preview-title">Preview & Comparison</h3>
            <div class="png-bmp-preview-content">
                <div class="png-bmp-preview-item">
                    <div class="png-bmp-preview-label">Original PNG</div>
                    <img id="originalImage" class="png-bmp-preview-image" alt="Original PNG" />
                    <div class="png-bmp-file-info" id="originalInfo"></div>
                </div>
                <div class="png-bmp-preview-item">
                    <div class="png-bmp-preview-label">Converted BMP</div>
                    <img id="convertedImage" class="png-bmp-preview-image" alt="Converted BMP" />
                    <div class="png-bmp-file-info" id="convertedInfo"></div>
                </div>
            </div>
        </div>

        <div class="png-bmp-buttons">
            <button id="convertBtn" class="png-bmp-btn png-bmp-btn-primary" disabled>
                Convert to BMP
            </button>
            <button id="downloadBtn" class="png-bmp-btn png-bmp-btn-success" disabled>
                Download BMP
            </button>
            <button id="resetBtn" class="png-bmp-btn png-bmp-btn-secondary">
                Reset
            </button>
        </div>

        <div class="png-bmp-related-tools">
            <h3 class="png-bmp-related-tools-title">Related Tools</h3>
            <div class="png-bmp-related-tools-grid">
                <a href="https://www.webtoolskit.org/p/bmp-to-png.html" class="png-bmp-related-tool-item" rel="noopener">
                    <div class="png-bmp-related-tool-icon">
                        <i class="fas fa-undo-alt"></i>
                    </div>
                    <div class="png-bmp-related-tool-name">BMP to PNG</div>
                </a>

                <a href="https://www.webtoolskit.org/p/image-converter_23.html" class="png-bmp-related-tool-item" rel="noopener">
                    <div class="png-bmp-related-tool-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="png-bmp-related-tool-name">Image Converter</div>
                </a>

                <a href="https://www.webtoolskit.org/p/png-to-jpg.html" class="png-bmp-related-tool-item" rel="noopener">
                    <div class="png-bmp-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="png-bmp-related-tool-name">PNG to JPG</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert PNG to BMP Online - Legacy Format Support</h2>
            <p>Our <strong>PNG to BMP Converter</strong> transforms compressed PNG images into uncompressed BMP format, ensuring compatibility with legacy systems and older software that may not support modern image formats. BMP (Bitmap) files provide raw, uncompressed image data ideal for professional workflows and embedded systems.</p>

            <p>While BMP files are significantly larger than PNG, they offer universal compatibility and are essential for certain applications that require uncompressed bitmap data. Our tool processes conversions locally in your browser, maintaining privacy while delivering high-quality results.</p>

            <h3>Frequently Asked Questions About PNG to BMP Conversion</h3>

            <h4>Can PNG be converted to BMP?</h4>
            <p>Yes, PNG can be easily converted to BMP using our free online converter. The conversion process preserves image quality while creating an uncompressed BMP file compatible with older systems and legacy software that may not support PNG format.</p>

            <h4>Why use BMP instead of PNG?</h4>
            <p>Use BMP instead of PNG for compatibility with older systems, legacy software, and applications that don't support PNG. BMP is also preferred for certain professional workflows, embedded systems, and when you need uncompressed raw image data.</p>

            <h4>Is a PNG file a bitmap?</h4>
            <p>Yes, PNG is technically a bitmap format that stores image data as pixels, but it uses compression and advanced features. BMP (bitmap) specifically refers to the uncompressed Windows bitmap format. Both are raster/bitmap images, but PNG is compressed while BMP is typically uncompressed.</p>

            <h4>What are the disadvantages of BMP file?</h4>
            <p>BMP disadvantages include very large file sizes due to no compression, limited web browser support, no transparency support, and inefficient storage. BMP files can be 10-50 times larger than PNG equivalents, making them impractical for web use or storage-limited applications.</p>

            <h4>Is BMP larger than PNG?</h4>
            <p>Yes, BMP files are significantly larger than PNG files. BMP uses no compression, while PNG uses lossless compression. A BMP file can be 10-50 times larger than the same image in PNG format, making PNG much more efficient for storage and web use.</p>
        </div>

        <div class="png-bmp-features">
            <h3 class="png-bmp-features-title">Key Features</h3>
            <ul class="png-bmp-features-list">
                <li class="png-bmp-features-item">Convert PNG to BMP instantly</li>
                <li class="png-bmp-features-item">Uncompressed output format</li>
                <li class="png-bmp-features-item">Legacy system compatibility</li>
                <li class="png-bmp-features-item">Professional workflow support</li>
                <li class="png-bmp-features-item">Client-side processing for privacy</li>
                <li class="png-bmp-features-item">High-quality conversion</li>
                <li class="png-bmp-features-item">Real-time preview</li>
                <li class="png-bmp-features-item">Raw bitmap data output</li>
            </ul>
        </div>
    </div>

    <script>
        // PNG to BMP Converter Tool - Self-contained IIFE
        (function() {
            'use strict';

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                sizeWarning: () => document.getElementById('sizeWarning'),
                previewSection: () => document.getElementById('previewSection'),
                originalImage: () => document.getElementById('originalImage'),
                convertedImage: () => document.getElementById('convertedImage'),
                originalInfo: () => document.getElementById('originalInfo'),
                convertedInfo: () => document.getElementById('convertedInfo'),
                convertBtn: () => document.getElementById('convertBtn'),
                downloadBtn: () => document.getElementById('downloadBtn'),
                resetBtn: () => document.getElementById('resetBtn')
            };

            let originalFile = null;
            let convertedBlob = null;

            function init() {
                setupEventListeners();
            }

            function setupEventListeners() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();
                const convertBtn = elements.convertBtn();
                const downloadBtn = elements.downloadBtn();
                const resetBtn = elements.resetBtn();

                // File upload events
                uploadArea.addEventListener('click', () => fileInput.click());
                fileInput.addEventListener('change', handleFileSelect);

                // Drag and drop events
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('drop', handleDrop);

                // Button events
                convertBtn.addEventListener('click', convertImage);
                downloadBtn.addEventListener('click', downloadImage);
                resetBtn.addEventListener('click', resetTool);
            }

            function handleFileSelect(event) {
                const file = event.target.files[0];
                if (file) processFile(file);
            }

            function handleDragOver(event) {
                event.preventDefault();
                elements.uploadArea().classList.add('dragover');
            }

            function handleDragLeave(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
            }

            function handleDrop(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
                const files = event.dataTransfer.files;
                if (files.length > 0) processFile(files[0]);
            }

            function processFile(file) {
                if (!file.type.includes('png')) {
                    alert('Please select a PNG image file.');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB.');
                    return;
                }

                originalFile = file;
                displayOriginalImage();
                elements.convertBtn().disabled = false;
                elements.sizeWarning().style.display = 'block';
            }

            function displayOriginalImage() {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const originalImage = elements.originalImage();
                    originalImage.src = e.target.result;
                    
                    const originalInfo = elements.originalInfo();
                    originalInfo.textContent = `${originalFile.name} (${formatFileSize(originalFile.size)})`;
                    
                    elements.previewSection().style.display = 'block';
                };
                reader.readAsDataURL(originalFile);
            }

            function convertImage() {
                if (!originalFile) return;

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    // Convert to BMP format (uncompressed)
                    canvas.toBlob((blob) => {
                        convertedBlob = blob;
                        displayConvertedImage();
                        elements.downloadBtn().disabled = false;
                    }, 'image/bmp');
                };

                img.src = URL.createObjectURL(originalFile);
            }

            function displayConvertedImage() {
                const convertedImage = elements.convertedImage();
                convertedImage.src = URL.createObjectURL(convertedBlob);
                
                const convertedInfo = elements.convertedInfo();
                const fileName = originalFile.name.replace(/\.[^/.]+$/, '') + '.bmp';
                const sizeIncrease = ((convertedBlob.size / originalFile.size) * 100).toFixed(1);
                convertedInfo.innerHTML = `${fileName} (${formatFileSize(convertedBlob.size)})<br><small style="color: #dc3545;">Size increase: ${sizeIncrease}%</small>`;
            }

            function downloadImage() {
                if (!convertedBlob) return;

                const link = document.createElement('a');
                link.href = URL.createObjectURL(convertedBlob);
                link.download = originalFile.name.replace(/\.[^/.]+$/, '') + '.bmp';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            function resetTool() {
                originalFile = null;
                convertedBlob = null;
                elements.fileInput().value = '';
                elements.previewSection().style.display = 'none';
                elements.sizeWarning().style.display = 'none';
                elements.convertBtn().disabled = true;
                elements.downloadBtn().disabled = true;
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', init);
        })();
    </script>
</body>
</html>