<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Mailto Link Generator - Create Email Links with Subject and Body</title>
    <meta name="description" content="Generate mailto links instantly with our free Mailto Link Generator. Create email links with pre-filled subject, body, CC, and BCC recipients for websites and marketing.">
    <meta name="keywords" content="mailto link generator, email link generator, mailto link, email link, contact link, email marketing links">
    <link rel="canonical" href="https://www.webtoolskit.org/p/mailto-link-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Mailto Link Generator - Create Email Links with Subject and Body",
        "description": "Generate mailto links instantly with our free Mailto Link Generator. Create email links with pre-filled subject, body, CC, and BCC recipients for websites and marketing.",
        "url": "https://www.webtoolskit.org/p/mailto-link-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Mailto Link Generator",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Mailto link creation",
                "Pre-filled email subject",
                "Email body customization",
                "Multiple recipients support",
                "CC and BCC functionality"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Mailto Link" },
            { "@type": "CopyAction", "name": "Copy Email Link" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I create a mailto link with subject and body?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Enter the recipient email address, add your desired subject line and email body text, then click 'Generate Mailto Link'. Our tool will create a mailto link that opens the user's email client with all fields pre-filled."
          }
        },
        {
          "@type": "Question",
          "name": "What is a mailto link and how does it work?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A mailto link is a special URL that opens the user's default email client when clicked. It can include pre-filled recipient, subject, body, CC, and BCC fields, making it easy for website visitors to contact you with structured emails."
          }
        },
        {
          "@type": "Question",
          "name": "Can I create mailto links with multiple recipients?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can add multiple recipients in the TO field by separating email addresses with commas. You can also use CC and BCC fields for additional recipients who should receive copies of the email."
          }
        },
        {
          "@type": "Question",
          "name": "How do I generate mailto links for email marketing?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Create mailto links with compelling subject lines and pre-written email bodies that encourage engagement. Use them in newsletters, websites, and social media to make it easy for customers to contact you with specific inquiries or feedback."
          }
        },
        {
          "@type": "Question",
          "name": "Do mailto links work on mobile devices?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, mailto links work on mobile devices and will open the default email app (like Gmail, Mail, or Outlook) with the pre-filled information. This provides a seamless experience across desktop and mobile platforms."
          }
        }
      ]
    }
    </script>

    <style>
        /* Mailto Link Generator Widget - Simplified & Template Compatible */
        .mailto-link-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .mailto-link-widget-container * { box-sizing: border-box; }

        .mailto-link-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .mailto-link-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .mailto-link-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .mailto-link-field {
            display: flex;
            flex-direction: column;
        }

        .mailto-link-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .mailto-link-input,
        .mailto-link-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .mailto-link-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .mailto-link-input:focus,
        .mailto-link-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .mailto-link-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .mailto-link-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .mailto-link-btn:hover { transform: translateY(-2px); }

        .mailto-link-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .mailto-link-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .mailto-link-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .mailto-link-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .mailto-link-btn-success {
            background-color: #10b981;
            color: white;
        }

        .mailto-link-btn-success:hover {
            background-color: #059669;
        }

        .mailto-link-btn-email {
            background-color: #dc2626;
            color: white;
        }

        .mailto-link-btn-email:hover {
            background-color: #b91c1c;
        }

        .mailto-link-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .mailto-link-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .mailto-link-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .mailto-link-preview {
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
        }

        .mailto-link-preview-title {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .mailto-link-preview-content {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .mailto-link-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .mailto-link-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        @media (max-width: 768px) {
            .mailto-link-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .mailto-link-widget-title { font-size: 1.875rem; }
            .mailto-link-buttons { flex-direction: column; }
            .mailto-link-btn { flex: none; }
        }

        [data-theme="dark"] .mailto-link-input:focus,
        [data-theme="dark"] .mailto-link-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .mailto-link-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .mailto-link-output::selection { background-color: var(--primary-color); color: white; }

        .mailto-link-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="whatsapp-link-generator"] .mailto-link-related-tool-icon { background: linear-gradient(145deg, #25D366, #128C7E); }
        a[href*="paypal-link-generator"] .mailto-link-related-tool-icon { background: linear-gradient(145deg, #0070BA, #005EA6); }
        a[href*="http-headers-lookup"] .mailto-link-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }

        .mailto-link-related-tool-item:hover .mailto-link-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="whatsapp-link-generator"]:hover .mailto-link-related-tool-icon { background: linear-gradient(145deg, #34d399, #10b981); }
        a[href*="paypal-link-generator"]:hover .mailto-link-related-tool-icon { background: linear-gradient(145deg, #0ea5e9, #0284c7); }
        a[href*="http-headers-lookup"]:hover .mailto-link-related-tool-icon { background: linear-gradient(145deg, #7c7cf8, #6366f1); }

        .mailto-link-related-tool-item { box-shadow: none; border: none; }
        .mailto-link-related-tool-item:hover { box-shadow: none; border: none; }
        .mailto-link-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .mailto-link-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .mailto-link-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .mailto-link-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .mailto-link-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .mailto-link-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .mailto-link-related-tool-item:hover .mailto-link-related-tool-name { color: var(--primary-color); }

        .mailto-link-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .mailto-link-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .mailto-link-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .mailto-link-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .mailto-link-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .mailto-link-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .mailto-link-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .mailto-link-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .mailto-link-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .mailto-link-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .mailto-link-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .mailto-link-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .mailto-link-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .mailto-link-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="mailto-link-widget-container">
        <h1 class="mailto-link-widget-title">Mailto Link Generator</h1>
        <p class="mailto-link-widget-description">
            Create mailto links with pre-filled subject, body, CC, and BCC recipients. Perfect for websites, email marketing, and customer contact forms.
        </p>

        <form class="mailto-link-form">
            <div class="mailto-link-field">
                <label for="emailTo" class="mailto-link-label">To (Email Recipients):</label>
                <input
                    type="email"
                    id="emailTo"
                    class="mailto-link-input"
                    placeholder="<EMAIL>, <EMAIL>"
                />
            </div>

            <div class="mailto-link-field">
                <label for="emailCc" class="mailto-link-label">CC (optional):</label>
                <input
                    type="email"
                    id="emailCc"
                    class="mailto-link-input"
                    placeholder="<EMAIL>"
                />
            </div>

            <div class="mailto-link-field">
                <label for="emailBcc" class="mailto-link-label">BCC (optional):</label>
                <input
                    type="email"
                    id="emailBcc"
                    class="mailto-link-input"
                    placeholder="<EMAIL>"
                />
            </div>

            <div class="mailto-link-field">
                <label for="emailSubject" class="mailto-link-label">Subject (optional):</label>
                <input
                    type="text"
                    id="emailSubject"
                    class="mailto-link-input"
                    placeholder="Enter email subject"
                />
            </div>

            <div class="mailto-link-field">
                <label for="emailBody" class="mailto-link-label">Body (optional):</label>
                <textarea
                    id="emailBody"
                    class="mailto-link-textarea"
                    placeholder="Enter email body text..."
                ></textarea>
            </div>
        </form>

        <div class="mailto-link-buttons">
            <button class="mailto-link-btn mailto-link-btn-primary" onclick="MailtoLinkGenerator.generate()">
                Generate Mailto Link
            </button>
            <button class="mailto-link-btn mailto-link-btn-secondary" onclick="MailtoLinkGenerator.clear()">
                Clear All
            </button>
            <button class="mailto-link-btn mailto-link-btn-success" onclick="MailtoLinkGenerator.copy()">
                Copy Link
            </button>
            <button class="mailto-link-btn mailto-link-btn-email" onclick="MailtoLinkGenerator.test()">
                Test Link
            </button>
        </div>

        <div class="mailto-link-result">
            <h3 class="mailto-link-result-title">Generated Mailto Link:</h3>
            <div class="mailto-link-output" id="mailtoOutput">Click "Generate Mailto Link" to create your email link...</div>

            <div class="mailto-link-preview" id="linkPreview" style="display: none;">
                <div class="mailto-link-preview-title">Email Preview:</div>
                <div class="mailto-link-preview-content" id="previewContent"></div>
            </div>
        </div>

        <div class="mailto-link-related-tools">
            <h3 class="mailto-link-related-tools-title">Related Tools</h3>
            <div class="mailto-link-related-tools-grid">
                <a href="/p/whatsapp-link-generator.html" class="mailto-link-related-tool-item" rel="noopener">
                    <div class="mailto-link-related-tool-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="mailto-link-related-tool-name">WhatsApp Link Generator</div>
                </a>

                <a href="/p/paypal-link-generator.html" class="mailto-link-related-tool-item" rel="noopener">
                    <div class="mailto-link-related-tool-icon">
                        <i class="fab fa-paypal"></i>
                    </div>
                    <div class="mailto-link-related-tool-name">PayPal Link Generator</div>
                </a>

                <a href="/p/http-headers-lookup.html" class="mailto-link-related-tool-item" rel="noopener">
                    <div class="mailto-link-related-tool-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="mailto-link-related-tool-name">HTTP Headers Lookup</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Mailto Link Generator for Email Marketing</h2>
            <p>Our <strong>Mailto Link Generator</strong> creates professional email links that open users' default email clients with pre-filled information. Perfect for websites, email marketing campaigns, and customer contact forms, these mailto links streamline communication by automatically populating recipient, subject, body, CC, and BCC fields.</p>
            <p>Ideal for businesses, marketers, web developers, and customer service teams who want to improve email engagement and reduce friction in the contact process. Generate mailto links that work seamlessly across desktop and mobile email clients, ensuring consistent user experience across all platforms.</p>

            <h3>How to Use the Mailto Link Generator</h3>
            <ol>
                <li><strong>Enter Recipients:</strong> Add email addresses in the TO field, separated by commas for multiple recipients.</li>
                <li><strong>Add CC/BCC (Optional):</strong> Include additional recipients who should receive copies of the email.</li>
                <li><strong>Set Subject & Body:</strong> Create compelling subject lines and pre-written email content.</li>
                <li><strong>Generate Link:</strong> Click "Generate Mailto Link" to create your custom email link.</li>
            </ol>

            <h3>Frequently Asked Questions About Mailto Link Generation</h3>

            <h4>How do I create a mailto link with subject and body?</h4>
            <p>Enter the recipient email address, add your desired subject line and email body text, then click 'Generate Mailto Link'. Our tool will create a mailto link that opens the user's email client with all fields pre-filled.</p>

            <h4>What is a mailto link and how does it work?</h4>
            <p>A mailto link is a special URL that opens the user's default email client when clicked. It can include pre-filled recipient, subject, body, CC, and BCC fields, making it easy for website visitors to contact you with structured emails.</p>

            <h4>Can I create mailto links with multiple recipients?</h4>
            <p>Yes, you can add multiple recipients in the TO field by separating email addresses with commas. You can also use CC and BCC fields for additional recipients who should receive copies of the email.</p>

            <h4>How do I generate mailto links for email marketing?</h4>
            <p>Create mailto links with compelling subject lines and pre-written email bodies that encourage engagement. Use them in newsletters, websites, and social media to make it easy for customers to contact you with specific inquiries or feedback.</p>

            <h4>Do mailto links work on mobile devices?</h4>
            <p>Yes, mailto links work on mobile devices and will open the default email app (like Gmail, Mail, or Outlook) with the pre-filled information. This provides a seamless experience across desktop and mobile platforms.</p>
        </div>

        <div class="mailto-link-features">
            <h3 class="mailto-link-features-title">Key Features:</h3>
            <ul class="mailto-link-features-list">
                <li class="mailto-link-features-item" style="margin-bottom: 0.3em;">Pre-filled Email Fields</li>
                <li class="mailto-link-features-item" style="margin-bottom: 0.3em;">Multiple Recipients Support</li>
                <li class="mailto-link-features-item" style="margin-bottom: 0.3em;">CC and BCC Functionality</li>
                <li class="mailto-link-features-item" style="margin-bottom: 0.3em;">Custom Subject Lines</li>
                <li class="mailto-link-features-item" style="margin-bottom: 0.3em;">Email Body Customization</li>
                <li class="mailto-link-features-item" style="margin-bottom: 0.3em;">Mobile Device Compatible</li>
                <li class="mailto-link-features-item">100% Free and Instant</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="mailto-link-notification" id="mailtoNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                toInput: () => document.getElementById('emailTo'),
                ccInput: () => document.getElementById('emailCc'),
                bccInput: () => document.getElementById('emailBcc'),
                subjectInput: () => document.getElementById('emailSubject'),
                bodyInput: () => document.getElementById('emailBody'),
                output: () => document.getElementById('mailtoOutput'),
                preview: () => document.getElementById('linkPreview'),
                previewContent: () => document.getElementById('previewContent'),
                notification: () => document.getElementById('mailtoNotification')
            };

            function validateEmails(emailString) {
                if (!emailString || !emailString.trim()) return true; // Optional field

                const emails = emailString.split(',').map(email => email.trim());
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                return emails.every(email => emailRegex.test(email));
            }

            function generateMailtoLink(to, cc, bcc, subject, body) {
                let link = 'mailto:';

                // Add TO recipients
                if (to && to.trim()) {
                    link += encodeURIComponent(to.trim());
                }

                // Build query parameters
                const params = [];

                if (cc && cc.trim()) {
                    params.push(`cc=${encodeURIComponent(cc.trim())}`);
                }

                if (bcc && bcc.trim()) {
                    params.push(`bcc=${encodeURIComponent(bcc.trim())}`);
                }

                if (subject && subject.trim()) {
                    params.push(`subject=${encodeURIComponent(subject.trim())}`);
                }

                if (body && body.trim()) {
                    params.push(`body=${encodeURIComponent(body.trim())}`);
                }

                if (params.length > 0) {
                    link += '?' + params.join('&');
                }

                return link;
            }

            function updatePreview(to, cc, bcc, subject, body) {
                const preview = elements.preview();
                const previewContent = elements.previewContent();

                if (to.trim() || cc.trim() || bcc.trim() || subject.trim() || body.trim()) {
                    let previewText = '';

                    if (to.trim()) {
                        previewText += `To: ${to.trim()}\n`;
                    }

                    if (cc.trim()) {
                        previewText += `CC: ${cc.trim()}\n`;
                    }

                    if (bcc.trim()) {
                        previewText += `BCC: ${bcc.trim()}\n`;
                    }

                    if (subject.trim()) {
                        previewText += `Subject: ${subject.trim()}\n`;
                    } else {
                        previewText += `Subject: (No subject)\n`;
                    }

                    if (body.trim()) {
                        previewText += `\nBody:\n${body.trim()}`;
                    } else {
                        previewText += `\nBody: (No message body)`;
                    }

                    previewContent.textContent = previewText;
                    preview.style.display = 'block';
                } else {
                    preview.style.display = 'none';
                }
            }

            window.MailtoLinkGenerator = {
                generate() {
                    const to = elements.toInput().value;
                    const cc = elements.ccInput().value;
                    const bcc = elements.bccInput().value;
                    const subject = elements.subjectInput().value;
                    const body = elements.bodyInput().value;
                    const output = elements.output();

                    // Validate required field
                    if (!to.trim()) {
                        output.textContent = 'Please enter at least one recipient email address in the TO field.';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                        return;
                    }

                    // Validate email formats
                    if (!validateEmails(to)) {
                        output.textContent = 'Please enter valid email addresses in the TO field.';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                        return;
                    }

                    if (!validateEmails(cc)) {
                        output.textContent = 'Please enter valid email addresses in the CC field.';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                        return;
                    }

                    if (!validateEmails(bcc)) {
                        output.textContent = 'Please enter valid email addresses in the BCC field.';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                        return;
                    }

                    try {
                        const mailtoLink = generateMailtoLink(to, cc, bcc, subject, body);
                        output.textContent = mailtoLink;
                        output.style.color = '';
                        updatePreview(to, cc, bcc, subject, body);
                    } catch (error) {
                        output.textContent = 'Error generating mailto link. Please check your input.';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                    }
                },

                clear() {
                    elements.toInput().value = '';
                    elements.ccInput().value = '';
                    elements.bccInput().value = '';
                    elements.subjectInput().value = '';
                    elements.bodyInput().value = '';
                    elements.output().textContent = 'Click "Generate Mailto Link" to create your email link...';
                    elements.output().style.color = '';
                    elements.preview().style.display = 'none';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Click "Generate Mailto Link" to create your email link...' || text.includes('Please enter') || text.includes('Error')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                test() {
                    const text = elements.output().textContent;
                    if (text === 'Click "Generate Mailto Link" to create your email link...' || text.includes('Please enter') || text.includes('Error')) {
                        alert('Please generate a mailto link first.');
                        return;
                    }

                    // Open the mailto link
                    window.location.href = text;
                    this.showNotification('Email client opened!');
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message = '✓ Copied to clipboard!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Real-time preview updates
                const inputs = [
                    elements.toInput(),
                    elements.ccInput(),
                    elements.bccInput(),
                    elements.subjectInput(),
                    elements.bodyInput()
                ];

                inputs.forEach(input => {
                    input.addEventListener('input', function() {
                        const to = elements.toInput().value;
                        const cc = elements.ccInput().value;
                        const bcc = elements.bccInput().value;
                        const subject = elements.subjectInput().value;
                        const body = elements.bodyInput().value;

                        updatePreview(to, cc, bcc, subject, body);
                    });
                });

                // Enter key shortcuts
                elements.toInput().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        MailtoLinkGenerator.generate();
                    }
                });

                elements.subjectInput().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        MailtoLinkGenerator.generate();
                    }
                });

                elements.bodyInput().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        MailtoLinkGenerator.generate();
                    }
                });

                // Global keyboard shortcut
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        MailtoLinkGenerator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
