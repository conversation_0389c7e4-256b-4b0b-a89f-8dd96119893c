<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Speed Converter - Convert MPH, KM/H, Knots & More</title>
    <meta name="description" content="Instantly convert between speed units like miles per hour (MPH), kilometers per hour (KM/H), knots, and meters per second (m/s). Free online tool for driving, flying, and science.">
    <meta name="keywords" content="speed converter, mph to km/h, km/h to mph, convert knots to mph, m/s to km/h, velocity converter">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Speed Converter - MPH, KM/H, Knots, m/s",
        "description": "Convert between various speed units including miles per hour, kilometers per hour, knots, and meters per second. Free online tool for accurate, real-time conversions.",
        "url": "https://www.webtoolskit.org/p/speed-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-25",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Speed Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Speed Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert RPM to speed (mph or km/h)?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You cannot directly convert RPM (revolutions per minute) to a linear speed like mph or km/h without knowing the radius or diameter of the rotating object (e.g., a tire). The formula is: Speed = RPM × Circumference × 60. A larger tire will travel a greater distance per revolution, resulting in a higher speed for the same RPM. Our tool converts between standard linear speed units, not rotational speeds."
          }
        },
        {
          "@type": "Question",
          "name": "What is 4000 RPM in mph?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The speed in mph for 4000 RPM depends entirely on the size of the tire and the vehicle's gear ratio. For example, for a car with 25-inch diameter tires in a specific gear, 4000 RPM might equate to over 100 mph. For a smaller wheel, it would be much less. There is no single answer without more information."
          }
        },
        {
          "@type": "Question",
          "name": "How fast is 70 RPM in mph?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The speed for 70 RPM depends on the context. For a standard 26-inch bicycle wheel, 70 RPM translates to a speed of about 5.5 mph. For a much larger wheel, like on a tractor, 70 RPM would result in a higher linear speed."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert km to mph?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert kilometers per hour (km/h) to miles per hour (mph), you multiply the speed in km/h by the conversion factor 0.621371. The formula is: Speed in mph = Speed in km/h × 0.621371."
          }
        },
        {
          "@type": "Question",
          "name": "How fast is 100 km in mph?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using the conversion factor, 100 kilometers per hour is equal to 62.14 miles per hour. You can get this by calculating 100 km/h × 0.621371 = 62.1371 mph."
          }
        }
      ]
    }
    </script>

    <style>
        /* Speed Converter Widget - Simplified & Template Compatible */
        .speed-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .speed-converter-widget-container * { box-sizing: border-box; }

        .speed-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .speed-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .speed-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .speed-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .speed-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .speed-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .speed-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .speed-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .speed-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .speed-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .speed-converter-btn:hover { transform: translateY(-2px); }

        .speed-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .speed-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .speed-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .speed-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .speed-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .speed-converter-btn-success:hover {
            background-color: #059669;
        }

        .speed-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .speed-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .speed-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .speed-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .speed-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .speed-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .speed-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .speed-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .speed-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .speed-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .speed-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="pace-converter"] .speed-converter-related-tool-icon { background: linear-gradient(145deg, #A855F7, #9333EA); }
        a[href*="length-converter"] .speed-converter-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="time-converter"] .speed-converter-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }

        .speed-converter-related-tool-item:hover .speed-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="pace-converter"]:hover .speed-converter-related-tool-icon { background: linear-gradient(145deg, #b879f9, #a855f7); }
        a[href*="length-converter"]:hover .speed-converter-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="time-converter"]:hover .speed-converter-related-tool-icon { background: linear-gradient(145deg, #7c7ee9, #6366f1); }
        
        .speed-converter-related-tool-item { box-shadow: none; border: none; }
        .speed-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .speed-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .speed-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .speed-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .speed-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .speed-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .speed-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .speed-converter-related-tool-item:hover .speed-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .speed-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .speed-converter-widget-title { font-size: 1.875rem; }
            .speed-converter-buttons { flex-direction: column; }
            .speed-converter-btn { flex: none; }
            .speed-converter-input-group { grid-template-columns: 1fr; }
            .speed-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .speed-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .speed-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .speed-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .speed-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .speed-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .speed-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .speed-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .speed-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .speed-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .speed-converter-output::selection { background-color: var(--primary-color); color: white; }
        .speed-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .speed-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="speed-converter-widget-container">
        <h1 class="speed-converter-widget-title">Speed Converter</h1>
        <p class="speed-converter-widget-description">
            Instantly convert between different units of speed, including MPH, KM/H, Knots, and Meters per Second. Perfect for any speed conversion needs.
        </p>
        
        <div class="speed-converter-input-group">
            <label for="speedFromInput" class="speed-converter-label">From:</label>
            <input 
                type="number" 
                id="speedFromInput" 
                class="speed-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="speedFromUnit" class="speed-converter-select">
                <option value="mph" selected>Miles per hour (mph)</option>
                <option value="kmh">Kilometers per hour (km/h)</option>
                <option value="ms">Meters per second (m/s)</option>
                <option value="kn">Knots (kn)</option>
                <option value="fts">Feet per second (ft/s)</option>
            </select>
        </div>

        <div class="speed-converter-input-group">
            <label for="speedToInput" class="speed-converter-label">To:</label>
            <input 
                type="number" 
                id="speedToInput" 
                class="speed-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="speedToUnit" class="speed-converter-select">
                <option value="mph">Miles per hour (mph)</option>
                <option value="kmh" selected>Kilometers per hour (km/h)</option>
                <option value="ms">Meters per second (m/s)</option>
                <option value="kn">Knots (kn)</option>
                <option value="fts">Feet per second (ft/s)</option>
            </select>
        </div>

        <div class="speed-converter-buttons">
            <button class="speed-converter-btn speed-converter-btn-primary" onclick="SpeedConverter.convert()">
                Convert Speed
            </button>
            <button class="speed-converter-btn speed-converter-btn-secondary" onclick="SpeedConverter.clear()">
                Clear All
            </button>
            <button class="speed-converter-btn speed-converter-btn-success" onclick="SpeedConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="speed-converter-result">
            <h3 class="speed-converter-result-title">Conversion Result:</h3>
            <div class="speed-converter-output" id="speedConverterOutput">
                Your converted speed will appear here...
            </div>
        </div>

        <div class="speed-converter-related-tools">
            <h3 class="speed-converter-related-tools-title">Related Tools</h3>
            <div class="speed-converter-related-tools-grid">
                <a href="/p/pace-converter.html" class="speed-converter-related-tool-item" rel="noopener">
                    <div class="speed-converter-related-tool-icon">
                        <i class="fas fa-running"></i>
                    </div>
                    <div class="speed-converter-related-tool-name">Pace Converter</div>
                </a>

                <a href="/p/length-converter.html" class="speed-converter-related-tool-item" rel="noopener">
                    <div class="speed-converter-related-tool-icon">
                        <i class="fas fa-ruler"></i>
                    </div>
                    <div class="speed-converter-related-tool-name">Length Converter</div>
                </a>

                <a href="/p/time-converter.html" class="speed-converter-related-tool-item" rel="noopener">
                    <div class="speed-converter-related-tool-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="speed-converter-related-tool-name">Time Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Fast and Accurate Speed Unit Conversions</h2>
            <p>In a world where different regions and industries use different units, a reliable <strong>Speed Converter</strong> is an invaluable tool. Whether you're a pilot converting knots, a driver switching between MPH and KM/H, or a scientist working in meters per second, our tool delivers fast and precise conversions. It eliminates the need for manual calculations and helps you understand speed in the units you're most familiar with. This is the ultimate utility for anyone dealing with measurements of velocity.</p>
            <p>Our tool supports all major speed units, including miles per hour (MPH), kilometers per hour (KM/H), meters per second (m/s), knots (kn), and feet per second (ft/s). The interface is designed for simplicity and efficiency, providing instant results with a single click.</p>

            <h3>How to Use the Speed Converter</h3>
            <ol>
                <li><strong>Enter Your Speed:</strong> Type the speed value you want to convert into the "From" input field.</li>
                <li><strong>Select Units:</strong> Use the dropdown menus to choose your starting unit and the unit you want to convert to.</li>
                <li><strong>Click Convert:</strong> Press the "Convert Speed" button. The accurate result will immediately appear below.</li>
                <li><strong>Copy or Reset:</strong> Use the "Copy Result" button to save the value or "Clear All" to perform a new calculation.</li>
            ol>

            <h3>Frequently Asked Questions About Speed Conversion</h3>

            <h4>How do you convert RPM to speed (mph or km/h)?</h4>
            <p>You cannot directly convert RPM (revolutions per minute) to a linear speed like mph or km/h without knowing the radius or diameter of the rotating object (e.g., a tire). The formula is: Speed = RPM × Circumference × 60. A larger tire will travel a greater distance per revolution, resulting in a higher speed for the same RPM. Our tool converts between standard linear speed units, not rotational speeds.</p>

            <h4>What is 4000 RPM in mph?</h4>
            <p>The speed in mph for 4000 RPM depends entirely on the size of the tire and the vehicle's gear ratio. For example, for a car with 25-inch diameter tires in a specific gear, 4000 RPM might equate to over 100 mph. For a smaller wheel, it would be much less. There is no single answer without more information.</p>

            <h4>How fast is 70 RPM in mph?</h4>
            <p>The speed for 70 RPM depends on the context. For a standard 26-inch bicycle wheel, 70 RPM translates to a speed of about 5.5 mph. For a much larger wheel, like on a tractor, 70 RPM would result in a higher linear speed.</p>

            <h4>How do you convert km to mph?</h4>
            <p>To convert kilometers per hour (km/h) to miles per hour (mph), you multiply the speed in km/h by the conversion factor 0.621371. The formula is: Speed in mph = Speed in km/h × 0.621371.</p>

            <h4>How fast is 100 km in mph?</h4>
            <p>Using the conversion factor, 100 kilometers per hour is equal to 62.14 miles per hour. You can get this by calculating 100 km/h × 0.621371 = 62.1371 mph.</p>
        </div>

        <div class="speed-converter-features">
            <h3 class="speed-converter-features-title">Key Features:</h3>
            <ul class="speed-converter-features-list">
                <li class="speed-converter-features-item" style="margin-bottom: 0.3em;">MPH, KM/H, Knots, and more</li>
                <li class="speed-converter-features-item" style="margin-bottom: 0.3em;">Ideal for travel and science</li>
                <li class="speed-converter-features-item" style="margin-bottom: 0.3em;">High-precision calculations</li>
                <li class="speed-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="speed-converter-features-item" style="margin-bottom: 0.3em;">Clean, user-friendly interface</li>
                <li class="speed-converter-features-item" style="margin-bottom: 0.3em;">Responsive for mobile and desktop</li>
                <li class="speed-converter-features-item">Private and secure conversions</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="speed-converter-notification" id="speedConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Speed Converter
        (function() {
            'use strict';

            // Conversion factors to meters per second (m/s)
            const conversionFactors = {
                'ms': 1,
                'kmh': 0.277778,
                'mph': 0.44704,
                'kn': 0.514444,
                'fts': 0.3048
            };

            const elements = {
                fromInput: () => document.getElementById('speedFromInput'),
                toInput: () => document.getElementById('speedToInput'),
                fromUnit: () => document.getElementById('speedFromUnit'),
                toUnit: () => document.getElementById('speedToUnit'),
                output: () => document.getElementById('speedConverterOutput'),
                notification: () => document.getElementById('speedConverterNotification')
            };

            window.SpeedConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to meters/second first, then to target unit
                    const valueInBaseUnit = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInBaseUnit / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (Math.abs(value) >= 1000000) {
                        return value.toExponential(6);
                    } else if (Math.abs(value) < 0.000001 && value !== 0) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toFixed(10)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = {
                        'mph': 'mph',
                        'kmh': 'km/h',
                        'ms': 'm/s',
                        'kn': 'knots',
                        'fts': 'ft/s'
                    };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted speed will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        SpeedConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>