<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Beautifier - Online JS Formatter</title>
    <meta name="description" content="Instantly format and beautify messy, minified, or unreadable JavaScript code. Our free online JS formatter cleans and indents your code for better readability.">
    <link rel="canonical" href="https://www.webtoolskit.org/p/javascript-beautifier.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "JavaScript Beautifier - Online JS Formatter",
        "description": "Instantly format and beautify messy, minified, or unreadable JavaScript code. Our free online JS formatter cleans and indents your code for better readability.",
        "url": "https://www.webtoolskit.org/p/javascript-beautifier.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-28",
        "dateModified": "2025-06-28",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "JavaScript Beautifier",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Beautify JavaScript Code" },
            { "@type": "CopyAction", "name": "Copy Formatted JavaScript" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a JavaScript beautifier?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A JavaScript beautifier, or JS formatter, is a tool that automatically reformats messy, minified, or poorly written JavaScript code to make it clean, readable, and well-structured. It applies consistent indentation, spacing, and line breaks without changing the code's logic, making it easier to debug and maintain."
          }
        },
        {
          "@type": "Question",
          "name": "How do you make JavaScript code readable?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The best way to make JavaScript code readable is to use a code formatter or beautifier. These tools automatically apply consistent styling rules, such as indenting code blocks within functions and loops, placing each statement on a new line, and adding appropriate spacing around operators. This creates a clean structure that is easy for developers to follow."
          }
        },
        {
          "@type": "Question",
          "name": "How do I format JS code in VS Code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "In Visual Studio Code, you can easily format JavaScript code by installing the 'Prettier - Code formatter' extension. Once installed, you can format the entire document by pressing Shift + Alt + F (on Windows) or Shift + Option + F (on Mac), or by right-clicking and selecting 'Format Document'."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between minify and beautify?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Minify and beautify are opposite processes. Minification removes all unnecessary characters from code (like whitespace, comments, and newlines) to reduce file size for faster website performance. Beautification adds those characters back in a structured way to make the code readable for humans during development and debugging."
          }
        },
        {
          "@type": "Question",
          "name": "Is there an online tool to deobfuscate JavaScript?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, many online tools can deobfuscate JavaScript. A JS beautifier is the first step in deobfuscation, as it formats the code to reveal its structure. For more heavily obfuscated code, a dedicated JavaScript Deobfuscator tool is needed, which attempts to reverse common obfuscation techniques like variable renaming and string encoding."
          }
        }
      ]
    }
    </script>


    <style>
        /* JavaScript Beautifier Widget - Simplified & Template Compatible */
        .javascript-beautifier-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .javascript-beautifier-widget-container * { box-sizing: border-box; }

        .javascript-beautifier-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .javascript-beautifier-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .javascript-beautifier-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .javascript-beautifier-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 150px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .javascript-beautifier-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .javascript-beautifier-options {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .javascript-beautifier-option-label {
            font-weight: 500;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .javascript-beautifier-select {
            padding: var(--spacing-sm);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-family: var(--font-family);
            font-weight: 500;
        }

        .javascript-beautifier-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .javascript-beautifier-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .javascript-beautifier-btn:hover { transform: translateY(-2px); }

        .javascript-beautifier-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .javascript-beautifier-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .javascript-beautifier-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .javascript-beautifier-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .javascript-beautifier-btn-success {
            background-color: #10b981;
            color: white;
        }

        .javascript-beautifier-btn-success:hover {
            background-color: #059669;
        }

        .javascript-beautifier-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .javascript-beautifier-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .javascript-beautifier-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            white-space: pre-wrap;
            min-height: 150px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .javascript-beautifier-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .javascript-beautifier-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .javascript-beautifier-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .javascript-beautifier-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .javascript-beautifier-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .javascript-beautifier-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .javascript-beautifier-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .javascript-beautifier-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="javascript-minifier"] .javascript-beautifier-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="javascript-deobfuscator"] .javascript-beautifier-related-tool-icon { background: linear-gradient(145deg, #06B6D4, #0891B2); }
        a[href*="html-beautifier"] .javascript-beautifier-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }

        .javascript-beautifier-related-tool-item:hover .javascript-beautifier-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="javascript-minifier"]:hover .javascript-beautifier-related-tool-icon { background: linear-gradient(145deg, #1DE9B6, #14B8A6); }
        a[href*="javascript-deobfuscator"]:hover .javascript-beautifier-related-tool-icon { background: linear-gradient(145deg, #2DD4BF, #06B6D4); }
        a[href*="html-beautifier"]:hover .javascript-beautifier-related-tool-icon { background: linear-gradient(145deg, #F97316, #EA580C); }
        
        .javascript-beautifier-related-tool-item { box-shadow: none; border: none; }
        .javascript-beautifier-related-tool-item:hover { box-shadow: none; border: none; }
        .javascript-beautifier-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .javascript-beautifier-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .javascript-beautifier-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .javascript-beautifier-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .javascript-beautifier-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .javascript-beautifier-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .javascript-beautifier-related-tool-item:hover .javascript-beautifier-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .javascript-beautifier-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .javascript-beautifier-widget-title { font-size: 1.875rem; }
            .javascript-beautifier-buttons { flex-direction: column; }
            .javascript-beautifier-btn { flex: none; }
            .javascript-beautifier-options { flex-direction: column; align-items: flex-start; }
            .javascript-beautifier-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .javascript-beautifier-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .javascript-beautifier-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .javascript-beautifier-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .javascript-beautifier-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .javascript-beautifier-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .javascript-beautifier-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .javascript-beautifier-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .javascript-beautifier-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .javascript-beautifier-select:focus, .javascript-beautifier-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .javascript-beautifier-output::selection { background-color: var(--primary-color); color: white; }
        @media (max-width: 600px) { .javascript-beautifier-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="javascript-beautifier-widget-container">
        <h1 class="javascript-beautifier-widget-title">JavaScript Beautifier</h1>
        <p class="javascript-beautifier-widget-description">
            Unscramble messy, minified, or obfuscated JavaScript code. Our online JS formatter instantly applies proper indentation and styling to make your code clean and readable.
        </p>
        
        <div class="javascript-beautifier-input-group">
            <label for="jsBeautifierInput" class="javascript-beautifier-label">Enter your JavaScript code:</label>
            <textarea 
                id="jsBeautifierInput" 
                class="javascript-beautifier-textarea"
                placeholder="function hello(n){for(var i=0;i<n;i++){console.log('Hello, World!');}}"
                rows="8"
            ></textarea>
        </div>

        <div class="javascript-beautifier-options">
            <label for="jsIndentSize" class="javascript-beautifier-option-label">Indentation:</label>
            <select id="jsIndentSize" class="javascript-beautifier-select">
                <option value="2">2 Spaces</option>
                <option value="4" selected>4 Spaces</option>
                <option value="tab">Tabs</option>
            </select>
        </div>

        <div class="javascript-beautifier-buttons">
            <button class="javascript-beautifier-btn javascript-beautifier-btn-primary" onclick="JsBeautifier.beautify()">
                Beautify JS
            </button>
            <button class="javascript-beautifier-btn javascript-beautifier-btn-secondary" onclick="JsBeautifier.clear()">
                Clear All
            </button>
            <button class="javascript-beautifier-btn javascript-beautifier-btn-success" onclick="JsBeautifier.copy()">
                Copy Result
            </button>
        </div>

        <div class="javascript-beautifier-result">
            <h3 class="javascript-beautifier-result-title">Formatted JavaScript:</h3>
            <div class="javascript-beautifier-output" id="jsBeautifierOutput">
                Your formatted JavaScript will appear here...
            </div>
        </div>

        <div class="javascript-beautifier-related-tools">
            <h3 class="javascript-beautifier-related-tools-title">Related Tools</h3>
            <div class="javascript-beautifier-related-tools-grid">
                <a href="/p/javascript-minifier.html" class="javascript-beautifier-related-tool-item" rel="noopener">
                    <div class="javascript-beautifier-related-tool-icon">
                        <i class="fas fa-compress-alt"></i>
                    </div>
                    <div class="javascript-beautifier-related-tool-name">JavaScript Minifier</div>
                </a>
                <a href="/p/javascript-deobfuscator.html" class="javascript-beautifier-related-tool-item" rel="noopener">
                    <div class="javascript-beautifier-related-tool-icon">
                        <i class="fas fa-unlock-alt"></i>
                    </div>
                    <div class="javascript-beautifier-related-tool-name">Javascript DeObfuscator</div>
                </a>
                <a href="/p/html-beautifier.html" class="javascript-beautifier-related-tool-item" rel="noopener">
                    <div class="javascript-beautifier-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="javascript-beautifier-related-tool-name">HTML Beautifier</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Make Your JavaScript Code Readable Instantly</h2>
            <p>Whether you're dealing with minified code from a library, poorly formatted code from a colleague, or just want to clean up your own work, our <strong>JavaScript Beautifier</strong> is the perfect solution. Readable code is fundamental to efficient development, making it easier to spot bugs, understand logic, and collaborate with others. This tool takes any valid JavaScript and reformats it into a clean, conventional, and perfectly indented style, saving you time and preventing headaches.</p>
            
            <h3>How to Use the JavaScript Beautifier</h3>
            <ol>
                <li><strong>Paste Your JS Code:</strong> Copy the JavaScript you want to format and paste it into the input area.</li>
                <li><strong>Select Indentation:</strong> Choose your preferred indentation style—2 spaces, 4 spaces, or tabs—to match your project's coding standards.</li>
                <li><strong>Click to Beautify:</strong> Press the "Beautify JS" button. The tool will instantly process and display the clean, formatted code in the output box, ready to be copied.</li>
            </ol>
            
            <h3>The Benefits of Well-Formatted Code</h3>
            <p>Using a JS formatter is a standard practice in modern web development for many reasons. Clean code is not just about aesthetics; it has practical benefits that improve workflow and code quality. It helps in faster debugging by making control flow and block scopes visually clear. It also promotes consistency across a team, ensuring that all developers are working with a uniform code style, which simplifies code reviews and knowledge transfer.</p>
        
            <h3>Frequently Asked Questions About JavaScript Beautifier</h3>
            
            <h4>What is a JavaScript beautifier?</h4>
            <p>A JavaScript beautifier, or JS formatter, is a tool that automatically reformats messy, minified, or poorly written JavaScript code to make it clean, readable, and well-structured. It applies consistent indentation, spacing, and line breaks without changing the code's logic, making it easier to debug and maintain.</p>
            
            <h4>How do you make JavaScript code readable?</h4>
            <p>The best way to make JavaScript code readable is to use a code formatter or beautifier. These tools automatically apply consistent styling rules, such as indenting code blocks within functions and loops, placing each statement on a new line, and adding appropriate spacing around operators. This creates a clean structure that is easy for developers to follow.</p>
            
            <h4>How do I format JS code in VS Code?</h4>
            <p>In Visual Studio Code, you can easily format JavaScript code by installing the 'Prettier - Code formatter' extension. Once installed, you can format the entire document by pressing Shift + Alt + F (on Windows) or Shift + Option + F (on Mac), or by right-clicking and selecting 'Format Document'.</p>
            
            <h4>What is the difference between minify and beautify?</h4>
            <p>Minify and beautify are opposite processes. Minification removes all unnecessary characters from code (like whitespace, comments, and newlines) to reduce file size for faster website performance. Beautification adds those characters back in a structured way to make the code readable for humans during development and debugging.</p>
            
            <h4>Is there an online tool to deobfuscate JavaScript?</h4>
            <p>Yes, many online tools can deobfuscate JavaScript. A JS beautifier is the first step in deobfuscation, as it formats the code to reveal its structure. For more heavily obfuscated code, a dedicated JavaScript Deobfuscator tool is needed, which attempts to reverse common obfuscation techniques like variable renaming and string encoding.</p>
        </div>

        <div class="javascript-beautifier-features">
            <h3 class="javascript-beautifier-features-title">Key Features:</h3>
            <ul class="javascript-beautifier-features-list">
                <li class="javascript-beautifier-features-item">Customizable indentation</li>
                <li class="javascript-beautifier-features-item">Instant in-browser formatting</li>
                <li class="javascript-beautifier-features-item">Handles minified & messy code</li>
                <li class="javascript-beautifier-features-item">Improves code readability</li>
                <li class="javascript-beautifier-features-item">Syntax-aware formatting</li>
                <li class="javascript-beautifier-features-item">One-click copy to clipboard</li>
                <li class="javascript-beautifier-features-item">Helps in code debugging</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="javascript-beautifier-notification" id="jsBeautifierNotification">
        ✓ Copied to clipboard!
    </div>

    <!-- JS Beautify Library (CDN) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.7/beautify.min.js"></script>

    <script>
        // JavaScript Beautifier
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('jsBeautifierInput'),
                output: () => document.getElementById('jsBeautifierOutput'),
                notification: () => document.getElementById('jsBeautifierNotification'),
                indentSize: () => document.getElementById('jsIndentSize')
            };

            window.JsBeautifier = {
                beautify() {
                    const input = elements.input();
                    const output = elements.output();
                    const jsText = input.value;

                    if (!jsText.trim()) {
                        output.textContent = 'Please enter JavaScript code to beautify.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        indent: elements.indentSize().value
                    };

                    try {
                        const result = this.processJs(jsText, options);
                        output.textContent = result || 'Could not format the provided JavaScript.';
                    } catch (error) {
                        output.textContent = `Error: Failed to beautify. Please check for syntax errors in your code.`;
                        output.style.color = '#dc2626';
                        console.error("Beautify Error:", error);
                    }
                },

                processJs(text, options) {
                    if (typeof js_beautify === 'undefined') {
                        throw new Error('Beautifier library not loaded.');
                    }

                    const beautifyOptions = {
                        indent_char: ' ',
                        indent_size: 4,
                        indent_with_tabs: false,
                        brace_style: 'collapse',
                        keep_array_indentation: false,
                        end_with_newline: true,
                        space_after_anon_function: true,
                    };

                    if (options.indent === '2') {
                        beautifyOptions.indent_size = 2;
                    } else if (options.indent === 'tab') {
                        beautifyOptions.indent_with_tabs = true;
                        beautifyOptions.indent_size = 1; 
                    }
                    
                    return js_beautify(text, beautifyOptions);
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your formatted JavaScript will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your formatted JavaScript will appear here...', 'Please enter JavaScript code to beautify.', 'Could not format the provided JavaScript.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        JsBeautifier.beautify();
                    }
                });
            });
        })();
    </script>
</body>
</html>