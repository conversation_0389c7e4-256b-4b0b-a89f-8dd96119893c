<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Javascript Obfuscator - Protect Your JS Code</title>
    <meta name="description" content="Protect your JavaScript code from theft and reverse-engineering with our free online obfuscator. Make your JS code unreadable to humans while keeping it functional.">
    <link rel="canonical" href="https://www.webtoolskit.org/p/javascript-obfuscator.html">
    <!-- Font Awesome CDN for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Javascript Obfuscator - Protect Your JS Code",
        "description": "Protect your JavaScript code from theft and reverse-engineering with our free online obfuscator. Make your JS code unreadable to humans while keeping it functional.",
        "url": "https://www.webtoolskit.org/p/javascript-obfuscator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-07-01",
        "dateModified": "2025-07-01",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Javascript Obfuscator",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Obfuscate JavaScript Code" },
            { "@type": "CopyAction", "name": "Copy Obfuscated JavaScript" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the purpose of obfuscating JavaScript?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The main purpose of obfuscating JavaScript is to protect intellectual property. It transforms human-readable code into a scrambled, unreadable version that is extremely difficult to understand or reverse-engineer, deterring code theft and unauthorized reuse."
          }
        },
        {
          "@type": "Question",
          "name": "How do you make JavaScript code unreadable?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You can make JavaScript code unreadable by using an obfuscator tool like this one. It applies various techniques such as renaming variables, replacing strings with hexadecimal codes, injecting dead code, and flattening the control flow. The result is a functionally identical script that is incomprehensible to humans."
          }
        },
        {
          "@type": "Question",
          "name": "Is obfuscating JavaScript secure?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Obfuscation provides a layer of security through obscurity, but it is not foolproof. It can effectively deter casual attackers and protect business logic from competitors, but a determined and skilled attacker can eventually reverse-engineer the code. It should be seen as a deterrent, not a complete security solution."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between obfuscation and encryption?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Obfuscation makes code hard to read but it remains executable by a browser's JavaScript engine. Encryption transforms code into an unreadable format that is not directly executable; it must be decrypted with a key before it can be run. Obfuscated code is a deterrent, while encrypted code is inaccessible without the key."
          }
        },
        {
          "@type": "Question",
          "name": "Does obfuscation slow down JavaScript?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, obfuscation can slow down JavaScript execution. Techniques like string array lookups and control-flow flattening add computational overhead. Additionally, obfuscated code is often larger than the original, increasing download time. The performance impact varies depending on the strength and type of obfuscation applied."
          }
        }
      ]
    }
    </script>


    <style>
        /* Javascript Obfuscator Widget - Simplified & Template Compatible */
        .javascript-obfuscator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .javascript-obfuscator-widget-container * { box-sizing: border-box; }

        .javascript-obfuscator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .javascript-obfuscator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .javascript-obfuscator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .javascript-obfuscator-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 150px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .javascript-obfuscator-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .javascript-obfuscator-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .javascript-obfuscator-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .javascript-obfuscator-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .javascript-obfuscator-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .javascript-obfuscator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .javascript-obfuscator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .javascript-obfuscator-btn:hover { transform: translateY(-2px); }

        .javascript-obfuscator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .javascript-obfuscator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .javascript-obfuscator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .javascript-obfuscator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .javascript-obfuscator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .javascript-obfuscator-btn-success:hover {
            background-color: #059669;
        }

        .javascript-obfuscator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .javascript-obfuscator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .javascript-obfuscator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            white-space: pre-wrap;
            min-height: 150px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .javascript-obfuscator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .javascript-obfuscator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .javascript-obfuscator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .javascript-obfuscator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .javascript-obfuscator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .javascript-obfuscator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .javascript-obfuscator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .javascript-obfuscator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="javascript-deobfuscator"] .javascript-obfuscator-related-tool-icon { background: linear-gradient(145deg, #06B6D4, #0891B2); }
        a[href*="javascript-minifier"] .javascript-obfuscator-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="javascript-beautifier"] .javascript-obfuscator-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }


        .javascript-obfuscator-related-tool-item:hover .javascript-obfuscator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="javascript-deobfuscator"]:hover .javascript-obfuscator-related-tool-icon { background: linear-gradient(145deg, #2DD4BF, #06B6D4); }
        a[href*="javascript-minifier"]:hover .javascript-obfuscator-related-tool-icon { background: linear-gradient(145deg, #1DE9B6, #14B8A6); }
        a[href*="javascript-beautifier"]:hover .javascript-obfuscator-related-tool-icon { background: linear-gradient(145deg, #F87171, #EF4444); }
        
        .javascript-obfuscator-related-tool-item { box-shadow: none; border: none; }
        .javascript-obfuscator-related-tool-item:hover { box-shadow: none; border: none; }
        .javascript-obfuscator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .javascript-obfuscator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .javascript-obfuscator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .javascript-obfuscator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .javascript-obfuscator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .javascript-obfuscator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .javascript-obfuscator-related-tool-item:hover .javascript-obfuscator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .javascript-obfuscator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .javascript-obfuscator-widget-title { font-size: 1.875rem; }
            .javascript-obfuscator-buttons { flex-direction: column; }
            .javascript-obfuscator-btn { flex: none; }
            .javascript-obfuscator-options { grid-template-columns: 1fr; }
            .javascript-obfuscator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .javascript-obfuscator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .javascript-obfuscator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .javascript-obfuscator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .javascript-obfuscator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .javascript-obfuscator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .javascript-obfuscator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .javascript-obfuscator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .javascript-obfuscator-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .javascript-obfuscator-checkbox:focus, .javascript-obfuscator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .javascript-obfuscator-output::selection { background-color: var(--primary-color); color: white; }
        @media (max-width: 600px) { .javascript-obfuscator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="javascript-obfuscator-widget-container">
        <h1 class="javascript-obfuscator-widget-title">Javascript Obfuscator</h1>
        <p class="javascript-obfuscator-widget-description">
            Protect your intellectual property by making your JavaScript code unreadable. Our obfuscator transforms your code, making it difficult to steal or reverse-engineer.
        </p>
        
        <div class="javascript-obfuscator-input-group">
            <label for="jsObfuscatorInput" class="javascript-obfuscator-label">Paste your JavaScript code to protect:</label>
            <textarea 
                id="jsObfuscatorInput" 
                class="javascript-obfuscator-textarea"
                placeholder="function sayHello(name) {&#10;  const message = 'Hello, ' + name + '!';&#10;  console.log(message);&#10;}&#10;&#10;sayHello('World');"
                rows="8"
            ></textarea>
        </div>

        <div class="javascript-obfuscator-options">
            <div class="javascript-obfuscator-option">
                <input type="checkbox" id="obfuscateCompact" class="javascript-obfuscator-checkbox" checked>
                <label for="obfuscateCompact" class="javascript-obfuscator-option-label">Compact Code</label>
            </div>
            <div class="javascript-obfuscator-option">
                <input type="checkbox" id="obfuscateStringArray" class="javascript-obfuscator-checkbox" checked>
                <label for="obfuscateStringArray" class="javascript-obfuscator-option-label">String Array</label>
            </div>
             <div class="javascript-obfuscator-option">
                <input type="checkbox" id="obfuscateControlFlow" class="javascript-obfuscator-checkbox">
                <label for="obfuscateControlFlow" class="javascript-obfuscator-option-label">Control Flow Flattening</label>
            </div>
            <div class="javascript-obfuscator-option">
                <input type="checkbox" id="obfuscateDeadCode" class="javascript-obfuscator-checkbox">
                <label for="obfuscateDeadCode" class="javascript-obfuscator-option-label">Dead Code Injection</label>
            </div>
        </div>

        <div class="javascript-obfuscator-buttons">
            <button class="javascript-obfuscator-btn javascript-obfuscator-btn-primary" onclick="JsObfuscator.obfuscate()">
                Obfuscate JS
            </button>
            <button class="javascript-obfuscator-btn javascript-obfuscator-btn-secondary" onclick="JsObfuscator.clear()">
                Clear All
            </button>
            <button class="javascript-obfuscator-btn javascript-obfuscator-btn-success" onclick="JsObfuscator.copy()">
                Copy Result
            </button>
        </div>

        <div class="javascript-obfuscator-result">
            <h3 class="javascript-obfuscator-result-title">Obfuscated JavaScript:</h3>
            <div class="javascript-obfuscator-output" id="jsObfuscatorOutput">
                Your protected JavaScript code will appear here...
            </div>
        </div>

        <div class="javascript-obfuscator-related-tools">
            <h3 class="javascript-obfuscator-related-tools-title">Related Tools</h3>
            <div class="javascript-obfuscator-related-tools-grid">
                <a href="/p/javascript-deobfuscator.html" class="javascript-obfuscator-related-tool-item" rel="noopener">
                    <div class="javascript-obfuscator-related-tool-icon">
                        <i class="fas fa-unlock-alt"></i>
                    </div>
                    <div class="javascript-obfuscator-related-tool-name">Javascript DeObfuscator</div>
                </a>
                <a href="/p/javascript-minifier.html" class="javascript-obfuscator-related-tool-item" rel="noopener">
                    <div class="javascript-obfuscator-related-tool-icon">
                        <i class="fas fa-compress-alt"></i>
                    </div>
                    <div class="javascript-obfuscator-related-tool-name">JavaScript Minifier</div>
                </a>
                <a href="/p/javascript-beautifier.html" class="javascript-obfuscator-related-tool-item" rel="noopener">
                    <div class="javascript-obfuscator-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="javascript-obfuscator-related-tool-name">JavaScript Beautifier</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Protect Your Code with Our Javascript Obfuscator</h2>
            <p>If you've invested significant time and effort into writing proprietary JavaScript, the last thing you want is for it to be easily copied and reused without permission. Our free online <strong>Javascript Obfuscator</strong> is your first line of defense. This powerful tool transforms your clean, readable source code into a functionally identical but humanly incomprehensible version. By renaming variables, scrambling strings, and mangling the control flow, it makes reverse-engineering your code an incredibly difficult and time-consuming task.</p>
            
            <h3>How to Use the Javascript Obfuscator</h3>
            <ol>
                <li><strong>Paste Your Code:</strong> Enter your original, readable JavaScript into the input box above.</li>
                <li><strong>Select Obfuscation Options:</strong> Choose the techniques you want to apply. Higher-strength options provide more protection but may impact performance.</li>
                <li><strong>Click to Obfuscate:</strong> Press the "Obfuscate JS" button. The tool will apply the selected transformations to your code.</li>
                <li><strong>Copy the Protected Code:</strong> The resulting obfuscated script will appear in the output box, ready to be copied and deployed on your website.</li>
            </ol>
            
            <h3>Why Obfuscate Your JavaScript?</h3>
            <p>Obfuscation is a critical step for protecting client-side business logic, algorithms, or any unique functionality that gives you a competitive edge. While it's not a substitute for robust server-side security, it effectively prevents casual code theft and makes it much harder for competitors to analyze your application's front-end workings. It acts as a strong deterrent, raising the bar for anyone attempting to understand or duplicate your code.</p>
        
            <h3>Frequently Asked Questions About Javascript Obfuscator</h3>
            
            <h4>What is the purpose of obfuscating JavaScript?</h4>
            <p>The main purpose of obfuscating JavaScript is to protect intellectual property. It transforms human-readable code into a scrambled, unreadable version that is extremely difficult to understand or reverse-engineer, deterring code theft and unauthorized reuse.</p>
            
            <h4>How do you make JavaScript code unreadable?</h4>
            <p>You can make JavaScript code unreadable by using an obfuscator tool like this one. It applies various techniques such as renaming variables, replacing strings with hexadecimal codes, injecting dead code, and flattening the control flow. The result is a functionally identical script that is incomprehensible to humans.</p>
            
            <h4>Is obfuscating JavaScript secure?</h4>
            <p>Obfuscation provides a layer of security through obscurity, but it is not foolproof. It can effectively deter casual attackers and protect business logic from competitors, but a determined and skilled attacker can eventually reverse-engineer the code. It should be seen as a deterrent, not a complete security solution.</p>
            
            <h4>What is the difference between obfuscation and encryption?</h4>
            <p>Obfuscation makes code hard to read but it remains executable by a browser's JavaScript engine. Encryption transforms code into an unreadable format that is not directly executable; it must be decrypted with a key before it can be run. Obfuscated code is a deterrent, while encrypted code is inaccessible without the key.</p>
            
            <h4>Does obfuscation slow down JavaScript?</h4>
            <p>Yes, obfuscation can slow down JavaScript execution. Techniques like string array lookups and control-flow flattening add computational overhead. Additionally, obfuscated code is often larger than the original, increasing download time. The performance impact varies depending on the strength and type of obfuscation applied.</p>
        </div>

        <div class="javascript-obfuscator-features">
            <h3 class="javascript-obfuscator-features-title">Key Features:</h3>
            <ul class="javascript-obfuscator-features-list">
                <li class="javascript-obfuscator-features-item">Protects intellectual property</li>
                <li class="javascript-obfuscator-features-item">Prevents reverse-engineering</li>
                <li class="javascript-obfuscator-features-item">Multiple obfuscation layers</li>
                <li class="javascript-obfuscator-features-item">Makes code unreadable</li>
                <li class="javascript-obfuscator-features-item">Deters code theft</li>
                <li class="javascript-obfuscator-features-item">Customizable protection</li>
                <li class="javascript-obfuscator-features-item">One-click copy to clipboard</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="javascript-obfuscator-notification" id="jsObfuscatorNotification">
        ✓ Copied to clipboard!
    </div>

    <!-- Javascript-Obfuscator Library (CDN) -->
    <script src="https://cdn.jsdelivr.net/npm/javascript-obfuscator/dist/index.browser.js"></script>

    <script>
        // Javascript Obfuscator
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('jsObfuscatorInput'),
                output: () => document.getElementById('jsObfuscatorOutput'),
                notification: () => document.getElementById('jsObfuscatorNotification'),
                compact: () => document.getElementById('obfuscateCompact'),
                stringArray: () => document.getElementById('obfuscateStringArray'),
                controlFlow: () => document.getElementById('obfuscateControlFlow'),
                deadCode: () => document.getElementById('obfuscateDeadCode'),
            };

            window.JsObfuscator = {
                obfuscate() {
                    const input = elements.input();
                    const output = elements.output();
                    const jsText = input.value;

                    if (!jsText.trim()) {
                        output.textContent = 'Please enter JavaScript code to obfuscate.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';

                    try {
                        if (typeof JavaScriptObfuscator === 'undefined') {
                            throw new Error('Obfuscator library not loaded.');
                        }
                        
                        const obfuscationOptions = {
                            compact: elements.compact().checked,
                            controlFlowFlattening: elements.controlFlow().checked,
                            deadCodeInjection: elements.deadCode().checked,
                            stringArray: elements.stringArray().checked,
                            stringArrayEncoding: ['base64'],
                            stringArrayThreshold: 0.75,
                            renameGlobals: false,
                        };

                        const obfuscationResult = JavaScriptObfuscator.obfuscate(jsText, obfuscationOptions);
                        output.textContent = obfuscationResult.getObfuscatedCode();

                    } catch (error) {
                        output.textContent = `Error: ${error.message}. Please check your JavaScript for syntax errors.`;
                        output.style.color = '#dc2626';
                        console.error("Obfuscation Error:", error);
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your protected JavaScript code will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your protected JavaScript code will appear here...', 'Please enter JavaScript code to obfuscate.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        JsObfuscator.obfuscate();
                    }
                });
            });
        })();
    </script>
</body>
</html>