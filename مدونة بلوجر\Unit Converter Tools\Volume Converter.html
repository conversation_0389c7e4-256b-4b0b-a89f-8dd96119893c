<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Converter - Convert Liters, Gallons, Cubic Meters & More</title>
    <meta name="description" content="Instantly convert between volume units like liters, gallons, cubic meters, and cups. A free and easy-to-use online tool for cooking, engineering, and scientific calculations.">
    <meta name="keywords" content="volume converter, liters to gallons, convert cubic meters, fluid converter, measurement converter, liquid volume converter">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Volume Converter - Liters, Gallons, Cubic Feet & More",
        "description": "Convert between various volume units including liters, gallons, cubic meters, cups, and milliliters. Free online tool with high precision for any calculation.",
        "url": "https://www.webtoolskit.org/p/volume-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-25",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Volume Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Volume Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert cubic volume?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert cubic volume, you multiply the initial volume by a conversion factor. For example, to convert cubic meters to cubic feet, you use the factor that 1 cubic meter equals approximately 35.3147 cubic feet. So, 5 cubic meters would be 5 × 35.3147 = 176.5735 cubic feet. Our Volume Converter automates this for many different cubic and liquid units."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert volume into cubic feet?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert a volume from another unit into cubic feet, you must know the correct conversion factor. For example: 1 US gallon is approximately 0.133681 cubic feet, and 1 liter is about 0.0353147 cubic feet. You multiply your initial volume by this factor. For instance, 100 gallons would be 100 * 0.133681 = 13.3681 cubic feet."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert volume into liquid?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "This question can be a bit confusing, as 'liquid' isn't a unit. Both cubic units (like cubic meters) and liquid units (like liters or gallons) measure volume. The task is to convert from one type of volume unit to another. For example, you can convert from cubic feet (often used for solid volumes) to gallons (a liquid volume unit). 1 cubic foot is equivalent to about 7.48 US gallons."
          }
        },
        {
          "@type": "Question",
          "name": "What is the trick to converting units?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The main 'trick' is to use a conversion factor and know whether to multiply or divide. A simple rule is: if you are converting from a larger unit to a smaller one (like gallons to cups), you multiply. If you are converting from a smaller unit to a larger one (like milliliters to liters), you divide. The easiest trick of all is to use a reliable online tool like our Volume Converter, which handles all the math for you."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert cubic units to gallons?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert a cubic measurement to gallons, you multiply by a specific conversion factor. Key conversions are: 1 cubic foot ≈ 7.48 US gallons, and 1 cubic meter ≈ 264.172 US gallons. So, a container with a volume of 2 cubic feet would hold approximately 2 × 7.48 = 14.96 US gallons."
          }
        }
      ]
    }
    </script>

    <style>
        /* Volume Converter Widget - Simplified & Template Compatible */
        .volume-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .volume-converter-widget-container * { box-sizing: border-box; }

        .volume-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .volume-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .volume-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .volume-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .volume-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .volume-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .volume-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .volume-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .volume-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .volume-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .volume-converter-btn:hover { transform: translateY(-2px); }

        .volume-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .volume-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .volume-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .volume-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .volume-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .volume-converter-btn-success:hover {
            background-color: #059669;
        }

        .volume-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .volume-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .volume-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .volume-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .volume-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .volume-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .volume-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .volume-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .volume-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .volume-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .volume-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="length-converter"] .volume-converter-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="area-converter"] .volume-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="weight-converter"] .volume-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }

        .volume-converter-related-tool-item:hover .volume-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="length-converter"]:hover .volume-converter-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="area-converter"]:hover .volume-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        a[href*="weight-converter"]:hover .volume-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        
        .volume-converter-related-tool-item { box-shadow: none; border: none; }
        .volume-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .volume-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .volume-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .volume-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .volume-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .volume-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .volume-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .volume-converter-related-tool-item:hover .volume-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .volume-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .volume-converter-widget-title { font-size: 1.875rem; }
            .volume-converter-buttons { flex-direction: column; }
            .volume-converter-btn { flex: none; }
            .volume-converter-input-group { grid-template-columns: 1fr; }
            .volume-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .volume-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .volume-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .volume-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .volume-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .volume-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .volume-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .volume-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .volume-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .volume-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .volume-converter-output::selection { background-color: var(--primary-color); color: white; }
        .volume-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .volume-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="volume-converter-widget-container">
        <h1 class="volume-converter-widget-title">Volume Converter</h1>
        <p class="volume-converter-widget-description">
            Easily convert between liquid and cubic volume units. Switch between liters, gallons, cubic meters, cups, and more for any task.
        </p>
        
        <div class="volume-converter-input-group">
            <label for="volumeFromInput" class="volume-converter-label">From:</label>
            <input 
                type="number" 
                id="volumeFromInput" 
                class="volume-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="volumeFromUnit" class="volume-converter-select">
                <option value="l" selected>Liters (L)</option>
                <option value="ml">Milliliters (mL)</option>
                <option value="gal">US Gallons (gal)</option>
                <option value="qt">US Quarts (qt)</option>
                <option value="pt">US Pints (pt)</option>
                <option value="cup">US Cups (cup)</option>
                <option value="m3">Cubic Meters (m³)</option>
                <option value="ft3">Cubic Feet (ft³)</option>
                <option value="in3">Cubic Inches (in³)</option>
            </select>
        </div>

        <div class="volume-converter-input-group">
            <label for="volumeToInput" class="volume-converter-label">To:</label>
            <input 
                type="number" 
                id="volumeToInput" 
                class="volume-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="volumeToUnit" class="volume-converter-select">
                <option value="l">Liters (L)</option>
                <option value="ml">Milliliters (mL)</option>
                <option value="gal" selected>US Gallons (gal)</option>
                <option value="qt">US Quarts (qt)</option>
                <option value="pt">US Pints (pt)</option>
                <option value="cup">US Cups (cup)</option>
                <option value="m3">Cubic Meters (m³)</option>
                <option value="ft3">Cubic Feet (ft³)</option>
                <option value="in3">Cubic Inches (in³)</option>
            </select>
        </div>

        <div class="volume-converter-buttons">
            <button class="volume-converter-btn volume-converter-btn-primary" onclick="VolumeConverter.convert()">
                Convert Volume
            </button>
            <button class="volume-converter-btn volume-converter-btn-secondary" onclick="VolumeConverter.clear()">
                Clear All
            </button>
            <button class="volume-converter-btn volume-converter-btn-success" onclick="VolumeConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="volume-converter-result">
            <h3 class="volume-converter-result-title">Conversion Result:</h3>
            <div class="volume-converter-output" id="volumeConverterOutput">
                Your converted volume will appear here...
            </div>
        </div>

        <div class="volume-converter-related-tools">
            <h3 class="volume-converter-related-tools-title">Related Tools</h3>
            <div class="volume-converter-related-tools-grid">
                <a href="/p/length-converter.html" class="volume-converter-related-tool-item" rel="noopener">
                    <div class="volume-converter-related-tool-icon">
                        <i class="fas fa-ruler"></i>
                    </div>
                    <div class="volume-converter-related-tool-name">Length Converter</div>
                </a>

                <a href="/p/area-converter.html" class="volume-converter-related-tool-item" rel="noopener">
                    <div class="volume-converter-related-tool-icon">
                        <i class="fas fa-vector-square"></i>
                    </div>
                    <div class="volume-converter-related-tool-name">Area Converter</div>
                </a>

                <a href="/p/weight-converter.html" class="volume-converter-related-tool-item" rel="noopener">
                    <div class="volume-converter-related-tool-icon">
                        <i class="fas fa-weight"></i>
                    </div>
                    <div class="volume-converter-related-tool-name">Weight Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>The Ultimate Tool for Fast Volume Conversions</h2>
            <p>From a chef in the kitchen to an engineer on-site, converting volume is a fundamental task. Our <strong>Volume Converter</strong> is a versatile tool that bridges the gap between different measurement systems, allowing you to convert liquid and cubic volumes with ease. Whether you're switching from liters to gallons, cubic feet to cubic meters, or milliliters to cups, our converter provides accurate results in an instant. This tool is essential for cooking, construction, scientific experiments, and any scenario where precise volume measurements matter.</p>
            <p>Forget the hassle of looking up conversion factors or performing tedious calculations. This tool handles everything from common kitchen measurements to large-scale industrial volumes, ensuring you have the right numbers for your project.</p>

            <h3>How to Use the Volume Converter</h3>
            <ol>
                <li><strong>Input Your Value:</strong> Enter the numerical value you need to convert into the "From" field.</li>
                <li><strong>Choose Your Units:</strong> Select the starting unit (e.g., Liters) and the target unit (e.g., US Gallons) from the dropdown lists.</li>
                <li><strong>Get the Result:</strong> Click the "Convert Volume" button. The converted value will be displayed immediately.</li>
                <li><strong>Copy or Reset:</strong> Use the "Copy Result" button for convenience or "Clear All" to start a new conversion.</li>
            </ol>

            <h3>Frequently Asked Questions About Volume Conversion</h3>

            <h4>How to convert cubic volume?</h4>
            <p>To convert cubic volume, you multiply the initial volume by a conversion factor. For example, to convert cubic meters to cubic feet, you use the factor that 1 cubic meter equals approximately 35.3147 cubic feet. So, 5 cubic meters would be 5 × 35.3147 = 176.5735 cubic feet. Our Volume Converter automates this for many different cubic and liquid units.</p>

            <h4>How do you convert volume into cubic feet?</h4>
            <p>To convert a volume from another unit into cubic feet, you must know the correct conversion factor. For example: 1 US gallon is approximately 0.133681 cubic feet, and 1 liter is about 0.0353147 cubic feet. You multiply your initial volume by this factor. For instance, 100 gallons would be 100 * 0.133681 = 13.3681 cubic feet.</p>

            <h4>How to convert volume into liquid?</h4>
            <p>This question can be a bit confusing, as 'liquid' isn't a unit. Both cubic units (like cubic meters) and liquid units (like liters or gallons) measure volume. The task is to convert from one type of volume unit to another. For example, you can convert from cubic feet (often used for solid volumes) to gallons (a liquid volume unit). 1 cubic foot is equivalent to about 7.48 US gallons.</p>

            <h4>What is the trick to converting units?</h4>
            <p>The main 'trick' is to use a conversion factor and know whether to multiply or divide. A simple rule is: if you are converting from a larger unit to a smaller one (like gallons to cups), you multiply. If you are converting from a smaller unit to a larger one (like milliliters to liters), you divide. The easiest trick of all is to use a reliable online tool like our Volume Converter, which handles all the math for you.</p>

            <h4>How to convert cubic units to gallons?</h4>
            <p>To convert a cubic measurement to gallons, you multiply by a specific conversion factor. Key conversions are: 1 cubic foot ≈ 7.48 US gallons, and 1 cubic meter ≈ 264.172 US gallons. So, a container with a volume of 2 cubic feet would hold approximately 2 × 7.48 = 14.96 US gallons.</p>
        </div>

        <div class="volume-converter-features">
            <h3 class="volume-converter-features-title">Key Features:</h3>
            <ul class="volume-converter-features-list">
                <li class="volume-converter-features-item" style="margin-bottom: 0.3em;">Supports liquid & cubic units</li>
                <li class="volume-converter-features-item" style="margin-bottom: 0.3em;">Includes US cooking units</li>
                <li class="volume-converter-features-item" style="margin-bottom: 0.3em;">High-precision calculations</li>
                <li class="volume-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="volume-converter-features-item" style="margin-bottom: 0.3em;">Clean, responsive design</li>
                <li class="volume-converter-features-item" style="margin-bottom: 0.3em;">Client-side for instant results</li>
                <li class="volume-converter-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="volume-converter-notification" id="volumeConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Volume Converter
        (function() {
            'use strict';

            // Conversion factors to Liters (L)
            const conversionFactors = {
                'l': 1,
                'ml': 0.001,
                'gal': 3.78541,
                'qt': 0.946353,
                'pt': 0.473176,
                'cup': 0.236588,
                'm3': 1000,
                'ft3': 28.3168,
                'in3': 0.0163871
            };

            const elements = {
                fromInput: () => document.getElementById('volumeFromInput'),
                toInput: () => document.getElementById('volumeToInput'),
                fromUnit: () => document.getElementById('volumeFromUnit'),
                toUnit: () => document.getElementById('volumeToUnit'),
                output: () => document.getElementById('volumeConverterOutput'),
                notification: () => document.getElementById('volumeConverterNotification')
            };

            window.VolumeConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to Liters first, then to target unit
                    const valueInLiters = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInLiters / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value, value)} = ${formattedResult} ${this.getUnitName(toUnit.value, convertedValue)}`;
                },

                formatResult(value) {
                    if (Math.abs(value) >= 1000000) {
                        return value.toExponential(6);
                    } else if (Math.abs(value) < 0.000001 && value !== 0) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toFixed(10)).toString();
                    }
                },

                getUnitName(unit, value) {
                    const plural = value !== 1;
                    const unitNames = {
                        'l': plural ? 'liters' : 'liter',
                        'ml': plural ? 'milliliters' : 'milliliter',
                        'gal': plural ? 'US gallons' : 'US gallon',
                        'qt': plural ? 'US quarts' : 'US quart',
                        'pt': plural ? 'US pints' : 'US pint',
                        'cup': plural ? 'US cups' : 'US cup',
                        'm3': plural ? 'cubic meters' : 'cubic meter',
                        'ft3': plural ? 'cubic feet' : 'cubic foot',
                        'in3': plural ? 'cubic inches' : 'cubic inch'
                    };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted volume will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        VolumeConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>