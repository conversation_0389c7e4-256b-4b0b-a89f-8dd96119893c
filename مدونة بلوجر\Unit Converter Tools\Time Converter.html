<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Time Converter - Convert Hours, Minutes, Seconds & More</title>
    <meta name="description" content="Instantly convert between time units like hours, minutes, seconds, days, and weeks. A free and easy-to-use online tool for scheduling, video editing, and time management.">
    <meta name="keywords" content="time converter, hours to minutes, minutes to seconds, convert time, time calculator, time unit converter">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Time Converter - Hours, Minutes, Seconds, Days",
        "description": "Convert between various time units including hours, minutes, seconds, days, weeks, and years. Free online tool with precise calculations for any application.",
        "url": "https://www.webtoolskit.org/p/time-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-25",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Time Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Time Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert minutes to hours and minutes?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert minutes to hours and minutes, divide the total number of minutes by 60. The whole number part of the result is the hours, and the remainder is the minutes. For example, 150 minutes ÷ 60 = 2 with a remainder of 30. So, it's 2 hours and 30 minutes."
          }
        },
        {
          "@type": "Question",
          "name": "What is the formula for time converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The formula depends on the units you're converting. To go from a larger unit to a smaller one, you multiply (e.g., hours × 60 = minutes). To go from a smaller unit to a larger one, you divide (e.g., seconds ÷ 60 = minutes). A digital time converter automates this using a base unit like seconds for all calculations."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert 296 minutes into hours?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert 296 minutes into a decimal number of hours, you divide by 60: 296 ÷ 60 = 4.933 hours. To convert it into hours and minutes, you find the whole number and the remainder: 296 ÷ 60 = 4 with a remainder of 56. So, 296 minutes is equal to 4 hours and 56 minutes."
          }
        },
        {
          "@type": "Question",
          "name": "What is 1.7 hours in time?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert 1.7 hours into hours and minutes, take the whole number as the hours (1 hour). Then, convert the decimal part to minutes by multiplying it by 60. So, 0.7 × 60 = 42 minutes. This means 1.7 hours is equal to 1 hour and 42 minutes."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert time into number of hours?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert any time unit into hours, you use division or multiplication. Convert minutes to hours by dividing by 60. Convert days to hours by multiplying by 24. For a format like HH:MM:SS, you can calculate total hours with the formula: Hours + (Minutes / 60) + (Seconds / 3600)."
          }
        }
      ]
    }
    </script>

    <style>
        /* Time Converter Widget - Simplified & Template Compatible */
        .time-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .time-converter-widget-container * { box-sizing: border-box; }

        .time-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .time-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .time-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .time-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .time-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .time-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .time-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .time-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .time-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .time-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .time-converter-btn:hover { transform: translateY(-2px); }

        .time-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .time-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .time-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .time-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .time-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .time-converter-btn-success:hover {
            background-color: #059669;
        }

        .time-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .time-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .time-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .time-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .time-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .time-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .time-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .time-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .time-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .time-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .time-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="speed-converter"] .time-converter-related-tool-icon { background: linear-gradient(145deg, #4F46E5, #4338CA); }
        a[href*="pace-converter"] .time-converter-related-tool-icon { background: linear-gradient(145deg, #A855F7, #9333EA); }
        a[href*="length-converter"] .time-converter-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }

        .time-converter-related-tool-item:hover .time-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="speed-converter"]:hover .time-converter-related-tool-icon { background: linear-gradient(145deg, #6366f1, #4f46e5); }
        a[href*="pace-converter"]:hover .time-converter-related-tool-icon { background: linear-gradient(145deg, #b879f9, #a855f7); }
        a[href*="length-converter"]:hover .time-converter-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        
        .time-converter-related-tool-item { box-shadow: none; border: none; }
        .time-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .time-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .time-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .time-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .time-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .time-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .time-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .time-converter-related-tool-item:hover .time-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .time-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .time-converter-widget-title { font-size: 1.875rem; }
            .time-converter-buttons { flex-direction: column; }
            .time-converter-btn { flex: none; }
            .time-converter-input-group { grid-template-columns: 1fr; }
            .time-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .time-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .time-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .time-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .time-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .time-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .time-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .time-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .time-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .time-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .time-converter-output::selection { background-color: var(--primary-color); color: white; }
        .time-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .time-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="time-converter-widget-container">
        <h1 class="time-converter-widget-title">Time Converter</h1>
        <p class="time-converter-widget-description">
            Effortlessly convert between various units of time, from seconds and minutes to days and weeks. Perfect for project planning, video editing, and more.
        </p>
        
        <div class="time-converter-input-group">
            <label for="timeFromInput" class="time-converter-label">From:</label>
            <input 
                type="number" 
                id="timeFromInput" 
                class="time-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="timeFromUnit" class="time-converter-select">
                <option value="s">Seconds</option>
                <option value="min" selected>Minutes</option>
                <option value="hr">Hours</option>
                <option value="day">Days</option>
                <option value="wk">Weeks</option>
                <option value="mo">Months (avg)</option>
                <option value="yr">Years (365d)</option>
            </select>
        </div>

        <div class="time-converter-input-group">
            <label for="timeToInput" class="time-converter-label">To:</label>
            <input 
                type="number" 
                id="timeToInput" 
                class="time-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="timeToUnit" class="time-converter-select">
                <option value="s">Seconds</option>
                <option value="min">Minutes</option>
                <option value="hr" selected>Hours</option>
                <option value="day">Days</option>
                <option value="wk">Weeks</option>
                <option value="mo">Months (avg)</option>
                <option value="yr">Years (365d)</option>
            </select>
        </div>

        <div class="time-converter-buttons">
            <button class="time-converter-btn time-converter-btn-primary" onclick="TimeConverter.convert()">
                Convert Time
            </button>
            <button class="time-converter-btn time-converter-btn-secondary" onclick="TimeConverter.clear()">
                Clear All
            </button>
            <button class="time-converter-btn time-converter-btn-success" onclick="TimeConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="time-converter-result">
            <h3 class="time-converter-result-title">Conversion Result:</h3>
            <div class="time-converter-output" id="timeConverterOutput">
                Your converted time will appear here...
            </div>
        </div>

        <div class="time-converter-related-tools">
            <h3 class="time-converter-related-tools-title">Related Tools</h3>
            <div class="time-converter-related-tools-grid">
                <a href="/p/speed-converter.html" class="time-converter-related-tool-item" rel="noopener">
                    <div class="time-converter-related-tool-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="time-converter-related-tool-name">Speed Converter</div>
                </a>

                <a href="/p/pace-converter.html" class="time-converter-related-tool-item" rel="noopener">
                    <div class="time-converter-related-tool-icon">
                        <i class="fas fa-running"></i>
                    </div>
                    <div class="time-converter-related-tool-name">Pace Converter</div>
                </a>

                <a href="/p/length-converter.html" class="time-converter-related-tool-item" rel="noopener">
                    <div class="time-converter-related-tool-icon">
                        <i class="fas fa-ruler"></i>
                    </div>
                    <div class="time-converter-related-tool-name">Length Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Instant and Accurate Time Unit Conversions</h2>
            <p>Managing time across different units is a common challenge in many fields. Whether you're a project manager calculating task durations, a video editor syncing footage, or simply planning your schedule, our <strong>Time Converter</strong> is the tool you need. It provides a fast, reliable, and user-friendly way to convert between seconds, minutes, hours, days, weeks, and more. Forget manual calculations and potential errors; this tool ensures accuracy for all your time-based needs.</p>
            <p>With support for a wide range of units, our converter can handle everything from short durations to long-term planning. The intuitive design allows you to get your results with just a few clicks, making it an indispensable tool for professionals and everyday users alike.</p>

            <h3>How to Use the Time Converter</h3>
            <ol>
                <li><strong>Enter a Value:</strong> Type the time value you want to convert into the "From" field.</li>
                <li><strong>Select Your Units:</strong> Choose the starting time unit and the unit you want to convert to from the dropdown menus.</li>
                <li><strong>Click Convert:</strong> Press the "Convert Time" button to instantly see the calculated result.</li>
                <li><strong>Copy or Clear:</strong> Use the "Copy Result" button to save the value, or click "Clear All" to begin a new conversion.</li>
            </ol>

            <h3>Frequently Asked Questions About Time Conversion</h3>

            <h4>How do you convert minutes to hours and minutes?</h4>
            <p>To convert minutes to hours and minutes, divide the total number of minutes by 60. The whole number part of the result is the hours, and the remainder is the minutes. For example, 150 minutes ÷ 60 = 2 with a remainder of 30. So, it's 2 hours and 30 minutes.</p>

            <h4>What is the formula for time converter?</h4>
            <p>The formula depends on the units you're converting. To go from a larger unit to a smaller one, you multiply (e.g., hours × 60 = minutes). To go from a smaller unit to a larger one, you divide (e.g., seconds ÷ 60 = minutes). A digital time converter automates this using a base unit like seconds for all calculations.</p>

            <h4>How to convert 296 minutes into hours?</h4>
            <p>To convert 296 minutes into a decimal number of hours, you divide by 60: 296 ÷ 60 = 4.933 hours. To convert it into hours and minutes, you find the whole number and the remainder: 296 ÷ 60 = 4 with a remainder of 56. So, 296 minutes is equal to 4 hours and 56 minutes.</p>

            <h4>What is 1.7 hours in time?</h4>
            <p>To convert 1.7 hours into hours and minutes, take the whole number as the hours (1 hour). Then, convert the decimal part to minutes by multiplying it by 60. So, 0.7 × 60 = 42 minutes. This means 1.7 hours is equal to 1 hour and 42 minutes.</p>

            <h4>How to convert time into number of hours?</h4>
            <p>To convert any time unit into hours, you use division or multiplication. Convert minutes to hours by dividing by 60. Convert days to hours by multiplying by 24. For a format like HH:MM:SS, you can calculate total hours with the formula: Hours + (Minutes / 60) + (Seconds / 3600).</p>
        </div>

        <div class="time-converter-features">
            <h3 class="time-converter-features-title">Key Features:</h3>
            <ul class="time-converter-features-list">
                <li class="time-converter-features-item" style="margin-bottom: 0.3em;">Seconds, minutes, hours, days, etc.</li>
                <li class="time-converter-features-item" style="margin-bottom: 0.3em;">Handles large time values</li>
                <li class="time-converter-features-item" style="margin-bottom: 0.3em;">High-precision decimal results</li>
                <li class="time-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="time-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side processing</li>
                <li class="time-converter-features-item" style="margin-bottom: 0.3em;">Responsive on all devices</li>
                <li class="time-converter-features-item">100% free, no data stored</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="time-converter-notification" id="timeConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Time Converter
        (function() {
            'use strict';

            // Conversion factors to seconds (s)
            const conversionFactors = {
                's': 1,
                'min': 60,
                'hr': 3600,
                'day': 86400,
                'wk': 604800,
                'mo': 2628000, // Based on an average of 30.4167 days
                'yr': 31536000  // Based on 365 days
            };

            const elements = {
                fromInput: () => document.getElementById('timeFromInput'),
                toInput: () => document.getElementById('timeToInput'),
                fromUnit: () => document.getElementById('timeFromUnit'),
                toUnit: () => document.getElementById('timeToUnit'),
                output: () => document.getElementById('timeConverterOutput'),
                notification: () => document.getElementById('timeConverterNotification')
            };

            window.TimeConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to seconds first, then to target unit
                    const valueInSeconds = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInSeconds / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value, value)} = ${formattedResult} ${this.getUnitName(toUnit.value, convertedValue)}`;
                },

                formatResult(value) {
                    if (Math.abs(value) >= 1000000) {
                        return value.toExponential(6);
                    } else if (Math.abs(value) < 0.000001 && value !== 0) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toFixed(10)).toString();
                    }
                },

                getUnitName(unit, value) {
                    const plural = value !== 1;
                    const unitNames = {
                        's': plural ? 'seconds' : 'second',
                        'min': plural ? 'minutes' : 'minute',
                        'hr': plural ? 'hours' : 'hour',
                        'day': plural ? 'days' : 'day',
                        'wk': plural ? 'weeks' : 'week',
                        'mo': plural ? 'months' : 'month',
                        'yr': plural ? 'years' : 'year'
                    };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted time will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        TimeConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>