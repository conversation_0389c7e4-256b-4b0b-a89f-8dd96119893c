<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JPG to BMP Converter - Free Online Image Format Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free JPG to BMP Converter - Convert Images Online",
        "description": "Convert JPG images to BMP format instantly. Free online tool with uncompressed output, legacy system compatibility, and quality preservation.",
        "url": "https://www.webtoolskit.org/p/jpg-to-bmp.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-22",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "JPG to BMP Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert JPG to BMP" },
            { "@type": "DownloadAction", "name": "Download Converted BMP" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Can I convert JPG to BMP?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can easily convert JPG to BMP using our free online converter. The conversion transforms compressed JPG files into uncompressed BMP format, ensuring compatibility with legacy systems and applications that require bitmap files."
          }
        },
        {
          "@type": "Question",
          "name": "What are the benefits of BMP over JPG image storage?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "BMP offers uncompressed storage with no quality loss from compression, universal compatibility across all systems, pixel-perfect accuracy for professional work, and support for various color depths. It's ideal for applications requiring exact pixel data without compression artifacts."
          }
        },
        {
          "@type": "Question",
          "name": "Why are BMP files so large?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "BMP files are large because they store raw, uncompressed pixel data. Each pixel's color information is stored individually without compression, resulting in files that can be 5-20 times larger than compressed formats like JPG, but with perfect quality preservation."
          }
        },
        {
          "@type": "Question",
          "name": "Is JPG a bitmap?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, JPG is technically a bitmap format as it stores pixel-based image data. However, it uses lossy compression to reduce file size, unlike BMP which stores raw bitmap data without compression. Both represent images as grids of pixels."
          }
        },
        {
          "@type": "Question",
          "name": "What are the disadvantages of BMP file?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "BMP disadvantages include very large file sizes, limited web browser support, no compression benefits, slower loading times, and increased storage requirements. They're not suitable for web use or situations where file size matters."
          }
        }
      ]
    }
    </script>

    <style>
        /* JPG to BMP Widget - Simplified & Template Compatible */
        .jpg-bmp-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .jpg-bmp-widget-container * { box-sizing: border-box; }

        .jpg-bmp-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .jpg-bmp-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .jpg-bmp-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-lg);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
        }

        .jpg-bmp-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .jpg-bmp-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .jpg-bmp-upload-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .jpg-bmp-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .jpg-bmp-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .jpg-bmp-file-input {
            display: none;
        }

        .jpg-bmp-size-warning {
            background-color: #fef3cd;
            border: 1px solid #fecaca;
            color: #92400e;
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-lg);
            font-size: 0.875rem;
            display: none;
        }

        .jpg-bmp-preview {
            display: none;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .jpg-bmp-preview-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .jpg-bmp-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: flex-start;
        }

        .jpg-bmp-preview-item {
            flex: 1;
            text-align: center;
        }

        .jpg-bmp-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .jpg-bmp-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-sm);
        }

        .jpg-bmp-file-info {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .jpg-bmp-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .jpg-bmp-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .jpg-bmp-btn:hover { transform: translateY(-2px); }

        .jpg-bmp-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .jpg-bmp-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .jpg-bmp-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .jpg-bmp-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .jpg-bmp-btn-success {
            background-color: #10b981;
            color: white;
        }

        .jpg-bmp-btn-success:hover {
            background-color: #059669;
        }

        .jpg-bmp-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .jpg-bmp-btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .jpg-bmp-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="png-to-bmp"] .jpg-bmp-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .jpg-bmp-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="jpg-to-png"] .jpg-bmp-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .jpg-bmp-related-tool-item:hover .jpg-bmp-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="png-to-bmp"]:hover .jpg-bmp-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .jpg-bmp-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="jpg-to-png"]:hover .jpg-bmp-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .jpg-bmp-related-tool-item { box-shadow: none; border: none; }
        .jpg-bmp-related-tool-item:hover { box-shadow: none; border: none; }
        .jpg-bmp-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .jpg-bmp-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .jpg-bmp-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .jpg-bmp-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .jpg-bmp-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .jpg-bmp-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .jpg-bmp-related-tool-item:hover .jpg-bmp-related-tool-name { color: var(--primary-color); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .jpg-bmp-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .jpg-bmp-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .jpg-bmp-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .jpg-bmp-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .jpg-bmp-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .jpg-bmp-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .jpg-bmp-widget-title { font-size: 1.875rem; }
            .jpg-bmp-buttons { flex-direction: column; }
            .jpg-bmp-btn { flex: none; }
            .jpg-bmp-preview-content { flex-direction: column; }
            .jpg-bmp-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .jpg-bmp-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .jpg-bmp-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .jpg-bmp-related-tool-name { font-size: 0.875rem; }
            .jpg-bmp-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .jpg-bmp-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .jpg-bmp-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .jpg-bmp-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .jpg-bmp-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .jpg-bmp-upload-area:hover { background-color: var(--card-bg); }
        .jpg-bmp-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        [data-theme="dark"] .jpg-bmp-size-warning { background-color: #451a03; border-color: #92400e; color: #fbbf24; }
    </style>
</head>
<body>
    <div class="jpg-bmp-widget-container">
        <h1 class="jpg-bmp-widget-title">JPG to BMP Converter</h1>
        <p class="jpg-bmp-widget-description">
            Convert JPG images to BMP format for legacy system compatibility and uncompressed output. Preserve existing quality without further compression.
        </p>
        
        <div class="jpg-bmp-upload-area" id="uploadArea">
            <div class="jpg-bmp-upload-icon">📁</div>
            <div class="jpg-bmp-upload-text">Click to select JPG image or drag & drop</div>
            <div class="jpg-bmp-upload-subtext">Supports JPG/JPEG files (Max 10MB)</div>
            <input type="file" id="fileInput" class="jpg-bmp-file-input" accept=".jpg,.jpeg">
        </div>

        <div class="jpg-bmp-size-warning" id="sizeWarning">
            <strong>File Size Notice:</strong> BMP files are uncompressed and will be significantly larger than JPG. The converted file may be 5-20 times larger than the original.
        </div>

        <div class="jpg-bmp-preview" id="previewSection">
            <h3 class="jpg-bmp-preview-title">Preview & Comparison</h3>
            <div class="jpg-bmp-preview-content">
                <div class="jpg-bmp-preview-item">
                    <div class="jpg-bmp-preview-label">Original JPG</div>
                    <img id="originalImage" class="jpg-bmp-preview-image" alt="Original JPG" />
                    <div class="jpg-bmp-file-info" id="originalInfo"></div>
                </div>
                <div class="jpg-bmp-preview-item">
                    <div class="jpg-bmp-preview-label">Converted BMP</div>
                    <img id="convertedImage" class="jpg-bmp-preview-image" alt="Converted BMP" />
                    <div class="jpg-bmp-file-info" id="convertedInfo"></div>
                </div>
            </div>
        </div>

        <div class="jpg-bmp-buttons">
            <button id="convertBtn" class="jpg-bmp-btn jpg-bmp-btn-primary" disabled>
                Convert to BMP
            </button>
            <button id="downloadBtn" class="jpg-bmp-btn jpg-bmp-btn-success" disabled>
                Download BMP
            </button>
            <button id="resetBtn" class="jpg-bmp-btn jpg-bmp-btn-secondary">
                Reset
            </button>
        </div>

        <div class="jpg-bmp-related-tools">
            <h3 class="jpg-bmp-related-tools-title">Related Tools</h3>
            <div class="jpg-bmp-related-tools-grid">
                <a href="https://www.webtoolskit.org/p/png-to-bmp.html" class="jpg-bmp-related-tool-item" rel="noopener">
                    <div class="jpg-bmp-related-tool-icon">
                        <i class="fas fa-undo-alt"></i>
                    </div>
                    <div class="jpg-bmp-related-tool-name">PNG to BMP</div>
                </a>

                <a href="https://www.webtoolskit.org/p/image-converter_23.html" class="jpg-bmp-related-tool-item" rel="noopener">
                    <div class="jpg-bmp-related-tool-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="jpg-bmp-related-tool-name">Image Converter</div>
                </a>

                <a href="https://www.webtoolskit.org/p/jpg-to-png.html" class="jpg-bmp-related-tool-item" rel="noopener">
                    <div class="jpg-bmp-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="jpg-bmp-related-tool-name">JPG to PNG</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert JPG to BMP Online - Uncompressed Format</h2>
            <p>Our <strong>JPG to BMP Converter</strong> transforms compressed JPG images into uncompressed BMP format, ensuring compatibility with legacy systems and applications that require bitmap files. While BMP files are significantly larger, they provide raw pixel data without additional compression.</p>

            <p>Converting JPG to BMP is essential for legacy system compatibility, professional workflows requiring uncompressed data, and applications that don't support modern image formats. Our tool processes conversions locally in your browser, maintaining privacy while preserving existing image quality.</p>

            <h3>Frequently Asked Questions About JPG to BMP Conversion</h3>

            <h4>Can I convert JPG to BMP?</h4>
            <p>Yes, you can easily convert JPG to BMP using our free online converter. The conversion transforms compressed JPG files into uncompressed BMP format, ensuring compatibility with legacy systems and applications that require bitmap files.</p>

            <h4>What are the benefits of BMP over JPG image storage?</h4>
            <p>BMP offers uncompressed storage with no quality loss from compression, universal compatibility across all systems, pixel-perfect accuracy for professional work, and support for various color depths. It's ideal for applications requiring exact pixel data without compression artifacts.</p>

            <h4>Why are BMP files so large?</h4>
            <p>BMP files are large because they store raw, uncompressed pixel data. Each pixel's color information is stored individually without compression, resulting in files that can be 5-20 times larger than compressed formats like JPG, but with perfect quality preservation.</p>

            <h4>Is JPG a bitmap?</h4>
            <p>Yes, JPG is technically a bitmap format as it stores pixel-based image data. However, it uses lossy compression to reduce file size, unlike BMP which stores raw bitmap data without compression. Both represent images as grids of pixels.</p>

            <h4>What are the disadvantages of BMP file?</h4>
            <p>BMP disadvantages include very large file sizes, limited web browser support, no compression benefits, slower loading times, and increased storage requirements. They're not suitable for web use or situations where file size matters.</p>
        </div>

        <div class="jpg-bmp-features">
            <h3 class="jpg-bmp-features-title">Key Features</h3>
            <ul class="jpg-bmp-features-list">
                <li class="jpg-bmp-features-item">Convert JPG to BMP instantly</li>
                <li class="jpg-bmp-features-item">Preserve existing image quality</li>
                <li class="jpg-bmp-features-item">Legacy system compatibility</li>
                <li class="jpg-bmp-features-item">Uncompressed output format</li>
                <li class="jpg-bmp-features-item">Client-side processing for privacy</li>
                <li class="jpg-bmp-features-item">Professional workflow support</li>
                <li class="jpg-bmp-features-item">Real-time preview</li>
                <li class="jpg-bmp-features-item">Raw bitmap data output</li>
            </ul>
        </div>
    </div>

    <script>
        // JPG to BMP Converter Tool - Self-contained IIFE
        (function() {
            'use strict';

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                sizeWarning: () => document.getElementById('sizeWarning'),
                previewSection: () => document.getElementById('previewSection'),
                originalImage: () => document.getElementById('originalImage'),
                convertedImage: () => document.getElementById('convertedImage'),
                originalInfo: () => document.getElementById('originalInfo'),
                convertedInfo: () => document.getElementById('convertedInfo'),
                convertBtn: () => document.getElementById('convertBtn'),
                downloadBtn: () => document.getElementById('downloadBtn'),
                resetBtn: () => document.getElementById('resetBtn')
            };

            let originalFile = null;
            let convertedBlob = null;

            function init() {
                setupEventListeners();
            }

            function setupEventListeners() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();
                const convertBtn = elements.convertBtn();
                const downloadBtn = elements.downloadBtn();
                const resetBtn = elements.resetBtn();

                // File upload events
                uploadArea.addEventListener('click', () => fileInput.click());
                fileInput.addEventListener('change', handleFileSelect);

                // Drag and drop events
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('drop', handleDrop);

                // Button events
                convertBtn.addEventListener('click', convertImage);
                downloadBtn.addEventListener('click', downloadImage);
                resetBtn.addEventListener('click', resetTool);
            }

            function handleFileSelect(event) {
                const file = event.target.files[0];
                if (file) processFile(file);
            }

            function handleDragOver(event) {
                event.preventDefault();
                elements.uploadArea().classList.add('dragover');
            }

            function handleDragLeave(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
            }

            function handleDrop(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
                const files = event.dataTransfer.files;
                if (files.length > 0) processFile(files[0]);
            }

            function processFile(file) {
                if (!file.type.includes('jpeg') && !file.type.includes('jpg')) {
                    alert('Please select a JPG/JPEG image file.');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB.');
                    return;
                }

                originalFile = file;
                displayOriginalImage();
                elements.convertBtn().disabled = false;
                elements.sizeWarning().style.display = 'block';
            }

            function displayOriginalImage() {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const originalImage = elements.originalImage();
                    originalImage.src = e.target.result;
                    
                    const originalInfo = elements.originalInfo();
                    originalInfo.textContent = `${originalFile.name} (${formatFileSize(originalFile.size)})`;
                    
                    elements.previewSection().style.display = 'block';
                };
                reader.readAsDataURL(originalFile);
            }

            function convertImage() {
                if (!originalFile) return;

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    // Convert to BMP format (uncompressed)
                    canvas.toBlob((blob) => {
                        convertedBlob = blob;
                        displayConvertedImage();
                        elements.downloadBtn().disabled = false;
                    }, 'image/bmp');
                };

                img.src = URL.createObjectURL(originalFile);
            }

            function displayConvertedImage() {
                const convertedImage = elements.convertedImage();
                convertedImage.src = URL.createObjectURL(convertedBlob);
                
                const convertedInfo = elements.convertedInfo();
                const fileName = originalFile.name.replace(/\.[^/.]+$/, '') + '.bmp';
                const sizeIncrease = ((convertedBlob.size / originalFile.size) * 100).toFixed(1);
                convertedInfo.innerHTML = `${fileName} (${formatFileSize(convertedBlob.size)})<br><small style="color: #dc3545;">Size increase: ${sizeIncrease}%</small>`;
            }

            function downloadImage() {
                if (!convertedBlob) return;

                const link = document.createElement('a');
                link.href = URL.createObjectURL(convertedBlob);
                link.download = originalFile.name.replace(/\.[^/.]+$/, '') + '.bmp';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            function resetTool() {
                originalFile = null;
                convertedBlob = null;
                elements.fileInput().value = '';
                elements.previewSection().style.display = 'none';
                elements.sizeWarning().style.display = 'none';
                elements.convertBtn().disabled = true;
                elements.downloadBtn().disabled = true;
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', init);
        })();
    </script>
</body>
</html>