<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Contact Us Page Generator - Create Professional Contact Pages</title>
    <meta name="description" content="Generate professional contact us pages instantly with our free Contact Us Page Generator. Create contact forms, business information layouts, and improve customer communication.">
    <meta name="keywords" content="contact us page generator, contact form generator, contact page builder, business contact page, professional contact form">
    <link rel="canonical" href="https://www.webtoolskit.org/p/contact-us-page-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Contact Us Page Generator - Create Professional Contact Pages",
        "description": "Generate professional contact us pages instantly with our free Contact Us Page Generator. Create contact forms, business information layouts, and improve customer communication.",
        "url": "https://www.webtoolskit.org/p/contact-us-page-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Contact Us Page Generator",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Contact page creation",
                "Form builder",
                "Business information layout",
                "Professional design templates",
                "Mobile-responsive forms"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Contact Page" },
            { "@type": "CopyAction", "name": "Copy Generated Code" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I create a professional contact us page for my website?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using our Contact Us Page Generator is the easiest way. Simply fill in your business information, contact details, and form preferences in the fields above, then click 'Generate Contact Page'. The tool will create a professional, mobile-responsive contact page with HTML and CSS that you can customize and add to your website."
          }
        },
        {
          "@type": "Question",
          "name": "What should I include on my contact us page?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A professional contact page should include your business name, phone number, email address, physical address, business hours, a contact form, and optionally a map. Our generator helps you include all these essential elements in a well-organized, user-friendly layout that builds trust with your visitors."
          }
        },
        {
          "@type": "Question",
          "name": "How to make a contact form that actually works?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our Contact Us Page Generator creates functional HTML contact forms with proper validation and user-friendly design. The generated forms include required field validation, email format checking, and clear submission buttons. You'll need to connect the form to your server-side processing script or email service to handle submissions."
          }
        },
        {
          "@type": "Question",
          "name": "What are the best practices for contact page design?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Best practices include keeping forms simple with only essential fields, using clear labels and instructions, making the page mobile-responsive, including multiple contact methods, adding trust signals like business address and hours, and ensuring fast loading times. Our generator follows all these best practices automatically."
          }
        },
        {
          "@type": "Question",
          "name": "How do I optimize my contact page for better conversions?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To optimize contact page conversions, use clear headlines, minimize form fields, add trust elements like testimonials or certifications, provide multiple contact options, ensure mobile responsiveness, and include a compelling call-to-action. Our generator creates conversion-optimized layouts that encourage visitor engagement."
          }
        }
      ]
    }
    </script>

    <style>
        /* Contact Us Page Generator Widget - Simplified & Template Compatible */
        .contact-us-page-generator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .contact-us-page-generator-widget-container * { box-sizing: border-box; }

        .contact-us-page-generator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .contact-us-page-generator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .contact-us-page-generator-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .contact-us-page-generator-field {
            display: flex;
            flex-direction: column;
        }

        .contact-us-page-generator-field-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }

        .contact-us-page-generator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .contact-us-page-generator-input,
        .contact-us-page-generator-textarea,
        .contact-us-page-generator-select {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .contact-us-page-generator-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .contact-us-page-generator-input:focus,
        .contact-us-page-generator-textarea:focus,
        .contact-us-page-generator-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .contact-us-page-generator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .contact-us-page-generator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .contact-us-page-generator-btn:hover { transform: translateY(-2px); }

        .contact-us-page-generator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .contact-us-page-generator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .contact-us-page-generator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .contact-us-page-generator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .contact-us-page-generator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .contact-us-page-generator-btn-success:hover {
            background-color: #059669;
        }

        .contact-us-page-generator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .contact-us-page-generator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .contact-us-page-generator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .contact-us-page-generator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .contact-us-page-generator-notification.show { transform: translateX(0); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        @media (max-width: 768px) {
            .contact-us-page-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .contact-us-page-generator-widget-title { font-size: 1.875rem; }
            .contact-us-page-generator-buttons { flex-direction: column; }
            .contact-us-page-generator-btn { flex: none; }
            .contact-us-page-generator-field-row { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .contact-us-page-generator-input:focus,
        [data-theme="dark"] .contact-us-page-generator-textarea:focus,
        [data-theme="dark"] .contact-us-page-generator-select:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .contact-us-page-generator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .contact-us-page-generator-output::selection { background-color: var(--primary-color); color: white; }

        .contact-us-page-generator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="about-us-generator"] .contact-us-page-generator-related-tool-icon { background: linear-gradient(145deg, #84CC16, #65A30D); }
        a[href*="terms-of-use-page-generator"] .contact-us-page-generator-related-tool-icon { background: linear-gradient(145deg, #06B6D4, #0891B2); }
        a[href*="gmail-generator"] .contact-us-page-generator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .contact-us-page-generator-related-tool-item:hover .contact-us-page-generator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="about-us-generator"]:hover .contact-us-page-generator-related-tool-icon { background: linear-gradient(145deg, #a3e635, #84cc16); }
        a[href*="terms-of-use-page-generator"]:hover .contact-us-page-generator-related-tool-icon { background: linear-gradient(145deg, #22d3ee, #06b6d4); }
        a[href*="gmail-generator"]:hover .contact-us-page-generator-related-tool-icon { background: linear-gradient(145deg, #9373f7, #8a4ff0); }

        .contact-us-page-generator-related-tool-item { box-shadow: none; border: none; }
        .contact-us-page-generator-related-tool-item:hover { box-shadow: none; border: none; }
        .contact-us-page-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .contact-us-page-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .contact-us-page-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .contact-us-page-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .contact-us-page-generator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .contact-us-page-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .contact-us-page-generator-related-tool-item:hover .contact-us-page-generator-related-tool-name { color: var(--primary-color); }

        .contact-us-page-generator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .contact-us-page-generator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .contact-us-page-generator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .contact-us-page-generator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .contact-us-page-generator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .contact-us-page-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .contact-us-page-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .contact-us-page-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .contact-us-page-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .contact-us-page-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .contact-us-page-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .contact-us-page-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .contact-us-page-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .contact-us-page-generator-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="contact-us-page-generator-widget-container">
        <h1 class="contact-us-page-generator-widget-title">Contact Us Page Generator</h1>
        <p class="contact-us-page-generator-widget-description">
            Create professional contact us pages with forms and business information layouts. Generate mobile-responsive contact pages that improve customer communication and build trust.
        </p>

        <form class="contact-us-page-generator-form">
            <div class="contact-us-page-generator-field-row">
                <div class="contact-us-page-generator-field">
                    <label for="businessName" class="contact-us-page-generator-label">Business Name:</label>
                    <input
                        type="text"
                        id="businessName"
                        class="contact-us-page-generator-input"
                        placeholder="Your Business Name"
                    />
                </div>
                <div class="contact-us-page-generator-field">
                    <label for="contactEmail" class="contact-us-page-generator-label">Email Address:</label>
                    <input
                        type="email"
                        id="contactEmail"
                        class="contact-us-page-generator-input"
                        placeholder="<EMAIL>"
                    />
                </div>
            </div>

            <div class="contact-us-page-generator-field-row">
                <div class="contact-us-page-generator-field">
                    <label for="contactPhone" class="contact-us-page-generator-label">Phone Number:</label>
                    <input
                        type="tel"
                        id="contactPhone"
                        class="contact-us-page-generator-input"
                        placeholder="(*************"
                    />
                </div>
                <div class="contact-us-page-generator-field">
                    <label for="businessHours" class="contact-us-page-generator-label">Business Hours:</label>
                    <input
                        type="text"
                        id="businessHours"
                        class="contact-us-page-generator-input"
                        placeholder="Mon-Fri 9AM-5PM"
                    />
                </div>
            </div>

            <div class="contact-us-page-generator-field">
                <label for="businessAddress" class="contact-us-page-generator-label">Business Address:</label>
                <textarea
                    id="businessAddress"
                    class="contact-us-page-generator-textarea"
                    placeholder="123 Main Street, City, State 12345"
                ></textarea>
            </div>

            <div class="contact-us-page-generator-field-row">
                <div class="contact-us-page-generator-field">
                    <label for="pageTitle" class="contact-us-page-generator-label">Page Title:</label>
                    <input
                        type="text"
                        id="pageTitle"
                        class="contact-us-page-generator-input"
                        placeholder="Contact Us"
                    />
                </div>
                <div class="contact-us-page-generator-field">
                    <label for="pageStyle" class="contact-us-page-generator-label">Page Style:</label>
                    <select
                        id="pageStyle"
                        class="contact-us-page-generator-select"
                    >
                        <option value="modern">Modern</option>
                        <option value="classic">Classic</option>
                        <option value="minimal">Minimal</option>
                    </select>
                </div>
            </div>

            <div class="contact-us-page-generator-field">
                <label for="welcomeMessage" class="contact-us-page-generator-label">Welcome Message (Optional):</label>
                <textarea
                    id="welcomeMessage"
                    class="contact-us-page-generator-textarea"
                    placeholder="We'd love to hear from you. Send us a message and we'll respond as soon as possible."
                ></textarea>
            </div>
        </form>

        <div class="contact-us-page-generator-buttons">
            <button class="contact-us-page-generator-btn contact-us-page-generator-btn-primary" onclick="ContactUsPageGenerator.generate()">
                Generate Contact Page
            </button>
            <button class="contact-us-page-generator-btn contact-us-page-generator-btn-secondary" onclick="ContactUsPageGenerator.clear()">
                Clear All
            </button>
            <button class="contact-us-page-generator-btn contact-us-page-generator-btn-success" onclick="ContactUsPageGenerator.copy()">
                Copy HTML Code
            </button>
        </div>

        <div class="contact-us-page-generator-result">
            <h3 class="contact-us-page-generator-result-title">Generated Contact Page HTML:</h3>
            <div class="contact-us-page-generator-output" id="contactUsPageOutput">Your generated contact page HTML will appear here...</div>
        </div>

        <div class="contact-us-page-generator-related-tools">
            <h3 class="contact-us-page-generator-related-tools-title">Related Tools</h3>
            <div class="contact-us-page-generator-related-tools-grid">
                <a href="/p/about-us-generator.html" class="contact-us-page-generator-related-tool-item" rel="noopener">
                    <div class="contact-us-page-generator-related-tool-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="contact-us-page-generator-related-tool-name">About Us Page Generator</div>
                </a>

                <a href="/p/terms-of-use-page-generator.html" class="contact-us-page-generator-related-tool-item" rel="noopener">
                    <div class="contact-us-page-generator-related-tool-icon">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <div class="contact-us-page-generator-related-tool-name">Terms of Use Page Generator</div>
                </a>

                <a href="/p/gmail-generator.html" class="contact-us-page-generator-related-tool-item" rel="noopener">
                    <div class="contact-us-page-generator-related-tool-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-us-page-generator-related-tool-name">Gmail Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Contact Us Page Generator for Better Customer Communication</h2>
            <p>Our <strong>Contact Us Page Generator</strong> helps you create professional, conversion-optimized contact pages that make it easy for customers to reach your business. A well-designed contact page builds trust, improves customer satisfaction, and can significantly increase your lead generation and sales conversions.</p>
            <p>Whether you're a small business owner, web developer, or marketing professional, our tool generates mobile-responsive contact pages with functional forms, business information layouts, and professional styling that follows current web design best practices.</p>

            <h3>How to Use the Contact Us Page Generator</h3>
            <ol>
                <li><strong>Enter Business Information:</strong> Fill in your business name, contact details, address, and hours in the form above.</li>
                <li><strong>Customize Settings:</strong> Choose your page title, style preference, and add a welcome message if desired.</li>
                <li><strong>Generate and Implement:</strong> Click "Generate Contact Page" and copy the HTML code to add to your website.</li>
            </ol>

            <h3>Frequently Asked Questions About Contact Page Creation</h3>

            <h4>How do I create a professional contact us page for my website?</h4>
            <p>Using our Contact Us Page Generator is the easiest way. Simply fill in your business information, contact details, and form preferences in the fields above, then click 'Generate Contact Page'. The tool will create a professional, mobile-responsive contact page with HTML and CSS that you can customize and add to your website.</p>

            <h4>What should I include on my contact us page?</h4>
            <p>A professional contact page should include your business name, phone number, email address, physical address, business hours, a contact form, and optionally a map. Our generator helps you include all these essential elements in a well-organized, user-friendly layout that builds trust with your visitors.</p>

            <h4>How to make a contact form that actually works?</h4>
            <p>Our Contact Us Page Generator creates functional HTML contact forms with proper validation and user-friendly design. The generated forms include required field validation, email format checking, and clear submission buttons. You'll need to connect the form to your server-side processing script or email service to handle submissions.</p>

            <h4>What are the best practices for contact page design?</h4>
            <p>Best practices include keeping forms simple with only essential fields, using clear labels and instructions, making the page mobile-responsive, including multiple contact methods, adding trust signals like business address and hours, and ensuring fast loading times. Our generator follows all these best practices automatically.</p>

            <h4>How do I optimize my contact page for better conversions?</h4>
            <p>To optimize contact page conversions, use clear headlines, minimize form fields, add trust elements like testimonials or certifications, provide multiple contact options, ensure mobile responsiveness, and include a compelling call-to-action. Our generator creates conversion-optimized layouts that encourage visitor engagement.</p>
        </div>

        <div class="contact-us-page-generator-features">
            <h3 class="contact-us-page-generator-features-title">Key Features:</h3>
            <ul class="contact-us-page-generator-features-list">
                <li class="contact-us-page-generator-features-item" style="margin-bottom: 0.3em;">Professional Contact Page Design</li>
                <li class="contact-us-page-generator-features-item" style="margin-bottom: 0.3em;">Mobile-Responsive Forms</li>
                <li class="contact-us-page-generator-features-item" style="margin-bottom: 0.3em;">Business Information Layout</li>
                <li class="contact-us-page-generator-features-item" style="margin-bottom: 0.3em;">Form Validation Included</li>
                <li class="contact-us-page-generator-features-item" style="margin-bottom: 0.3em;">Multiple Style Options</li>
                <li class="contact-us-page-generator-features-item" style="margin-bottom: 0.3em;">SEO-Optimized Structure</li>
                <li class="contact-us-page-generator-features-item">100% Free and Customizable</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="contact-us-page-generator-notification" id="contactUsPageNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                businessName: () => document.getElementById('businessName'),
                contactEmail: () => document.getElementById('contactEmail'),
                contactPhone: () => document.getElementById('contactPhone'),
                businessHours: () => document.getElementById('businessHours'),
                businessAddress: () => document.getElementById('businessAddress'),
                pageTitle: () => document.getElementById('pageTitle'),
                pageStyle: () => document.getElementById('pageStyle'),
                welcomeMessage: () => document.getElementById('welcomeMessage'),
                output: () => document.getElementById('contactUsPageOutput'),
                notification: () => document.getElementById('contactUsPageNotification')
            };

            window.ContactUsPageGenerator = {
                generate() {
                    const businessName = elements.businessName().value.trim();
                    const contactEmail = elements.contactEmail().value.trim();
                    const contactPhone = elements.contactPhone().value.trim();
                    const businessHours = elements.businessHours().value.trim();
                    const businessAddress = elements.businessAddress().value.trim();
                    const pageTitle = elements.pageTitle().value.trim() || 'Contact Us';
                    const pageStyle = elements.pageStyle().value;
                    const welcomeMessage = elements.welcomeMessage().value.trim();
                    const output = elements.output();

                    if (!businessName && !contactEmail && !contactPhone) {
                        output.textContent = 'Please enter at least your business name, email, or phone number to generate a contact page.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';

                    try {
                        const contactPageHTML = this.generateContactPageHTML({
                            businessName,
                            contactEmail,
                            contactPhone,
                            businessHours,
                            businessAddress,
                            pageTitle,
                            pageStyle,
                            welcomeMessage
                        });

                        output.textContent = contactPageHTML;
                    } catch (error) {
                        output.textContent = 'Error: Unable to generate contact page. Please check your input and try again.';
                        output.style.color = '#dc2626';
                    }
                },

                getStyleCSS(style) {
                    const baseCSS = `
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .contact-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .contact-header { text-align: center; margin-bottom: 40px; }
        .contact-header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .welcome-message { font-size: 1.1rem; color: #666; max-width: 600px; margin: 0 auto; }
        .contact-content { display: grid; grid-template-columns: 1fr 1fr; gap: 40px; }
        .contact-info h2, .contact-form h2 { margin-bottom: 20px; font-size: 1.5rem; }
        .info-item { margin-bottom: 15px; padding: 10px 0; border-bottom: 1px solid #eee; }
        .info-item:last-child { border-bottom: none; }
        .info-item strong { color: #333; }
        .info-item a { color: #0066cc; text-decoration: none; }
        .info-item a:hover { text-decoration: underline; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 600; }
        .form-group input, .form-group textarea { width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; }
        .form-group input:focus, .form-group textarea:focus { outline: none; border-color: #0066cc; }
        .submit-btn { background: #0066cc; color: white; padding: 12px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
        .submit-btn:hover { background: #0052a3; }
        @media (max-width: 768px) { .contact-content { grid-template-columns: 1fr; gap: 30px; } .contact-header h1 { font-size: 2rem; } }`;

                    const styleVariations = {
                        modern: `
        .contact-header h1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
        .contact-info, .contact-form { background: #f8f9fa; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .submit-btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }`,

                        classic: `
        .contact-header h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; display: inline-block; }
        .contact-info, .contact-form { border: 1px solid #ddd; padding: 30px; background: #fff; }
        .submit-btn { background: #3498db; }
        .submit-btn:hover { background: #2980b9; }`,

                        minimal: `
        .contact-header h1 { color: #333; font-weight: 300; }
        .contact-info h2, .contact-form h2 { font-weight: 300; color: #666; }
        .info-item { border-bottom: 1px solid #f0f0f0; }
        .form-group input, .form-group textarea { border: 1px solid #e0e0e0; }
        .submit-btn { background: #333; padding: 15px 40px; }
        .submit-btn:hover { background: #555; }`
                    };

                    return baseCSS + (styleVariations[style] || styleVariations.modern);
                },

                generateContactPageHTML(data) {
                    const styleCSS = this.getStyleCSS(data.pageStyle);

                    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.escapeHtml(data.pageTitle)}${data.businessName ? ' - ' + this.escapeHtml(data.businessName) : ''}</title>
    <style>
${styleCSS}
    </style>
</head>
<body>
    <div class="contact-container">
        <header class="contact-header">
            <h1>${this.escapeHtml(data.pageTitle)}</h1>
            ${data.welcomeMessage ? `<p class="welcome-message">${this.escapeHtml(data.welcomeMessage)}</p>` : ''}
        </header>

        <div class="contact-content">
            <div class="contact-info">
                <h2>Get in Touch</h2>
                ${data.businessName ? `<div class="info-item">
                    <strong>Business:</strong> ${this.escapeHtml(data.businessName)}
                </div>` : ''}
                ${data.contactEmail ? `<div class="info-item">
                    <strong>Email:</strong> <a href="mailto:${this.escapeHtml(data.contactEmail)}">${this.escapeHtml(data.contactEmail)}</a>
                </div>` : ''}
                ${data.contactPhone ? `<div class="info-item">
                    <strong>Phone:</strong> <a href="tel:${this.escapeHtml(data.contactPhone.replace(/[^0-9+]/g, ''))}">${this.escapeHtml(data.contactPhone)}</a>
                </div>` : ''}
                ${data.businessHours ? `<div class="info-item">
                    <strong>Hours:</strong> ${this.escapeHtml(data.businessHours)}
                </div>` : ''}
                ${data.businessAddress ? `<div class="info-item">
                    <strong>Address:</strong><br>${this.escapeHtml(data.businessAddress).replace(/\\n/g, '<br>')}
                </div>` : ''}
            </div>

            <div class="contact-form">
                <h2>Send us a Message</h2>
                <form action="#" method="POST">
                    <div class="form-group">
                        <label for="name">Name *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject">
                    </div>
                    <div class="form-group">
                        <label for="message">Message *</label>
                        <textarea id="message" name="message" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="submit-btn">Send Message</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>`;
                },

                escapeHtml(text) {
                    const div = document.createElement('div');
                    div.textContent = text;
                    return div.innerHTML;
                },

                clear() {
                    elements.businessName().value = '';
                    elements.contactEmail().value = '';
                    elements.contactPhone().value = '';
                    elements.businessHours().value = '';
                    elements.businessAddress().value = '';
                    elements.pageTitle().value = '';
                    elements.pageStyle().value = 'modern';
                    elements.welcomeMessage().value = '';
                    elements.output().textContent = 'Your generated contact page HTML will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Your generated contact page HTML will appear here...' || text.startsWith('Please enter') || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        ContactUsPageGenerator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
