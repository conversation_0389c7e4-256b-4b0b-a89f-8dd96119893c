<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weight Converter - Convert Kilograms, Pounds, Ounces & More</title>
    <meta name="description" content="Instantly convert between weight units like kilograms, pounds, ounces, grams, and stones. A free and easy-to-use online tool for cooking, science, and everyday use.">
    <meta name="keywords" content="weight converter, kg to lbs, lbs to kg, convert pounds to kilograms, mass converter, measurement converter">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Weight Converter - Convert kg, lbs, oz, g, st",
        "description": "Convert between kilograms, pounds, ounces, grams, stones, and other weight units instantly. Free online tool with real-time conversion and one-click copying.",
        "url": "https://www.webtoolskit.org/p/weight-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-25",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Weight Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Weight Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert 1 kg to lbs?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert 1 kilogram (kg) to pounds (lbs), you multiply by the conversion factor. 1 kg is equal to approximately 2.20462 lbs. So, the calculation is 1 kg × 2.20462 = 2.20462 lbs."
          }
        },
        {
          "@type": "Question",
          "name": "What is the formula for calculating weight?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "In physics, the formula for weight is Weight = Mass × Gravitational Acceleration (W = mg). However, in everyday language, 'weight' is used interchangeably with 'mass.' Our converter tool converts between different units of mass (like kilograms and pounds), not the force of weight."
          }
        },
        {
          "@type": "Question",
          "name": "What is the unit of weight per area?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A unit of weight per area is a measure of pressure or load distribution. Common units include pounds per square inch (PSI) or kilograms per square meter (kg/m²). It describes how a certain weight is spread over a surface area and is a different measurement from simple mass."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert weights and measures?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert between any weights and measures, you use a specific conversion factor. For example, to convert pounds to ounces, you multiply by 16 because there are 16 ounces in a pound. Using a digital weight converter tool is the easiest way, as it automates these calculations for you with high accuracy."
          }
        },
        {
          "@type": "Question",
          "name": "What is the easy trick to convert kg to lbs?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A simple trick for a quick mental estimate is to double the number of kilograms and then add 10% of that result. For example, for 50 kg: double it to get 100, then add 10% of 100 (which is 10) to get 110 lbs. The actual answer is 110.23 lbs, so this trick is very close for everyday use."
          }
        }
      ]
    }
    </script>

    <style>
        /* Weight Converter Widget - Simplified & Template Compatible */
        .weight-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .weight-converter-widget-container * { box-sizing: border-box; }

        .weight-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .weight-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .weight-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .weight-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .weight-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .weight-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .weight-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .weight-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .weight-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .weight-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .weight-converter-btn:hover { transform: translateY(-2px); }

        .weight-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .weight-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .weight-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .weight-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .weight-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .weight-converter-btn-success:hover {
            background-color: #059669;
        }

        .weight-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .weight-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .weight-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .weight-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .weight-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .weight-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .weight-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .weight-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .weight-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .weight-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .weight-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="length-converter"] .weight-converter-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="volume-converter"] .weight-converter-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="area-converter"] .weight-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .weight-converter-related-tool-item:hover .weight-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="length-converter"]:hover .weight-converter-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="volume-converter"]:hover .weight-converter-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="area-converter"]:hover .weight-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        
        .weight-converter-related-tool-item { box-shadow: none; border: none; }
        .weight-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .weight-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .weight-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .weight-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .weight-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .weight-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .weight-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .weight-converter-related-tool-item:hover .weight-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .weight-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .weight-converter-widget-title { font-size: 1.875rem; }
            .weight-converter-buttons { flex-direction: column; }
            .weight-converter-btn { flex: none; }
            .weight-converter-input-group { grid-template-columns: 1fr; }
            .weight-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .weight-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .weight-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .weight-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .weight-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .weight-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .weight-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .weight-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .weight-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .weight-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .weight-converter-output::selection { background-color: var(--primary-color); color: white; }
        .weight-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .weight-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="weight-converter-widget-container">
        <h1 class="weight-converter-widget-title">Weight Converter</h1>
        <p class="weight-converter-widget-description">
            Effortlessly convert between various units of weight, including kilograms, pounds, ounces, and grams. Ideal for recipes, workouts, and shipping.
        </p>
        
        <div class="weight-converter-input-group">
            <label for="weightFromInput" class="weight-converter-label">From:</label>
            <input 
                type="number" 
                id="weightFromInput" 
                class="weight-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="weightFromUnit" class="weight-converter-select">
                <option value="kg" selected>Kilograms (kg)</option>
                <option value="g">Grams (g)</option>
                <option value="mg">Milligrams (mg)</option>
                <option value="lbs">Pounds (lbs)</option>
                <option value="oz">Ounces (oz)</option>
                <option value="st">Stones (st)</option>
                <option value="t">Metric Tonnes (t)</option>
            </select>
        </div>

        <div class="weight-converter-input-group">
            <label for="weightToInput" class="weight-converter-label">To:</label>
            <input 
                type="number" 
                id="weightToInput" 
                class="weight-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="weightToUnit" class="weight-converter-select">
                <option value="kg">Kilograms (kg)</option>
                <option value="g">Grams (g)</option>
                <option value="mg">Milligrams (mg)</option>
                <option value="lbs" selected>Pounds (lbs)</option>
                <option value="oz">Ounces (oz)</option>
                <option value="st">Stones (st)</option>
                <option value="t">Metric Tonnes (t)</option>
            </select>
        </div>

        <div class="weight-converter-buttons">
            <button class="weight-converter-btn weight-converter-btn-primary" onclick="WeightConverter.convert()">
                Convert Weight
            </button>
            <button class="weight-converter-btn weight-converter-btn-secondary" onclick="WeightConverter.clear()">
                Clear All
            </button>
            <button class="weight-converter-btn weight-converter-btn-success" onclick="WeightConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="weight-converter-result">
            <h3 class="weight-converter-result-title">Conversion Result:</h3>
            <div class="weight-converter-output" id="weightConverterOutput">
                Your converted weight will appear here...
            </div>
        </div>

        <div class="weight-converter-related-tools">
            <h3 class="weight-converter-related-tools-title">Related Tools</h3>
            <div class="weight-converter-related-tools-grid">
                <a href="/p/length-converter.html" class="weight-converter-related-tool-item" rel="noopener">
                    <div class="weight-converter-related-tool-icon">
                        <i class="fas fa-ruler"></i>
                    </div>
                    <div class="weight-converter-related-tool-name">Length Converter</div>
                </a>

                <a href="/p/volume-converter.html" class="weight-converter-related-tool-item" rel="noopener">
                    <div class="weight-converter-related-tool-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="weight-converter-related-tool-name">Volume Converter</div>
                </a>

                <a href="/p/area-converter.html" class="weight-converter-related-tool-item" rel="noopener">
                    <div class="weight-converter-related-tool-icon">
                        <i class="fas fa-vector-square"></i>
                    </div>
                    <div class="weight-converter-related-tool-name">Area Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Accurate Weight Conversions in an Instant</h2>
            <p>Whether you're in the kitchen, at the gym, or in a science lab, you often need to convert between different units of weight. Our free <strong>Weight Converter</strong> is designed to make these calculations effortless and precise. Forget about complex math and memorizing conversion factors; this tool lets you seamlessly switch between kilograms (kg), pounds (lbs), ounces (oz), grams (g), stones (st), and more with just a few clicks. It's the perfect companion for anyone who needs reliable weight conversions on the fly.</p>
            <p>From converting recipe ingredients from grams to ounces to checking your body weight in kilograms, pounds, and stones, our tool supports a wide range of practical applications. The clean, intuitive interface ensures you get the information you need without any hassle, saving you time and preventing errors.</p>

            <h3>How to Use the Weight Converter</h3>
            <ol>
                <li><strong>Enter Your Value:</strong> Type the number you want to convert in the "From" input field.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., Kilograms) and your target unit (e.g., Pounds) from the dropdowns.</li>
                <li><strong>Convert:</strong> Click the "Convert Weight" button to see the accurate result displayed instantly.</li>
                <li><strong>Copy or Clear:</strong> Use the "Copy Result" button to save the value, or click "Clear All" to perform a new conversion.</li>
            </ol>

            <h3>Frequently Asked Questions About Weight Conversion</h3>

            <h4>How do you convert 1 kg to lbs?</h4>
            <p>To convert 1 kilogram (kg) to pounds (lbs), you multiply by the conversion factor. 1 kg is equal to approximately 2.20462 lbs. So, the calculation is 1 kg × 2.20462 = 2.20462 lbs.</p>

            <h4>What is the formula for calculating weight?</h4>
            <p>In physics, the formula for weight is Weight = Mass × Gravitational Acceleration (W = mg). However, in everyday language, 'weight' is used interchangeably with 'mass.' Our converter tool converts between different units of mass (like kilograms and pounds), not the force of weight.</p>
            
            <h4>What is the unit of weight per area?</h4>
            <p>A unit of weight per area is a measure of pressure or load distribution. Common units include pounds per square inch (PSI) or kilograms per square meter (kg/m²). It describes how a certain weight is spread over a surface area and is a different measurement from simple mass.</p>

            <h4>How to convert weights and measures?</h4>
            <p>To convert between any weights and measures, you use a specific conversion factor. For example, to convert pounds to ounces, you multiply by 16 because there are 16 ounces in a pound. Using a digital weight converter tool is the easiest way, as it automates these calculations for you with high accuracy.</p>

            <h4>What is the easy trick to convert kg to lbs?</h4>
            <p>A simple trick for a quick mental estimate is to double the number of kilograms and then add 10% of that result. For example, for 50 kg: double it to get 100, then add 10% of 100 (which is 10) to get 110 lbs. The actual answer is 110.23 lbs, so this trick is very close for everyday use.</p>
        </div>

        <div class="weight-converter-features">
            <h3 class="weight-converter-features-title">Key Features:</h3>
            <ul class="weight-converter-features-list">
                <li class="weight-converter-features-item" style="margin-bottom: 0.3em;">Metric to Imperial conversion</li>
                <li class="weight-converter-features-item" style="margin-bottom: 0.3em;">Kg, lbs, oz, g, stone support</li>
                <li class="weight-converter-features-item" style="margin-bottom: 0.3em;">High precision results</li>
                <li class="weight-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="weight-converter-features-item" style="margin-bottom: 0.3em;">User-friendly for all devices</li>
                <li class="weight-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side processing</li>
                <li class="weight-converter-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="weight-converter-notification" id="weightConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Weight Converter
        (function() {
            'use strict';

            // Conversion factors to kilograms (kg)
            const conversionFactors = {
                'kg': 1,
                'g': 0.001,
                'mg': 0.000001,
                'lbs': 0.45359237,
                'oz': 0.02834952,
                'st': 6.35029318,
                't': 1000
            };

            const elements = {
                fromInput: () => document.getElementById('weightFromInput'),
                toInput: () => document.getElementById('weightToInput'),
                fromUnit: () => document.getElementById('weightFromUnit'),
                toUnit: () => document.getElementById('weightToUnit'),
                output: () => document.getElementById('weightConverterOutput'),
                notification: () => document.getElementById('weightConverterNotification')
            };

            window.WeightConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to kilograms first, then to target unit
                    const valueInKilograms = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInKilograms / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (Math.abs(value) >= 1000000) {
                        return value.toExponential(6);
                    } else if (Math.abs(value) < 0.000001 && value !== 0) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toFixed(10)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = {
                        'kg': 'kilograms',
                        'g': 'grams',
                        'mg': 'milligrams',
                        'lbs': 'pounds',
                        'oz': 'ounces',
                        'st': 'stones',
                        't': 'metric tonnes'
                    };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted weight will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        WeightConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>