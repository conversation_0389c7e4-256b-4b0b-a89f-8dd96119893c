<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free MD5 Generator - Create MD5 Hash Online Instantly</title>
    <meta name="description" content="Generate MD5 hash values instantly with our free MD5 Generator. Create secure MD5 hashes for text strings, passwords, and data integrity verification online.">
    <meta name="keywords" content="md5 generator, md5 hash, md5 hash generator, text to md5, password hash, data integrity, md5 checksum">
    <link rel="canonical" href="https://www.webtoolskit.org/p/md5-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free MD5 Generator - Create MD5 Hash Online Instantly",
        "description": "Generate MD5 hash values instantly with our free MD5 Generator. Create secure MD5 hashes for text strings, passwords, and data integrity verification online.",
        "url": "https://www.webtoolskit.org/p/md5-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "MD5 Generator",
            "applicationCategory": "SecurityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "MD5 hash generation",
                "Text to MD5 conversion",
                "Password hashing",
                "Data integrity verification",
                "Instant hash creation"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate MD5 Hash" },
            { "@type": "CopyAction", "name": "Copy Generated Hash" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I generate an MD5 hash from text?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using our MD5 Generator is simple. Enter your text in the input field above, then click 'Generate MD5 Hash'. The tool will instantly create a 32-character hexadecimal MD5 hash value that you can copy and use for your needs."
          }
        },
        {
          "@type": "Question",
          "name": "What is MD5 hash used for?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "MD5 hashes are commonly used for data integrity verification, password storage (though not recommended for new applications), file checksums, and creating unique identifiers. They help verify that data hasn't been altered during transmission or storage."
          }
        },
        {
          "@type": "Question",
          "name": "Is MD5 hash secure for passwords?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "MD5 is no longer considered secure for password hashing due to vulnerabilities and the ability to create collision attacks. For password security, use stronger algorithms like bcrypt, scrypt, or Argon2 instead."
          }
        },
        {
          "@type": "Question",
          "name": "Can I reverse an MD5 hash back to original text?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "MD5 is a one-way hash function, meaning you cannot directly reverse it to get the original text. However, common passwords and text can sometimes be found using rainbow tables or brute force attacks, which is why MD5 isn't recommended for security-critical applications."
          }
        },
        {
          "@type": "Question",
          "name": "What's the difference between MD5 and SHA256?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "MD5 produces a 128-bit (32 hex characters) hash and is faster but less secure. SHA256 produces a 256-bit (64 hex characters) hash and is much more secure against attacks. SHA256 is recommended for security applications where MD5 is no longer suitable."
          }
        }
      ]
    }
    </script>

    <style>
        /* MD5 Generator Widget - Simplified & Template Compatible */
        .md5-generator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .md5-generator-widget-container * { box-sizing: border-box; }

        .md5-generator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .md5-generator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .md5-generator-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .md5-generator-field {
            display: flex;
            flex-direction: column;
        }

        .md5-generator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .md5-generator-input,
        .md5-generator-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .md5-generator-textarea {
            resize: vertical;
            min-height: 120px;
        }

        .md5-generator-input:focus,
        .md5-generator-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .md5-generator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .md5-generator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .md5-generator-btn:hover { transform: translateY(-2px); }

        .md5-generator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .md5-generator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .md5-generator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .md5-generator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .md5-generator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .md5-generator-btn-success:hover {
            background-color: #059669;
        }

        .md5-generator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .md5-generator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .md5-generator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .md5-generator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .md5-generator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        @media (max-width: 768px) {
            .md5-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .md5-generator-widget-title { font-size: 1.875rem; }
            .md5-generator-buttons { flex-direction: column; }
            .md5-generator-btn { flex: none; }
        }

        [data-theme="dark"] .md5-generator-input:focus,
        [data-theme="dark"] .md5-generator-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .md5-generator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .md5-generator-output::selection { background-color: var(--primary-color); color: white; }

        .md5-generator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="password-generator"] .md5-generator-related-tool-icon { background: linear-gradient(145deg, #0EA5E9, #0284C7); }
        a[href*="base64-encode"] .md5-generator-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="base64-decode"] .md5-generator-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }

        .md5-generator-related-tool-item:hover .md5-generator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="password-generator"]:hover .md5-generator-related-tool-icon { background: linear-gradient(145deg, #38bdf8, #0ea5e9); }
        a[href*="base64-encode"]:hover .md5-generator-related-tool-icon { background: linear-gradient(145deg, #fbbf24, #f59e0b); }
        a[href*="base64-decode"]:hover .md5-generator-related-tool-icon { background: linear-gradient(145deg, #34d399, #10b981); }

        .md5-generator-related-tool-item { box-shadow: none; border: none; }
        .md5-generator-related-tool-item:hover { box-shadow: none; border: none; }
        .md5-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .md5-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .md5-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .md5-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .md5-generator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .md5-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .md5-generator-related-tool-item:hover .md5-generator-related-tool-name { color: var(--primary-color); }

        .md5-generator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .md5-generator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .md5-generator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .md5-generator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .md5-generator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .md5-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .md5-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .md5-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .md5-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .md5-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .md5-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .md5-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .md5-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .md5-generator-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="md5-generator-widget-container">
        <h1 class="md5-generator-widget-title">MD5 Generator</h1>
        <p class="md5-generator-widget-description">
            Generate MD5 hash values instantly for text strings, passwords, and data integrity verification. Our free MD5 generator creates secure 128-bit hash values in seconds.
        </p>

        <form class="md5-generator-form">
            <div class="md5-generator-field">
                <label for="md5Input" class="md5-generator-label">Enter Text to Hash:</label>
                <textarea
                    id="md5Input"
                    class="md5-generator-textarea"
                    placeholder="Enter your text here to generate MD5 hash..."
                ></textarea>
            </div>
        </form>

        <div class="md5-generator-buttons">
            <button class="md5-generator-btn md5-generator-btn-primary" onclick="MD5Generator.generate()">
                Generate MD5 Hash
            </button>
            <button class="md5-generator-btn md5-generator-btn-secondary" onclick="MD5Generator.clear()">
                Clear All
            </button>
            <button class="md5-generator-btn md5-generator-btn-success" onclick="MD5Generator.copy()">
                Copy Hash
            </button>
        </div>

        <div class="md5-generator-result">
            <h3 class="md5-generator-result-title">Generated MD5 Hash:</h3>
            <div class="md5-generator-output" id="md5Output">Your generated MD5 hash will appear here...</div>
        </div>

        <div class="md5-generator-related-tools">
            <h3 class="md5-generator-related-tools-title">Related Tools</h3>
            <div class="md5-generator-related-tools-grid">
                <a href="/p/password-generator.html" class="md5-generator-related-tool-item" rel="noopener">
                    <div class="md5-generator-related-tool-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="md5-generator-related-tool-name">Password Generator</div>
                </a>

                <a href="/p/base64-encode.html" class="md5-generator-related-tool-item" rel="noopener">
                    <div class="md5-generator-related-tool-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="md5-generator-related-tool-name">Base64 Encode</div>
                </a>

                <a href="/p/base64-decode.html" class="md5-generator-related-tool-item" rel="noopener">
                    <div class="md5-generator-related-tool-icon">
                        <i class="fas fa-unlock-alt"></i>
                    </div>
                    <div class="md5-generator-related-tool-name">Base64 Decode</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional MD5 Generator for Secure Hash Creation</h2>
            <p>Our <strong>MD5 Generator</strong> provides instant, reliable MD5 hash creation for text strings, passwords, and data verification needs. MD5 (Message Digest Algorithm 5) is a widely-used cryptographic hash function that produces a 128-bit hash value, typically represented as a 32-character hexadecimal number.</p>
            <p>Whether you're a developer, system administrator, or security professional, our tool makes it easy to generate MD5 hashes quickly and accurately. Simply enter your text and get an instant MD5 hash that you can use for data integrity checks, password storage, or file verification purposes.</p>

            <h3>How to Use the MD5 Generator</h3>
            <ol>
                <li><strong>Enter Your Text:</strong> Type or paste the text you want to hash into the input field above.</li>
                <li><strong>Generate Hash:</strong> Click the "Generate MD5 Hash" button to create your MD5 hash instantly.</li>
                <li><strong>Copy and Use:</strong> Copy the generated 32-character hash and use it for your specific needs.</li>
            </ol>

            <h3>Frequently Asked Questions About MD5 Hashes</h3>

            <h4>How do I generate an MD5 hash from text?</h4>
            <p>Using our MD5 Generator is simple. Enter your text in the input field above, then click 'Generate MD5 Hash'. The tool will instantly create a 32-character hexadecimal MD5 hash value that you can copy and use for your needs.</p>

            <h4>What is MD5 hash used for?</h4>
            <p>MD5 hashes are commonly used for data integrity verification, password storage (though not recommended for new applications), file checksums, and creating unique identifiers. They help verify that data hasn't been altered during transmission or storage.</p>

            <h4>Is MD5 hash secure for passwords?</h4>
            <p>MD5 is no longer considered secure for password hashing due to vulnerabilities and the ability to create collision attacks. For password security, use stronger algorithms like bcrypt, scrypt, or Argon2 instead.</p>

            <h4>Can I reverse an MD5 hash back to original text?</h4>
            <p>MD5 is a one-way hash function, meaning you cannot directly reverse it to get the original text. However, common passwords and text can sometimes be found using rainbow tables or brute force attacks, which is why MD5 isn't recommended for security-critical applications.</p>

            <h4>What's the difference between MD5 and SHA256?</h4>
            <p>MD5 produces a 128-bit (32 hex characters) hash and is faster but less secure. SHA256 produces a 256-bit (64 hex characters) hash and is much more secure against attacks. SHA256 is recommended for security applications where MD5 is no longer suitable.</p>
        </div>

        <div class="md5-generator-features">
            <h3 class="md5-generator-features-title">Key Features:</h3>
            <ul class="md5-generator-features-list">
                <li class="md5-generator-features-item" style="margin-bottom: 0.3em;">Instant MD5 Hash Generation</li>
                <li class="md5-generator-features-item" style="margin-bottom: 0.3em;">Text to MD5 Conversion</li>
                <li class="md5-generator-features-item" style="margin-bottom: 0.3em;">Data Integrity Verification</li>
                <li class="md5-generator-features-item" style="margin-bottom: 0.3em;">Password Hash Creation</li>
                <li class="md5-generator-features-item" style="margin-bottom: 0.3em;">One-Click Copy to Clipboard</li>
                <li class="md5-generator-features-item" style="margin-bottom: 0.3em;">Mobile-Friendly Interface</li>
                <li class="md5-generator-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="md5-generator-notification" id="md5Notification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            // MD5 implementation
            function md5(string) {
                function md5cycle(x, k) {
                    var a = x[0], b = x[1], c = x[2], d = x[3];
                    a = ff(a, b, c, d, k[0], 7, -680876936);
                    d = ff(d, a, b, c, k[1], 12, -389564586);
                    c = ff(c, d, a, b, k[2], 17, 606105819);
                    b = ff(b, c, d, a, k[3], 22, -1044525330);
                    a = ff(a, b, c, d, k[4], 7, -176418897);
                    d = ff(d, a, b, c, k[5], 12, 1200080426);
                    c = ff(c, d, a, b, k[6], 17, -1473231341);
                    b = ff(b, c, d, a, k[7], 22, -45705983);
                    a = ff(a, b, c, d, k[8], 7, 1770035416);
                    d = ff(d, a, b, c, k[9], 12, -1958414417);
                    c = ff(c, d, a, b, k[10], 17, -42063);
                    b = ff(b, c, d, a, k[11], 22, -1990404162);
                    a = ff(a, b, c, d, k[12], 7, 1804603682);
                    d = ff(d, a, b, c, k[13], 12, -40341101);
                    c = ff(c, d, a, b, k[14], 17, -1502002290);
                    b = ff(b, c, d, a, k[15], 22, 1236535329);
                    a = gg(a, b, c, d, k[1], 5, -165796510);
                    d = gg(d, a, b, c, k[6], 9, -1069501632);
                    c = gg(c, d, a, b, k[11], 14, 643717713);
                    b = gg(b, c, d, a, k[0], 20, -373897302);
                    a = gg(a, b, c, d, k[5], 5, -701558691);
                    d = gg(d, a, b, c, k[10], 9, 38016083);
                    c = gg(c, d, a, b, k[15], 14, -660478335);
                    b = gg(b, c, d, a, k[4], 20, -405537848);
                    a = gg(a, b, c, d, k[9], 5, 568446438);
                    d = gg(d, a, b, c, k[14], 9, -1019803690);
                    c = gg(c, d, a, b, k[3], 14, -187363961);
                    b = gg(b, c, d, a, k[8], 20, 1163531501);
                    a = gg(a, b, c, d, k[13], 5, -1444681467);
                    d = gg(d, a, b, c, k[2], 9, -51403784);
                    c = gg(c, d, a, b, k[7], 14, 1735328473);
                    b = gg(b, c, d, a, k[12], 20, -1926607734);
                    a = hh(a, b, c, d, k[5], 4, -378558);
                    d = hh(d, a, b, c, k[8], 11, -2022574463);
                    c = hh(c, d, a, b, k[11], 16, 1839030562);
                    b = hh(b, c, d, a, k[14], 23, -35309556);
                    a = hh(a, b, c, d, k[1], 4, -1530992060);
                    d = hh(d, a, b, c, k[4], 11, 1272893353);
                    c = hh(c, d, a, b, k[7], 16, -155497632);
                    b = hh(b, c, d, a, k[10], 23, -1094730640);
                    a = hh(a, b, c, d, k[13], 4, 681279174);
                    d = hh(d, a, b, c, k[0], 11, -358537222);
                    c = hh(c, d, a, b, k[3], 16, -722521979);
                    b = hh(b, c, d, a, k[6], 23, 76029189);
                    a = hh(a, b, c, d, k[9], 4, -640364487);
                    d = hh(d, a, b, c, k[12], 11, -421815835);
                    c = hh(c, d, a, b, k[15], 16, 530742520);
                    b = hh(b, c, d, a, k[2], 23, -995338651);
                    a = ii(a, b, c, d, k[0], 6, -198630844);
                    d = ii(d, a, b, c, k[7], 10, 1126891415);
                    c = ii(c, d, a, b, k[14], 15, -1416354905);
                    b = ii(b, c, d, a, k[5], 21, -57434055);
                    a = ii(a, b, c, d, k[12], 6, 1700485571);
                    d = ii(d, a, b, c, k[3], 10, -1894986606);
                    c = ii(c, d, a, b, k[10], 15, -1051523);
                    b = ii(b, c, d, a, k[1], 21, -2054922799);
                    a = ii(a, b, c, d, k[8], 6, 1873313359);
                    d = ii(d, a, b, c, k[15], 10, -30611744);
                    c = ii(c, d, a, b, k[6], 15, -1560198380);
                    b = ii(b, c, d, a, k[13], 21, 1309151649);
                    a = ii(a, b, c, d, k[4], 6, -145523070);
                    d = ii(d, a, b, c, k[11], 10, -1120210379);
                    c = ii(c, d, a, b, k[2], 15, 718787259);
                    b = ii(b, c, d, a, k[9], 21, -343485551);
                    x[0] = add32(a, x[0]);
                    x[1] = add32(b, x[1]);
                    x[2] = add32(c, x[2]);
                    x[3] = add32(d, x[3]);
                }

                function cmn(q, a, b, x, s, t) {
                    a = add32(add32(a, q), add32(x, t));
                    return add32((a << s) | (a >>> (32 - s)), b);
                }

                function ff(a, b, c, d, x, s, t) {
                    return cmn((b & c) | ((~b) & d), a, b, x, s, t);
                }

                function gg(a, b, c, d, x, s, t) {
                    return cmn((b & d) | (c & (~d)), a, b, x, s, t);
                }

                function hh(a, b, c, d, x, s, t) {
                    return cmn(b ^ c ^ d, a, b, x, s, t);
                }

                function ii(a, b, c, d, x, s, t) {
                    return cmn(c ^ (b | (~d)), a, b, x, s, t);
                }

                function md51(s) {
                    var n = s.length,
                        state = [1732584193, -271733879, -1732584194, 271733878], i;
                    for (i = 64; i <= s.length; i += 64) {
                        md5cycle(state, md5blk(s.substring(i - 64, i)));
                    }
                    s = s.substring(i - 64);
                    var tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
                    for (i = 0; i < s.length; i++)
                        tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);
                    tail[i >> 2] |= 0x80 << ((i % 4) << 3);
                    if (i > 55) {
                        md5cycle(state, tail);
                        for (i = 0; i < 16; i++) tail[i] = 0;
                    }
                    tail[14] = n * 8;
                    md5cycle(state, tail);
                    return state;
                }

                function md5blk(s) {
                    var md5blks = [], i;
                    for (i = 0; i < 64; i += 4) {
                        md5blks[i >> 2] = s.charCodeAt(i)
                            + (s.charCodeAt(i + 1) << 8)
                            + (s.charCodeAt(i + 2) << 16)
                            + (s.charCodeAt(i + 3) << 24);
                    }
                    return md5blks;
                }

                var hex_chr = '0123456789abcdef'.split('');

                function rhex(n) {
                    var s = '', j = 0;
                    for (; j < 4; j++)
                        s += hex_chr[(n >> (j * 8 + 4)) & 0x0F]
                            + hex_chr[(n >> (j * 8)) & 0x0F];
                    return s;
                }

                function hex(x) {
                    for (var i = 0; i < x.length; i++)
                        x[i] = rhex(x[i]);
                    return x.join('');
                }

                function add32(a, b) {
                    return (a + b) & 0xFFFFFFFF;
                }

                return hex(md51(string));
            }

            const elements = {
                input: () => document.getElementById('md5Input'),
                output: () => document.getElementById('md5Output'),
                notification: () => document.getElementById('md5Notification')
            };

            window.MD5Generator = {
                generate() {
                    const input = elements.input().value;
                    const output = elements.output();

                    if (!input.trim()) {
                        output.textContent = 'Please enter some text to generate MD5 hash.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const hash = md5(input);
                    output.textContent = hash;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your generated MD5 hash will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Your generated MD5 hash will appear here...' || text.startsWith('Please enter') || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        MD5Generator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
