<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free GST Calculator - Add or Remove GST Instantly</title>
    <meta name="description" content="A simple and free GST calculator to add or remove Goods and Services Tax from any amount. Ideal for Canada, Australia, India, and other countries.">
    <meta name="keywords" content="gst calculator, gst, goods and services tax, calculate gst, remove gst, add gst, gst inclusive, gst exclusive">
    <link rel="canonical" href="https://www.webtoolskit.org/p/gst-calculator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free GST Calculator - Add or Remove GST Instantly",
        "description": "A simple and free GST calculator to add or remove Goods and Services Tax from any amount. Ideal for Canada, Australia, India, and other countries.",
        "url": "https://www.webtoolskit.org/p/gst-calculator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-10",
        "dateModified": "2025-06-24",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "GST Calculator",
            "applicationCategory": "FinanceApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Add GST to a net amount",
                "Remove GST from a gross amount",
                "Customizable GST rate",
                "GST-inclusive and GST-exclusive calculations"
            ]
        },
        "potentialAction": {
             "@type": "Action",
             "name": "Calculate GST"
        }
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you calculate GST?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate GST, you can either add it to a base price or remove it from a total price. To add GST, multiply the base amount by the GST rate (e.g., $100 * 5% = $5). To remove GST from a total, divide the total amount by (1 + the GST rate) (e.g., $105 / 1.05 = $100). Our calculator does both automatically."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate GST from total amount in Canada?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "In Canada, the federal GST is 5%. To find the original amount before tax from a total price, divide the total by 1.05. For example, if a product costs $126 including GST, the pre-tax price is $126 / 1.05 = $120. The GST amount is $6. Our calculator's 'Remove GST' option simplifies this process."
          }
        },
        {
          "@type": "Question",
          "name": "How much is GST for $100?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The GST on $100 depends on the rate. If the GST rate is 5% (like in Canada), the GST would be $5. If the rate is 10% (like in Australia), the GST would be $10. You can enter any rate into our calculator to find the exact amount."
          }
        },
        {
          "@type": "Question",
          "name": "What is the rate of GST?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The GST rate varies by country. For example, it's 5% in Canada (federal), 10% in Australia, and has multiple slabs (5%, 12%, 18%, 28%) in India. Some Canadian provinces combine it into an HST (Harmonized Sales Tax) with a higher rate. Our calculator allows you to input any rate to match your specific needs."
          }
        },
        {
          "@type": "Question",
          "name": "What is the GST calculator?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A GST calculator is a simple online tool that helps you compute the Goods and Services Tax amount for a transaction. It can either add the tax to a net price (GST-exclusive) or extract the tax amount from a gross price (GST-inclusive), making it easy for businesses and consumers to manage tax calculations."
          }
        }
      ]
    }
    </script>


    <style>
        /* GST Calculator Widget - Simplified & Template Compatible */
        .gst-calculator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .gst-calculator-widget-container * { box-sizing: border-box; }

        .gst-calculator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .gst-calculator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .gst-calculator-input-group {
            margin-bottom: var(--spacing-lg);
        }

        .gst-calculator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .gst-calculator-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .gst-calculator-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .gst-calculator-options {
            display: flex;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            justify-content: center;
        }

        .gst-calculator-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .gst-calculator-radio {
            accent-color: var(--primary-color);
            cursor: pointer;
            width: 18px;
            height: 18px;
        }
        
        .gst-calculator-radio-label {
            font-weight: 500;
            cursor: pointer;
        }

        .gst-calculator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .gst-calculator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .gst-calculator-btn:hover { transform: translateY(-2px); }

        .gst-calculator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .gst-calculator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .gst-calculator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .gst-calculator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .gst-calculator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .gst-calculator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .gst-calculator-output {
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 120px;
            color: var(--text-color);
            line-height: 1.5;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .gst-calculator-output-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px dashed var(--border-color);
        }
        
        .gst-calculator-output-item:last-child {
            border-bottom: none;
        }

        .gst-calculator-output-label {
            font-weight: 600;
            color: var(--text-color-light);
        }
        
        .gst-calculator-output-value {
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--primary-color);
        }
        
        .gst-calculator-output-value.total {
            color: var(--text-color);
        }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }
        
        .gst-calculator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .gst-calculator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .gst-calculator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .gst-calculator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            margin-bottom: 0.3em;
        }

        .gst-calculator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 4px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 600px) { 
            .gst-calculator-features-list { 
                columns: 1 !important; 
                -webkit-columns: 1 !important; 
                -moz-columns: 1 !important; 
            } 
        }

        .gst-calculator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="sales-tax-calculator"] .gst-calculator-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="percentage-calculator"] .gst-calculator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="margin-calculator"] .gst-calculator-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }

        .gst-calculator-related-tool-item:hover .gst-calculator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .gst-calculator-related-tool-item { box-shadow: none; border: none; }
        .gst-calculator-related-tool-item:hover { box-shadow: none; border: none; }
        .gst-calculator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .gst-calculator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .gst-calculator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .gst-calculator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .gst-calculator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .gst-calculator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .gst-calculator-related-tool-item:hover .gst-calculator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .gst-calculator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .gst-calculator-widget-title { font-size: 1.875rem; }
            .gst-calculator-buttons { flex-direction: column; }
            .gst-calculator-btn { flex: none; }
            .gst-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .gst-calculator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .gst-calculator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .gst-calculator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .gst-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .gst-calculator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .gst-calculator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .gst-calculator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .gst-calculator-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .gst-calculator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
    </style>
</head>
<body>
    <div class="gst-calculator-widget-container">
        <h1 class="gst-calculator-widget-title">GST Calculator</h1>
        <p class="gst-calculator-widget-description">
            Easily add or remove Goods and Services Tax from any amount for accurate pricing and invoicing.
        </p>
        
        <div class="gst-calculator-input-group">
            <label for="gstAmountInput" class="gst-calculator-label">Amount</label>
            <input 
                id="gstAmountInput" 
                class="gst-calculator-input"
                type="number"
                placeholder="Enter amount (before or after GST)"
            />
        </div>

        <div class="gst-calculator-input-group">
            <label for="gstRateInput" class="gst-calculator-label">GST Rate (%)</label>
            <input 
                id="gstRateInput" 
                class="gst-calculator-input"
                type="number"
                placeholder="e.g., 5, 10, 18"
            />
        </div>

        <div class="gst-calculator-options">
            <div class="gst-calculator-option">
                <input type="radio" id="addGst" name="gst_calculation_type" class="gst-calculator-radio" value="add" checked>
                <label for="addGst" class="gst-calculator-radio-label">Add GST (Exclusive)</label>
            </div>
            <div class="gst-calculator-option">
                <input type="radio" id="removeGst" name="gst_calculation_type" class="gst-calculator-radio" value="remove">
                <label for="removeGst" class="gst-calculator-radio-label">Remove GST (Inclusive)</label>
            </div>
        </div>

        <div class="gst-calculator-buttons">
            <button class="gst-calculator-btn gst-calculator-btn-primary" onclick="GSTCalculator.calculate()">
                Calculate GST
            </button>
            <button class="gst-calculator-btn gst-calculator-btn-secondary" onclick="GSTCalculator.clear()">
                Clear All
            </button>
        </div>

        <div class="gst-calculator-result">
            <h3 class="gst-calculator-result-title">Calculation Result:</h3>
            <div class="gst-calculator-output" id="gstCalculatorOutput">
                Your GST calculation will appear here...
            </div>
        </div>

        <div class="gst-calculator-related-tools">
            <h3 class="gst-calculator-related-tools-title">Related Tools</h3>
            <div class="gst-calculator-related-tools-grid">
                <a href="/p/sales-tax-calculator.html" class="gst-calculator-related-tool-item" rel="noopener">
                    <div class="gst-calculator-related-tool-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="gst-calculator-related-tool-name">Sales Tax Calculator</div>
                </a>
                <a href="/p/percentage-calculator.html" class="gst-calculator-related-tool-item" rel="noopener">
                    <div class="gst-calculator-related-tool-icon">
                        <i class="fas fa-percent"></i>
                    </div>
                    <div class="gst-calculator-related-tool-name">Percentage Calculator</div>
                </a>
                <a href="/p/margin-calculator.html" class="gst-calculator-related-tool-item" rel="noopener">
                    <div class="gst-calculator-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="gst-calculator-related-tool-name">Margin Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Simplify Your Tax Calculations with Our GST Calculator</h2>
            <p>The Goods and Services Tax (GST) is a standard part of commerce in many countries, but calculating it correctly can be tricky. Whether you're a business owner creating invoices, a freelancer setting prices, or a consumer checking a bill, our <strong>GST Calculator</strong> makes the process effortless. This tool is designed to handle both common scenarios: adding GST to a net price (GST-exclusive) and extracting the GST amount from a gross price (GST-inclusive).</p>
            <p>Forget manual formulas and the risk of errors. With a customizable tax rate, our calculator is adaptable for use in various regions, including Canada, Australia, India, and New Zealand. Get a clear, instant breakdown of the base amount, tax, and total, ensuring your financial records are always accurate.</p>
            
            <h3>How to Use the GST Calculator</h3>
            <ol>
                <li><strong>Enter the Amount:</strong> Input the price, either before or after tax.</li>
                <li><strong>Set the GST Rate:</strong> Enter the applicable GST percentage for your region.</li>
                <li><strong>Choose Calculation Type:</strong> Select "Add GST" if your amount is pre-tax, or "Remove GST" if it already includes tax.</li>
                <li><strong>Calculate:</strong> Click the button to get an instant, detailed breakdown of your amounts.</li>
            </ol>
        
            <h3>Frequently Asked Questions About GST Calculator</h3>
            
            <h4>How do you calculate GST?</h4>
            <p>To calculate GST, you can either add it to a base price or remove it from a total price. To add GST, multiply the base amount by the GST rate (e.g., $100 * 5% = $5). To remove GST from a total, divide the total amount by (1 + the GST rate) (e.g., $105 / 1.05 = $100). Our calculator does both automatically.</p>
            
            <h4>How to calculate GST from total amount in Canada?</h4>
            <p>In Canada, the federal GST is 5%. To find the original amount before tax from a total price, divide the total by 1.05. For example, if a product costs $126 including GST, the pre-tax price is $126 / 1.05 = $120. The GST amount is $6. Our calculator's 'Remove GST' option simplifies this process.</p>
            
            <h4>How much is GST for $100?</h4>
            <p>The GST on $100 depends on the rate. If the GST rate is 5% (like in Canada), the GST would be $5. If the rate is 10% (like in Australia), the GST would be $10. You can enter any rate into our calculator to find the exact amount.</p>
            
            <h4>What is the rate of GST?</h4>
            <p>The GST rate varies by country. For example, it's 5% in Canada (federal), 10% in Australia, and has multiple slabs (5%, 12%, 18%, 28%) in India. Some Canadian provinces combine it into an HST (Harmonized Sales Tax) with a higher rate. Our calculator allows you to input any rate to match your specific needs.</p>
            
            <h4>What is the GST calculator?</h4>
            <p>A GST calculator is a simple online tool that helps you compute the Goods and Services Tax amount for a transaction. It can either add the tax to a net price (GST-exclusive) or extract the tax amount from a gross price (GST-inclusive), making it easy for businesses and consumers to manage tax calculations.</p>
        </div>
        
        <div class="gst-calculator-features">
            <h3 class="gst-calculator-features-title">Key Features:</h3>
            <ul class="gst-calculator-features-list">
                <li class="gst-calculator-features-item">Add GST to net amounts</li>
                <li class="gst-calculator-features-item">Remove GST from gross amounts</li>
                <li class="gst-calculator-features-item">Customizable tax rates</li>
                <li class="gst-calculator-features-item">Handles inclusive pricing</li>
                <li class="gst-calculator-features-item">Handles exclusive pricing</li>
                <li class="gst-calculator-features-item">Clear results breakdown</li>
                <li class="gst-calculator-features-item">Mobile-friendly interface</li>
                <li class="gst-calculator-features-item">Free and easy to use</li>
            </ul>
        </div>
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                amount: () => document.getElementById('gstAmountInput'),
                rate: () => document.getElementById('gstRateInput'),
                calcType: () => document.querySelector('input[name="gst_calculation_type"]:checked'),
                output: () => document.getElementById('gstCalculatorOutput')
            };
            
            const formatCurrency = (num) => {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 2
                }).format(num);
            };

            window.GSTCalculator = {
                calculate() {
                    const outputEl = elements.output();
                    const amount = parseFloat(elements.amount().value);
                    const rate = parseFloat(elements.rate().value);
                    const calcType = elements.calcType().value;

                    if (isNaN(amount) || amount < 0 || isNaN(rate) || rate < 0) {
                        outputEl.innerHTML = '<span style="color: #dc2626;">Please enter valid positive numbers for amount and rate.</span>';
                        return;
                    }

                    let baseAmount, gstAmount, totalAmount;
                    const rateDecimal = rate / 100;

                    if (calcType === 'add') { // GST Exclusive
                        baseAmount = amount;
                        gstAmount = baseAmount * rateDecimal;
                        totalAmount = baseAmount + gstAmount;
                    } else { // GST Inclusive
                        totalAmount = amount;
                        baseAmount = totalAmount / (1 + rateDecimal);
                        gstAmount = totalAmount - baseAmount;
                    }
                    
                    this.displayResults(baseAmount, gstAmount, totalAmount);
                },

                displayResults(base, gst, total) {
                    const outputEl = elements.output();
                    outputEl.style.color = '';
                    outputEl.innerHTML = `
                        <div class="gst-calculator-output-item">
                            <span class="gst-calculator-output-label">Base Amount:</span>
                            <span class="gst-calculator-output-value">${formatCurrency(base)}</span>
                        </div>
                        <div class="gst-calculator-output-item">
                            <span class="gst-calculator-output-label">GST Amount:</span>
                            <span class="gst-calculator-output-value">${formatCurrency(gst)}</span>
                        </div>
                        <div class="gst-calculator-output-item">
                            <span class="gst-calculator-output-label">Total Amount:</span>
                            <span class="gst-calculator-output-value total">${formatCurrency(total)}</span>
                        </div>
                    `;
                },

                clear() {
                    elements.amount().value = '';
                    elements.rate().value = '';
                    elements.output().innerHTML = 'Your GST calculation will appear here...';
                    elements.output().style.color = '';
                    document.getElementById('addGst').checked = true;
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Handle Enter key on input fields to trigger calculation
                const inputs = [elements.amount(), elements.rate()];
                inputs.forEach(input => {
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            GSTCalculator.calculate();
                        }
                    });
                });
            });
        })();
    </script>
</body>
</html>