<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Length Converter Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Length Converter - Convert Metric to Imperial Units",
        "description": "Convert between meters, feet, inches, kilometers, miles, and other length units instantly. Free online tool with real-time conversion and one-click copying.",
        "url": "https://www.webtoolskit.org/p/length-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Length Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Length Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert metric to imperial inches?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert metric to imperial inches, use these formulas: millimeters ÷ 25.4 = inches, centimeters ÷ 2.54 = inches, or meters × 39.37 = inches. For example, 100mm ÷ 25.4 = 3.94 inches."
          }
        },
        {
          "@type": "Question",
          "name": "What is the formula for converting mm to inches?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The formula for converting millimeters to inches is: inches = millimeters ÷ 25.4. This is because 1 inch equals exactly 25.4 millimeters. Simply divide your millimeter measurement by 25.4 to get the equivalent in inches."
          }
        },
        {
          "@type": "Question",
          "name": "How many mm makes 1 inch?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "1 inch equals exactly 25.4 millimeters. This is the internationally accepted conversion factor. So if you have 25.4mm, that equals 1 inch, or 50.8mm equals 2 inches, and so on."
          }
        },
        {
          "@type": "Question",
          "name": "What is the conversion table of length?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Common length conversions: 1 meter = 100 cm = 1000 mm = 3.28 feet = 39.37 inches. 1 foot = 12 inches = 30.48 cm. 1 kilometer = 1000 meters = 0.621 miles. 1 mile = 5280 feet = 1.609 kilometers. 1 yard = 3 feet = 36 inches = 0.914 meters."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert between metric units of length?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Converting between metric units is simple using powers of 10: multiply by 10 to go from larger to smaller units (meters to centimeters), or divide by 10 to go from smaller to larger units. For example: 1 meter = 10 decimeters = 100 centimeters = 1000 millimeters."
          }
        }
      ]
    }
    </script>

    <style>
        /* Length Converter Widget - Simplified & Template Compatible */
        .length-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .length-converter-widget-container * { box-sizing: border-box; }

        .length-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .length-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .length-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .length-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .length-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .length-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .length-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .length-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .length-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .length-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .length-converter-btn:hover { transform: translateY(-2px); }

        .length-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .length-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .length-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .length-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .length-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .length-converter-btn-success:hover {
            background-color: #059669;
        }

        .length-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .length-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .length-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .length-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .length-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .length-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .length-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .length-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .length-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .length-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .length-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="area-converter"] .length-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="volume-converter"] .length-converter-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="weight-converter"] .length-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }

        .length-converter-related-tool-item:hover .length-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="area-converter"]:hover .length-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        a[href*="volume-converter"]:hover .length-converter-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="weight-converter"]:hover .length-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        
        .length-converter-related-tool-item { box-shadow: none; border: none; }
        .length-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .length-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .length-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .length-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .length-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .length-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .length-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .length-converter-related-tool-item:hover .length-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .length-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .length-converter-widget-title { font-size: 1.875rem; }
            .length-converter-buttons { flex-direction: column; }
            .length-converter-btn { flex: none; }
            .length-converter-input-group { grid-template-columns: 1fr; }
            .length-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .length-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .length-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .length-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .length-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .length-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .length-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .length-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .length-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .length-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .length-converter-output::selection { background-color: var(--primary-color); color: white; }
        .length-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .length-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="length-converter-widget-container">
        <h1 class="length-converter-widget-title">Length Converter</h1>
        <p class="length-converter-widget-description">
            Convert between metric and imperial length units instantly. Perfect for measurements, engineering, and everyday calculations.
        </p>
        
        <div class="length-converter-input-group">
            <label for="lengthFromInput" class="length-converter-label">From:</label>
            <input 
                type="number" 
                id="lengthFromInput" 
                class="length-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="lengthFromUnit" class="length-converter-select">
                <option value="mm">Millimeters (mm)</option>
                <option value="cm">Centimeters (cm)</option>
                <option value="m" selected>Meters (m)</option>
                <option value="km">Kilometers (km)</option>
                <option value="in">Inches (in)</option>
                <option value="ft">Feet (ft)</option>
                <option value="yd">Yards (yd)</option>
                <option value="mi">Miles (mi)</option>
            </select>
        </div>

        <div class="length-converter-input-group">
            <label for="lengthToInput" class="length-converter-label">To:</label>
            <input 
                type="number" 
                id="lengthToInput" 
                class="length-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="lengthToUnit" class="length-converter-select">
                <option value="mm">Millimeters (mm)</option>
                <option value="cm">Centimeters (cm)</option>
                <option value="m">Meters (m)</option>
                <option value="km">Kilometers (km)</option>
                <option value="in" selected>Inches (in)</option>
                <option value="ft">Feet (ft)</option>
                <option value="yd">Yards (yd)</option>
                <option value="mi">Miles (mi)</option>
            </select>
        </div>

        <div class="length-converter-buttons">
            <button class="length-converter-btn length-converter-btn-primary" onclick="LengthConverter.convert()">
                Convert Length
            </button>
            <button class="length-converter-btn length-converter-btn-secondary" onclick="LengthConverter.clear()">
                Clear All
            </button>
            <button class="length-converter-btn length-converter-btn-success" onclick="LengthConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="length-converter-result">
            <h3 class="length-converter-result-title">Conversion Result:</h3>
            <div class="length-converter-output" id="lengthConverterOutput">
                Your converted length will appear here...
            </div>
        </div>

        <div class="length-converter-related-tools">
            <h3 class="length-converter-related-tools-title">Related Tools</h3>
            <div class="length-converter-related-tools-grid">
                <a href="/p/area-converter.html" class="length-converter-related-tool-item" rel="noopener">
                    <div class="length-converter-related-tool-icon">
                        <i class="fas fa-vector-square"></i>
                    </div>
                    <div class="length-converter-related-tool-name">Area Converter</div>
                </a>

                <a href="/p/volume-converter.html" class="length-converter-related-tool-item" rel="noopener">
                    <div class="length-converter-related-tool-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="length-converter-related-tool-name">Volume Converter</div>
                </a>

                <a href="/p/weight-converter.html" class="length-converter-related-tool-item" rel="noopener">
                    <div class="length-converter-related-tool-icon">
                        <i class="fas fa-weight"></i>
                    </div>
                    <div class="length-converter-related-tool-name">Weight Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert Length Units Instantly with Our Free Tool</h2>
            <p>Whether you're working on a construction project, following a recipe, or studying for an exam, converting between different length units is a common task. Our <strong>Length Converter</strong> makes it simple to switch between metric and imperial measurements instantly. From millimeters to miles, centimeters to feet, or meters to inches, this tool handles all your length conversion needs with precision and ease.</p>
            <p>The converter supports the most commonly used length units including millimeters, centimeters, meters, kilometers, inches, feet, yards, and miles. Simply enter your measurement, select the units you're converting from and to, then click convert to get your accurate result immediately.</p>

            <h3>How to Use the Length Converter</h3>
            <ol>
                <li><strong>Enter Your Value:</strong> Type the number you want to convert in the "From" input field.</li>
                <li><strong>Select Units:</strong> Choose your starting unit from the first dropdown and your target unit from the second dropdown.</li>
                <li><strong>Convert:</strong> Click the "Convert Length" button to see your result instantly displayed below.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to quickly copy the converted value to your clipboard.</li>
            </ol>

            <h3>Frequently Asked Questions About Length Conversion</h3>

            <h4>How do you convert metric to imperial inches?</h4>
            <p>To convert metric to imperial inches, use these formulas: millimeters ÷ 25.4 = inches, centimeters ÷ 2.54 = inches, or meters × 39.37 = inches. For example, 100mm ÷ 25.4 = 3.94 inches.</p>

            <h4>What is the formula for converting mm to inches?</h4>
            <p>The formula for converting millimeters to inches is: inches = millimeters ÷ 25.4. This is because 1 inch equals exactly 25.4 millimeters. Simply divide your millimeter measurement by 25.4 to get the equivalent in inches.</p>

            <h4>How many mm makes 1 inch?</h4>
            <p>1 inch equals exactly 25.4 millimeters. This is the internationally accepted conversion factor. So if you have 25.4mm, that equals 1 inch, or 50.8mm equals 2 inches, and so on.</p>

            <h4>What is the conversion table of length?</h4>
            <p>Common length conversions: 1 meter = 100 cm = 1000 mm = 3.28 feet = 39.37 inches. 1 foot = 12 inches = 30.48 cm. 1 kilometer = 1000 meters = 0.621 miles. 1 mile = 5280 feet = 1.609 kilometers. 1 yard = 3 feet = 36 inches = 0.914 meters.</p>

            <h4>How to convert between metric units of length?</h4>
            <p>Converting between metric units is simple using powers of 10: multiply by 10 to go from larger to smaller units (meters to centimeters), or divide by 10 to go from smaller to larger units. For example: 1 meter = 10 decimeters = 100 centimeters = 1000 millimeters.</p>
        </div>

        <div class="length-converter-features">
            <h3 class="length-converter-features-title">Key Features:</h3>
            <ul class="length-converter-features-list">
                <li class="length-converter-features-item" style="margin-bottom: 0.3em;">Instant metric to imperial conversion</li>
                <li class="length-converter-features-item" style="margin-bottom: 0.3em;">Support for 8 common length units</li>
                <li class="length-converter-features-item" style="margin-bottom: 0.3em;">High precision calculations</li>
                <li class="length-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="length-converter-features-item" style="margin-bottom: 0.3em;">Real-time conversion results</li>
                <li class="length-converter-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="length-converter-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="length-converter-notification" id="lengthConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Length Converter
        (function() {
            'use strict';

            // Conversion factors to meters
            const conversionFactors = {
                'mm': 0.001,
                'cm': 0.01,
                'm': 1,
                'km': 1000,
                'in': 0.0254,
                'ft': 0.3048,
                'yd': 0.9144,
                'mi': 1609.344
            };

            const elements = {
                fromInput: () => document.getElementById('lengthFromInput'),
                toInput: () => document.getElementById('lengthToInput'),
                fromUnit: () => document.getElementById('lengthFromUnit'),
                toUnit: () => document.getElementById('lengthToUnit'),
                output: () => document.getElementById('lengthConverterOutput'),
                notification: () => document.getElementById('lengthConverterNotification')
            };

            window.LengthConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to meters first, then to target unit
                    const valueInMeters = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInMeters / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (Math.abs(value) >= 1000000) {
                        return value.toExponential(6);
                    } else if (Math.abs(value) < 0.000001 && value !== 0) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toFixed(10)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = {
                        'mm': 'millimeters',
                        'cm': 'centimeters',
                        'm': 'meters',
                        'km': 'kilometers',
                        'in': 'inches',
                        'ft': 'feet',
                        'yd': 'yards',
                        'mi': 'miles'
                    };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted length will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        LengthConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>
