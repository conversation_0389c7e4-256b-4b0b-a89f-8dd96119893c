<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICO to PNG Converter - Free Online Icon to PNG Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free ICO to PNG Converter - Convert Icon Files to PNG Format",
        "description": "Convert ICO icon files to PNG format instantly. Free online tool with high-quality output, transparency support, and batch conversion capabilities.",
        "url": "https://www.webtoolskit.org/p/ico-to-png.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-21",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "ICO to PNG Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert ICO to PNG" },
            { "@type": "DownloadAction", "name": "Download PNG File" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert ICO image to PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Converting ICO to PNG is simple with our online tool. Upload your ICO file, and the converter automatically extracts the highest quality image from the icon file and converts it to PNG format. You can then download the converted PNG file instantly."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between ICO and PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "ICO files are container formats that can hold multiple images at different sizes (16x16, 32x32, 48x48, etc.) for icons, while PNG is a single-image format with better compression. PNG supports transparency like ICO but offers superior image quality and smaller file sizes for web use."
          }
        },
        {
          "@type": "Question",
          "name": "Can I use PNG instead of ICO for favicon?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, modern browsers support PNG favicons. While ICO is still the standard for maximum compatibility across all browsers and operating systems, PNG favicons work well for most modern web applications and offer better image quality."
          }
        },
        {
          "@type": "Question",
          "name": "Is an ICO file just a PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, ICO files are container formats that can contain multiple image formats including PNG, BMP, or GIF at various sizes. A single ICO file typically contains several versions of the same icon at different resolutions, while PNG is just one image at one resolution."
          }
        },
        {
          "@type": "Question",
          "name": "How do I make an ICO file from a PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert PNG to ICO, you need an ICO converter tool that can create the multi-size icon container. Upload your PNG image, select the desired icon sizes (16x16, 32x32, 48x48), and the tool will generate an ICO file containing all size variants."
          }
        }
      ]
    }
    </script>

    <style>
        /* ICO to PNG Converter Widget - Simplified & Template Compatible */
        .ico-png-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .ico-png-widget-container * { box-sizing: border-box; }

        .ico-png-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .ico-png-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .ico-png-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-xl);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
            position: relative;
            min-height: 160px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .ico-png-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .ico-png-upload-icon {
            font-size: 3rem;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-md);
        }

        .ico-png-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .ico-png-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.95rem;
        }

        .ico-png-file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .ico-png-preview {
            display: none;
            text-align: center;
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .ico-png-preview.show { display: block; }

        .ico-png-preview-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.125rem;
            font-weight: 600;
        }

        .ico-png-preview-image {
            max-width: 128px;
            max-height: 128px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            padding: var(--spacing-sm);
            margin: 0 auto var(--spacing-md);
            display: block;
        }

        .ico-png-preview-info {
            color: var(--text-color-light);
            font-size: 0.9rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .ico-png-info-item {
            background: var(--card-bg);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .ico-png-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .ico-png-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .ico-png-btn:hover { transform: translateY(-2px); }

        .ico-png-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .ico-png-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .ico-png-btn-primary:hover:not(:disabled) {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .ico-png-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .ico-png-btn-secondary:hover:not(:disabled) {
            background-color: var(--border-color);
        }

        .ico-png-btn-success {
            background-color: #10b981;
            color: white;
        }

        .ico-png-btn-success:hover:not(:disabled) {
            background-color: #059669;
        }

        .ico-png-progress {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            border: 1px solid var(--border-color);
        }

        .ico-png-progress.show { display: block; }

        .ico-png-progress-bar {
            width: 100%;
            height: 8px;
            background-color: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }

        .ico-png-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .ico-png-progress-text {
            text-align: center;
            color: var(--text-color);
            font-weight: 600;
        }

        .ico-png-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .ico-png-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .ico-png-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .ico-png-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .ico-png-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .ico-png-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .ico-png-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .ico-png-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="ico-converter"] .ico-png-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .ico-png-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="png-to-ico"] .ico-png-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        a[href*="ico-converter"]:hover .ico-png-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .ico-png-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="png-to-ico"]:hover .ico-png-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .ico-png-related-tool-item:hover .ico-png-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .ico-png-related-tool-item { box-shadow: none; border: none; }
        .ico-png-related-tool-item:hover { box-shadow: none; border: none; }
        .ico-png-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .ico-png-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .ico-png-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .ico-png-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .ico-png-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .ico-png-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .ico-png-related-tool-item:hover .ico-png-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .ico-png-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .ico-png-widget-title { font-size: 1.875rem; }
            .ico-png-buttons { flex-direction: column; }
            .ico-png-btn { flex: none; }
            .ico-png-upload-area { min-height: 140px; padding: var(--spacing-lg); }
            .ico-png-upload-icon { font-size: 2.5rem; }
            .ico-png-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .ico-png-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .ico-png-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .ico-png-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .ico-png-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .ico-png-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .ico-png-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .ico-png-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .ico-png-upload-area.dragover { background-color: rgba(96, 165, 250, 0.1); }
        .ico-png-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .ico-png-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .ico-png-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="ico-png-widget-container">
        <h1 class="ico-png-widget-title">ICO to PNG Converter</h1>
        <p class="ico-png-widget-description">
            Convert ICO icon files to PNG format with transparency support and high-quality output. Free, fast, and works in your browser.
        </p>
        
        <div class="ico-png-upload-area" id="icoUploadArea">
            <input type="file" class="ico-png-file-input" id="icoFileInput" accept=".ico" multiple>
            <div class="ico-png-upload-icon">📁</div>
            <div class="ico-png-upload-text">Drop ICO files here or click to browse</div>
            <div class="ico-png-upload-subtext">Supports single and batch conversion</div>
        </div>

        <div class="ico-png-preview" id="icoPreview">
            <div class="ico-png-preview-title">Preview:</div>
            <img class="ico-png-preview-image" id="previewImage" alt="Preview">
            <div class="ico-png-preview-info" id="previewInfo"></div>
        </div>

        <div class="ico-png-progress" id="conversionProgress">
            <div class="ico-png-progress-bar">
                <div class="ico-png-progress-fill" id="progressFill"></div>
            </div>
            <div class="ico-png-progress-text" id="progressText">Processing...</div>
        </div>

        <div class="ico-png-buttons">
            <button class="ico-png-btn ico-png-btn-primary" id="convertBtn" onclick="IcoPngConverter.convert()" disabled>
                Convert to PNG
            </button>
            <button class="ico-png-btn ico-png-btn-secondary" onclick="IcoPngConverter.clear()">
                Clear All
            </button>
            <button class="ico-png-btn ico-png-btn-success" id="downloadBtn" onclick="IcoPngConverter.downloadAll()" disabled>
                Download All
            </button>
        </div>

        <div class="ico-png-related-tools">
            <h3 class="ico-png-related-tools-title">Related Tools</h3>
            <div class="ico-png-related-tools-grid">
                <a href="/p/ico-converter.html" class="ico-png-related-tool-item" rel="noopener">
                    <div class="ico-png-related-tool-icon">
                        <i class="fas fa-icons"></i>
                    </div>
                    <div class="ico-png-related-tool-name">ICO Converter</div>
                </a>

                <a href="/p/image-converter.html" class="ico-png-related-tool-item" rel="noopener">
                    <div class="ico-png-related-tool-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="ico-png-related-tool-name">Image Converter</div>
                </a>

                <a href="/p/png-to-ico.html" class="ico-png-related-tool-item" rel="noopener">
                    <div class="ico-png-related-tool-icon">
                        <i class="fas fa-icons"></i>
                    </div>
                    <div class="ico-png-related-tool-name">PNG to ICO</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert ICO Icons to PNG Format Instantly</h2>
            <p>ICO files are Microsoft's icon format commonly used for favicons and application icons. While ICO files are perfect for Windows compatibility, PNG format offers better compression, superior image quality, and broader web support. Our <strong>ICO to PNG converter</strong> extracts the highest quality image from your ICO file and converts it to PNG format while preserving transparency and image details.</p>
            <p>This tool is particularly useful for web developers who need to convert website favicons to PNG format for better performance, or designers working with icon assets who need the flexibility of PNG format for further editing and optimization.</p>
            
            <h3>How to Convert ICO to PNG</h3>
            <ol>
                <li><strong>Upload ICO Files:</strong> Drag and drop your ICO files into the upload area or click to browse and select files from your computer.</li>
                <li><strong>Preview & Verify:</strong> The tool will show a preview of your ICO file and display its properties including dimensions and file size.</li>
                <li><strong>Convert & Download:</strong> Click "Convert to PNG" to process your files. Once conversion is complete, download the PNG files individually or use "Download All" for batch conversion.</li>
            </ol>
        
            <h3>Frequently Asked Questions About ICO to PNG</h3>
            
            <h4>How to convert ICO image to PNG?</h4>
            <p>Converting ICO to PNG is simple with our online tool. Upload your ICO file, and the converter automatically extracts the highest quality image from the icon file and converts it to PNG format. You can then download the converted PNG file instantly.</p>
            
            <h4>What is the difference between ICO and PNG?</h4>
            <p>ICO files are container formats that can hold multiple images at different sizes (16x16, 32x32, 48x48, etc.) for icons, while PNG is a single-image format with better compression. PNG supports transparency like ICO but offers superior image quality and smaller file sizes for web use.</p>
            
            <h4>Can I use PNG instead of ICO for favicon?</h4>
            <p>Yes, modern browsers support PNG favicons. While ICO is still the standard for maximum compatibility across all browsers and operating systems, PNG favicons work well for most modern web applications and offer better image quality.</p>
            
            <h4>Is an ICO file just a PNG?</h4>
            <p>No, ICO files are container formats that can contain multiple image formats including PNG, BMP, or GIF at various sizes. A single ICO file typically contains several versions of the same icon at different resolutions, while PNG is just one image at one resolution.</p>
            
            <h4>How do I make an ICO file from a PNG?</h4>
            <p>To convert PNG to ICO, you need an ICO converter tool that can create the multi-size icon container. Upload your PNG image, select the desired icon sizes (16x16, 32x32, 48x48), and the tool will generate an ICO file containing all size variants.</p>
        </div>

        <div class="ico-png-features">
            <h3 class="ico-png-features-title">Key Features:</h3>
            <ul class="ico-png-features-list">
                <li class="ico-png-features-item" style="margin-bottom: 0.3em;">High-quality ICO to PNG conversion</li>
                <li class="ico-png-features-item" style="margin-bottom: 0.3em;">Transparency preservation</li>
                <li class="ico-png-features-item" style="margin-bottom: 0.3em;">Batch conversion support</li>
                <li class="ico-png-features-item" style="margin-bottom: 0.3em;">Drag & drop interface</li>
                <li class="ico-png-features-item" style="margin-bottom: 0.3em;">Instant preview</li>
                <li class="ico-png-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="ico-png-features-item">No server upload required</li>
            </ul>
        </div>
    </div>

    <!-- Notification -->
    <div class="ico-png-notification" id="notification">
        ✓ Conversion completed!
    </div>

    <script>
        // ICO to PNG Converter - Self-contained IIFE
        (function() {
            'use strict';

            let convertedFiles = [];

            const elements = {
                uploadArea: () => document.getElementById('icoUploadArea'),
                fileInput: () => document.getElementById('icoFileInput'),
                preview: () => document.getElementById('icoPreview'),
                previewImage: () => document.getElementById('previewImage'),
                previewInfo: () => document.getElementById('previewInfo'),
                progress: () => document.getElementById('conversionProgress'),
                progressFill: () => document.getElementById('progressFill'),
                progressText: () => document.getElementById('progressText'),
                convertBtn: () => document.getElementById('convertBtn'),
                downloadBtn: () => document.getElementById('downloadBtn'),
                notification: () => document.getElementById('notification')
            };

            window.IcoPngConverter = {
                async convert() {
                    const files = elements.fileInput().files;
                    if (!files.length) return;

                    this.showProgress();
                    convertedFiles = [];

                    for (let i = 0; i < files.length; i++) {
                        try {
                            const file = files[i];
                            this.updateProgress((i / files.length) * 100, `Converting ${file.name}...`);
                            
                            const pngBlob = await this.convertIcoToPng(file);
                            convertedFiles.push({
                                name: file.name.replace('.ico', '.png'),
                                blob: pngBlob
                            });
                        } catch (error) {
                            console.error('Conversion error:', error);
                        }
                    }

                    this.updateProgress(100, 'Conversion complete!');
                    setTimeout(() => {
                        this.hideProgress();
                        elements.downloadBtn().disabled = false;
                        this.showNotification();
                    }, 500);
                },

                async convertIcoToPng(file) {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const img = new Image();
                            img.onload = () => {
                                const canvas = document.createElement('canvas');
                                const ctx = canvas.getContext('2d');
                                
                                canvas.width = img.width;
                                canvas.height = img.height;
                                ctx.drawImage(img, 0, 0);
                                
                                canvas.toBlob(resolve, 'image/png');
                            };
                            img.onerror = reject;
                            img.src = e.target.result;
                        };
                        reader.onerror = reject;
                        reader.readAsDataURL(file);
                    });
                },

                downloadAll() {
                    convertedFiles.forEach(file => {
                        const url = URL.createObjectURL(file.blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = file.name;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                    });
                },

                clear() {
                    elements.fileInput().value = '';
                    elements.preview().classList.remove('show');
                    elements.convertBtn().disabled = true;
                    elements.downloadBtn().disabled = true;
                    convertedFiles = [];
                    this.hideProgress();
                },

                showProgress() {
                    elements.progress().classList.add('show');
                },

                hideProgress() {
                    elements.progress().classList.remove('show');
                },

                updateProgress(percent, text) {
                    elements.progressFill().style.width = percent + '%';
                    elements.progressText().textContent = text;
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 3000);
                },

                showPreview(file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        elements.previewImage().src = e.target.result;
                        elements.previewInfo().innerHTML = `
                            <div class="ico-png-info-item"><strong>Name:</strong> ${file.name}</div>
                            <div class="ico-png-info-item"><strong>Size:</strong> ${(file.size / 1024).toFixed(1)} KB</div>
                            <div class="ico-png-info-item"><strong>Type:</strong> ${file.type || 'ICO'}</div>
                        `;
                        elements.preview().classList.add('show');
                    };
                    reader.readAsDataURL(file);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // File input change
                fileInput.addEventListener('change', function() {
                    if (this.files.length > 0) {
                        elements.convertBtn().disabled = false;
                        IcoPngConverter.showPreview(this.files[0]);
                    }
                });

                // Drag and drop
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    
                    const files = Array.from(e.dataTransfer.files).filter(file => 
                        file.name.toLowerCase().endsWith('.ico')
                    );
                    
                    if (files.length > 0) {
                        const dt = new DataTransfer();
                        files.forEach(file => dt.items.add(file));
                        fileInput.files = dt.files;
                        
                        elements.convertBtn().disabled = false;
                        IcoPngConverter.showPreview(files[0]);
                    }
                });

                // Click to upload
                uploadArea.addEventListener('click', () => fileInput.click());
            });
        })();
    </script>
</body>
</html>