<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free YouTube Thumbnail Downloader - Download HD Thumbnails Online</title>
    <meta name="description" content="Download YouTube video thumbnails in high quality instantly with our free YouTube Thumbnail Downloader. Get HD, SD, and custom thumbnail sizes for any YouTube video.">
    <meta name="keywords" content="youtube thumbnail downloader, youtube thumbnail, download youtube thumbnail, youtube image downloader, video thumbnail extractor">
    <link rel="canonical" href="https://www.webtoolskit.org/p/youtube-thumbnail-downloader.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free YouTube Thumbnail Downloader - Download HD Thumbnails Online",
        "description": "Download YouTube video thumbnails in high quality instantly with our free YouTube Thumbnail Downloader. Get HD, SD, and custom thumbnail sizes for any YouTube video.",
        "url": "https://www.webtoolskit.org/p/youtube-thumbnail-downloader.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "YouTube Thumbnail Downloader",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "YouTube thumbnail extraction",
                "High quality image download",
                "Multiple thumbnail sizes",
                "Video thumbnail preview",
                "Instant thumbnail access"
            ]
        },
        "potentialAction": [
            { "@type": "DownloadAction", "name": "Download YouTube Thumbnail" },
            { "@type": "ViewAction", "name": "Preview Thumbnail" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I download YouTube thumbnails in high quality?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Paste the YouTube video URL into the input field above and click 'Get Thumbnails'. Our tool will display all available thumbnail sizes including HD quality. Click the download button next to your preferred size to save the thumbnail image."
          }
        },
        {
          "@type": "Question",
          "name": "Can I get YouTube thumbnails without downloading the video?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, our tool extracts only the thumbnail images from YouTube videos without downloading the actual video content. You just need the video URL to access and download the thumbnail in various sizes."
          }
        },
        {
          "@type": "Question",
          "name": "What sizes are available for YouTube thumbnails?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "YouTube provides thumbnails in multiple sizes: Default (120x90), Medium (320x180), High (480x360), Standard (640x480), Max Resolution (1280x720), and sometimes HD (1920x1080) for newer videos."
          }
        },
        {
          "@type": "Question",
          "name": "Is it legal to download YouTube thumbnails?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "YouTube thumbnails are publicly accessible images. However, respect copyright and fair use guidelines. Use thumbnails for educational, commentary, or personal purposes. For commercial use, consider seeking permission from the content creator."
          }
        },
        {
          "@type": "Question",
          "name": "How do I extract thumbnail images from YouTube videos?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Simply copy the YouTube video URL and paste it into our tool. We'll automatically extract and display all available thumbnail sizes. You can then preview and download the thumbnail size that best fits your needs."
          }
        }
      ]
    }
    </script>

    <style>
        /* YouTube Thumbnail Downloader Widget - Simplified & Template Compatible */
        .youtube-thumbnail-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .youtube-thumbnail-widget-container * { box-sizing: border-box; }

        .youtube-thumbnail-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .youtube-thumbnail-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .youtube-thumbnail-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .youtube-thumbnail-field {
            display: flex;
            flex-direction: column;
        }

        .youtube-thumbnail-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .youtube-thumbnail-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .youtube-thumbnail-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .youtube-thumbnail-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .youtube-thumbnail-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .youtube-thumbnail-btn:hover { transform: translateY(-2px); }

        .youtube-thumbnail-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .youtube-thumbnail-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .youtube-thumbnail-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .youtube-thumbnail-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .youtube-thumbnail-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .youtube-thumbnail-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .youtube-thumbnail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }

        .youtube-thumbnail-item {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            text-align: center;
        }

        .youtube-thumbnail-preview {
            width: 100%;
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-md);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .youtube-thumbnail-info {
            margin-bottom: var(--spacing-md);
        }

        .youtube-thumbnail-size {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-xs);
        }

        .youtube-thumbnail-dimensions {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .youtube-thumbnail-download-btn {
            background-color: #dc2626;
            color: white;
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--border-radius-md);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            width: 100%;
        }

        .youtube-thumbnail-download-btn:hover {
            background-color: #b91c1c;
            transform: translateY(-1px);
        }

        .youtube-thumbnail-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .youtube-thumbnail-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        @media (max-width: 768px) {
            .youtube-thumbnail-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .youtube-thumbnail-widget-title { font-size: 1.875rem; }
            .youtube-thumbnail-buttons { flex-direction: column; }
            .youtube-thumbnail-btn { flex: none; }
            .youtube-thumbnail-grid { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .youtube-thumbnail-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .youtube-thumbnail-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }

        .youtube-thumbnail-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="vtt-to-srt"] .youtube-thumbnail-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="srt-to-vtt"] .youtube-thumbnail-related-tool-icon { background: linear-gradient(145deg, #3B82F6, #2563EB); }
        a[href*="password-generator"] .youtube-thumbnail-related-tool-icon { background: linear-gradient(145deg, #0EA5E9, #0284C7); }

        .youtube-thumbnail-related-tool-item:hover .youtube-thumbnail-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="vtt-to-srt"]:hover .youtube-thumbnail-related-tool-icon { background: linear-gradient(145deg, #9373f7, #8a4ff0); }
        a[href*="srt-to-vtt"]:hover .youtube-thumbnail-related-tool-icon { background: linear-gradient(145deg, #60a5fa, #3b82f6); }
        a[href*="password-generator"]:hover .youtube-thumbnail-related-tool-icon { background: linear-gradient(145deg, #38bdf8, #0ea5e9); }

        .youtube-thumbnail-related-tool-item { box-shadow: none; border: none; }
        .youtube-thumbnail-related-tool-item:hover { box-shadow: none; border: none; }
        .youtube-thumbnail-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .youtube-thumbnail-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .youtube-thumbnail-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .youtube-thumbnail-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .youtube-thumbnail-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .youtube-thumbnail-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .youtube-thumbnail-related-tool-item:hover .youtube-thumbnail-related-tool-name { color: var(--primary-color); }

        .youtube-thumbnail-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .youtube-thumbnail-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .youtube-thumbnail-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .youtube-thumbnail-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .youtube-thumbnail-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .youtube-thumbnail-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .youtube-thumbnail-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .youtube-thumbnail-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .youtube-thumbnail-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .youtube-thumbnail-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .youtube-thumbnail-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .youtube-thumbnail-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .youtube-thumbnail-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .youtube-thumbnail-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="youtube-thumbnail-widget-container">
        <h1 class="youtube-thumbnail-widget-title">YouTube Thumbnail Downloader</h1>
        <p class="youtube-thumbnail-widget-description">
            Download YouTube video thumbnails in high quality. Get HD, SD, and custom thumbnail sizes for any YouTube video instantly.
        </p>

        <form class="youtube-thumbnail-form">
            <div class="youtube-thumbnail-field">
                <label for="youtubeUrl" class="youtube-thumbnail-label">YouTube Video URL:</label>
                <input
                    type="url"
                    id="youtubeUrl"
                    class="youtube-thumbnail-input"
                    placeholder="https://www.youtube.com/watch?v=VIDEO_ID or https://youtu.be/VIDEO_ID"
                />
            </div>
        </form>

        <div class="youtube-thumbnail-buttons">
            <button class="youtube-thumbnail-btn youtube-thumbnail-btn-primary" onclick="YouTubeThumbnailDownloader.getThumbnails()">
                Get Thumbnails
            </button>
            <button class="youtube-thumbnail-btn youtube-thumbnail-btn-secondary" onclick="YouTubeThumbnailDownloader.clear()">
                Clear Results
            </button>
        </div>

        <div class="youtube-thumbnail-result" id="thumbnailResults" style="display: none;">
            <h3 class="youtube-thumbnail-result-title">Available Thumbnails:</h3>
            <div class="youtube-thumbnail-grid" id="thumbnailGrid"></div>
        </div>

        <div class="youtube-thumbnail-related-tools">
            <h3 class="youtube-thumbnail-related-tools-title">Related Tools</h3>
            <div class="youtube-thumbnail-related-tools-grid">
                <a href="/p/vtt-to-srt.html" class="youtube-thumbnail-related-tool-item" rel="noopener">
                    <div class="youtube-thumbnail-related-tool-icon">
                        <i class="fas fa-file-video"></i>
                    </div>
                    <div class="youtube-thumbnail-related-tool-name">VTT to SRT</div>
                </a>

                <a href="/p/srt-to-vtt.html" class="youtube-thumbnail-related-tool-item" rel="noopener">
                    <div class="youtube-thumbnail-related-tool-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="youtube-thumbnail-related-tool-name">SRT to VTT</div>
                </a>

                <a href="/p/password-generator.html" class="youtube-thumbnail-related-tool-item" rel="noopener">
                    <div class="youtube-thumbnail-related-tool-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="youtube-thumbnail-related-tool-name">Password Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional YouTube Thumbnail Downloader for Content Creation</h2>
            <p>Our <strong>YouTube Thumbnail Downloader</strong> extracts high-quality thumbnail images from any YouTube video instantly. Whether you're a content creator, marketer, or designer, our tool provides access to all available thumbnail sizes including HD quality for your projects and presentations.</p>
            <p>Perfect for content creators, social media managers, and digital marketers who need quick access to YouTube video thumbnails. The tool supports all YouTube URL formats and provides multiple thumbnail resolutions to meet your specific requirements without downloading the entire video.</p>

            <h3>How to Use the YouTube Thumbnail Downloader</h3>
            <ol>
                <li><strong>Copy YouTube URL:</strong> Copy the URL of any YouTube video you want to extract thumbnails from.</li>
                <li><strong>Paste URL:</strong> Paste the YouTube video URL into the input field above.</li>
                <li><strong>Get Thumbnails:</strong> Click "Get Thumbnails" to display all available thumbnail sizes.</li>
                <li><strong>Download:</strong> Click the download button next to your preferred thumbnail size to save the image.</li>
            </ol>

            <h3>Frequently Asked Questions About YouTube Thumbnail Downloads</h3>

            <h4>How do I download YouTube thumbnails in high quality?</h4>
            <p>Paste the YouTube video URL into the input field above and click 'Get Thumbnails'. Our tool will display all available thumbnail sizes including HD quality. Click the download button next to your preferred size to save the thumbnail image.</p>

            <h4>Can I get YouTube thumbnails without downloading the video?</h4>
            <p>Yes, our tool extracts only the thumbnail images from YouTube videos without downloading the actual video content. You just need the video URL to access and download the thumbnail in various sizes.</p>

            <h4>What sizes are available for YouTube thumbnails?</h4>
            <p>YouTube provides thumbnails in multiple sizes: Default (120x90), Medium (320x180), High (480x360), Standard (640x480), Max Resolution (1280x720), and sometimes HD (1920x1080) for newer videos.</p>

            <h4>Is it legal to download YouTube thumbnails?</h4>
            <p>YouTube thumbnails are publicly accessible images. However, respect copyright and fair use guidelines. Use thumbnails for educational, commentary, or personal purposes. For commercial use, consider seeking permission from the content creator.</p>

            <h4>How do I extract thumbnail images from YouTube videos?</h4>
            <p>Simply copy the YouTube video URL and paste it into our tool. We'll automatically extract and display all available thumbnail sizes. You can then preview and download the thumbnail size that best fits your needs.</p>
        </div>

        <div class="youtube-thumbnail-features">
            <h3 class="youtube-thumbnail-features-title">Key Features:</h3>
            <ul class="youtube-thumbnail-features-list">
                <li class="youtube-thumbnail-features-item" style="margin-bottom: 0.3em;">High Quality Thumbnail Extraction</li>
                <li class="youtube-thumbnail-features-item" style="margin-bottom: 0.3em;">Multiple Thumbnail Sizes</li>
                <li class="youtube-thumbnail-features-item" style="margin-bottom: 0.3em;">Instant Preview and Download</li>
                <li class="youtube-thumbnail-features-item" style="margin-bottom: 0.3em;">No Video Download Required</li>
                <li class="youtube-thumbnail-features-item" style="margin-bottom: 0.3em;">All YouTube URL Formats</li>
                <li class="youtube-thumbnail-features-item" style="margin-bottom: 0.3em;">Mobile-Friendly Interface</li>
                <li class="youtube-thumbnail-features-item">100% Free and Fast</li>
            </ul>
        </div>
    </div>

    <!-- Notification -->
    <div class="youtube-thumbnail-notification" id="thumbnailNotification">
        ✓ Thumbnail downloaded!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                urlInput: () => document.getElementById('youtubeUrl'),
                resultsContainer: () => document.getElementById('thumbnailResults'),
                thumbnailGrid: () => document.getElementById('thumbnailGrid'),
                notification: () => document.getElementById('thumbnailNotification')
            };

            function extractVideoId(url) {
                if (!url) return null;

                // Handle various YouTube URL formats
                const patterns = [
                    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([^&\n?#]+)/,
                    /youtube\.com\/watch\?.*v=([^&\n?#]+)/
                ];

                for (const pattern of patterns) {
                    const match = url.match(pattern);
                    if (match && match[1]) {
                        return match[1];
                    }
                }

                return null;
            }

            function getThumbnailSizes(videoId) {
                const baseUrl = `https://img.youtube.com/vi/${videoId}`;

                return [
                    {
                        name: 'Default',
                        dimensions: '120x90',
                        url: `${baseUrl}/default.jpg`,
                        quality: 'Low'
                    },
                    {
                        name: 'Medium',
                        dimensions: '320x180',
                        url: `${baseUrl}/mqdefault.jpg`,
                        quality: 'Medium'
                    },
                    {
                        name: 'High',
                        dimensions: '480x360',
                        url: `${baseUrl}/hqdefault.jpg`,
                        quality: 'High'
                    },
                    {
                        name: 'Standard',
                        dimensions: '640x480',
                        url: `${baseUrl}/sddefault.jpg`,
                        quality: 'Standard'
                    },
                    {
                        name: 'Max Resolution',
                        dimensions: '1280x720',
                        url: `${baseUrl}/maxresdefault.jpg`,
                        quality: 'HD'
                    }
                ];
            }

            function createThumbnailItem(thumbnail, videoId) {
                const item = document.createElement('div');
                item.className = 'youtube-thumbnail-item';

                item.innerHTML = `
                    <img src="${thumbnail.url}" alt="${thumbnail.name} thumbnail" class="youtube-thumbnail-preview"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div style="display: none; padding: 20px; color: #666; font-style: italic;">
                        Thumbnail not available
                    </div>
                    <div class="youtube-thumbnail-info">
                        <div class="youtube-thumbnail-size">${thumbnail.name}</div>
                        <div class="youtube-thumbnail-dimensions">${thumbnail.dimensions} • ${thumbnail.quality}</div>
                    </div>
                    <button class="youtube-thumbnail-download-btn" onclick="YouTubeThumbnailDownloader.downloadThumbnail('${thumbnail.url}', '${videoId}_${thumbnail.name.toLowerCase().replace(' ', '_')}.jpg')">
                        Download ${thumbnail.name}
                    </button>
                `;

                return item;
            }

            window.YouTubeThumbnailDownloader = {
                getThumbnails() {
                    const url = elements.urlInput().value.trim();
                    const resultsContainer = elements.resultsContainer();
                    const thumbnailGrid = elements.thumbnailGrid();

                    if (!url) {
                        alert('Please enter a YouTube video URL');
                        return;
                    }

                    const videoId = extractVideoId(url);
                    if (!videoId) {
                        alert('Invalid YouTube URL. Please enter a valid YouTube video URL.');
                        return;
                    }

                    // Clear previous results
                    thumbnailGrid.innerHTML = '';

                    // Get thumbnail sizes
                    const thumbnails = getThumbnailSizes(videoId);

                    // Create thumbnail items
                    thumbnails.forEach(thumbnail => {
                        const item = createThumbnailItem(thumbnail, videoId);
                        thumbnailGrid.appendChild(item);
                    });

                    // Show results
                    resultsContainer.style.display = 'block';
                    resultsContainer.scrollIntoView({ behavior: 'smooth' });
                },

                downloadThumbnail(url, filename) {
                    // Create a temporary anchor element to trigger download
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.target = '_blank';
                    a.rel = 'noopener noreferrer';

                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);

                    this.showNotification();
                },

                clear() {
                    elements.urlInput().value = '';
                    elements.resultsContainer().style.display = 'none';
                    elements.thumbnailGrid().innerHTML = '';
                },

                showNotification(message = '✓ Thumbnail downloaded!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Enter key shortcut
                elements.urlInput().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        YouTubeThumbnailDownloader.getThumbnails();
                    }
                });

                // Ctrl/Cmd + Enter shortcut
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        YouTubeThumbnailDownloader.getThumbnails();
                    }
                });

                // Sample URL for demonstration (uncomment to test)
                // elements.urlInput().value = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
            });
        })();
    </script>
</body>
</html>
