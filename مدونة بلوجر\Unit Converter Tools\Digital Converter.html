<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Converter - Convert Bytes, KB, MB, GB, TB & More</title>
    <meta name="description" content="Instantly convert between digital storage units like bytes, kilobytes, megabytes, gigabytes, and terabytes. A free online tool for file sizes, data storage, and network speeds.">
    <meta name="keywords" content="digital converter, byte converter, kb to mb, mb to gb, convert gigabytes, data storage converter, file size converter">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Digital Converter - Convert KB, MB, GB, TB",
        "description": "Convert between digital data storage units including bytes, kilobytes (KB), megabytes (MB), gigabytes (GB), and terabytes (TB), as well as their binary counterparts (KiB, MiB, GiB).",
        "url": "https://www.webtoolskit.org/p/digital-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-25",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Digital Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Digital Storage Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How many MB is 1 GB?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "This depends on the context. In marketing and for hard drive manufacturers, 1 Gigabyte (GB) is 1000 Megabytes (MB). For operating systems like Windows, 1 Gibibyte (GiB) is 1024 Mebibytes (MiB). This is why a 1TB hard drive often shows up as about 931 GB in your computer."
          }
        },
        {
          "@type": "Question",
          "name": "Is a kilobyte 1000 or 1024 bytes?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Technically, a kilobyte (KB) is 1000 bytes (decimal, base-10). A kibibyte (KiB) is 1024 bytes (binary, base-2). However, the terms are often used interchangeably, which causes confusion. Our tool allows you to convert between both decimal (KB, MB, GB) and binary (KiB, MiB, GiB) units for clarity."
          }
        },
        {
          "@type": "Question",
          "name": "What is bigger, MB or GB?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A Gigabyte (GB) is much bigger than a Megabyte (MB). There are 1000 megabytes in one gigabyte. To put it in perspective, a standard HD movie might be a few gigabytes in size, while a single photo is usually a few megabytes."
          }
        },
        {
          "@type": "Question",
          "name": "How do you calculate file size?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "File size is the amount of space a file occupies on a storage medium, measured in units like bytes, kilobytes (KB), megabytes (MB), etc. It's determined by the amount of data in the file. For example, a text file's size is based on the number of characters, while an image's size depends on its resolution and compression."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between megabytes (MB) and megabits (Mbps)?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Megabytes (MB) measure file size or storage capacity, while megabits per second (Mbps) measure data transfer speed (like your internet connection). There are 8 bits in 1 byte, so a 100 Mbps internet connection can theoretically download at a maximum of 12.5 megabytes per second (100 ÷ 8)."
          }
        }
      ]
    }
    </script>

    <style>
        /* Digital Converter Widget - Simplified & Template Compatible */
        .digital-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .digital-converter-widget-container * { box-sizing: border-box; }

        .digital-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .digital-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .digital-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .digital-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .digital-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .digital-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .digital-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .digital-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .digital-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .digital-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .digital-converter-btn:hover { transform: translateY(-2px); }

        .digital-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .digital-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .digital-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .digital-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .digital-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .digital-converter-btn-success:hover {
            background-color: #059669;
        }

        .digital-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .digital-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .digital-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .digital-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .digital-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .digital-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .digital-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .digital-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .digital-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .digital-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .digital-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="speed-converter"] .digital-converter-related-tool-icon { background: linear-gradient(145deg, #4F46E5, #4338CA); }
        a[href*="time-converter"] .digital-converter-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }
        a[href*="frequency-converter"] .digital-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .digital-converter-related-tool-item:hover .digital-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="speed-converter"]:hover .digital-converter-related-tool-icon { background: linear-gradient(145deg, #6366f1, #4f46e5); }
        a[href*="time-converter"]:hover .digital-converter-related-tool-icon { background: linear-gradient(145deg, #7c7ee9, #6366f1); }
        a[href*="frequency-converter"]:hover .digital-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        
        .digital-converter-related-tool-item { box-shadow: none; border: none; }
        .digital-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .digital-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .digital-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .digital-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .digital-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .digital-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .digital-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .digital-converter-related-tool-item:hover .digital-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .digital-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .digital-converter-widget-title { font-size: 1.875rem; }
            .digital-converter-buttons { flex-direction: column; }
            .digital-converter-btn { flex: none; }
            .digital-converter-input-group { grid-template-columns: 1fr; }
            .digital-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .digital-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .digital-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .digital-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .digital-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .digital-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .digital-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .digital-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .digital-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .digital-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .digital-converter-output::selection { background-color: var(--primary-color); color: white; }
        .digital-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .digital-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="digital-converter-widget-container">
        <h1 class="digital-converter-widget-title">Digital Storage Converter</h1>
        <p class="digital-converter-widget-description">
            Quickly convert between digital data units like Bytes, Kilobytes (KB), Megabytes (MB), Gigabytes (GB), and Terabytes (TB), including their binary equivalents (KiB, MiB, GiB).
        </p>
        
        <div class="digital-converter-input-group">
            <label for="digitalFromInput" class="digital-converter-label">From:</label>
            <input 
                type="number" 
                id="digitalFromInput" 
                class="digital-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="digitalFromUnit" class="digital-converter-select">
                <option value="b">Bytes (B)</option>
                <option value="kb">Kilobytes (KB)</option>
                <option value="kib">Kibibytes (KiB)</option>
                <option value="mb" selected>Megabytes (MB)</option>
                <option value="mib">Mebibytes (MiB)</option>
                <option value="gb">Gigabytes (GB)</option>
                <option value="gib">Gibibytes (GiB)</option>
                <option value="tb">Terabytes (TB)</option>
                <option value="tib">Tebibytes (TiB)</option>
            </select>
        </div>

        <div class="digital-converter-input-group">
            <label for="digitalToInput" class="digital-converter-label">To:</label>
            <input 
                type="number" 
                id="digitalToInput" 
                class="digital-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="digitalToUnit" class="digital-converter-select">
                <option value="b">Bytes (B)</option>
                <option value="kb">Kilobytes (KB)</option>
                <option value="kib">Kibibytes (KiB)</option>
                <option value="mb">Megabytes (MB)</option>
                <option value="mib">Mebibytes (MiB)</option>
                <option value="gb" selected>Gigabytes (GB)</option>
                <option value="gib">Gibibytes (GiB)</option>
                <option value="tb">Terabytes (TB)</option>
                <option value="tib">Tebibytes (TiB)</option>
            </select>
        </div>

        <div class="digital-converter-buttons">
            <button class="digital-converter-btn digital-converter-btn-primary" onclick="DigitalConverter.convert()">
                Convert Data
            </button>
            <button class="digital-converter-btn digital-converter-btn-secondary" onclick="DigitalConverter.clear()">
                Clear All
            </button>
            <button class="digital-converter-btn digital-converter-btn-success" onclick="DigitalConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="digital-converter-result">
            <h3 class="digital-converter-result-title">Conversion Result:</h3>
            <div class="digital-converter-output" id="digitalConverterOutput">
                Your converted data size will appear here...
            </div>
        </div>

        <div class="digital-converter-related-tools">
            <h3 class="digital-converter-related-tools-title">Related Tools</h3>
            <div class="digital-converter-related-tools-grid">
                <a href="/p/speed-converter.html" class="digital-converter-related-tool-item" rel="noopener">
                    <div class="digital-converter-related-tool-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="digital-converter-related-tool-name">Speed Converter</div>
                </a>

                <a href="/p/time-converter.html" class="digital-converter-related-tool-item" rel="noopener">
                    <div class="digital-converter-related-tool-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="digital-converter-related-tool-name">Time Converter</div>
                </a>

                <a href="/p/frequency-converter.html" class="digital-converter-related-tool-item" rel="noopener">
                    <div class="digital-converter-related-tool-icon">
                        <i class="fas fa-wave-square"></i>
                    </div>
                    <div class="digital-converter-related-tool-name">Frequency Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Easy Digital Data Storage Conversions</h2>
            <p>In the digital age, we're constantly dealing with file sizes, storage capacities, and data transfer rates. But the difference between a kilobyte and a kibibyte can be confusing. Our free <strong>Digital Converter</strong> tool is designed to make these conversions simple and clear. It allows you to instantly switch between decimal units (like KB, MB, GB), which are typically used for marketing, and binary units (KiB, MiB, GiB), which are used by operating systems. This tool is essential for anyone who works with computers, from software developers to everyday users trying to understand their hard drive space.</p>

            <h3>How to Use the Digital Converter</h3>
            <ol>
                <li><strong>Enter Data Value:</strong> Type the digital value you want to convert into the "From" input field.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., Megabytes) and your target unit (e.g., Gigabytes).</li>
                <li><strong>Click Convert:</strong> Press the "Convert Data" button to get an accurate result instantly.</li>
                <li><strong>Copy or Reset:</strong> Use "Copy Result" to save the value to your clipboard or "Clear All" to start a new conversion.</li>
            </ol>

            <h3>Frequently Asked Questions About Digital Storage</h3>

            <h4>How many MB is 1 GB?</h4>
            <p>This depends on the context. In marketing and for hard drive manufacturers, 1 Gigabyte (GB) is 1000 Megabytes (MB). For operating systems like Windows, 1 Gibibyte (GiB) is 1024 Mebibytes (MiB). This is why a 1TB hard drive often shows up as about 931 GB in your computer.</p>

            <h4>Is a kilobyte 1000 or 1024 bytes?</h4>
            <p>Technically, a kilobyte (KB) is 1000 bytes (decimal, base-10). A kibibyte (KiB) is 1024 bytes (binary, base-2). However, the terms are often used interchangeably, which causes confusion. Our tool allows you to convert between both decimal (KB, MB, GB) and binary (KiB, MiB, GiB) units for clarity.</p>

            <h4>What is bigger, MB or GB?</h4>
            <p>A Gigabyte (GB) is much bigger than a Megabyte (MB). There are 1000 megabytes in one gigabyte. To put it in perspective, a standard HD movie might be a few gigabytes in size, while a single photo is usually a few megabytes.</p>

            <h4>How do you calculate file size?</h4>
            <p>File size is the amount of space a file occupies on a storage medium, measured in units like bytes, kilobytes (KB), megabytes (MB), etc. It's determined by the amount of data in the file. For example, a text file's size is based on the number of characters, while an image's size depends on its resolution and compression.</p>

            <h4>What is the difference between megabytes (MB) and megabits (Mbps)?</h4>
            <p>Megabytes (MB) measure file size or storage capacity, while megabits per second (Mbps) measure data transfer speed (like your internet connection). There are 8 bits in 1 byte, so a 100 Mbps internet connection can theoretically download at a maximum of 12.5 megabytes per second (100 ÷ 8).</p>
        </div>

        <div class="digital-converter-features">
            <h3 class="digital-converter-features-title">Key Features:</h3>
            <ul class="digital-converter-features-list">
                <li class="digital-converter-features-item" style="margin-bottom: 0.3em;">Decimal units (KB, MB, GB)</li>
                <li class="digital-converter-features-item" style="margin-bottom: 0.3em;">Binary units (KiB, MiB, GiB)</li>
                <li class="digital-converter-features-item" style="margin-bottom: 0.3em;">Ideal for file sizes & drive capacity</li>
                <li class="digital-converter-features-item" style="margin-bottom: 0.3em;">Supports up to Terabytes/Tebibytes</li>
                <li class="digital-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="digital-converter-features-item" style="margin-bottom: 0.3em;">Simple, responsive interface</li>
                <li class="digital-converter-features-item">100% private and secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="digital-converter-notification" id="digitalConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Digital Converter
        (function() {
            'use strict';

            // Conversion factors to a base unit of bytes (B)
            const conversionFactors = {
                'b': 1,
                'kb': 1000,
                'kib': 1024,
                'mb': 1e6,
                'mib': 1024**2,
                'gb': 1e9,
                'gib': 1024**3,
                'tb': 1e12,
                'tib': 1024**4
            };

            const elements = {
                fromInput: () => document.getElementById('digitalFromInput'),
                toInput: () => document.getElementById('digitalToInput'),
                fromUnit: () => document.getElementById('digitalFromUnit'),
                toUnit: () => document.getElementById('digitalToUnit'),
                output: () => document.getElementById('digitalConverterOutput'),
                notification: () => document.getElementById('digitalConverterNotification')
            };

            window.DigitalConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to bytes first, then to target unit
                    const valueInBytes = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInBytes / conversionFactors[toUnit.value];

                    const formattedResult = this.formatResult(convertedValue);
                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (Number.isInteger(value)) {
                       return value.toString();
                    }
                    if (Math.abs(value) >= 1000000) {
                        return value.toExponential(6);
                    } else if (Math.abs(value) < 0.000001 && value !== 0) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toFixed(10)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = {
                        'b': 'Bytes', 'kb': 'KB', 'kib': 'KiB',
                        'mb': 'MB', 'mib': 'MiB', 'gb': 'GB',
                        'gib': 'GiB', 'tb': 'TB', 'tib': 'TiB'
                    };
                    return unitNames[unit] || unit.toUpperCase();
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted data size will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        DigitalConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>