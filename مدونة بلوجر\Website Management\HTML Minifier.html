<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML Minifier - Compress & Optimize HTML Code Online</title>
    <meta name="description" content="Reduce your website's file size and improve load times with our free online HTML Minifier. Compress HTML by removing comments, whitespace, and optional tags.">
    <meta name="keywords" content="html minifier, minify html, html compress, html optimizer, reduce html size, online html minifier">
    <link rel="canonical" href="https://www.webtoolskit.org/p/html-minifier.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareApplication"],
        "name": "HTML Minifier - Compress & Optimize HTML Code Online",
        "description": "Reduce your website's file size and improve load times with our free online HTML Minifier. Compress HTML by removing comments, whitespace, and optional tags.",
        "url": "https://www.webtoolskit.org/p/html-minifier.html",
        "applicationCategory": "DeveloperTool",
        "operatingSystem": "Any",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-26",
        "dateModified": "2025-06-26",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "HTML Minifier",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Minify HTML" },
            { "@type": "CopyAction", "name": "Copy Minified HTML" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is an HTML minifier?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "An HTML minifier is a tool that removes unnecessary characters from HTML source code without changing its functionality. This includes removing whitespace, comments, and optional closing tags, which results in a smaller, more compact file."
          }
        },
        {
          "@type": "Question",
          "name": "Why should I minify HTML?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Minifying HTML is a crucial web optimization technique. It reduces the file size of your HTML documents, which leads to faster page load times for your users. Faster loading speeds improve user experience, can lower bounce rates, and reduce bandwidth consumption for both the server and the client."
          }
        },
        {
          "@type": "Question",
          "name": "How does HTML minification work?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "HTML minification works by parsing the code and systematically removing characters that are not required for the browser to render the page correctly. This includes code comments (<!-- ... -->), extra spaces, line breaks, and tabs. Advanced minifiers can also remove optional closing tags like </body>, </html>, </p>, </li>, etc."
          }
        },
        {
          "@type": "Question",
          "name": "Does minifying HTML improve SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, indirectly. Page speed is a confirmed ranking factor for search engines like Google. By minifying your HTML, you make your pages load faster. This improved performance can positively impact your site's SEO rankings, especially for mobile users. It's a key part of technical SEO and Core Web Vitals optimization."
          }
        },
        {
          "@type": "Question",
          "name": "Is it safe to minify HTML?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, it is generally very safe to minify HTML. Reputable minifiers are designed to preserve the functionality of your code. However, it's always a best practice to keep a backup of your original, un-minified (beautified) code for development and debugging, as minified code is very difficult for humans to read."
          }
        }
      ]
    }
    </script>

    <style>
        /* HTML Minifier Widget - Simplified & Template Compatible */
        .html-minifier-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .html-minifier-widget-container * { box-sizing: border-box; }

        .html-minifier-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .html-minifier-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .html-minifier-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .html-minifier-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 150px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .html-minifier-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .html-minifier-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .html-minifier-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .html-minifier-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .html-minifier-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .html-minifier-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .html-minifier-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .html-minifier-btn:hover { transform: translateY(-2px); }

        .html-minifier-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .html-minifier-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .html-minifier-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .html-minifier-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .html-minifier-btn-success {
            background-color: #10b981;
            color: white;
        }

        .html-minifier-btn-success:hover {
            background-color: #059669;
        }

        .html-minifier-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .html-minifier-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .html-minifier-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 150px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .html-minifier-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .html-minifier-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .html-minifier-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .html-minifier-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .html-minifier-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
        }

        .html-minifier-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .html-minifier-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .html-minifier-related-tool-icon {
            width: 72px; height: 72px; margin: 0 auto var(--spacing-sm); border-radius: 18px; display: flex; align-items: center; justify-content: center; font-size: 2.25rem; color: white; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15); border: none;
        }
        
        a[href*="html-beautifier"] .html-minifier-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="css-minifier"] .html-minifier-related-tool-icon { background: linear-gradient(145deg, #4F46E5, #4338CA); }
        a[href*="javascript-minifier"] .html-minifier-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }

        .html-minifier-related-tool-item:hover .html-minifier-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="html-beautifier"]:hover .html-minifier-related-tool-icon { background: linear-gradient(145deg, #ffae2b, #e48817); }
        a[href*="css-minifier"]:hover .html-minifier-related-tool-icon { background: linear-gradient(145deg, #625afa, #5448de); }
        a[href*="javascript-minifier"]:hover .html-minifier-related-tool-icon { background: linear-gradient(145deg, #25c9b7, #1eab9f); }
        
        .html-minifier-related-tool-item { box-shadow: none; border: none; }
        .html-minifier-related-tool-item:hover { box-shadow: none; border: none; }
        .html-minifier-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .html-minifier-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .html-minifier-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .html-minifier-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .html-minifier-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .html-minifier-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .html-minifier-related-tool-item:hover .html-minifier-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .html-minifier-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .html-minifier-widget-title { font-size: 1.875rem; }
            .html-minifier-buttons { flex-direction: column; }
            .html-minifier-btn { flex: none; }
            .html-minifier-options { grid-template-columns: 1fr; }
            .html-minifier-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .html-minifier-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .html-minifier-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .html-minifier-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .html-minifier-features-list { columns: 1; }
            .html-minifier-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .html-minifier-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .html-minifier-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .html-minifier-related-tool-name { font-size: 0.75rem; }
        }
        
        [data-theme="dark"] .html-minifier-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .html-minifier-btn:focus, .html-minifier-checkbox:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .html-minifier-output::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="html-minifier-widget-container">
        <h1 class="html-minifier-widget-title">HTML Minifier</h1>
        <p class="html-minifier-widget-description">
            Optimize your website by compressing HTML code. Reduce file size and improve page load speed with our powerful and easy-to-use online tool.
        </p>
        
        <div class="html-minifier-input-group">
            <label for="htmlMinifierInput" class="html-minifier-label">Paste your HTML here:</label>
            <textarea 
                id="htmlMinifierInput" 
                class="html-minifier-textarea"
                placeholder="<!-- Paste your HTML code here to compress it -->"
                rows="8"
            ></textarea>
        </div>

        <div class="html-minifier-options">
            <div class="html-minifier-option">
                <input type="checkbox" id="removeComments" class="html-minifier-checkbox" checked>
                <label for="removeComments" class="html-minifier-option-label">Remove HTML comments</label>
            </div>
            <div class="html-minifier-option">
                <input type="checkbox" id="collapseWhitespace" class="html-minifier-checkbox" checked>
                <label for="collapseWhitespace" class="html-minifier-option-label">Collapse whitespace</label>
            </div>
            <div class="html-minifier-option">
                <input type="checkbox" id="removeOptionalTags" class="html-minifier-checkbox">
                <label for="removeOptionalTags" class="html-minifier-option-label">Remove optional tags</label>
            </div>
        </div>

        <div class="html-minifier-buttons">
            <button class="html-minifier-btn html-minifier-btn-primary" onclick="HTMLMinifier.minify()">
                Minify HTML
            </button>
            <button class="html-minifier-btn html-minifier-btn-secondary" onclick="HTMLMinifier.clear()">
                Clear All
            </button>
            <button class="html-minifier-btn html-minifier-btn-success" onclick="HTMLMinifier.copy()">
                Copy Minified Code
            </button>
        </div>

        <div class="html-minifier-result">
            <h3 class="html-minifier-result-title">Minified HTML:</h3>
            <div class="html-minifier-output" id="htmlMinifierOutput">
                Your minified HTML will appear here...
            </div>
        </div>

        <div class="html-minifier-related-tools">
            <h3 class="html-minifier-related-tools-title">Related Tools</h3>
            <div class="html-minifier-related-tools-grid">
                <a href="/p/html-beautifier.html" class="html-minifier-related-tool-item" rel="noopener">
                    <div class="html-minifier-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="html-minifier-related-tool-name">HTML Beautifier</div>
                </a>
                <a href="/p/css-minifier.html" class="html-minifier-related-tool-item" rel="noopener">
                    <div class="html-minifier-related-tool-icon">
                        <i class="fas fa-file-code"></i>
                    </div>
                    <div class="html-minifier-related-tool-name">CSS Minifier</div>
                </a>
                <a href="/p/javascript-minifier.html" class="html-minifier-related-tool-item" rel="noopener">
                    <div class="html-minifier-related-tool-icon">
                        <i class="fas fa-compress-alt"></i>
                    </div>
                    <div class="html-minifier-related-tool-name">JavaScript Minifier</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Speed Up Your Site with Our Free HTML Minifier</h2>
            <p>In today's fast-paced digital world, every millisecond counts. Our <strong>HTML Minifier</strong> is an essential tool for web developers and site owners looking to enhance performance. By compressing your HTML code, you can significantly reduce your website's file size, leading to faster load times, lower bandwidth costs, and a better user experience. This simple optimization is a key factor in improving your site's Core Web Vitals and overall SEO performance.</p>
            <p>This tool intelligently removes all unnecessary characters from your source code without affecting its structure or functionality. It strips out comments, eliminates redundant whitespace, and can even remove optional tags that modern browsers don't require. The result is a lean, lightweight HTML file that is delivered and rendered by the browser more quickly, giving you a competitive edge.</p>
            
            <h3>How to Use the HTML Minifier</h3>
            <ol>
                <li><strong>Paste Your HTML:</strong> Copy your original HTML code and paste it into the input field above.</li>
                <li><strong>Select Options:</strong> Choose your desired minification settings, such as removing comments or collapsing whitespace.</li>
                <li><strong>Minify & Copy:</strong> Click the "Minify HTML" button. The optimized code will appear instantly, ready to be copied and deployed to your production environment.</li>
            </ol>
        
            <h2 style="margin-top: var(--spacing-xl);">Frequently Asked Questions About HTML Minification</h2>
            
            <h4>What is an HTML minifier?</h4>
            <p>An HTML minifier is a tool that removes unnecessary characters from HTML source code without changing its functionality. This includes removing whitespace, comments, and optional closing tags, which results in a smaller, more compact file.</p>
            
            <h4>Why should I minify HTML?</h4>
            <p>Minifying HTML is a crucial web optimization technique. It reduces the file size of your HTML documents, which leads to faster page load times for your users. Faster loading speeds improve user experience, can lower bounce rates, and reduce bandwidth consumption for both the server and the client.</p>
            
            <h4>How does HTML minification work?</h4>
            <p>HTML minification works by parsing the code and systematically removing characters that are not required for the browser to render the page correctly. This includes code comments (<!-- ... -->), extra spaces, line breaks, and tabs. Advanced minifiers can also remove optional closing tags like &lt;/body&gt;, &lt;/html&gt;, &lt;/p&gt;, &lt;/li&gt;, etc.</p>
            
            <h4>Does minifying HTML improve SEO?</h4>
            <p>Yes, indirectly. Page speed is a confirmed ranking factor for search engines like Google. By minifying your HTML, you make your pages load faster. This improved performance can positively impact your site's SEO rankings, especially for mobile users. It's a key part of technical SEO and Core Web Vitals optimization.</p>
            
            <h4>Is it safe to minify HTML?</h4>
            <p>Yes, it is generally very safe to minify HTML. Reputable minifiers are designed to preserve the functionality of your code. However, it's always a best practice to keep a backup of your original, un-minified (beautified) code for development and debugging, as minified code is very difficult for humans to read.</p>
        </div>

        <div class="html-minifier-features">
            <h3 class="html-minifier-features-title">Key Features:</h3>
            <ul class="html-minifier-features-list">
                <li class="html-minifier-features-item">Reduces File Size</li>
                <li class="html-minifier-features-item">Improves Page Load Speed</li>
                <li class="html-minifier-features-item">Removes Code Comments</li>
                <li class="html-minifier-features-item">Collapses Whitespace</li>
                <li class="html-minifier-features-item">Removes Optional Tags</li>
                <li class="html-minifier-features-item">Boosts SEO Performance</li>
                <li class="html-minifier-features-item">One-Click Copying</li>
                <li class="html-minifier-features-item">Secure & Client-Side</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="html-minifier-notification" id="htmlMinifierNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('htmlMinifierInput'),
                output: () => document.getElementById('htmlMinifierOutput'),
                notification: () => document.getElementById('htmlMinifierNotification'),
                removeComments: () => document.getElementById('removeComments').checked,
                collapseWhitespace: () => document.getElementById('collapseWhitespace').checked,
                removeOptionalTags: () => document.getElementById('removeOptionalTags').checked
            };

            window.HTMLMinifier = {
                minify() {
                    const input = elements.input();
                    const output = elements.output();
                    let html = input.value;

                    if (!html.trim()) {
                        output.textContent = 'Please enter HTML code to minify.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    
                    try {
                        if (elements.removeComments()) {
                            // Protect content inside script/style tags from comment removal
                            html = html.replace(/<!--[\s\S]*?-->/g, '');
                        }

                        if (elements.collapseWhitespace()) {
                            // Collapse whitespace between tags
                            html = html.replace(/>\s+</g, '><');
                            // Collapse multiple spaces within text nodes (carefully)
                            html = html.replace(/(<(pre|script|style)[\s\S]*?>)[\s\S]*?(<\/\2>)|(\s+)/g, (match, tagBlock, tagName, closeTag, whitespace) => {
                                if (tagBlock) return tagBlock; // If it's a pre/script/style block, return it unchanged
                                if (whitespace) return ' '; // Otherwise, collapse whitespace to a single space
                                return '';
                            }).trim();
                        }
                        
                        if (elements.removeOptionalTags()) {
                             html = html.replace(/<\/(?:html|head|body|p|li|dt|dd|rt|rp|optgroup|option|colgroup|thead|tbody|tfoot|tr|td|th)>/gi, '');
                             html = html.replace(/<html[^>]*>/i, '');
                        }

                        output.textContent = html;
                    } catch (error) {
                        output.textContent = `Error: ${error.message}`;
                        output.style.color = '#dc2626';
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your minified HTML will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your minified HTML will appear here...', 'Please enter HTML code to minify.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        HTMLMinifier.minify();
                    }
                });
            });
        })();
    </script>
</body>
</html>