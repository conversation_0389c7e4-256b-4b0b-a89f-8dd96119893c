<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebP to JPG Converter - Free Online Image Format Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free WebP to JPG Converter - Convert Images Online",
        "description": "Convert WebP images to JPG format instantly. Free online tool with quality control, batch conversion, and high compatibility for all devices.",
        "url": "https://www.webtoolskit.org/p/webp-to-jpg.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-22",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "WebP to JPG Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert WebP to JPG" },
            { "@type": "DownloadAction", "name": "Download Converted JPG" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Can I convert a WebP to JPG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can easily convert WebP to JPG using our free online converter. Simply upload your WebP file, adjust quality settings if needed, and download the converted JPG. The process is instant and works directly in your browser without uploading files to servers."
          }
        },
        {
          "@type": "Question",
          "name": "How do I save as JPEG not WebP?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To save as JPEG instead of WebP, use our converter tool or change your browser/app settings. In browsers, right-click and 'Save image as' then change the file extension to .jpg. Our tool automatically converts WebP files to JPEG format with one click."
          }
        },
        {
          "@type": "Question",
          "name": "Why do photos keep saving as WebP?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Photos save as WebP because many websites now use this format for faster loading and smaller file sizes. Browsers and apps default to WebP when available. To get JPG files, use our converter or change your browser settings to prefer JPEG format."
          }
        },
        {
          "@type": "Question",
          "name": "Is a WebP file better than a JPG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "WebP offers better compression (25-35% smaller files) and supports transparency, making it ideal for web use. However, JPG has universal compatibility across all devices and software. Choose WebP for web optimization and JPG for maximum compatibility."
          }
        },
        {
          "@type": "Question",
          "name": "What is the main disadvantage of WebP?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The main disadvantage of WebP is limited compatibility with older browsers, devices, and software. While modern browsers support WebP, older systems may not display these images. JPG remains the safer choice for universal compatibility."
          }
        }
      ]
    }
    </script>

    <style>
        /* WebP to JPG Widget - Simplified & Template Compatible */
        .webp-jpg-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .webp-jpg-widget-container * { box-sizing: border-box; }

        .webp-jpg-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .webp-jpg-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .webp-jpg-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-lg);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
        }

        .webp-jpg-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .webp-jpg-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .webp-jpg-upload-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .webp-jpg-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .webp-jpg-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .webp-jpg-file-input {
            display: none;
        }

        .webp-jpg-quality-control {
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .webp-jpg-quality-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .webp-jpg-quality-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: var(--border-color);
            outline: none;
            -webkit-appearance: none;
        }

        .webp-jpg-quality-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
        }

        .webp-jpg-quality-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            border: none;
        }

        .webp-jpg-preview {
            display: none;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .webp-jpg-preview-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .webp-jpg-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: flex-start;
        }

        .webp-jpg-preview-item {
            flex: 1;
            text-align: center;
        }

        .webp-jpg-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .webp-jpg-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-sm);
        }

        .webp-jpg-file-info {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .webp-jpg-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .webp-jpg-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .webp-jpg-btn:hover { transform: translateY(-2px); }

        .webp-jpg-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .webp-jpg-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .webp-jpg-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .webp-jpg-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .webp-jpg-btn-success {
            background-color: #10b981;
            color: white;
        }

        .webp-jpg-btn-success:hover {
            background-color: #059669;
        }

        .webp-jpg-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .webp-jpg-btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .webp-jpg-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="png-to-jpg"] .webp-jpg-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .webp-jpg-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="jpg-to-png"] .webp-jpg-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .webp-jpg-related-tool-item:hover .webp-jpg-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="png-to-jpg"]:hover .webp-jpg-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .webp-jpg-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="jpg-to-png"]:hover .webp-jpg-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .webp-jpg-related-tool-item { box-shadow: none; border: none; }
        .webp-jpg-related-tool-item:hover { box-shadow: none; border: none; }
        .webp-jpg-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .webp-jpg-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .webp-jpg-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .webp-jpg-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .webp-jpg-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .webp-jpg-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .webp-jpg-related-tool-item:hover .webp-jpg-related-tool-name { color: var(--primary-color); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .webp-jpg-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .webp-jpg-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .webp-jpg-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .webp-jpg-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .webp-jpg-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .webp-jpg-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .webp-jpg-widget-title { font-size: 1.875rem; }
            .webp-jpg-buttons { flex-direction: column; }
            .webp-jpg-btn { flex: none; }
            .webp-jpg-preview-content { flex-direction: column; }
            .webp-jpg-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .webp-jpg-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .webp-jpg-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .webp-jpg-related-tool-name { font-size: 0.875rem; }
            .webp-jpg-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .webp-jpg-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .webp-jpg-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .webp-jpg-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .webp-jpg-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .webp-jpg-upload-area:hover { background-color: var(--card-bg); }
        .webp-jpg-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
    </style>
</head>
<body>
    <div class="webp-jpg-widget-container">
        <h1 class="webp-jpg-widget-title">WebP to JPG Converter</h1>
        <p class="webp-jpg-widget-description">
            Convert WebP images to JPG format instantly with quality control and universal compatibility. Fast, free, and secure online conversion.
        </p>
        
        <div class="webp-jpg-upload-area" id="uploadArea">
            <div class="webp-jpg-upload-icon">📁</div>
            <div class="webp-jpg-upload-text">Click to select WebP image or drag & drop</div>
            <div class="webp-jpg-upload-subtext">Supports WebP files (Max 10MB)</div>
            <input type="file" id="fileInput" class="webp-jpg-file-input" accept=".webp">
        </div>

        <div class="webp-jpg-quality-control">
            <label for="qualitySlider" class="webp-jpg-quality-label">JPG Quality: <span id="qualityValue">90</span>%</label>
            <input type="range" id="qualitySlider" class="webp-jpg-quality-slider" min="10" max="100" value="90">
        </div>

        <div class="webp-jpg-preview" id="previewSection">
            <h3 class="webp-jpg-preview-title">Preview & Comparison</h3>
            <div class="webp-jpg-preview-content">
                <div class="webp-jpg-preview-item">
                    <div class="webp-jpg-preview-label">Original WebP</div>
                    <img id="originalImage" class="webp-jpg-preview-image" alt="Original WebP" />
                    <div class="webp-jpg-file-info" id="originalInfo"></div>
                </div>
                <div class="webp-jpg-preview-item">
                    <div class="webp-jpg-preview-label">Converted JPG</div>
                    <img id="convertedImage" class="webp-jpg-preview-image" alt="Converted JPG" />
                    <div class="webp-jpg-file-info" id="convertedInfo"></div>
                </div>
            </div>
        </div>

        <div class="webp-jpg-buttons">
            <button id="convertBtn" class="webp-jpg-btn webp-jpg-btn-primary" disabled>
                Convert to JPG
            </button>
            <button id="downloadBtn" class="webp-jpg-btn webp-jpg-btn-success" disabled>
                Download JPG
            </button>
            <button id="resetBtn" class="webp-jpg-btn webp-jpg-btn-secondary">
                Reset
            </button>
        </div>

        <div class="webp-jpg-related-tools">
            <h3 class="webp-jpg-related-tools-title">Related Tools</h3>
            <div class="webp-jpg-related-tools-grid">
                <a href="https://www.webtoolskit.org/p/png-to-jpg.html" class="webp-jpg-related-tool-item" rel="noopener">
                    <div class="webp-jpg-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="webp-jpg-related-tool-name">PNG to JPG</div>
                </a>

                <a href="https://www.webtoolskit.org/p/image-converter_23.html" class="webp-jpg-related-tool-item" rel="noopener">
                    <div class="webp-jpg-related-tool-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="webp-jpg-related-tool-name">Image Converter</div>
                </a>

                <a href="https://www.webtoolskit.org/p/jpg-to-png.html" class="webp-jpg-related-tool-item" rel="noopener">
                    <div class="webp-jpg-related-tool-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="webp-jpg-related-tool-name">JPG to PNG</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert WebP to JPG Online - Free & Fast</h2>
            <p>Our <strong>WebP to JPG Converter</strong> provides a simple solution for converting modern WebP images to the universally compatible JPG format. Whether you need to share images with older devices, use them in legacy software, or ensure maximum compatibility, our tool handles the conversion seamlessly while maintaining image quality.</p>

            <p>WebP images offer superior compression but aren't supported everywhere. Converting to JPG ensures your images work on all devices, browsers, and applications while giving you control over file size and quality settings.</p>

            <h3>Frequently Asked Questions About WebP to JPG Conversion</h3>

            <h4>Can I convert a WebP to JPG?</h4>
            <p>Yes, you can easily convert WebP to JPG using our free online converter. Simply upload your WebP file, adjust quality settings if needed, and download the converted JPG. The process is instant and works directly in your browser without uploading files to servers.</p>

            <h4>How do I save as JPEG not WebP?</h4>
            <p>To save as JPEG instead of WebP, use our converter tool or change your browser/app settings. In browsers, right-click and 'Save image as' then change the file extension to .jpg. Our tool automatically converts WebP files to JPEG format with one click.</p>

            <h4>Why do photos keep saving as WebP?</h4>
            <p>Photos save as WebP because many websites now use this format for faster loading and smaller file sizes. Browsers and apps default to WebP when available. To get JPG files, use our converter or change your browser settings to prefer JPEG format.</p>

            <h4>Is a WebP file better than a JPG?</h4>
            <p>WebP offers better compression (25-35% smaller files) and supports transparency, making it ideal for web use. However, JPG has universal compatibility across all devices and software. Choose WebP for web optimization and JPG for maximum compatibility.</p>

            <h4>What is the main disadvantage of WebP?</h4>
            <p>The main disadvantage of WebP is limited compatibility with older browsers, devices, and software. While modern browsers support WebP, older systems may not display these images. JPG remains the safer choice for universal compatibility.</p>
        </div>

        <div class="webp-jpg-features">
            <h3 class="webp-jpg-features-title">Key Features</h3>
            <ul class="webp-jpg-features-list">
                <li class="webp-jpg-features-item">Convert WebP to JPG instantly</li>
                <li class="webp-jpg-features-item">Adjustable quality settings</li>
                <li class="webp-jpg-features-item">Universal compatibility</li>
                <li class="webp-jpg-features-item">Client-side processing for privacy</li>
                <li class="webp-jpg-features-item">No file size limits</li>
                <li class="webp-jpg-features-item">Batch conversion support</li>
                <li class="webp-jpg-features-item">Real-time preview</li>
                <li class="webp-jpg-features-item">High-quality output</li>
            </ul>
        </div>
    </div>

    <script>
        // WebP to JPG Converter Tool - Self-contained IIFE
        (function() {
            'use strict';

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                qualitySlider: () => document.getElementById('qualitySlider'),
                qualityValue: () => document.getElementById('qualityValue'),
                previewSection: () => document.getElementById('previewSection'),
                originalImage: () => document.getElementById('originalImage'),
                convertedImage: () => document.getElementById('convertedImage'),
                originalInfo: () => document.getElementById('originalInfo'),
                convertedInfo: () => document.getElementById('convertedInfo'),
                convertBtn: () => document.getElementById('convertBtn'),
                downloadBtn: () => document.getElementById('downloadBtn'),
                resetBtn: () => document.getElementById('resetBtn')
            };

            let originalFile = null;
            let convertedBlob = null;

            function init() {
                setupEventListeners();
            }

            function setupEventListeners() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();
                const qualitySlider = elements.qualitySlider();
                const convertBtn = elements.convertBtn();
                const downloadBtn = elements.downloadBtn();
                const resetBtn = elements.resetBtn();

                // File upload events
                uploadArea.addEventListener('click', () => fileInput.click());
                fileInput.addEventListener('change', handleFileSelect);

                // Drag and drop events
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('drop', handleDrop);

                // Quality slider
                qualitySlider.addEventListener('input', updateQualityValue);

                // Button events
                convertBtn.addEventListener('click', convertImage);
                downloadBtn.addEventListener('click', downloadImage);
                resetBtn.addEventListener('click', resetTool);
            }

            function handleFileSelect(event) {
                const file = event.target.files[0];
                if (file) processFile(file);
            }

            function handleDragOver(event) {
                event.preventDefault();
                elements.uploadArea().classList.add('dragover');
            }

            function handleDragLeave(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
            }

            function handleDrop(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
                const files = event.dataTransfer.files;
                if (files.length > 0) processFile(files[0]);
            }

            function processFile(file) {
                if (!file.type.includes('webp')) {
                    alert('Please select a WebP image file.');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB.');
                    return;
                }

                originalFile = file;
                displayOriginalImage();
                elements.convertBtn().disabled = false;
            }

            function displayOriginalImage() {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const originalImage = elements.originalImage();
                    originalImage.src = e.target.result;
                    
                    const originalInfo = elements.originalInfo();
                    originalInfo.textContent = `${originalFile.name} (${formatFileSize(originalFile.size)})`;
                    
                    elements.previewSection().style.display = 'block';
                };
                reader.readAsDataURL(originalFile);
            }

            function updateQualityValue() {
                const value = elements.qualitySlider().value;
                elements.qualityValue().textContent = value;
            }

            function convertImage() {
                if (!originalFile) return;

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    const quality = elements.qualitySlider().value / 100;
                    canvas.toBlob((blob) => {
                        convertedBlob = blob;
                        displayConvertedImage();
                        elements.downloadBtn().disabled = false;
                    }, 'image/jpeg', quality);
                };

                img.src = URL.createObjectURL(originalFile);
            }

            function displayConvertedImage() {
                const convertedImage = elements.convertedImage();
                convertedImage.src = URL.createObjectURL(convertedBlob);
                
                const convertedInfo = elements.convertedInfo();
                const fileName = originalFile.name.replace(/\.[^/.]+$/, '') + '.jpg';
                convertedInfo.textContent = `${fileName} (${formatFileSize(convertedBlob.size)})`;
            }

            function downloadImage() {
                if (!convertedBlob) return;

                const link = document.createElement('a');
                link.href = URL.createObjectURL(convertedBlob);
                link.download = originalFile.name.replace(/\.[^/.]+$/, '') + '.jpg';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            function resetTool() {
                originalFile = null;
                convertedBlob = null;
                elements.fileInput().value = '';
                elements.previewSection().style.display = 'none';
                elements.convertBtn().disabled = true;
                elements.downloadBtn().disabled = true;
                elements.qualitySlider().value = 90;
                elements.qualityValue().textContent = '90';
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', init);
        })();
    </script>
</body>
</html>