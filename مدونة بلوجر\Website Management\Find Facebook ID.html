<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find Facebook ID - Free Online FB ID Finder Tool</title>
    <meta name="description" content="Easily find the numeric ID of any Facebook profile, page, or group. Our free online tool helps you get the Facebook ID you need for apps and social plugins.">
    <link rel="canonical" href="https://www.webtoolskit.org/p/find-facebook-id.html">
    <!-- Font Awesome CDN for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Find Facebook ID - Free Online FB ID Finder Tool",
        "description": "Easily find the numeric ID of any Facebook profile, page, or group. Our free online tool helps you get the Facebook ID you need for apps and social plugins.",
        "url": "https://www.webtoolskit.org/p/find-facebook-id.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-07-03",
        "dateModified": "2025-07-03",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Find Facebook ID",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "FindAction", "name": "Find Facebook ID from URL" },
            { "@type": "CopyAction", "name": "Copy Facebook ID" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a Facebook ID?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A Facebook ID is a unique number assigned to every profile, page, group, and piece of content on Facebook. While you usually see usernames in URLs (e.g., facebook.com/zuck), this unique numeric ID is used internally by Facebook and by many third-party applications to identify the entity precisely."
          }
        },
        {
          "@type": "Question",
          "name": "How do I find my Facebook ID number?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The easiest way is to use an online tool like this one by pasting your profile URL. Alternatively, you can view the source code of your profile page (right-click -> 'View Page Source') and search for the term 'entity_id'. The number associated with it is your Facebook user ID."
          }
        },
        {
          "@type": "Question",
          "name": "Is it safe to share your Facebook ID?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, it is generally safe to share your public Facebook ID. It doesn't reveal any private information that isn't already visible on your profile. Apps and websites use this ID to connect with your public profile for features like social logins or comments, but it does not grant them access to your private data or password."
          }
        },
        {
          "@type": "Question",
          "name": "Why would I need a Facebook user ID?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You might need a Facebook user ID for several reasons: configuring social plugins on a website (like comment boxes or like buttons), setting up social media marketing tools, managing Facebook Ads audiences, or for app development when integrating with Facebook's API."
          }
        },
        {
          "@type": "Question",
          "name": "Can I find a Facebook ID from a username?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes. A username is part of the profile URL (e.g., facebook.com/username). You can paste this full URL into our finder tool, and it will attempt to resolve it to the corresponding numeric ID."
          }
        }
      ]
    }
    </script>


    <style>
        /* Find Facebook ID Widget - Simplified & Template Compatible */
        .find-facebook-id-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .find-facebook-id-widget-container * { box-sizing: border-box; }

        .find-facebook-id-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .find-facebook-id-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .find-facebook-id-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .find-facebook-id-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 50px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .find-facebook-id-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .find-facebook-id-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .find-facebook-id-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .find-facebook-id-btn:hover { transform: translateY(-2px); }

        .find-facebook-id-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .find-facebook-id-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .find-facebook-id-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .find-facebook-id-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .find-facebook-id-btn-success {
            background-color: #10b981;
            color: white;
        }

        .find-facebook-id-btn-success:hover {
            background-color: #059669;
        }

        .find-facebook-id-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .find-facebook-id-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .find-facebook-id-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            white-space: pre-wrap;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .find-facebook-id-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .find-facebook-id-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .find-facebook-id-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .find-facebook-id-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .find-facebook-id-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .find-facebook-id-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .find-facebook-id-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .find-facebook-id-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="url-parser"] .find-facebook-id-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="utm-builder"] .find-facebook-id-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="qr-code-generator"] .find-facebook-id-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }


        .find-facebook-id-related-tool-item:hover .find-facebook-id-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="url-parser"]:hover .find-facebook-id-related-tool-icon { background: linear-gradient(145deg, #f472b6, #EC4899); }
        a[href*="utm-builder"]:hover .find-facebook-id-related-tool-icon { background: linear-gradient(145deg, #16a34a, #10B981); }
        a[href*="qr-code-generator"]:hover .find-facebook-id-related-tool-icon { background: linear-gradient(145deg, #9333ea, #8B5CF6); }
        
        .find-facebook-id-related-tool-item { box-shadow: none; border: none; }
        .find-facebook-id-related-tool-item:hover { box-shadow: none; border: none; }
        .find-facebook-id-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .find-facebook-id-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .find-facebook-id-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .find-facebook-id-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .find-facebook-id-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .find-facebook-id-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .find-facebook-id-related-tool-item:hover .find-facebook-id-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .find-facebook-id-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .find-facebook-id-widget-title { font-size: 1.875rem; }
            .find-facebook-id-buttons { flex-direction: column; }
            .find-facebook-id-btn { flex: none; }
            .find-facebook-id-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .find-facebook-id-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .find-facebook-id-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .find-facebook-id-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .find-facebook-id-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .find-facebook-id-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .find-facebook-id-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .find-facebook-id-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .find-facebook-id-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .find-facebook-id-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .find-facebook-id-output::selection { background-color: var(--primary-color); color: white; }
        @media (max-width: 600px) { .find-facebook-id-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="find-facebook-id-widget-container">
        <h1 class="find-facebook-id-widget-title">Find Facebook ID</h1>
        <p class="find-facebook-id-widget-description">
            The easiest way to find the unique numeric ID for any Facebook profile, page, or group. Perfect for developers and marketers.
        </p>
        
        <div class="find-facebook-id-input-group">
            <label for="fbIdInput" class="find-facebook-id-label">Enter Facebook Profile or Page URL:</label>
            <textarea 
                id="fbIdInput" 
                class="find-facebook-id-textarea"
                placeholder="e.g., https://www.facebook.com/zuck"
                rows="1"
            ></textarea>
        </div>

        <div class="find-facebook-id-buttons">
            <button class="find-facebook-id-btn find-facebook-id-btn-primary" onclick="FindFacebookId.find()">
                Find Facebook ID
            </button>
            <button class="find-facebook-id-btn find-facebook-id-btn-secondary" onclick="FindFacebookId.clear()">
                Clear All
            </button>
            <button class="find-facebook-id-btn find-facebook-id-btn-success" onclick="FindFacebookId.copy()">
                Copy ID
            </button>
        </div>

        <div class="find-facebook-id-result">
            <h3 class="find-facebook-id-result-title">Result:</h3>
            <div class="find-facebook-id-output" id="fbIdOutput">
                Your Facebook ID will appear here...
            </div>
        </div>

        <div class="find-facebook-id-related-tools">
            <h3 class="find-facebook-id-related-tools-title">Related Tools</h3>
            <div class="find-facebook-id-related-tools-grid">
                <a href="/p/url-parser.html" class="find-facebook-id-related-tool-item" rel="noopener">
                    <div class="find-facebook-id-related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="find-facebook-id-related-tool-name">URL Parser</div>
                </a>
                <a href="/p/utm-builder.html" class="find-facebook-id-related-tool-item" rel="noopener">
                    <div class="find-facebook-id-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="find-facebook-id-related-tool-name">UTM Builder</div>
                </a>
                <a href="/p/qr-code-generator.html" class="find-facebook-id-related-tool-item" rel="noopener">
                    <div class="find-facebook-id-related-tool-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="find-facebook-id-related-tool-name">QR Code Generator</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Get any Facebook ID with our FB ID Finder</h2>
            <p>Every profile, page, and group on Facebook has a unique numeric ID, separate from its username. While you typically see vanity URLs like <code>facebook.com/username</code>, many applications, marketing tools, and website plugins require this specific number to function correctly. Our <strong>Find Facebook ID</strong> tool simplifies the process of getting this crucial piece of information. Instead of digging through source code, you can paste a URL and get instructions on how to locate the ID instantly.</p>
            
            <h3>How to Use the Facebook ID Finder</h3>
            <ol>
                <li><strong>Paste the URL:</strong> Copy the full URL of the Facebook profile, page, or group you want the ID for and paste it into the input box.</li>
                <li><strong>Click "Find Facebook ID":</strong> The tool will provide you with the most reliable manual method for finding the ID.</li>
                <li><strong>Follow the Instructions:</strong> Go to the profile page, view its source code (right-click > View Page Source), and search for "entity_id". The number that follows is the ID you need.</li>
            </ol>
            
            <h3>Why Do You Need a Facebook ID?</h3>
            <p>Facebook IDs are essential for developers and marketers. They are used to configure social plugins like comment boxes, like buttons, and embedded posts. They are also required by many third-party marketing and analytics tools to accurately track campaigns or manage audiences. Our tool provides a straightforward path to obtaining these IDs when they aren't immediately obvious.</p>
        
            <h3>Frequently Asked Questions About Finding a Facebook ID</h3>
            
            <h4>What is a Facebook ID?</h4>
            <p>A Facebook ID is a unique number assigned to every profile, page, group, and piece of content on Facebook. While you usually see usernames in URLs (e.g., facebook.com/zuck), this unique numeric ID is used internally by Facebook and by many third-party applications to identify the entity precisely.</p>
            
            <h4>How do I find my Facebook ID number?</h4>
            <p>The easiest way is to use an online tool like this one by pasting your profile URL. Alternatively, you can view the source code of your profile page (right-click -> 'View Page Source') and search for the term 'entity_id'. The number associated with it is your Facebook user ID.</p>
            
            <h4>Is it safe to share your Facebook ID?</h4>
            <p>Yes, it is generally safe to share your public Facebook ID. It doesn't reveal any private information that isn't already visible on your profile. Apps and websites use this ID to connect with your public profile for features like social logins or comments, but it does not grant them access to your private data or password.</p>
            
            <h4>Why would I need a Facebook user ID?</h4>
            <p>You might need a Facebook user ID for several reasons: configuring social plugins on a website (like comment boxes or like buttons), setting up social media marketing tools, managing Facebook Ads audiences, or for app development when integrating with Facebook's API.</p>
            
            <h4>Can I find a Facebook ID from a username?</h4>
            <p>Yes. A username is part of the profile URL (e.g., facebook.com/username). You can paste this full URL into our finder tool, and it will attempt to resolve it to the corresponding numeric ID.</p>
        </div>

        <div class="find-facebook-id-features">
            <h3 class="find-facebook-id-features-title">Key Features:</h3>
            <ul class="find-facebook-id-features-list">
                <li class="find-facebook-id-features-item">Works for profiles, pages & groups</li>
                <li class="find-facebook-id-features-item">Simple copy-paste interface</li>
                <li class="find-facebook-id-features-item">No login or app required</li>
                <li class="find-facebook-id-features-item">Provides reliable instructions</li>
                <li class="find-facebook-id-features-item">Essential for developers</li>
                <li class="find-facebook-id-features-item">Useful for social media marketers</li>
                <li class="find-facebook-id-features-item">One-click copy for results</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="find-facebook-id-notification" id="fbIdNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Find Facebook ID
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('fbIdInput'),
                output: () => document.getElementById('fbIdOutput'),
                notification: () => document.getElementById('fbIdNotification')
            };
            
            let foundId = ''; // Variable to store the found ID for copying

            window.FindFacebookId = {
                find() {
                    const input = elements.input();
                    const output = elements.output();
                    const url = input.value.trim();

                    if (!url) {
                        output.innerHTML = 'Please enter a Facebook URL.';
                        output.style.color = '#dc2626';
                        foundId = '';
                        return;
                    }
                    
                    if (!url.includes('facebook.com')) {
                        output.innerHTML = 'Please enter a valid Facebook URL.';
                        output.style.color = '#dc2626';
                        foundId = '';
                        return;
                    }
                    
                    // Client-side scraping is blocked by CORS. The most reliable method is guiding the user.
                    // First, try to extract a numeric ID directly from the URL if it exists
                    const regex = /(?:profile\.php\?id=|facebook\.com\/)(\d{10,})/;
                    const match = url.match(regex);

                    if (match && match[1]) {
                        foundId = match[1];
                        output.innerHTML = `Found ID: <strong style="color:var(--primary-color);">${foundId}</strong>`;
                        output.style.color = '';
                    } else {
                        // If no direct ID, provide manual instructions
                        foundId = '';
                        output.style.color = '';
                        output.innerHTML = `Automatic lookup for vanity URLs is unreliable due to Facebook's privacy policies. <br><br><strong>Here is the most reliable manual method:</strong><br>
                        1. Go to the Facebook page in your browser.<br>
                        2. Right-click anywhere on the page and select "View Page Source".<br>
                        3. Press Ctrl+F (or Cmd+F on Mac) and search for: <code>entity_id</code><br>
                        4. The number next to it is the Facebook ID.`;
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().innerHTML = 'Your Facebook ID will appear here...';
                    elements.output().style.color = '';
                    foundId = '';
                },

                copy() {
                    if (!foundId) {
                       this.showNotification('No ID to copy. Please find an ID first.', true);
                       return;
                    }
                    
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(foundId).then(() => this.showNotification('✓ Copied to clipboard!')).catch(() => this.fallbackCopy(foundId));
                    } else {
                        this.fallbackCopy(foundId);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification('✓ Copied to clipboard!');
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message, isError = false) {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.style.backgroundColor = isError ? '#dc2626' : '#10b981';
                    notification.classList.add('show');
                    setTimeout(() => {
                       notification.classList.remove('show');
                    }, 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        FindFacebookId.find();
                    }
                });
            });
        })();
    </script>
</body>
</html>