<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word Counter Widget</title>
    
    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Word Counter - Count Words, Characters & More",
        "description": "Count words, characters, paragraphs, sentences, and estimate reading time instantly. Free online tool with real-time counting for writers, students, and professionals.",
        "url": "https://www.webtoolskit.org/p/word-counter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Word Counter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "AssessAction", "name": "Count Words" },
            { "@type": "AssessAction", "name": "Count Characters" },
            { "@type": "CopyAction", "name": "Copy Statistics" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How many words are 750 characters?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Based on the average English word length of 4.7 characters, 750 characters is approximately 160 words. If you include a space after each word, the average character count per word is 5.7, which would make 750 characters equal to about 131 words. Our Word Counter tool can give you the exact count for any specific text."
          }
        },
        {
          "@type": "Question",
          "name": "How many pages is 1000 words in MLA format?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "In standard MLA format, which requires double-spacing and a 12-point font like Times New Roman, 1000 words will be approximately 4 pages long. If the document were single-spaced, it would be about 2 pages."
          }
        },
        {
          "@type": "Question",
          "name": "What does 750 words look like on a page?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A 750-word document typically fills about 1.5 pages when single-spaced or 3 pages when double-spaced, using a standard 12-point font. This is a common length for many blog posts, articles, and academic essays."
          }
        },
        {
          "@type": "Question",
          "name": "Is it possible to write 1000 words in 1 hour?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, it is possible for an experienced writer or a fast typist, but it is challenging. It requires a sustained writing speed of about 17 words per minute without long pauses for editing or research. This pace is most achievable when drafting content on a familiar subject."
          }
        },
        {
          "@type": "Question",
          "name": "How many pages is 100,000 words?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "100,000 words is the length of a typical full-length novel. In a standard book format or a document using a 12-point font and double-spacing, this would translate to approximately 400 pages. If single-spaced, it would be around 200 pages."
          }
        }
      ]
    }
    </script>

    <style>
        /* Word Counter - Standardized Styles */
        .widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .widget-container * { box-sizing: border-box; }

        .widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 200px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .stat {
            text-align: center;
            padding: var(--spacing-md);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .stat-number {
            display: block;
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-color-light);
            font-weight: 500;
        }

        .buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .btn:hover { transform: translateY(-2px); }

        .btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover { background-color: var(--border-color); }

        .btn-success {
            background-color: #10b981;
            color: white;
        }

        .btn-success:hover { background-color: #059669; }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .notification.show { transform: translateX(0); }
        
        /* === START: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        .related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="case-converter"] .related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="remove-line-breaks"] .related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="random-word-generator"] .related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }
        
        .related-tool-item:hover .related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .related-tool-item {
            box-shadow: none;
            border: none;
            text-align: center;
            text-decoration: none;
            color: inherit;
            transition: var(--transition-base);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            display: block;
            width: 100%;
            max-width: 160px;
        }
        
        .related-tool-item:hover {
            transform: translateY(0);
            background-color: transparent;
        }
        
        .related-tools {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }
        
        .related-tools-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-xl);
            font-size: 1.5rem;
            font-weight: 700;
            text-align: center;
        }
        
        .related-tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
            justify-items: center;
        }
        
        .related-tool-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            margin-top: var(--spacing-sm);
            line-height: 1.3;
        }
        
        .related-tool-item:hover .related-tool-name {
            color: var(--primary-color);
        }
        
        /* === END: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        /* SEO Content Section Styles */
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        
        /* === START: STANDARDIZED FEATURES SECTION === */
        .features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }
        
        .features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }
        
        .features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            margin-bottom: 0.3em;
        }
        
        .features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 600px) {
            .features-list {
                columns: 1 !important;
                -webkit-columns: 1 !important;
                -moz-columns: 1 !important;
            }
        }
        /* === END: STANDARDIZED FEATURES SECTION === */
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .widget-container {
                margin: var(--spacing-md);
                padding: var(--spacing-lg);
            }
            .widget-title { font-size: 1.875rem; }
            .buttons { flex-direction: column; }
            .btn { flex: none; }
            .stats { grid-template-columns: repeat(2, 1fr); }
            
            .related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .related-tool-item { padding: var(--spacing-md); max-width: none; }
            .related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .related-tool-name { font-size: 0.875rem; }
        }
        
        @media (max-width: 480px) {
            .related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .related-tool-name { font-size: 0.75rem; }
        }

        /* Focus & Accessibility */
        .btn:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .textarea::selection {
            background-color: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="widget-container">
        <h1 class="widget-title">Word Counter</h1>
        <p class="widget-description">
            Count words, characters, paragraphs, and sentences in real-time. Perfect for writers, students, and content creators.
        </p>
        
        <label for="input" class="label">Enter your text:</label>
        <textarea 
            id="input" 
            class="textarea"
            placeholder="Type or paste your text here to see live word count statistics..."
            rows="8"
        ></textarea>

        <div class="stats">
            <div class="stat">
                <span class="stat-number" id="words">0</span>
                <span class="stat-label">Words</span>
            </div>
            <div class="stat">
                <span class="stat-number" id="characters">0</span>
                <span class="stat-label">Characters</span>
            </div>
            <div class="stat">
                <span class="stat-number" id="charactersNoSpaces">0</span>
                <span class="stat-label">Characters (no spaces)</span>
            </div>
            <div class="stat">
                <span class="stat-number" id="sentences">0</span>
                <span class="stat-label">Sentences</span>
            </div>
            <div class="stat">
                <span class="stat-number" id="paragraphs">0</span>
                <span class="stat-label">Paragraphs</span>
            </div>
            <div class="stat">
                <span class="stat-number" id="readingTime">0</span>
                <span class="stat-label">Reading Time (min)</span>
            </div>
        </div>

        <div class="buttons">
            <button class="btn btn-secondary" onclick="Tool.clear()">Clear All</button>
            <button class="btn btn-success" onclick="Tool.copy()">Copy Statistics</button>
        </div>
        
        <div class="related-tools">
            <h3 class="related-tools-title">Related Tools</h3>
            <div class="related-tools-grid">
                <a href="/p/case-converter.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon"><i class="fas fa-text-height"></i></div>
                    <div class="related-tool-name">Case Converter</div>
                </a>
                <a href="/p/remove-line-breaks.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon"><i class="fas fa-align-left"></i></div>
                    <div class="related-tool-name">Remove Line Breaks</div>
                </a>
                <a href="/p/random-word-generator.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon"><i class="fas fa-random"></i></div>
                    <div class="related-tool-name">Random Word Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Your Essential Tool for Precise Text Analysis</h2>
            <p>A <strong>Word Counter</strong> is an indispensable tool for anyone who works with text. Whether you are a student striving to meet the word count requirements of an essay, a professional writer crafting an article, an SEO specialist optimizing content, or a social media manager staying within character limits, this tool provides instant and accurate feedback. Our online word counter analyzes your text in real-time, providing a detailed breakdown of not just words, but also characters, sentences, paragraphs, and even an estimated reading time.</p>
            
            <h3>How to Use the Word Counter</h3>
            <p>Using our tool is incredibly simple. Just follow these steps:</p>
            <ol>
                <li><strong>Enter Your Text:</strong> Type or paste your content directly into the text area above.</li>
                <li><strong>See Live Results:</strong> As you type, the statistics below the text box—including words, characters, sentences, and more—will update instantly.</li>
                <li><strong>Analyze the Data:</strong> Review the detailed metrics to ensure your text meets specific length requirements or readability goals.</li>
            </ol>
            
            <h3>Frequently Asked Questions About Word Counter</h3>
            
            <h4>How many words are 750 characters?</h4>
            <p>The number of words in 750 characters can vary, but a good estimate is between 130 and 160 words. This calculation is based on the average English word being about 4.7 characters long. When you factor in a space after each word, the average length becomes 5.7 characters. Our tool removes the guesswork by providing exact counts for your specific text.</p>
            
            <h4>How many pages is 1000 words in MLA format?</h4>
            <p>In standard MLA format (1-inch margins, 12-point Times New Roman font, double-spaced), 1000 words will typically be about <strong>4 pages</strong>. If the same text were single-spaced, it would be approximately 2 pages.</p>
            
            <h4>What does 750 words look like on a page?</h4>
            <p>A 750-word document is a common length for blog posts and short essays. Using a standard 12-point font, it will fill about <strong>1.5 pages if single-spaced</strong> and <strong>3 pages if double-spaced</strong>.</p>
            
            <h4>Is it possible to write 1000 words in 1 hour?</h4>
            <p>Yes, but it's a fast pace. Writing 1000 words in one hour requires an average speed of around 17 words per minute, which is achievable for proficient typists who can draft content without frequent pauses for research or editing. It's a common goal for focused writing sprints.</p>
            
            <h4>How many pages is 100,000 words?</h4>
            <p>A manuscript of 100,000 words is the standard length for a full-length novel. When formatted for a book or a document with double-spacing and a 12-point font, it will be approximately <strong>400 pages</strong> long. If single-spaced, it would be around 200 pages.</p>
        </div>

        <div class="features">
            <h3 class="features-title">Key Features:</h3>
            <ul class="features-list">
                <li class="features-item">Counts words and characters</li>
                <li class="features-item">Tracks sentences and paragraphs</li>
                <li class="features-item">Estimates reading time</li>
                <li class="features-item">Real-time statistics update</li>
                <li class="features-item">One-click copy for all stats</li>
                <li class="features-item">Clean, responsive design</li>
            </ul>
        </div>
    </div>

    <div class="notification" id="notification">✓ Statistics copied to clipboard!</div>

    <script>
        // Ultra Simplified Word Counter
        (function() {
            'use strict';

            window.Tool = {
                count() {
                    const text = document.getElementById('input').value;

                    // Count words (split by whitespace, filter empty)
                    const words = text.trim() ? text.trim().split(/\s+/).length : 0;

                    // Count characters
                    const characters = text.length;
                    const charactersNoSpaces = text.replace(/\s/g, '').length;

                    // Count sentences (split by sentence endings)
                    const sentences = text.trim() ? (text.match(/[.!?…]+/g) || []).length || (words > 0 ? 1 : 0) : 0;

                    // Count paragraphs (split by double line breaks)
                    const paragraphs = text.trim() ? text.split(/\n\s*\n/).filter(p => p.trim()).length : 0;

                    // Estimate reading time (average 200 words per minute)
                    const readingTime = Math.ceil(words / 200);

                    // Update display
                    document.getElementById('words').textContent = words;
                    document.getElementById('characters').textContent = characters;
                    document.getElementById('charactersNoSpaces').textContent = charactersNoSpaces;
                    document.getElementById('sentences').textContent = sentences;
                    document.getElementById('paragraphs').textContent = paragraphs;
                    document.getElementById('readingTime').textContent = readingTime;
                },

                clear() {
                    document.getElementById('input').value = '';
                    this.count();
                },

                copy() {
                    if (document.getElementById('words').textContent === '0') return;
                    
                    const stats = [
                        `Words: ${document.getElementById('words').textContent}`,
                        `Characters: ${document.getElementById('characters').textContent}`,
                        `Characters (no spaces): ${document.getElementById('charactersNoSpaces').textContent}`,
                        `Sentences: ${document.getElementById('sentences').textContent}`,
                        `Paragraphs: ${document.getElementById('paragraphs').textContent}`,
                        `Reading Time: ~${document.getElementById('readingTime').textContent} min`
                    ].join('\n');

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(stats).then(() => this.notify());
                    } else {
                        const el = document.createElement('textarea');
                        el.value = stats;
                        el.style.cssText = 'position:fixed;left:-999px;top:-999px';
                        document.body.appendChild(el);
                        el.select();
                        document.execCommand('copy');
                        document.body.removeChild(el);
                        this.notify();
                    }
                },

                notify() {
                    const n = document.getElementById('notification');
                    n.classList.add('show');
                    setTimeout(() => n.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Real-time counting
                document.getElementById('input').addEventListener('input', () => Tool.count());

                // Initial count
                Tool.count();
            });
        })();
    </script>
</body>
</html>