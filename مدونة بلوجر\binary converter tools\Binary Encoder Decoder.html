<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary Encoder/Decoder - Free Online Binary Tool</title>
    <meta name="description" content="A powerful online tool to encode text to binary and decode binary to text. Free, fast, and easy to use with customizable formatting options for developers and students.">
    <meta name="keywords" content="binary encoder, binary decoder, binary encoder decoder, text to binary, binary to text, encode binary, decode binary, online tool">
    <link rel="canonical" href="https://www.webtoolskit.org/p/binary-encoder-decoder.html" />

    <!-- Page-specific Open Graph Meta Tags -->
    <meta property="og:url" content="https://www.webtoolskit.org/p/binary-encoder-decoder.html" />
    <meta property="og:title" content="Binary Encoder/Decoder - Free Online Binary Utilities" />
    <meta property="og:description" content="Encode text into binary or decode binary back to text with this versatile online tool. Perfect for data processing, programming, and educational purposes." />
    <meta property="og:image" content="https://www.webtoolskit.org/images/binary-og.jpg" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Binary Encoder/Decoder - Encode and Decode Binary Code",
        "description": "A powerful online tool to encode text to binary and decode binary to text. Free, fast, and easy to use with customizable formatting options for developers and students.",
        "url": "https://www.webtoolskit.org/p/binary-encoder-decoder.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-19",
        "dateModified": "2025-06-19",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Binary Encoder/Decoder",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Encode Text to Binary" },
            { "@type": "ControlAction", "name": "Decode Binary to Text" },
            { "@type": "CopyAction", "name": "Copy Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to decode binary code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To decode binary code into text, you first group the binary digits into 8-bit chunks (bytes). Each byte corresponds to a number in the ASCII table. You convert each byte to its decimal value and then find the character that matches that value. Our tool automates this entire process instantly."
          }
        },
        {
          "@type": "Question",
          "name": "How to encode binary code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To encode text into binary, you take each character from the text, find its corresponding decimal value in the ASCII character set, and then convert that decimal value into an 8-bit binary number. These binary numbers are then concatenated to form the complete binary string."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between binary encoder and decoder?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A binary encoder converts data from a human-readable format (like text) into a binary format (strings of 0s and 1s) for a computer to process. A binary decoder performs the reverse operation, converting binary data back into a human-readable format."
          }
        },
        {
          "@type": "Question",
          "name": "What is an example of a binary encoding?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A simple example is encoding the word 'Hello'. Each letter is converted to its 8-bit binary form: 'H' is 01001000, 'e' is 01100101, 'l' is 01101100, and 'o' is 01101111. The full binary encoding for 'Hello' would be '01001000 01100101 01101100 01101100 01101111'."
          }
        },
        {
          "@type": "Question",
          "name": "What does a binary encoder do?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A binary encoder translates information from any source format, such as text, images, or sound, into a sequence of binary digits (0s and 1s). This binary representation is the fundamental language that computers use to process, store, and transmit digital information."
          }
        }
      ]
    }
    </script>

    <style>
        /* Binary Encoder/Decoder Widget - Simplified & Template Compatible */
        .binary-encoder-decoder-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .binary-encoder-decoder-widget-container * { box-sizing: border-box; }

        .binary-encoder-decoder-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .binary-encoder-decoder-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .binary-encoder-decoder-io-group {
            margin-bottom: var(--spacing-lg);
            position: relative;
        }

        .binary-encoder-decoder-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .binary-encoder-decoder-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 140px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }
        
        #encoderDecoderBinaryInput { font-family: 'SF Mono', Monaco, monospace; }

        .binary-encoder-decoder-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .binary-encoder-decoder-controls {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
            margin: var(--spacing-md) 0;
            padding: var(--spacing-lg) 0;
            border-top: 1px solid var(--border-color);
            border-bottom: 1px solid var(--border-color);
        }
        
        .binary-encoder-decoder-buttons {
            display: flex;
            gap: var(--spacing-md);
            flex-wrap: wrap;
            justify-content: center;
            width: 100%;
        }

        .binary-encoder-decoder-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex-grow: 1;
            min-width: 160px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .binary-encoder-decoder-btn:hover { transform: translateY(-2px); }

        .binary-encoder-decoder-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .binary-encoder-decoder-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .binary-encoder-decoder-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .binary-encoder-decoder-btn-secondary:hover {
            background-color: var(--border-color);
        }
        
        .binary-encoder-decoder-options {
            display: flex;
            gap: var(--spacing-lg);
            margin-top: var(--spacing-md);
        }
        
        .binary-encoder-decoder-option { display: flex; align-items: center; gap: var(--spacing-sm); }
        .binary-encoder-decoder-checkbox { width: 18px; height: 18px; accent-color: var(--primary-color); cursor: pointer; }
        .binary-encoder-decoder-option-label { margin: 0; font-weight: 500; cursor: pointer; color: var(--text-color); font-size: 0.95rem; }

        .binary-encoder-decoder-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .binary-encoder-decoder-notification.show { transform: translateX(0); }
        
        .seo-content { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); color: var(--text-color-light); line-height: 1.7; }
        .seo-content h2, .seo-content h3, .seo-content h4 { color: var(--text-color); margin-bottom: var(--spacing-md); }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code { background-color: var(--background-color-alt); padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 6px; font-family: 'SF Mono', Monaco, monospace; }

        .binary-encoder-decoder-features { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .binary-encoder-decoder-features-title { color: var(--text-color); margin-bottom: var(--spacing-md); font-size: 1.25rem; font-weight: 700; }
        .binary-encoder-decoder-features-list { list-style: none; padding: 0; margin: 0; columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; }
        .binary-encoder-decoder-features-item { padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg); color: var(--text-color-light); position: relative; margin-bottom: 0.3em; }
        .binary-encoder-decoder-features-item:before { content: ""; position: absolute; left: 0; top: calc(var(--spacing-sm) + 2px); width: 12px; height: 6px; border-left: 2px solid #10b981; border-bottom: 2px solid #10b981; transform: rotate(-45deg); }
        
        .binary-encoder-decoder-related-tool-icon { width: 72px; height: 72px; margin: 0 auto var(--spacing-sm); border-radius: 18px; display: flex; align-items: center; justify-content: center; font-size: 2.25rem; color: white; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15); border: none; }
        a[href*="binary-to-text"] .binary-encoder-decoder-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="text-to-binary"] .binary-encoder-decoder-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="binary-to-ascii"] .binary-encoder-decoder-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }
        .binary-encoder-decoder-related-tool-item:hover .binary-encoder-decoder-related-tool-icon { transform: translateY(-5px) scale(1.05); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15); }
        a[href*="binary-to-text"]:hover .binary-encoder-decoder-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="text-to-binary"]:hover .binary-encoder-decoder-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="binary-to-ascii"]:hover .binary-encoder-decoder-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .binary-encoder-decoder-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .binary-encoder-decoder-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .binary-encoder-decoder-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .binary-encoder-decoder-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .binary-encoder-decoder-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .binary-encoder-decoder-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .binary-encoder-decoder-related-tool-item:hover .binary-encoder-decoder-related-tool-name { color: var(--primary-color); }
        
        .copy-btn { position: absolute; top: 8px; right: 8px; background: var(--background-color-alt); border: 1px solid var(--border-color); color: var(--text-color-light); padding: 6px 10px; border-radius: var(--border-radius-md); cursor: pointer; transition: var(--transition-base); opacity: 0.7; }
        .copy-btn:hover { background: var(--border-color); opacity: 1; }

        @media (max-width: 768px) {
            .binary-encoder-decoder-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .binary-encoder-decoder-widget-title { font-size: 1.875rem; }
            .binary-encoder-decoder-buttons { flex-direction: column; }
            .binary-encoder-decoder-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .binary-encoder-decoder-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .binary-encoder-decoder-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .binary-encoder-decoder-related-tool-name { font-size: 0.875rem; }
        }
        @media (max-width: 600px) {
            .binary-encoder-decoder-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }
        @media (max-width: 480px) {
            .binary-encoder-decoder-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .binary-encoder-decoder-related-tool-item { padding: var(--spacing-sm); }
            .binary-encoder-decoder-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .binary-encoder-decoder-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="binary-encoder-decoder-widget-container">
        <h1 class="binary-encoder-decoder-widget-title">Binary Encoder &amp; Decoder</h1>
        <p class="binary-encoder-decoder-widget-description">
            A versatile two-way tool to convert plain text to binary code (encode) and binary code back to readable text (decode) with ease.
        </p>
        
        <div class="binary-encoder-decoder-io-group">
            <label for="encoderDecoderTextInput" class="binary-encoder-decoder-label">Text Input</label>
            <textarea 
                id="encoderDecoderTextInput" 
                class="binary-encoder-decoder-textarea"
                placeholder="Type or paste your text here..."
                rows="5"
            ></textarea>
            <button class="copy-btn" onclick="BinaryEncoderDecoder.copy('text')">Copy</button>
        </div>

        <div class="binary-encoder-decoder-controls">
            <div class="binary-encoder-decoder-buttons">
                <button class="binary-encoder-decoder-btn binary-encoder-decoder-btn-primary" onclick="BinaryEncoderDecoder.encode()">
                    Encode (Text to Binary) <i class="fas fa-arrow-down"></i>
                </button>
                <button class="binary-encoder-decoder-btn binary-encoder-decoder-btn-primary" onclick="BinaryEncoderDecoder.decode()">
                    <i class="fas fa-arrow-up"></i> Decode (Binary to Text)
                </button>
            </div>
            <div class="binary-encoder-decoder-options">
                <div class="binary-encoder-decoder-option">
                    <input type="checkbox" id="addSpaces" class="binary-encoder-decoder-checkbox" checked>
                    <label for="addSpaces" class="binary-encoder-decoder-option-label">Space between bytes (for encoding)</label>
                </div>
            </div>
        </div>

        <div class="binary-encoder-decoder-io-group">
            <label for="encoderDecoderBinaryInput" class="binary-encoder-decoder-label">Binary Input / Output</label>
            <textarea 
                id="encoderDecoderBinaryInput" 
                class="binary-encoder-decoder-textarea"
                placeholder="Type or paste your binary code here..."
                rows="5"
            ></textarea>
            <button class="copy-btn" onclick="BinaryEncoderDecoder.copy('binary')">Copy</button>
        </div>

        <button class="binary-encoder-decoder-btn binary-encoder-decoder-btn-secondary" onclick="BinaryEncoderDecoder.clearAll()" style="margin-bottom: var(--spacing-xl);">
            Clear All
        </button>
        
        <div class="binary-encoder-decoder-related-tools">
            <h3 class="binary-encoder-decoder-related-tools-title">Related Tools</h3>
            <div class="binary-encoder-decoder-related-tools-grid">
                <a href="/p/binary-to-text.html" class="binary-encoder-decoder-related-tool-item" rel="noopener">
                    <div class="binary-encoder-decoder-related-tool-icon"><i class="fas fa-font"></i></div>
                    <div class="binary-encoder-decoder-related-tool-name">Binary to Text</div>
                </a>
                <a href="/p/text-to-binary.html" class="binary-encoder-decoder-related-tool-item" rel="noopener">
                    <div class="binary-encoder-decoder-related-tool-icon"><i class="fas fa-file-alt"></i></div>
                    <div class="binary-encoder-decoder-related-tool-name">Text to Binary</div>
                </a>
                <a href="/p/binary-to-ascii.html" class="binary-encoder-decoder-related-tool-item" rel="noopener">
                    <div class="binary-encoder-decoder-related-tool-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="binary-encoder-decoder-related-tool-name">Binary to ASCII</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Your All-in-One Binary Encoder and Decoder Tool</h2>
            <p>The <strong>Binary Encoder/Decoder</strong> is a comprehensive tool designed to simplify the conversion between human-readable text and machine-readable binary code. In computing, encoding is the process of converting data into a format required for a variety of processing needs, while decoding is the reverse process. Our tool handles both functions seamlessly, making it an indispensable asset for developers, students, and anyone curious about the digital world's foundational language.</p>
            <p>Use the encoder to see how words and sentences are represented as sequences of 0s and 1s. Use the decoder to translate cryptic binary strings back into meaningful text. This dual functionality is perfect for tasks like data analysis, programming assignments, or simply learning the principles of character encoding like ASCII and UTF-8.</p>
            
            <h3>How to Use the Binary Encoder/Decoder</h3>
            <ol>
                <li><strong>To Encode:</strong> Enter your plain text into the "Text Input" box. Click the "Encode (Text to Binary)" button. The binary equivalent will instantly appear in the "Binary Input / Output" box below.</li>
                <li><strong>To Decode:</strong> Enter your binary string into the "Binary Input / Output" box. The tool is smart enough to handle binary with or without spaces. Click the "Decode (Binary to Text)" button. The readable text will appear in the "Text Input" box above.</li>
                <li><strong>Options:</strong> Before encoding, you can choose to add spaces between each 8-bit byte for better readability.</li>
            </ol>
        
            <h3>Frequently Asked Questions About the Binary Encoder/Decoder</h3>
            <h4>How to decode binary code?</h4>
            <p>To decode binary code into text, you first group the binary digits into 8-bit chunks (bytes). Each byte corresponds to a number in the ASCII table. You convert each byte to its decimal value and then find the character that matches that value. Our tool automates this entire process instantly.</p>
            
            <h4>How to encode binary code?</h4>
            <p>To encode text into binary, you take each character from the text, find its corresponding decimal value in the ASCII character set, and then convert that decimal value into an 8-bit binary number. These binary numbers are then concatenated to form the complete binary string.</p>
            
            <h4>What is the difference between binary encoder and decoder?</h4>
            <p>A binary encoder converts data from a human-readable format (like text) into a binary format (strings of 0s and 1s) for a computer to process. A binary decoder performs the reverse operation, converting binary data back into a human-readable format.</p>
            
            <h4>What is an example of a binary encoding?</h4>
            <p>A simple example is encoding the word 'Hello'. Each letter is converted to its 8-bit binary form: 'H' is <code>01001000</code>, 'e' is <code>01100101</code>, 'l' is <code>01101100</code>, and 'o' is <code>01101111</code>. The full binary encoding for 'Hello' would be <code>01001000 01100101 01101100 01101100 01101111</code>.</p>
            
            <h4>What does a binary encoder do?</h4>
            <p>A binary encoder translates information from any source format, such as text, images, or sound, into a sequence of binary digits (0s and 1s). This binary representation is the fundamental language that computers use to process, store, and transmit digital information.</p>
        </div>

        <div class="binary-encoder-decoder-features">
            <h3 class="binary-encoder-decoder-features-title">Key Features:</h3>
            <ul class="binary-encoder-decoder-features-list">
                <li class="binary-encoder-decoder-features-item">Dual-functionality: Encode & Decode</li>
                <li class="binary-encoder-decoder-features-item">Handles standard ASCII characters</li>
                <li class="binary-encoder-decoder-features-item">Intelligent binary string parsing</li>
                <li-class="binary-encoder-decoder-features-item">Customizable output formatting</li>
                <li class="binary-encoder-decoder-features-item">Clear error messages for invalid input</li>
                <li class="binary-encoder-decoder-features-item">One-click copy for easy sharing</li>
                <li class="binary-encoder-decoder-features-item">Real-time processing</li>
                <li class="binary-encoder-decoder-features-item">Mobile-friendly and responsive</li>
            </ul>
        </div>
    </div>

    <div class="binary-encoder-decoder-notification" id="binaryEncoderDecoderNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                textInput: document.getElementById('encoderDecoderTextInput'),
                binaryInput: document.getElementById('encoderDecoderBinaryInput'),
                notification: document.getElementById('binaryEncoderDecoderNotification'),
                addSpacesCheckbox: document.getElementById('addSpaces')
            };

            window.BinaryEncoderDecoder = {
                encode() {
                    const text = elements.textInput.value;
                    if (!text.trim()) {
                        elements.binaryInput.value = 'Please enter text to encode.';
                        return;
                    }

                    const addSpaces = elements.addSpacesCheckbox.checked;
                    let binaryResult = [];
                    for (let i = 0; i < text.length; i++) {
                        const binaryChar = text[i].charCodeAt(0).toString(2).padStart(8, '0');
                        binaryResult.push(binaryChar);
                    }
                    elements.binaryInput.value = binaryResult.join(addSpaces ? ' ' : '');
                },

                decode() {
                    const binary = elements.binaryInput.value.replace(/\s/g, '');
                    if (!binary.trim()) {
                        elements.textInput.value = 'Please enter binary code to decode.';
                        return;
                    }

                    if (/[^01]/.test(binary)) {
                        elements.textInput.value = 'Error: Input contains non-binary characters.';
                        return;
                    }

                    if (binary.length % 8 !== 0) {
                        elements.textInput.value = 'Error: Binary string length is not a multiple of 8.';
                        return;
                    }

                    let textResult = '';
                    for (let i = 0; i < binary.length; i += 8) {
                        const byte = binary.substr(i, 8);
                        const decimal = parseInt(byte, 2);
                        textResult += String.fromCharCode(decimal);
                    }
                    elements.textInput.value = textResult;
                },

                clearAll() {
                    elements.textInput.value = '';
                    elements.binaryInput.value = '';
                },

                copy(type) {
                    const el = (type === 'text') ? elements.textInput : elements.binaryInput;
                    const textToCopy = el.value;

                    if (!textToCopy || textToCopy.includes('Please enter') || textToCopy.includes('Error:')) {
                        return;
                    }

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(textToCopy).then(() => this.showNotification());
                    } else {
                        this.fallbackCopy(textToCopy);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };
        })();
    </script>
</body>
</html>