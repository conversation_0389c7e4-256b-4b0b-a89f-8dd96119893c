<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HEX to Octal Converter - Free Online Tool</title>
    <meta name="description" content="Easily convert hexadecimal (base-16) numbers to octal (base-8) online. Our free HEX to Octal converter is fast, accurate, and simple to use for any programming or educational task.">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free HEX to Octal Converter - Convert Base-16 to Base-8 Online",
        "description": "Easily convert hexadecimal (base-16) numbers to octal (base-8) online. Our free HEX to Octal converter is fast, accurate, and simple to use for any programming or educational task.",
        "url": "https://www.webtoolskit.org/p/hex-to-octal.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-19",
        "dateModified": "2025-06-19",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "HEX to Octal Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert HEX to Octal" },
            { "@type": "CopyAction", "name": "Copy Octal Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert from HEX to Octal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The most common manual method is to convert the hexadecimal number to binary first, and then convert the binary number to octal. First, convert each hex digit to its 4-bit binary equivalent. Then, group the resulting binary string into sets of 3 bits (starting from the right) and convert each 3-bit group into its octal digit equivalent."
          }
        },
        {
          "@type": "Question",
          "name": "What is the octal equivalent of the hex value 1A?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The octal equivalent of the hex value 1A is 32. To get this, convert 1A to binary: 1 becomes 0001 and A becomes 1010, so you have 00011010. Now, group these bits into sets of three from the right: 011 and 010. 011 in binary is 3 in octal, and 010 is 2 in octal. Combining these gives you 32."
          }
        },
        {
          "@type": "Question",
          "name": "Can you convert HEX to Octal directly?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "While technically possible, there is no simple, direct arithmetic method for converting HEX to Octal in the way you might convert from decimal. The standard and most straightforward method always involves using binary as an intermediate step, as both hexadecimal (base-16) and octal (base-8) are powers of two, making binary conversion very clean."
          }
        },
        {
          "@type": "Question",
          "name": "What is the fastest way to convert HEX to Octal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The fastest and most error-free method is to use a reliable online tool like this HEX to Octal Converter. It performs the conversion instantly, handles large numbers, and eliminates the need for manual, multi-step calculations, saving you time and ensuring accuracy."
          }
        },
        {
          "@type": "Question",
          "name": "Where are HEX and Octal number systems used?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Hexadecimal (HEX) is widely used in computing for representing memory addresses, color codes in web design (e.g., #FFFFFF), and MAC addresses. Octal is less common today but is still used in some computing contexts, most notably for file permissions in Unix and Linux operating systems (e.g., chmod 755)."
          }
        }
      ]
    }
    </script>


    <style>
        /* HEX to Octal Widget - Simplified & Template Compatible */
        .hex-to-octal-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .hex-to-octal-widget-container * { box-sizing: border-box; }

        .hex-to-octal-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hex-to-octal-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .hex-to-octal-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .hex-to-octal-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .hex-to-octal-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .hex-to-octal-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .hex-to-octal-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .hex-to-octal-btn:hover { transform: translateY(-2px); }

        .hex-to-octal-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .hex-to-octal-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .hex-to-octal-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .hex-to-octal-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .hex-to-octal-btn-success {
            background-color: #10b981;
            color: white;
        }

        .hex-to-octal-btn-success:hover {
            background-color: #059669;
        }

        .hex-to-octal-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .hex-to-octal-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .hex-to-octal-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .hex-to-octal-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .hex-to-octal-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .hex-to-octal-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .hex-to-octal-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .hex-to-octal-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .hex-to-octal-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .hex-to-octal-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .hex-to-octal-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="octal-to-hex"] .hex-to-octal-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="hex-to-decimal"] .hex-to-octal-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="hex-to-binary"] .hex-to-octal-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .hex-to-octal-related-tool-item:hover .hex-to-octal-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="octal-to-hex"]:hover .hex-to-octal-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="hex-to-decimal"]:hover .hex-to-octal-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="hex-to-binary"]:hover .hex-to-octal-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .hex-to-octal-related-tool-item { box-shadow: none; border: none; }
        .hex-to-octal-related-tool-item:hover { box-shadow: none; border: none; }
        .hex-to-octal-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .hex-to-octal-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .hex-to-octal-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .hex-to-octal-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .hex-to-octal-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .hex-to-octal-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .hex-to-octal-related-tool-item:hover .hex-to-octal-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .hex-to-octal-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .hex-to-octal-widget-title { font-size: 1.875rem; }
            .hex-to-octal-buttons { flex-direction: column; }
            .hex-to-octal-btn { flex: none; }
            .hex-to-octal-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .hex-to-octal-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .hex-to-octal-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .hex-to-octal-related-tool-name { font-size: 0.875rem; }
            .hex-to-octal-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .hex-to-octal-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .hex-to-octal-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .hex-to-octal-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .hex-to-octal-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .hex-to-octal-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .hex-to-octal-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .hex-to-octal-output::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="hex-to-octal-widget-container">
        <h1 class="hex-to-octal-widget-title">HEX to Octal Converter</h1>
        <p class="hex-to-octal-widget-description">
            Instantly convert hexadecimal (base-16) numbers into their octal (base-8) equivalents with our simple and accurate online tool.
        </p>
        
        <div class="hex-to-octal-input-group">
            <label for="hexToOctalInput" class="hex-to-octal-label">Enter a HEX number:</label>
            <textarea 
                id="hexToOctalInput" 
                class="hex-to-octal-textarea"
                placeholder="Enter a hexadecimal number here (e.g., 1A3)..."
                rows="4"
            ></textarea>
        </div>

        <div class="hex-to-octal-buttons">
            <button class="hex-to-octal-btn hex-to-octal-btn-primary" onclick="HexToOctalConverter.convert()">
                Convert to Octal
            </button>
            <button class="hex-to-octal-btn hex-to-octal-btn-secondary" onclick="HexToOctalConverter.clear()">
                Clear All
            </button>
            <button class="hex-to-octal-btn hex-to-octal-btn-success" onclick="HexToOctalConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="hex-to-octal-result">
            <h3 class="hex-to-octal-result-title">Octal Result:</h3>
            <div class="hex-to-octal-output" id="hexToOctalOutput">
                Your octal result will appear here...
            </div>
        </div>

        <div class="hex-to-octal-related-tools">
            <h3 class="hex-to-octal-related-tools-title">Related Tools</h3>
            <div class="hex-to-octal-related-tools-grid">
                <a href="/p/octal-to-hex.html" class="hex-to-octal-related-tool-item" rel="noopener">
                    <div class="hex-to-octal-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="hex-to-octal-related-tool-name">Octal to HEX</div>
                </a>

                <a href="/p/hex-to-decimal.html" class="hex-to-octal-related-tool-item" rel="noopener">
                    <div class="hex-to-octal-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="hex-to-octal-related-tool-name">HEX to Decimal</div>
                </a>

                <a href="/p/hex-to-binary.html" class="hex-to-octal-related-tool-item" rel="noopener">
                    <div class="hex-to-octal-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="hex-to-octal-related-tool-name">HEX to Binary</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Seamless HEX to Octal Conversion</h2>
            <p>Our <strong>HEX to Octal Converter</strong> is a powerful utility designed to bridge the gap between two important number systems in computer science: hexadecimal (base-16) and octal (base-8). While hexadecimal is widely used for memory addressing and color representation, octal holds its ground in areas like file permissions on Unix-based systems. Converting between them is a common task for programmers, students, and system administrators. This tool streamlines that process, providing quick and precise conversions without the need for complex manual calculations.</p>
            
            <h3>How to Use the HEX to Octal Converter</h3>
            <ol>
                <li><strong>Enter HEX Value:</strong> Type or paste the hexadecimal number you want to convert into the input field. The input can include digits 0-9 and letters A-F (case-insensitive).</li>
                <li><strong>Click Convert:</strong> Press the "Convert to Octal" button.</li>
                <li><strong>Get the Result:</strong> The octal equivalent will be displayed instantly in the output area, ready for you to use or copy.</li>
            </ol>
            
            <p>The conversion process typically involves an intermediate step through binary, as both hex and octal are powers of two. Our tool handles this complex logic behind the scenes, ensuring you get an accurate result every time, whether you're converting a simple value or a very large number.</p>

            <h3>Frequently Asked Questions</h3>
            
            <h4>How do you convert from HEX to Octal?</h4>
            <p>The most common manual method is to convert the hexadecimal number to binary first, and then convert the binary number to octal. First, convert each hex digit to its 4-bit binary equivalent. Then, group the resulting binary string into sets of 3 bits (starting from the right) and convert each 3-bit group into its octal digit equivalent.</p>
            
            <h4>What is the octal equivalent of the hex value 1A?</h4>
            <p>The octal equivalent of the hex value <code>1A</code> is <code>32</code>. To get this, convert <code>1A</code> to binary: <code>1</code> becomes <code>0001</code> and <code>A</code> becomes <code>1010</code>, so you have <code>00011010</code>. Now, group these bits into sets of three from the right: <code>011</code> and <code>010</code>. <code>011</code> in binary is 3 in octal, and <code>010</code> is 2 in octal. Combining these gives you 32.</p>
            
            <h4>Can you convert HEX to Octal directly?</h4>
            <p>While technically possible, there is no simple, direct arithmetic method for converting HEX to Octal in the way you might convert from decimal. The standard and most straightforward method always involves using binary as an intermediate step, as both hexadecimal (base-16) and octal (base-8) are powers of two, making binary conversion very clean.</p>
            
            <h4>What is the fastest way to convert HEX to Octal?</h4>
            <p>The fastest and most error-free method is to use a reliable online tool like this HEX to Octal Converter. It performs the conversion instantly, handles large numbers, and eliminates the need for manual, multi-step calculations, saving you time and ensuring accuracy.</p>
            
            <h4>Where are HEX and Octal number systems used?</h4>
            <p>Hexadecimal (HEX) is widely used in computing for representing memory addresses, color codes in web design (e.g., #FFFFFF), and MAC addresses. Octal is less common today but is still used in some computing contexts, most notably for file permissions in Unix and Linux operating systems (e.g., <code>chmod 755</code>).</p>
        </div>


        <div class="hex-to-octal-features">
            <h3 class="hex-to-octal-features-title">Key Features:</h3>
            <ul class="hex-to-octal-features-list">
                <li class="hex-to-octal-features-item">Fast and accurate conversions</li>
                <li class="hex-to-octal-features-item">Supports large hex numbers</li>
                <li class="hex-to-octal-features-item">Simple, intuitive interface</li>
                <li class="hex-to-octal-features-item">Case-insensitive input (a-f, A-F)</li>
                <li class="hex-to-octal-features-item">One-click copy to clipboard</li>
                <li class="hex-to-octal-features-item">Free to use and mobile-friendly</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="hex-to-octal-notification" id="hexToOctalNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // HEX to Octal Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('hexToOctalInput'),
                output: () => document.getElementById('hexToOctalOutput'),
                notification: () => document.getElementById('hexToOctalNotification')
            };

            window.HexToOctalConverter = {
                convert() {
                    const inputEl = elements.input();
                    const outputEl = elements.output();
                    let hexValue = inputEl.value.trim();

                    if (!hexValue) {
                        outputEl.textContent = 'Please enter a hexadecimal number.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }

                    // Sanitize input to only allow valid hex characters
                    const sanitizedHex = hexValue.replace(/[^0-9a-fA-F]/g, '');
                    if (!sanitizedHex) {
                        outputEl.textContent = 'Invalid input. Please enter a valid HEX number (0-9, A-F).';
                        outputEl.style.color = '#dc2626';
                        return;
                    }

                    outputEl.style.color = '';
                    
                    try {
                        // Use BigInt for large number support. Prepend '0x' for BigInt to recognize it as hex.
                        const decimalBigInt = BigInt('0x' + sanitizedHex);
                        const octalResult = decimalBigInt.toString(8);
                        outputEl.textContent = octalResult;
                    } catch (error) {
                        outputEl.textContent = `Error: Input is invalid or too large to process.`;
                        outputEl.style.color = '#dc2626';
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your octal result will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your octal result will appear here...', 'Please enter a hexadecimal number.', 'Invalid input. Please enter a valid HEX number (0-9, A-F).'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        HexToOctalConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>