<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML Beautifier - Free Online HTML Formatter Tool</title>
    <meta name="description" content="Instantly format and beautify messy HTML code with our free online HTML Beautifier. Clean up your code, improve readability, and choose your preferred indentation style.">
    <meta name="keywords" content="html beautifier, html formatter, pretty print html, format html, clean html code, online html beautifier">
    <link rel="canonical" href="https://www.webtoolskit.org/p/html-beautifier.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareApplication"],
        "name": "HTML Beautifier - Free Online HTML Formatter",
        "description": "Instantly format and beautify messy HTML code with our free online HTML Beautifier. Clean up your code, improve readability, and choose your preferred indentation style.",
        "url": "https://www.webtoolskit.org/p/html-beautifier.html",
        "applicationCategory": "DeveloperTool",
        "operatingSystem": "Any",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-26",
        "dateModified": "2025-06-26",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "HTML Beautifier",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Beautify HTML" },
            { "@type": "CopyAction", "name": "Copy Formatted HTML" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is an HTML beautifier?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "An HTML beautifier, also known as an HTML formatter or pretty printer, is a tool that automatically reformats messy, unindented, or poorly structured HTML code into a clean, readable format. It adds consistent indentation, line breaks, and spacing, making the code easier for developers to read, debug, and maintain."
          }
        },
        {
          "@type": "Question",
          "name": "How do you beautify HTML code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using our online tool is simple: 1. Paste your messy HTML code into the input field. 2. Select your desired indentation style (e.g., 2 spaces, 4 spaces, or tabs). 3. Click the 'Beautify HTML' button. The tool will instantly generate the clean, formatted code in the output box, ready for you to copy."
          }
        },
        {
          "@type": "Question",
          "name": "Why is it important to format HTML code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Well-formatted HTML is crucial for several reasons. It significantly improves code readability, which helps developers quickly understand the document structure. This makes debugging easier, as nested elements and closing tags are clearly visible. It also promotes consistency in team projects, ensuring everyone works with a clean and standardized codebase."
          }
        },
        {
          "@type": "Question",
          "name": "Can an HTML beautifier fix broken HTML?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "An HTML beautifier primarily focuses on formatting and structure, not validation or repair. It can make syntax errors (like mismatched or unclosed tags) more obvious by highlighting incorrect nesting through indentation, but it will not automatically fix them. For fixing broken code, you should use an HTML validator tool."
          }
        },
        {
          "@type": "Question",
          "name": "What is the best online HTML beautifier?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The 'best' tool often depends on specific needs, but a great online HTML beautifier should be fast, reliable, and offer customization options. Our tool is designed to provide a seamless experience with features like adjustable indentation and one-click copying. It's an excellent choice for developers, students, and designers who need a quick and effective way to clean up their HTML code for free."
          }
        }
      ]
    }
    </script>

    <style>
        /* HTML Beautifier Widget - Simplified & Template Compatible */
        .html-beautifier-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .html-beautifier-widget-container * { box-sizing: border-box; }

        .html-beautifier-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .html-beautifier-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .html-beautifier-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .html-beautifier-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 150px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .html-beautifier-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .html-beautifier-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .html-beautifier-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .html-beautifier-select {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-size: 0.95rem;
            cursor: pointer;
        }
        
        .html-beautifier-select:focus {
             outline: 2px solid var(--primary-color);
             outline-offset: 2px;
        }

        .html-beautifier-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
            white-space: nowrap;
        }

        .html-beautifier-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .html-beautifier-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .html-beautifier-btn:hover { transform: translateY(-2px); }

        .html-beautifier-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .html-beautifier-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .html-beautifier-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .html-beautifier-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .html-beautifier-btn-success {
            background-color: #10b981;
            color: white;
        }

        .html-beautifier-btn-success:hover {
            background-color: #059669;
        }

        .html-beautifier-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .html-beautifier-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .html-beautifier-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 150px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
        }

        .html-beautifier-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .html-beautifier-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .html-beautifier-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .html-beautifier-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .html-beautifier-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
        }

        .html-beautifier-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .html-beautifier-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .html-beautifier-related-tool-icon {
            width: 72px; height: 72px; margin: 0 auto var(--spacing-sm); border-radius: 18px; display: flex; align-items: center; justify-content: center; font-size: 2.25rem; color: white; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15); border: none;
        }
        
        a[href*="html-minifier"] .html-beautifier-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }
        a[href*="css-beautifier"] .html-beautifier-related-tool-icon { background: linear-gradient(145deg, #0EA5E9, #0284C7); }
        a[href*="javascript-beautifier"] .html-beautifier-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }

        .html-beautifier-related-tool-item:hover .html-beautifier-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="html-minifier"]:hover .html-beautifier-related-tool-icon { background: linear-gradient(145deg, #7a7dfa, #5c55fa); }
        a[href*="css-beautifier"]:hover .html-beautifier-related-tool-icon { background: linear-gradient(145deg, #2eb6fa, #1295d8); }
        a[href*="javascript-beautifier"]:hover .html-beautifier-related-tool-icon { background: linear-gradient(145deg, #fa5b5b, #e73737); }
        
        .html-beautifier-related-tool-item { box-shadow: none; border: none; }
        .html-beautifier-related-tool-item:hover { box-shadow: none; border: none; }
        .html-beautifier-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .html-beautifier-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .html-beautifier-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .html-beautifier-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .html-beautifier-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .html-beautifier-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .html-beautifier-related-tool-item:hover .html-beautifier-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .html-beautifier-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .html-beautifier-widget-title { font-size: 1.875rem; }
            .html-beautifier-buttons { flex-direction: column; }
            .html-beautifier-btn { flex: none; }
            .html-beautifier-options { grid-template-columns: 1fr; }
            .html-beautifier-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .html-beautifier-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .html-beautifier-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .html-beautifier-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .html-beautifier-features-list { columns: 1; }
            .html-beautifier-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .html-beautifier-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .html-beautifier-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .html-beautifier-related-tool-name { font-size: 0.75rem; }
        }
        
        [data-theme="dark"] .html-beautifier-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .html-beautifier-btn:focus, .html-beautifier-select:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .html-beautifier-output::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="html-beautifier-widget-container">
        <h1 class="html-beautifier-widget-title">HTML Beautifier</h1>
        <p class="html-beautifier-widget-description">
            Effortlessly clean up and format your messy HTML code. Our online tool makes your code readable, organized, and easy to debug with just one click.
        </p>
        
        <div class="html-beautifier-input-group">
            <label for="htmlBeautifierInput" class="html-beautifier-label">Enter your HTML code:</label>
            <textarea 
                id="htmlBeautifierInput" 
                class="html-beautifier-textarea"
                placeholder="<!DOCTYPE html><html><head><title>My Page</title></head><body><h1>Welcome</h1><p>This is my website.</p></body></html>"
                rows="8"
            ></textarea>
        </div>

        <div class="html-beautifier-options">
            <div class="html-beautifier-option">
                <label for="indentationStyle" class="html-beautifier-option-label">Indentation:</label>
                <select id="indentationStyle" class="html-beautifier-select">
                    <option value="  ">2 Spaces</option>
                    <option value="    " selected>4 Spaces</option>
                    <option value="\t">Tabs</option>
                </select>
            </div>
        </div>

        <div class="html-beautifier-buttons">
            <button class="html-beautifier-btn html-beautifier-btn-primary" onclick="HTMLBeautifier.beautify()">
                Beautify HTML
            </button>
            <button class="html-beautifier-btn html-beautifier-btn-secondary" onclick="HTMLBeautifier.clear()">
                Clear All
            </button>
            <button class="html-beautifier-btn html-beautifier-btn-success" onclick="HTMLBeautifier.copy()">
                Copy Result
            </button>
        </div>

        <div class="html-beautifier-result">
            <h3 class="html-beautifier-result-title">Formatted HTML:</h3>
            <div class="html-beautifier-output" id="htmlBeautifierOutput">
                Your formatted HTML will appear here...
            </div>
        </div>

        <div class="html-beautifier-related-tools">
            <h3 class="html-beautifier-related-tools-title">Related Tools</h3>
            <div class="html-beautifier-related-tools-grid">
                <a href="/p/html-minifier.html" class="html-beautifier-related-tool-item" rel="noopener">
                    <div class="html-beautifier-related-tool-icon">
                        <i class="fas fa-compress-alt"></i>
                    </div>
                    <div class="html-beautifier-related-tool-name">HTML Minifier</div>
                </a>
                <a href="/p/css-beautifier.html" class="html-beautifier-related-tool-item" rel="noopener">
                    <div class="html-beautifier-related-tool-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="html-beautifier-related-tool-name">CSS Beautifier</div>
                </a>
                <a href="/p/javascript-beautifier.html" class="html-beautifier-related-tool-item" rel="noopener">
                    <div class="html-beautifier-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="html-beautifier-related-tool-name">JavaScript Beautifier</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Transform Your Code with Our Online HTML Formatter</h2>
            <p>Tired of wading through messy, unreadable HTML? Our <strong>HTML Beautifier</strong> is a free online tool designed to bring order to chaos. With a single click, it transforms jumbled code into a perfectly formatted, indented, and human-friendly structure. This process, often called "pretty printing," is essential for developers, students, and anyone working with HTML, as it dramatically improves code clarity and maintainability.</p>
            <p>Whether you're debugging a complex layout, collaborating with a team, or just want to standardize your code, a reliable HTML formatter is indispensable. Our tool handles nested tags, attributes, and content, applying consistent spacing and line breaks to reveal the true structure of your document. This not only makes your code look professional but also makes it significantly easier to spot errors like mismatched tags or improper nesting.</p>
            
            <h3>How to Use the HTML Beautifier</h3>
            <ol>
                <li><strong>Paste Your Code:</strong> Copy your unformatted HTML and paste it into the input area above.</li>
                <li><strong>Choose Indentation:</strong> Select your preferred indentation style—2 spaces, 4 spaces, or tabs—from the options menu.</li>
                <li><strong>Beautify and Copy:</strong> Click the "Beautify HTML" button. Your clean, formatted code will appear instantly in the result box, ready to be copied and used.</li>
            </ol>
        
            <h2 style="margin-top: var(--spacing-xl);">Frequently Asked Questions About HTML Beautifiers</h2>
            
            <h4>What is an HTML beautifier?</h4>
            <p>An HTML beautifier, also known as an HTML formatter or pretty printer, is a tool that automatically reformats messy, unindented, or poorly structured HTML code into a clean, readable format. It adds consistent indentation, line breaks, and spacing, making the code easier for developers to read, debug, and maintain.</p>
            
            <h4>How do you beautify HTML code?</h4>
            <p>Using our online tool is simple: 1. Paste your messy HTML code into the input field. 2. Select your desired indentation style (e.g., 2 spaces, 4 spaces, or tabs). 3. Click the 'Beautify HTML' button. The tool will instantly generate the clean, formatted code in the output box, ready for you to copy.</p>
            
            <h4>Why is it important to format HTML code?</h4>
            <p>Well-formatted HTML is crucial for several reasons. It significantly improves code readability, which helps developers quickly understand the document structure. This makes debugging easier, as nested elements and closing tags are clearly visible. It also promotes consistency in team projects, ensuring everyone works with a clean and standardized codebase.</p>
            
            <h4>Can an HTML beautifier fix broken HTML?</h4>
            <p>An HTML beautifier primarily focuses on formatting and structure, not validation or repair. It can make syntax errors (like mismatched or unclosed tags) more obvious by highlighting incorrect nesting through indentation, but it will not automatically fix them. For fixing broken code, you should use an HTML validator tool.</p>

            <h4>What is the best online HTML beautifier?</h4>
            <p>The 'best' tool often depends on specific needs, but a great online HTML beautifier should be fast, reliable, and offer customization options. Our tool is designed to provide a seamless experience with features like adjustable indentation and one-click copying. It's an excellent choice for developers, students, and designers who need a quick and effective way to clean up their HTML code for free.</p>
        </div>

        <div class="html-beautifier-features">
            <h3 class="html-beautifier-features-title">Key Features:</h3>
            <ul class="html-beautifier-features-list">
                <li class="html-beautifier-features-item">Customizable Indentation</li>
                <li class="html-beautifier-features-item">One-Click Formatting</li>
                <li class="html-beautifier-features-item">Improves Code Readability</li>
                <li class="html-beautifier-features-item">Helps Identify Errors</li>
                <li class="html-beautifier-features-item">Completely Free to Use</li>
                <li class="html-beautifier-features-item">Copy to Clipboard</li>
                <li class="html-beautifier-features-item">Responsive & Fast</li>
                <li class="html-beautifier-features-item">No Server-Side Processing</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="html-beautifier-notification" id="htmlBeautifierNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('htmlBeautifierInput'),
                output: () => document.getElementById('htmlBeautifierOutput'),
                notification: () => document.getElementById('htmlBeautifierNotification'),
                indentStyle: () => document.getElementById('indentationStyle')
            };

            window.HTMLBeautifier = {
                beautify() {
                    const input = elements.input();
                    const output = elements.output();
                    const rawHtml = input.value;

                    if (!rawHtml.trim()) {
                        output.textContent = 'Please enter HTML code to beautify.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const indentChar = elements.indentStyle().value;

                    try {
                        const result = this.formatHtml(rawHtml, indentChar);
                        output.textContent = result;
                    } catch (error) {
                        output.textContent = `Error: ${error.message}`;
                        output.style.color = '#dc2626';
                    }
                },

                formatHtml(html, indentString) {
                    let indentLevel = 0;
                    let formattedHtml = '';
                    const selfClosingTags = ['area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'];

                    html = html.replace(/>\s+</g, '><').trim();
                    const tokens = html.split(/(<[^>]+>)/).filter(t => t.trim() !== '');

                    let inPreTag = false;

                    tokens.forEach(token => {
                        const trimmedToken = token.trim();
                        if (trimmedToken.startsWith('<')) { // It's a tag
                            if (trimmedToken.toLowerCase().startsWith('</pre')) {
                                inPreTag = false;
                            }
                            
                            if (inPreTag) {
                                formattedHtml += token;
                                return;
                            }

                            if (trimmedToken.startsWith('</')) { // Closing tag
                                indentLevel = Math.max(0, indentLevel - 1);
                                formattedHtml += '\n' + indentString.repeat(indentLevel) + token;
                            } else { // Opening or self-closing tag
                                const tagName = (trimmedToken.match(/<([^\s>]+)/) || [])[1];
                                if (formattedHtml.length > 0 && !formattedHtml.endsWith('\n')) {
                                    formattedHtml += '\n';
                                }
                                formattedHtml += indentString.repeat(indentLevel) + token;
                                if (tagName && !selfClosingTags.includes(tagName.toLowerCase()) && !trimmedToken.endsWith('/>')) {
                                    indentLevel++;
                                }
                            }
                            
                            if (trimmedToken.toLowerCase().startsWith('<pre')) {
                                inPreTag = true;
                            }

                        } else { // It's text content
                            if (inPreTag) {
                                formattedHtml += token;
                            } else {
                                if (formattedHtml.length > 0 && !formattedHtml.endsWith('\n')) {
                                    formattedHtml += '\n';
                                }
                                formattedHtml += indentString.repeat(indentLevel) + token;
                            }
                        }
                    });

                    return formattedHtml.trim();
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your formatted HTML will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your formatted HTML will appear here...', 'Please enter HTML code to beautify.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        HTMLBeautifier.beautify();
                    }
                });
            });
        })();
    </script>
</body>
</html>