<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free RGB to HEX Converter - Convert RGB Colors to HEX Online</title>
    <meta name="description" content="Convert RGB color values to HEX color codes instantly with our free RGB to HEX Converter. Perfect for web design, CSS development, and graphic design projects.">
    <meta name="keywords" content="rgb to hex, rgb to hex converter, color converter, hex color code, css colors, web design colors, color conversion">
    <link rel="canonical" href="https://www.webtoolskit.org/p/rgb-to-hex-converter.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free RGB to HEX Converter - Convert RGB Colors to HEX Online",
        "description": "Convert RGB color values to HEX color codes instantly with our free RGB to HEX Converter. Perfect for web design, CSS development, and graphic design projects.",
        "url": "https://www.webtoolskit.org/p/rgb-to-hex-converter.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "RGB to HEX Converter",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "RGB to HEX conversion",
                "Real-time color preview",
                "CSS color utility",
                "Web design tool",
                "Color code generation"
            ]
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert RGB to HEX" },
            { "@type": "CopyAction", "name": "Copy HEX Code" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I convert RGB to HEX color code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Enter your RGB values (Red, Green, Blue) in the input fields above, each ranging from 0-255. Our converter will instantly display the corresponding HEX color code that you can copy and use in your CSS, HTML, or design projects."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between RGB and HEX colors?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "RGB uses decimal values (0-255) for Red, Green, and Blue components, while HEX uses hexadecimal notation (#RRGGBB) with values 00-FF. Both represent the same colors but in different formats - RGB is more intuitive, while HEX is more compact for web use."
          }
        },
        {
          "@type": "Question",
          "name": "How do I find the HEX code for an RGB color?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Simply input your RGB values into our converter above. For example, RGB(255, 0, 0) converts to #FF0000 (red). The tool provides instant conversion with a live color preview to verify your results."
          }
        },
        {
          "@type": "Question",
          "name": "Can I convert RGB values to HEX for CSS?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, our converter is perfect for CSS development. It converts RGB values to the standard HEX format used in CSS stylesheets. You can copy the generated HEX code directly into your CSS color properties."
          }
        },
        {
          "@type": "Question",
          "name": "What RGB values make white in HEX?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "White in RGB is (255, 255, 255) which converts to #FFFFFF in HEX. This represents maximum intensity for all three color channels (Red, Green, Blue), creating pure white."
          }
        }
      ]
    }
    </script>

    <style>
        /* RGB to HEX Converter Widget - Simplified & Template Compatible */
        .rgb-hex-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .rgb-hex-widget-container * { box-sizing: border-box; }

        .rgb-hex-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .rgb-hex-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .rgb-hex-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .rgb-hex-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
        }

        .rgb-hex-field {
            display: flex;
            flex-direction: column;
        }

        .rgb-hex-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
            text-align: center;
        }

        .rgb-hex-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            text-align: center;
        }

        .rgb-hex-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .rgb-hex-color-preview {
            width: 100%;
            height: 80px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            margin: var(--spacing-lg) 0;
            background-color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #000000;
            text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }

        .rgb-hex-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .rgb-hex-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .rgb-hex-btn:hover { transform: translateY(-2px); }

        .rgb-hex-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .rgb-hex-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .rgb-hex-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .rgb-hex-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .rgb-hex-btn-success {
            background-color: #10b981;
            color: white;
        }

        .rgb-hex-btn-success:hover {
            background-color: #059669;
        }

        .rgb-hex-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .rgb-hex-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .rgb-hex-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 1.25rem;
            font-weight: 600;
            text-align: center;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .rgb-hex-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .rgb-hex-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        @media (max-width: 768px) {
            .rgb-hex-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .rgb-hex-widget-title { font-size: 1.875rem; }
            .rgb-hex-buttons { flex-direction: column; }
            .rgb-hex-btn { flex: none; }
            .rgb-hex-inputs { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .rgb-hex-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .rgb-hex-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .rgb-hex-output::selection { background-color: var(--primary-color); color: white; }

        .rgb-hex-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="hex-to-rgb"] .rgb-hex-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="color-converter"] .rgb-hex-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }
        a[href*="password-generator"] .rgb-hex-related-tool-icon { background: linear-gradient(145deg, #0EA5E9, #0284C7); }

        .rgb-hex-related-tool-item:hover .rgb-hex-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="hex-to-rgb"]:hover .rgb-hex-related-tool-icon { background: linear-gradient(145deg, #9373f7, #8a4ff0); }
        a[href*="color-converter"]:hover .rgb-hex-related-tool-icon { background: linear-gradient(145deg, #7c7cf8, #6366f1); }
        a[href*="password-generator"]:hover .rgb-hex-related-tool-icon { background: linear-gradient(145deg, #38bdf8, #0ea5e9); }

        .rgb-hex-related-tool-item { box-shadow: none; border: none; }
        .rgb-hex-related-tool-item:hover { box-shadow: none; border: none; }
        .rgb-hex-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .rgb-hex-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .rgb-hex-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .rgb-hex-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .rgb-hex-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .rgb-hex-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .rgb-hex-related-tool-item:hover .rgb-hex-related-tool-name { color: var(--primary-color); }

        .rgb-hex-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .rgb-hex-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .rgb-hex-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .rgb-hex-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .rgb-hex-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .rgb-hex-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .rgb-hex-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .rgb-hex-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .rgb-hex-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .rgb-hex-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .rgb-hex-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .rgb-hex-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .rgb-hex-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .rgb-hex-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="rgb-hex-widget-container">
        <h1 class="rgb-hex-widget-title">RGB to HEX Converter</h1>
        <p class="rgb-hex-widget-description">
            Convert RGB color values to HEX color codes instantly. Perfect for web design, CSS development, and graphic design projects.
        </p>

        <form class="rgb-hex-form">
            <div class="rgb-hex-inputs">
                <div class="rgb-hex-field">
                    <label for="redValue" class="rgb-hex-label">Red (0-255)</label>
                    <input
                        type="number"
                        id="redValue"
                        class="rgb-hex-input"
                        min="0"
                        max="255"
                        value="255"
                        placeholder="0-255"
                    />
                </div>
                <div class="rgb-hex-field">
                    <label for="greenValue" class="rgb-hex-label">Green (0-255)</label>
                    <input
                        type="number"
                        id="greenValue"
                        class="rgb-hex-input"
                        min="0"
                        max="255"
                        value="0"
                        placeholder="0-255"
                    />
                </div>
                <div class="rgb-hex-field">
                    <label for="blueValue" class="rgb-hex-label">Blue (0-255)</label>
                    <input
                        type="number"
                        id="blueValue"
                        class="rgb-hex-input"
                        min="0"
                        max="255"
                        value="0"
                        placeholder="0-255"
                    />
                </div>
            </div>

            <div class="rgb-hex-color-preview" id="colorPreview">
                Color Preview
            </div>
        </form>

        <div class="rgb-hex-buttons">
            <button class="rgb-hex-btn rgb-hex-btn-primary" onclick="RGBtoHEXConverter.convert()">
                Convert to HEX
            </button>
            <button class="rgb-hex-btn rgb-hex-btn-secondary" onclick="RGBtoHEXConverter.clear()">
                Clear Values
            </button>
            <button class="rgb-hex-btn rgb-hex-btn-success" onclick="RGBtoHEXConverter.copy()">
                Copy HEX Code
            </button>
        </div>

        <div class="rgb-hex-result">
            <h3 class="rgb-hex-result-title">HEX Color Code:</h3>
            <div class="rgb-hex-output" id="hexOutput">#FF0000</div>
        </div>

        <div class="rgb-hex-related-tools">
            <h3 class="rgb-hex-related-tools-title">Related Tools</h3>
            <div class="rgb-hex-related-tools-grid">
                <a href="/p/hex-to-rgb.html" class="rgb-hex-related-tool-item" rel="noopener">
                    <div class="rgb-hex-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="rgb-hex-related-tool-name">HEX to RGB</div>
                </a>

                <a href="/p/color-converter.html" class="rgb-hex-related-tool-item" rel="noopener">
                    <div class="rgb-hex-related-tool-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="rgb-hex-related-tool-name">Color Converter</div>
                </a>

                <a href="/p/password-generator.html" class="rgb-hex-related-tool-item" rel="noopener">
                    <div class="rgb-hex-related-tool-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="rgb-hex-related-tool-name">Password Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional RGB to HEX Converter for Web Design</h2>
            <p>Our <strong>RGB to HEX Converter</strong> transforms RGB color values into HEX color codes instantly, making it essential for web developers, graphic designers, and CSS enthusiasts. Whether you're working on website styling, creating digital artwork, or developing user interfaces, our tool provides accurate color conversion with real-time preview.</p>
            <p>Perfect for converting colors from design software that uses RGB values to web-compatible HEX codes. The tool features live color preview, instant conversion, and easy copy functionality to streamline your design workflow and ensure color consistency across your projects.</p>

            <h3>How to Use the RGB to HEX Converter</h3>
            <ol>
                <li><strong>Enter RGB Values:</strong> Input your Red, Green, and Blue values (0-255) in the respective fields.</li>
                <li><strong>View Live Preview:</strong> See the color preview update in real-time as you adjust values.</li>
                <li><strong>Convert to HEX:</strong> Click "Convert to HEX" to generate the corresponding HEX color code.</li>
                <li><strong>Copy and Use:</strong> Click "Copy HEX Code" to copy the result for use in your CSS or design projects.</li>
            </ol>

            <h3>Frequently Asked Questions About RGB to HEX Conversion</h3>

            <h4>How do I convert RGB to HEX color code?</h4>
            <p>Enter your RGB values (Red, Green, Blue) in the input fields above, each ranging from 0-255. Our converter will instantly display the corresponding HEX color code that you can copy and use in your CSS, HTML, or design projects.</p>

            <h4>What is the difference between RGB and HEX colors?</h4>
            <p>RGB uses decimal values (0-255) for Red, Green, and Blue components, while HEX uses hexadecimal notation (#RRGGBB) with values 00-FF. Both represent the same colors but in different formats - RGB is more intuitive, while HEX is more compact for web use.</p>

            <h4>How do I find the HEX code for an RGB color?</h4>
            <p>Simply input your RGB values into our converter above. For example, RGB(255, 0, 0) converts to #FF0000 (red). The tool provides instant conversion with a live color preview to verify your results.</p>

            <h4>Can I convert RGB values to HEX for CSS?</h4>
            <p>Yes, our converter is perfect for CSS development. It converts RGB values to the standard HEX format used in CSS stylesheets. You can copy the generated HEX code directly into your CSS color properties.</p>

            <h4>What RGB values make white in HEX?</h4>
            <p>White in RGB is (255, 255, 255) which converts to #FFFFFF in HEX. This represents maximum intensity for all three color channels (Red, Green, Blue), creating pure white.</p>
        </div>

        <div class="rgb-hex-features">
            <h3 class="rgb-hex-features-title">Key Features:</h3>
            <ul class="rgb-hex-features-list">
                <li class="rgb-hex-features-item" style="margin-bottom: 0.3em;">Real-time RGB to HEX Conversion</li>
                <li class="rgb-hex-features-item" style="margin-bottom: 0.3em;">Live Color Preview</li>
                <li class="rgb-hex-features-item" style="margin-bottom: 0.3em;">CSS Color Utility</li>
                <li class="rgb-hex-features-item" style="margin-bottom: 0.3em;">Web Design Tool</li>
                <li class="rgb-hex-features-item" style="margin-bottom: 0.3em;">One-Click Copy to Clipboard</li>
                <li class="rgb-hex-features-item" style="margin-bottom: 0.3em;">Mobile-Friendly Interface</li>
                <li class="rgb-hex-features-item">100% Free and Accurate</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="rgb-hex-notification" id="rgbHexNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                redInput: () => document.getElementById('redValue'),
                greenInput: () => document.getElementById('greenValue'),
                blueInput: () => document.getElementById('blueValue'),
                colorPreview: () => document.getElementById('colorPreview'),
                hexOutput: () => document.getElementById('hexOutput'),
                notification: () => document.getElementById('rgbHexNotification')
            };

            function validateRGBValue(value) {
                const num = parseInt(value);
                if (isNaN(num)) return 0;
                return Math.max(0, Math.min(255, num));
            }

            function componentToHex(c) {
                const hex = c.toString(16);
                return hex.length === 1 ? "0" + hex : hex;
            }

            function rgbToHex(r, g, b) {
                return "#" + componentToHex(r) + componentToHex(g) + componentToHex(b);
            }

            function getContrastColor(r, g, b) {
                // Calculate luminance using the relative luminance formula
                const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
                return luminance > 0.5 ? '#000000' : '#ffffff';
            }

            function updateColorPreview() {
                const r = validateRGBValue(elements.redInput().value);
                const g = validateRGBValue(elements.greenInput().value);
                const b = validateRGBValue(elements.blueInput().value);

                const hexColor = rgbToHex(r, g, b);
                const rgbColor = `rgb(${r}, ${g}, ${b})`;
                const contrastColor = getContrastColor(r, g, b);

                const preview = elements.colorPreview();
                preview.style.backgroundColor = rgbColor;
                preview.style.color = contrastColor;
                preview.textContent = `RGB(${r}, ${g}, ${b})`;

                // Update HEX output
                elements.hexOutput().textContent = hexColor.toUpperCase();

                return hexColor.toUpperCase();
            }

            window.RGBtoHEXConverter = {
                convert() {
                    updateColorPreview();
                },

                clear() {
                    elements.redInput().value = '0';
                    elements.greenInput().value = '0';
                    elements.blueInput().value = '0';
                    updateColorPreview();
                },

                copy() {
                    const text = elements.hexOutput().textContent;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Real-time conversion as user types
                const inputs = [elements.redInput(), elements.greenInput(), elements.blueInput()];

                inputs.forEach(input => {
                    input.addEventListener('input', function() {
                        // Validate and constrain input values
                        let value = parseInt(this.value);
                        if (isNaN(value)) value = 0;
                        if (value < 0) value = 0;
                        if (value > 255) value = 255;
                        this.value = value;

                        updateColorPreview();
                    });

                    input.addEventListener('blur', function() {
                        // Ensure valid value on blur
                        if (this.value === '' || isNaN(parseInt(this.value))) {
                            this.value = '0';
                            updateColorPreview();
                        }
                    });
                });

                // Initialize with default values
                updateColorPreview();

                // Enter key shortcut
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        RGBtoHEXConverter.convert();
                    }
                });

                // Add some preset color buttons for quick testing
                const presetColors = [
                    { name: 'Red', r: 255, g: 0, b: 0 },
                    { name: 'Green', r: 0, g: 255, b: 0 },
                    { name: 'Blue', r: 0, g: 0, b: 255 },
                    { name: 'White', r: 255, g: 255, b: 255 },
                    { name: 'Black', r: 0, g: 0, b: 0 }
                ];

                // You can uncomment this section to add preset color buttons
                /*
                const presetsContainer = document.createElement('div');
                presetsContainer.style.cssText = 'display: flex; gap: 8px; margin-top: 16px; flex-wrap: wrap; justify-content: center;';

                presetColors.forEach(color => {
                    const button = document.createElement('button');
                    button.textContent = color.name;
                    button.style.cssText = 'padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; background: white; cursor: pointer; font-size: 12px;';
                    button.addEventListener('click', function() {
                        elements.redInput().value = color.r;
                        elements.greenInput().value = color.g;
                        elements.blueInput().value = color.b;
                        updateColorPreview();
                    });
                    presetsContainer.appendChild(button);
                });

                elements.colorPreview().parentNode.insertBefore(presetsContainer, elements.colorPreview().nextSibling);
                */
            });
        })();
    </script>
</body>
</html>
