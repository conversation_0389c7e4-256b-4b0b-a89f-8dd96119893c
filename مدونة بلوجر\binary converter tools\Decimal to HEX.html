<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Decimal to Hex Converter - Free Online Tool</title>
    <meta name="description" content="Instantly convert decimal (base-10) numbers to hexadecimal (base-16) with our free online converter. Includes formatting options like letter casing and '0x' prefix.">
    <meta name="keywords" content="decimal to hex, decimal to hex converter, convert decimal to hex, base 10 to base 16, decimal converter, online tool">
    <link rel="canonical" href="https://www.webtoolskit.org/p/decimal-to-hex.html" />
    
    <!-- Page-specific Open Graph Meta Tags -->
    <meta property="og:url" content="https://www.webtoolskit.org/p/decimal-to-hex.html" />
    <meta property="og:title" content="Free Decimal to Hex Converter - Convert Decimal to Hexadecimal Online" />
    <meta property="og:description" content="A fast and accurate tool to convert any decimal value into its hexadecimal equivalent. Simple, free, and perfect for developers and students." />
    <meta property="og:image" content="https://www.webtoolskit.org/images/binary-og.jpg" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Decimal to Hex Converter - Convert Decimal (Base-10) to Hex (Base-16)",
        "description": "Instantly convert decimal (base-10) numbers to hexadecimal (base-16) with our free online converter. Includes formatting options like letter casing and '0x' prefix.",
        "url": "https://www.webtoolskit.org/p/decimal-to-hex.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-19",
        "dateModified": "2025-06-19",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Decimal to Hex Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Decimal to Hex" },
            { "@type": "CopyAction", "name": "Copy Hex Value" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I convert decimal to hexadecimal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert a decimal to a hexadecimal, you repeatedly divide the decimal number by 16. The remainders of each division, read from bottom to top, form the hexadecimal number. For remainders 10 through 15, you use the letters A through F. Our tool automates this entire process for you."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert 100.25 decimal to hexadecimal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "This is a two-part conversion. First, convert the integer part: 100 divided by 16 is 6 with a remainder of 4, so the integer part is 64. Then, convert the fractional part: 0.25 multiplied by 16 is 4.0. So, 100.25 in decimal is 64.4 in hexadecimal. Note that our tool primarily handles integer conversions."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert 122 to hexadecimal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert 122 to hex, divide 122 by 16, which gives 7 with a remainder of 10. The remainder 10 is represented by the letter 'A' in hexadecimal. Therefore, 122 in decimal is 7A in hex."
          }
        },
        {
          "@type": "Question",
          "name": "What is 55 decimal to hexadecimal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert 55 to hex, divide 55 by 16, which is 3 with a remainder of 7. Reading the quotient and then the remainder gives you the hex value. So, 55 in decimal is 37 in hexadecimal."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert base 10 to base 16?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Converting from base-10 (decimal) to base-16 (hexadecimal) involves the division method. You divide the base-10 number by 16 and note the remainder. You continue dividing the quotient by 16 until the quotient is 0. The hexadecimal result is the sequence of remainders from last to first."
          }
        }
      ]
    }
    </script>

    <style>
        /* Decimal to Hex Widget - Simplified & Template Compatible */
        .decimal-to-hex-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .decimal-to-hex-widget-container * { box-sizing: border-box; }

        .decimal-to-hex-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .decimal-to-hex-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .decimal-to-hex-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .decimal-to-hex-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .decimal-to-hex-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }
        
        .decimal-to-hex-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .decimal-to-hex-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .decimal-to-hex-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }
        
        .decimal-to-hex-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .decimal-to-hex-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .decimal-to-hex-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .decimal-to-hex-btn:hover { transform: translateY(-2px); }
        .decimal-to-hex-btn-primary { background-color: var(--primary-color); color: white; }
        .decimal-to-hex-btn-primary:hover { background-color: var(--secondary-color); box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4); }
        .decimal-to-hex-btn-secondary { background-color: var(--background-color-alt); color: var(--text-color); border: 1px solid var(--border-color); }
        .decimal-to-hex-btn-secondary:hover { background-color: var(--border-color); }
        .decimal-to-hex-btn-success { background-color: #10b981; color: white; }
        .decimal-to-hex-btn-success:hover { background-color: #059669; }

        .decimal-to-hex-result { background-color: var(--background-color-alt); border-radius: var(--border-radius-lg); padding: var(--spacing-lg); border-left: 4px solid var(--primary-color); border: 1px solid var(--border-color); }
        .decimal-to-hex-result-title { margin: 0 0 var(--spacing-md) 0; color: var(--text-color); font-size: 1.25rem; font-weight: 700; }
        .decimal-to-hex-output { background-color: var(--card-bg); border: 2px solid var(--border-color); border-radius: var(--border-radius-md); padding: var(--spacing-md) var(--spacing-lg); font-family: 'SF Mono', Monaco, monospace; font-size: var(--font-size-base); word-break: break-all; min-height: 60px; color: var(--text-color); line-height: 1.5; }

        .decimal-to-hex-notification { position: fixed; top: 20px; right: 20px; background-color: #10b981; color: white; padding: var(--spacing-md) var(--spacing-lg); border-radius: var(--border-radius-md); font-weight: 600; z-index: 10000; transform: translateX(400px); transition: var(--transition-base); }
        .decimal-to-hex-notification.show { transform: translateX(0); }
        
        .seo-content { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); color: var(--text-color-light); line-height: 1.7; }
        .seo-content h2, .seo-content h3, .seo-content h4 { color: var(--text-color); margin-bottom: var(--spacing-md); }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code { background-color: var(--background-color-alt); padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 6px; font-family: 'SF Mono', Monaco, monospace; }

        .decimal-to-hex-features { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .decimal-to-hex-features-title { color: var(--text-color); margin-bottom: var(--spacing-md); font-size: 1.25rem; font-weight: 700; }
        .decimal-to-hex-features-list { list-style: none; padding: 0; margin: 0; columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; }
        .decimal-to-hex-features-item { padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg); color: var(--text-color-light); position: relative; margin-bottom: 0.3em; }
        .decimal-to-hex-features-item:before { content: ""; position: absolute; left: 0; top: calc(var(--spacing-sm) + 2px); width: 12px; height: 6px; border-left: 2px solid #10b981; border-bottom: 2px solid #10b981; transform: rotate(-45deg); }
        
        .decimal-to-hex-related-tool-icon { width: 72px; height: 72px; margin: 0 auto var(--spacing-sm); border-radius: 18px; display: flex; align-items: center; justify-content: center; font-size: 2.25rem; color: white; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15); border: none; }
        a[href*="hex-to-decimal"] .decimal-to-hex-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="decimal-to-binary"] .decimal-to-hex-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="text-to-hex"] .decimal-to-hex-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }
        .decimal-to-hex-related-tool-item:hover .decimal-to-hex-related-tool-icon { transform: translateY(-5px) scale(1.05); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15); }
        a[href*="hex-to-decimal"]:hover .decimal-to-hex-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="decimal-to-binary"]:hover .decimal-to-hex-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="text-to-hex"]:hover .decimal-to-hex-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .decimal-to-hex-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .decimal-to-hex-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .decimal-to-hex-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .decimal-to-hex-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .decimal-to-hex-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .decimal-to-hex-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .decimal-to-hex-related-tool-item:hover .decimal-to-hex-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .decimal-to-hex-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .decimal-to-hex-widget-title { font-size: 1.875rem; }
            .decimal-to-hex-buttons { flex-direction: column; }
            .decimal-to-hex-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .decimal-to-hex-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .decimal-to-hex-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .decimal-to-hex-related-tool-name { font-size: 0.875rem; }
        }
        @media (max-width: 600px) { 
            .decimal-to-hex-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } 
        }
        @media (max-width: 480px) {
            .decimal-to-hex-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .decimal-to-hex-related-tool-item { padding: var(--spacing-sm); }
            .decimal-to-hex-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .decimal-to-hex-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="decimal-to-hex-widget-container">
        <h1 class="decimal-to-hex-widget-title">Decimal to Hex Converter</h1>
        <p class="decimal-to-hex-widget-description">
            Convert any decimal (base-10) number to its hexadecimal (base-16) equivalent with our fast and easy-to-use tool.
        </p>
        
        <div class="decimal-to-hex-input-group">
            <label for="decimalToHexInput" class="decimal-to-hex-label">Enter Decimal Value:</label>
            <textarea 
                id="decimalToHexInput" 
                class="decimal-to-hex-textarea"
                placeholder="Type your decimal number here (e.g., 255)..."
                rows="4"
            ></textarea>
        </div>
        
        <div class="decimal-to-hex-options">
            <div class="decimal-to-hex-option">
                <input type="checkbox" id="hexAddPrefix" class="decimal-to-hex-checkbox">
                <label for="hexAddPrefix" class="decimal-to-hex-option-label">Add "0x" prefix</label>
            </div>
            <div class="decimal-to-hex-option">
                <input type="checkbox" id="hexUppercase" class="decimal-to-hex-checkbox" checked>
                <label for="hexUppercase" class="decimal-to-hex-option-label">Use uppercase letters (A-F)</label>
            </div>
        </div>

        <div class="decimal-to-hex-buttons">
            <button class="decimal-to-hex-btn decimal-to-hex-btn-primary" onclick="DecimalToHexConverter.convert()">
                Convert to Hex
            </button>
            <button class="decimal-to-hex-btn decimal-to-hex-btn-secondary" onclick="DecimalToHexConverter.clear()">
                Clear All
            </button>
            <button class="decimal-to-hex-btn decimal-to-hex-btn-success" onclick="DecimalToHexConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="decimal-to-hex-result">
            <h3 class="decimal-to-hex-result-title">Hexadecimal Value:</h3>
            <div class="decimal-to-hex-output" id="decimalToHexOutput">
                Your hex value will appear here...
            </div>
        </div>
        
        <div class="decimal-to-hex-related-tools">
            <h3 class="decimal-to-hex-related-tools-title">Related Tools</h3>
            <div class="decimal-to-hex-related-tools-grid">
                <a href="/p/hex-to-decimal.html" class="decimal-to-hex-related-tool-item" rel="noopener">
                    <div class="decimal-to-hex-related-tool-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="decimal-to-hex-related-tool-name">Hex to Decimal</div>
                </a>
                <a href="/p/decimal-to-binary.html" class="decimal-to-hex-related-tool-item" rel="noopener">
                    <div class="decimal-to-hex-related-tool-icon"><i class="fas fa-calculator"></i></div>
                    <div class="decimal-to-hex-related-tool-name">Decimal to Binary</div>
                </a>
                <a href="/p/text-to-hex.html" class="decimal-to-hex-related-tool-item" rel="noopener">
                    <div class="decimal-to-hex-related-tool-icon"><i class="fas fa-font"></i></div>
                    <div class="decimal-to-hex-related-tool-name">Text to Hex</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>From Everyday Numbers to Programmer's Code</h2>
            <p>Our <strong>Decimal to Hex Converter</strong> is an essential tool for bridging the gap between the decimal (base-10) number system we use daily and the hexadecimal (base-16) system that is fundamental to computer programming and web development. While we think in terms of tens, computers operate on a binary system, which is conveniently represented in a more compact form by hexadecimal numbers.</p>
            <p>This conversion is crucial in many technical fields. Web designers use hex codes to define colors (e.g., decimal 255 becomes <code>FF</code> in hex), while programmers use them to work with memory addresses, bitmasks, and data representation. This tool removes the need for manual calculations, which involve repeated division and tracking remainders, giving you an accurate and instant conversion. It's the perfect utility for students learning number systems and professionals who need to convert values on the fly.</p>
            
            <h3>How to Use the Decimal to Hex Converter</h3>
            <ol>
                <li><strong>Enter Decimal Number:</strong> Type the base-10 number you want to convert into the input field.</li>
                <li><strong>Select Formatting Options:</strong> Choose whether to add the standard <code>0x</code> prefix or use uppercase (A-F) or lowercase (a-f) letters for your result.</li>
                <li><strong>Convert:</strong> Click the "Convert to Hex" button to get the hexadecimal equivalent instantly.</li>
                <li><strong>Copy and Go:</strong> Use the "Copy Result" button to easily transfer the hex code to your clipboard.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Decimal to Hex Conversion</h3>
            <h4>How do I convert decimal to hexadecimal?</h4>
            <p>To convert a decimal to a hexadecimal, you repeatedly divide the decimal number by 16. The remainders of each division, read from bottom to top, form the hexadecimal number. For remainders 10 through 15, you use the letters A through F. Our tool automates this entire process for you.</p>
            
            <h4>How to convert 100.25 decimal to hexadecimal?</h4>
            <p>This is a two-part conversion. First, convert the integer part: 100 divided by 16 is 6 with a remainder of 4, so the integer part is 64. Then, convert the fractional part: 0.25 multiplied by 16 is 4.0. So, 100.25 in decimal is 64.4 in hexadecimal. Note that our tool primarily handles integer conversions.</p>
            
            <h4>How to convert 122 to hexadecimal?</h4>
            <p>To convert 122 to hex, divide 122 by 16, which gives 7 with a remainder of 10. The remainder 10 is represented by the letter 'A' in hexadecimal. Therefore, 122 in decimal is 7A in hex.</p>
            
            <h4>What is 55 decimal to hexadecimal?</h4>
            <p>To convert 55 to hex, divide 55 by 16, which is 3 with a remainder of 7. Reading the quotient and then the remainder gives you the hex value. So, 55 in decimal is 37 in hexadecimal.</p>
            
            <h4>How do you convert base 10 to base 16?</h4>
            <p>Converting from base-10 (decimal) to base-16 (hexadecimal) involves the division method. You divide the base-10 number by 16 and note the remainder. You continue dividing the quotient by 16 until the quotient is 0. The hexadecimal result is the sequence of remainders from last to first.</p>
        </div>

        <div class="decimal-to-hex-features">
            <h3 class="decimal-to-hex-features-title">Key Features:</h3>
            <ul class="decimal-to-hex-features-list">
                <li class="decimal-to-hex-features-item">Instant decimal-to-hex conversion</li>
                <li class="decimal-to-hex-features-item">Supports large decimal numbers</li>
                <li class="decimal-to-hex-features-item">Option to add '0x' prefix</li>
                <li class="decimal-to-hex-features-item">Uppercase and lowercase options</li>
                <li class="decimal-to-hex-features-item">Clear error handling for invalid input</li>
                <li class="decimal-to-hex-features-item">One-click copy to clipboard</li>
                <li class="decimal-to-hex-features-item">Mobile-friendly and responsive</li>
                <li class="decimal-to-hex-features-item">Completely free and web-based</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="decimal-to-hex-notification" id="decimalToHexNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('decimalToHexInput'),
                output: () => document.getElementById('decimalToHexOutput'),
                notification: () => document.getElementById('decimalToHexNotification'),
                addPrefix: () => document.getElementById('hexAddPrefix'),
                uppercase: () => document.getElementById('hexUppercase')
            };

            window.DecimalToHexConverter = {
                convert() {
                    const input = elements.input();
                    const output = elements.output();
                    const decimal = input.value.trim();

                    if (!decimal) {
                        output.textContent = 'Please enter a decimal value to convert.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        addPrefix: elements.addPrefix().checked,
                        uppercase: elements.uppercase().checked
                    };
                    
                    const result = this.processDecimal(decimal, options);
                    
                    if (result.startsWith('Invalid')) {
                        output.style.color = '#dc2626';
                    }
                    output.textContent = result;
                },

                processDecimal(decimal, options) {
                    const num = parseInt(decimal, 10);
                    
                    if (isNaN(num)) {
                        return 'Invalid decimal input. Please enter a valid number.';
                    }
                    
                    let hexString = num.toString(16);
                    
                    if (options.uppercase) {
                        hexString = hexString.toUpperCase();
                    }
                    
                    if (options.addPrefix) {
                        hexString = '0x' + hexString;
                    }
                    
                    return hexString;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your hex value will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text.includes('will appear here') || text.includes('Please enter') || text.includes('Invalid')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        DecimalToHexConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>