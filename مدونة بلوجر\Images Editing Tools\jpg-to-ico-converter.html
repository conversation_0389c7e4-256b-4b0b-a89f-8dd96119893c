<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JPG to ICO Converter - Free Online Icon Creator</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free JPG to ICO Converter - Create Icons Online",
        "description": "Convert JPG images to ICO format instantly. Free online tool for creating Windows icons with multiple sizes and professional quality output.",
        "url": "https://www.webtoolskit.org/p/jpg-to-ico.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-22",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "JPG to ICO Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert JPG to ICO" },
            { "@type": "DownloadAction", "name": "Download ICO Icon" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Can you convert JPG to ICO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can easily convert JPG to ICO using our free online converter. The conversion transforms JPG images into Windows-compatible ICO format, perfect for creating application icons, favicons, and desktop shortcuts."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert JPG to ICO in Photoshop?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "In Photoshop, resize your JPG to icon dimensions (16x16, 32x32, 48x48), then use 'Save As' and select ICO format. However, our online converter is simpler and handles multiple sizes automatically without requiring expensive software."
          }
        },
        {
          "@type": "Question",
          "name": "How do I change something to an ICO file?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Upload your image to our converter, select desired icon sizes (16x16, 32x32, 48x48, 64x64), and download the ICO file. The converter automatically optimizes the image for different icon sizes and creates a multi-resolution ICO file."
          }
        },
        {
          "@type": "Question",
          "name": "What size should ICO files be?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Standard ICO sizes are 16x16, 32x32, 48x48, and 64x64 pixels. Modern ICO files often include multiple sizes in one file. 16x16 for small icons, 32x32 for desktop shortcuts, 48x48 for folder views, and 64x64 for high-DPI displays."
          }
        },
        {
          "@type": "Question",
          "name": "Is ICO better than PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "ICO is better for Windows icons as it supports multiple sizes in one file and has native Windows support. PNG offers better compression and quality but requires separate files for different sizes. Use ICO for Windows applications and PNG for web favicons."
          }
        }
      ]
    }
    </script>

    <style>
        /* JPG to ICO Widget - Simplified & Template Compatible */
        .jpg-ico-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .jpg-ico-widget-container * { box-sizing: border-box; }

        .jpg-ico-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .jpg-ico-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .jpg-ico-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-lg);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
        }

        .jpg-ico-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .jpg-ico-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .jpg-ico-upload-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .jpg-ico-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .jpg-ico-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .jpg-ico-file-input {
            display: none;
        }

        .jpg-ico-size-options {
            background-color: var(--background-color-alt);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            display: none;
        }

        .jpg-ico-size-title {
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }

        .jpg-ico-size-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-sm);
        }

        .jpg-ico-size-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .jpg-ico-size-checkbox {
            margin: 0;
        }

        .jpg-ico-size-label {
            color: var(--text-color-light);
            font-size: 0.875rem;
            cursor: pointer;
        }

        .jpg-ico-preview {
            display: none;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .jpg-ico-preview-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .jpg-ico-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: flex-start;
        }

        .jpg-ico-preview-item {
            flex: 1;
            text-align: center;
        }

        .jpg-ico-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .jpg-ico-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-sm);
        }

        .jpg-ico-file-info {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .jpg-ico-icon-sizes {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: center;
            margin-bottom: var(--spacing-sm);
        }

        .jpg-ico-icon-preview {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            padding: var(--spacing-xs);
            background-color: var(--card-bg);
        }

        .jpg-ico-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .jpg-ico-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .jpg-ico-btn:hover { transform: translateY(-2px); }

        .jpg-ico-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .jpg-ico-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .jpg-ico-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .jpg-ico-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .jpg-ico-btn-success {
            background-color: #10b981;
            color: white;
        }

        .jpg-ico-btn-success:hover {
            background-color: #059669;
        }

        .jpg-ico-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .jpg-ico-btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .jpg-ico-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="ico-to-png"] .jpg-ico-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .jpg-ico-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="ico-converter"] .jpg-ico-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .jpg-ico-related-tool-item:hover .jpg-ico-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="ico-to-png"]:hover .jpg-ico-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .jpg-ico-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="ico-converter"]:hover .jpg-ico-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .jpg-ico-related-tool-item { box-shadow: none; border: none; }
        .jpg-ico-related-tool-item:hover { box-shadow: none; border: none; }
        .jpg-ico-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .jpg-ico-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .jpg-ico-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .jpg-ico-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .jpg-ico-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .jpg-ico-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .jpg-ico-related-tool-item:hover .jpg-ico-related-tool-name { color: var(--primary-color); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .jpg-ico-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .jpg-ico-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .jpg-ico-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .jpg-ico-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .jpg-ico-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .jpg-ico-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .jpg-ico-widget-title { font-size: 1.875rem; }
            .jpg-ico-buttons { flex-direction: column; }
            .jpg-ico-btn { flex: none; }
            .jpg-ico-preview-content { flex-direction: column; }
            .jpg-ico-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .jpg-ico-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .jpg-ico-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .jpg-ico-related-tool-name { font-size: 0.875rem; }
            .jpg-ico-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
            .jpg-ico-size-grid { grid-template-columns: repeat(2, 1fr); }
        }

        @media (max-width: 480px) {
            .jpg-ico-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .jpg-ico-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .jpg-ico-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .jpg-ico-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .jpg-ico-upload-area:hover { background-color: var(--card-bg); }
        .jpg-ico-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
    </style>
</head>
<body>
    <div class="jpg-ico-widget-container">
        <h1 class="jpg-ico-widget-title">JPG to ICO Converter</h1>
        <p class="jpg-ico-widget-description">
            Convert JPG images to ICO format for Windows icons, favicons, and desktop shortcuts. Create multi-size icons with professional quality.
        </p>
        
        <div class="jpg-ico-upload-area" id="uploadArea">
            <div class="jpg-ico-upload-icon">📁</div>
            <div class="jpg-ico-upload-text">Click to select JPG image or drag & drop</div>
            <div class="jpg-ico-upload-subtext">Supports JPG/JPEG files (Max 10MB)</div>
            <input type="file" id="fileInput" class="jpg-ico-file-input" accept=".jpg,.jpeg">
        </div>

        <div class="jpg-ico-size-options" id="sizeOptions">
            <div class="jpg-ico-size-title">Select Icon Sizes (Multiple sizes recommended)</div>
            <div class="jpg-ico-size-grid">
                <div class="jpg-ico-size-option">
                    <input type="checkbox" id="size16" class="jpg-ico-size-checkbox" value="16" checked>
                    <label for="size16" class="jpg-ico-size-label">16x16 (Small icons)</label>
                </div>
                <div class="jpg-ico-size-option">
                    <input type="checkbox" id="size32" class="jpg-ico-size-checkbox" value="32" checked>
                    <label for="size32" class="jpg-ico-size-label">32x32 (Desktop shortcuts)</label>
                </div>
                <div class="jpg-ico-size-option">
                    <input type="checkbox" id="size48" class="jpg-ico-size-checkbox" value="48" checked>
                    <label for="size48" class="jpg-ico-size-label">48x48 (Folder views)</label>
                </div>
                <div class="jpg-ico-size-option">
                    <input type="checkbox" id="size64" class="jpg-ico-size-checkbox" value="64">
                    <label for="size64" class="jpg-ico-size-label">64x64 (High-DPI)</label>
                </div>
            </div>
        </div>

        <div class="jpg-ico-preview" id="previewSection">
            <h3 class="jpg-ico-preview-title">Preview & Icon Sizes</h3>
            <div class="jpg-ico-preview-content">
                <div class="jpg-ico-preview-item">
                    <div class="jpg-ico-preview-label">Original JPG</div>
                    <img id="originalImage" class="jpg-ico-preview-image" alt="Original JPG" />
                    <div class="jpg-ico-file-info" id="originalInfo"></div>
                </div>
                <div class="jpg-ico-preview-item">
                    <div class="jpg-ico-preview-label">ICO Preview</div>
                    <div class="jpg-ico-icon-sizes" id="iconSizes"></div>
                    <div class="jpg-ico-file-info" id="convertedInfo"></div>
                </div>
            </div>
        </div>

        <div class="jpg-ico-buttons">
            <button id="convertBtn" class="jpg-ico-btn jpg-ico-btn-primary" disabled>
                Create ICO Icon
            </button>
            <button id="downloadBtn" class="jpg-ico-btn jpg-ico-btn-success" disabled>
                Download ICO
            </button>
            <button id="resetBtn" class="jpg-ico-btn jpg-ico-btn-secondary">
                Reset
            </button>
        </div>

        <div class="jpg-ico-related-tools">
            <h3 class="jpg-ico-related-tools-title">Related Tools</h3>
            <div class="jpg-ico-related-tools-grid">
                <a href="https://www.webtoolskit.org/p/ico-to-png.html" class="jpg-ico-related-tool-item" rel="noopener">
                    <div class="jpg-ico-related-tool-icon">
                        <i class="fas fa-undo-alt"></i>
                    </div>
                    <div class="jpg-ico-related-tool-name">ICO to PNG</div>
                </a>

                <a href="https://www.webtoolskit.org/p/image-converter_23.html" class="jpg-ico-related-tool-item" rel="noopener">
                    <div class="jpg-ico-related-tool-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="jpg-ico-related-tool-name">Image Converter</div>
                </a>

                <a href="https://www.webtoolskit.org/p/ico-converter.html" class="jpg-ico-related-tool-item" rel="noopener">
                    <div class="jpg-ico-related-tool-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="jpg-ico-related-tool-name">ICO Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert JPG to ICO Online - Professional Icon Creator</h2>
            <p>Our <strong>JPG to ICO Converter</strong> transforms JPG images into Windows-compatible ICO format, perfect for creating application icons, favicons, and desktop shortcuts. The tool supports multiple icon sizes in a single ICO file, ensuring compatibility across different Windows contexts.</p>

            <p>Converting JPG to ICO is essential for Windows application development, website favicons, and desktop customization. Our tool creates multi-resolution ICO files that look crisp at any size, from small taskbar icons to large desktop shortcuts.</p>

            <h3>Frequently Asked Questions About JPG to ICO Conversion</h3>

            <h4>Can you convert JPG to ICO?</h4>
            <p>Yes, you can easily convert JPG to ICO using our free online converter. The conversion transforms JPG images into Windows-compatible ICO format, perfect for creating application icons, favicons, and desktop shortcuts.</p>

            <h4>How to convert JPG to ICO in Photoshop?</h4>
            <p>In Photoshop, resize your JPG to icon dimensions (16x16, 32x32, 48x48), then use 'Save As' and select ICO format. However, our online converter is simpler and handles multiple sizes automatically without requiring expensive software.</p>

            <h4>How do I change something to an ICO file?</h4>
            <p>Upload your image to our converter, select desired icon sizes (16x16, 32x32, 48x48, 64x64), and download the ICO file. The converter automatically optimizes the image for different icon sizes and creates a multi-resolution ICO file.</p>

            <h4>What size should ICO files be?</h4>
            <p>Standard ICO sizes are 16x16, 32x32, 48x48, and 64x64 pixels. Modern ICO files often include multiple sizes in one file. 16x16 for small icons, 32x32 for desktop shortcuts, 48x48 for folder views, and 64x64 for high-DPI displays.</p>

            <h4>Is ICO better than PNG?</h4>
            <p>ICO is better for Windows icons as it supports multiple sizes in one file and has native Windows support. PNG offers better compression and quality but requires separate files for different sizes. Use ICO for Windows applications and PNG for web favicons.</p>
        </div>

        <div class="jpg-ico-features">
            <h3 class="jpg-ico-features-title">Key Features</h3>
            <ul class="jpg-ico-features-list">
                <li class="jpg-ico-features-item">Convert JPG to ICO instantly</li>
                <li class="jpg-ico-features-item">Multiple icon sizes in one file</li>
                <li class="jpg-ico-features-item">Windows-compatible output</li>
                <li class="jpg-ico-features-item">Professional icon quality</li>
                <li class="jpg-ico-features-item">Client-side processing for privacy</li>
                <li class="jpg-ico-features-item">Favicon creation support</li>
                <li class="jpg-ico-features-item">Real-time preview</li>
                <li class="jpg-ico-features-item">Custom size selection</li>
            </ul>
        </div>
    </div>

    <script>
        // JPG to ICO Converter Tool - Self-contained IIFE
        (function() {
            'use strict';

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                sizeOptions: () => document.getElementById('sizeOptions'),
                previewSection: () => document.getElementById('previewSection'),
                originalImage: () => document.getElementById('originalImage'),
                iconSizes: () => document.getElementById('iconSizes'),
                originalInfo: () => document.getElementById('originalInfo'),
                convertedInfo: () => document.getElementById('convertedInfo'),
                convertBtn: () => document.getElementById('convertBtn'),
                downloadBtn: () => document.getElementById('downloadBtn'),
                resetBtn: () => document.getElementById('resetBtn')
            };

            let originalFile = null;
            let convertedBlob = null;

            function init() {
                setupEventListeners();
            }

            function setupEventListeners() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();
                const convertBtn = elements.convertBtn();
                const downloadBtn = elements.downloadBtn();
                const resetBtn = elements.resetBtn();

                // File upload events
                uploadArea.addEventListener('click', () => fileInput.click());
                fileInput.addEventListener('change', handleFileSelect);

                // Drag and drop events
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('drop', handleDrop);

                // Button events
                convertBtn.addEventListener('click', convertImage);
                downloadBtn.addEventListener('click', downloadImage);
                resetBtn.addEventListener('click', resetTool);
            }

            function handleFileSelect(event) {
                const file = event.target.files[0];
                if (file) processFile(file);
            }

            function handleDragOver(event) {
                event.preventDefault();
                elements.uploadArea().classList.add('dragover');
            }

            function handleDragLeave(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
            }

            function handleDrop(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
                const files = event.dataTransfer.files;
                if (files.length > 0) processFile(files[0]);
            }

            function processFile(file) {
                if (!file.type.includes('jpeg') && !file.type.includes('jpg')) {
                    alert('Please select a JPG/JPEG image file.');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB.');
                    return;
                }

                originalFile = file;
                displayOriginalImage();
                elements.convertBtn().disabled = false;
                elements.sizeOptions().style.display = 'block';
            }

            function displayOriginalImage() {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const originalImage = elements.originalImage();
                    originalImage.src = e.target.result;
                    
                    const originalInfo = elements.originalInfo();
                    originalInfo.textContent = `${originalFile.name} (${formatFileSize(originalFile.size)})`;
                    
                    elements.previewSection().style.display = 'block';
                };
                reader.readAsDataURL(originalFile);
            }

            function convertImage() {
                if (!originalFile) return;

                const selectedSizes = getSelectedSizes();
                if (selectedSizes.length === 0) {
                    alert('Please select at least one icon size.');
                    return;
                }

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    // Create icon previews
                    createIconPreviews(img, selectedSizes);
                    
                    // For demonstration, we'll create a simple ICO-like file
                    // Note: True ICO format requires complex binary structure
                    // This creates a PNG that can be renamed to .ico for basic use
                    const maxSize = Math.max(...selectedSizes);
                    canvas.width = maxSize;
                    canvas.height = maxSize;
                    
                    // Draw image to fit the canvas
                    const scale = Math.min(maxSize / img.width, maxSize / img.height);
                    const scaledWidth = img.width * scale;
                    const scaledHeight = img.height * scale;
                    const x = (maxSize - scaledWidth) / 2;
                    const y = (maxSize - scaledHeight) / 2;
                    
                    ctx.drawImage(img, x, y, scaledWidth, scaledHeight);

                    canvas.toBlob((blob) => {
                        convertedBlob = blob;
                        displayConvertedInfo();
                        elements.downloadBtn().disabled = false;
                    }, 'image/png');
                };

                img.src = URL.createObjectURL(originalFile);
            }

            function getSelectedSizes() {
                const checkboxes = document.querySelectorAll('.jpg-ico-size-checkbox:checked');
                return Array.from(checkboxes).map(cb => parseInt(cb.value));
            }

            function createIconPreviews(img, sizes) {
                const iconSizes = elements.iconSizes();
                iconSizes.innerHTML = '';

                sizes.forEach(size => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = size;
                    canvas.height = size;
                    
                    // Draw scaled image
                    const scale = Math.min(size / img.width, size / img.height);
                    const scaledWidth = img.width * scale;
                    const scaledHeight = img.height * scale;
                    const x = (size - scaledWidth) / 2;
                    const y = (size - scaledHeight) / 2;
                    
                    ctx.drawImage(img, x, y, scaledWidth, scaledHeight);
                    
                    const previewDiv = document.createElement('div');
                    previewDiv.className = 'jpg-ico-icon-preview';
                    previewDiv.style.width = size + 'px';
                    previewDiv.style.height = size + 'px';
                    previewDiv.appendChild(canvas);
                    
                    const label = document.createElement('div');
                    label.textContent = `${size}x${size}`;
                    label.style.fontSize = '0.75rem';
                    label.style.textAlign = 'center';
                    label.style.marginTop = '4px';
                    
                    const container = document.createElement('div');
                    container.appendChild(previewDiv);
                    container.appendChild(label);
                    
                    iconSizes.appendChild(container);
                });
            }

            function displayConvertedInfo() {
                const convertedInfo = elements.convertedInfo();
                const fileName = originalFile.name.replace(/\.[^/.]+$/, '') + '.ico';
                const selectedSizes = getSelectedSizes();
                convertedInfo.innerHTML = `${fileName} (${formatFileSize(convertedBlob.size)})<br><small>Sizes: ${selectedSizes.join('x, ')}x pixels</small>`;
            }

            function downloadImage() {
                if (!convertedBlob) return;

                const link = document.createElement('a');
                link.href = URL.createObjectURL(convertedBlob);
                link.download = originalFile.name.replace(/\.[^/.]+$/, '') + '.ico';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            function resetTool() {
                originalFile = null;
                convertedBlob = null;
                elements.fileInput().value = '';
                elements.previewSection().style.display = 'none';
                elements.sizeOptions().style.display = 'none';
                elements.convertBtn().disabled = true;
                elements.downloadBtn().disabled = true;
                elements.iconSizes().innerHTML = '';
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', init);
        })();
    </script>
</body>
</html>