<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JPG to GIF Converter - Free Online Image Format Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free JPG to GIF Converter - Convert Images Online",
        "description": "Convert JPG images to GIF format instantly. Free online tool with web compatibility, animation support, and optimized output for graphics and simple images.",
        "url": "https://www.webtoolskit.org/p/jpg-to-gif.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-22",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "JPG to GIF Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert JPG to GIF" },
            { "@type": "DownloadAction", "name": "Download Converted GIF" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Can you convert JPG to GIF?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can easily convert JPG to GIF using our free online converter. The conversion transforms JPG images into web-compatible GIF format, though GIF is limited to 256 colors so complex photos may experience some color reduction."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert JPG to GIF on iPhone?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use our web-based converter on your iPhone by opening Safari, uploading your JPG image, and downloading the converted GIF. No app installation required - works directly in your mobile browser with full functionality."
          }
        },
        {
          "@type": "Question",
          "name": "When might you want to use a GIF instead of a JPEG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use GIF instead of JPEG for simple graphics with few colors, images requiring transparency, animations, logos, icons, or when you need maximum browser compatibility. GIF works better for graphics with solid colors and sharp edges."
          }
        },
        {
          "@type": "Question",
          "name": "How does a GIF differ from a JPG or PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "GIF is limited to 256 colors and supports animation and transparency, making it ideal for simple graphics. JPG supports millions of colors with lossy compression for photos. PNG offers lossless compression with full color support and alpha transparency."
          }
        },
        {
          "@type": "Question",
          "name": "Can a JPEG file be animated?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, JPEG files cannot be animated. JPEG is a static image format designed for photographs. For animations, you need formats like GIF (simple animations), APNG (high-quality animations), or video formats like MP4 or WebM."
          }
        }
      ]
    }
    </script>

    <style>
        /* JPG to GIF Widget - Simplified & Template Compatible */
        .jpg-gif-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .jpg-gif-widget-container * { box-sizing: border-box; }

        .jpg-gif-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .jpg-gif-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .jpg-gif-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-lg);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
        }

        .jpg-gif-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .jpg-gif-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .jpg-gif-upload-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .jpg-gif-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .jpg-gif-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .jpg-gif-file-input {
            display: none;
        }

        .jpg-gif-color-warning {
            background-color: #fef3cd;
            border: 1px solid #fecaca;
            color: #92400e;
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-lg);
            font-size: 0.875rem;
            display: none;
        }

        .jpg-gif-preview {
            display: none;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .jpg-gif-preview-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .jpg-gif-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: flex-start;
        }

        .jpg-gif-preview-item {
            flex: 1;
            text-align: center;
        }

        .jpg-gif-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .jpg-gif-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-sm);
        }

        .jpg-gif-file-info {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .jpg-gif-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .jpg-gif-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .jpg-gif-btn:hover { transform: translateY(-2px); }

        .jpg-gif-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .jpg-gif-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .jpg-gif-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .jpg-gif-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .jpg-gif-btn-success {
            background-color: #10b981;
            color: white;
        }

        .jpg-gif-btn-success:hover {
            background-color: #059669;
        }

        .jpg-gif-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .jpg-gif-btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .jpg-gif-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="png-to-gif"] .jpg-gif-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .jpg-gif-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="jpg-to-png"] .jpg-gif-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .jpg-gif-related-tool-item:hover .jpg-gif-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="png-to-gif"]:hover .jpg-gif-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .jpg-gif-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="jpg-to-png"]:hover .jpg-gif-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .jpg-gif-related-tool-item { box-shadow: none; border: none; }
        .jpg-gif-related-tool-item:hover { box-shadow: none; border: none; }
        .jpg-gif-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .jpg-gif-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .jpg-gif-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .jpg-gif-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .jpg-gif-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .jpg-gif-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .jpg-gif-related-tool-item:hover .jpg-gif-related-tool-name { color: var(--primary-color); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .jpg-gif-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .jpg-gif-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .jpg-gif-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .jpg-gif-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .jpg-gif-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .jpg-gif-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .jpg-gif-widget-title { font-size: 1.875rem; }
            .jpg-gif-buttons { flex-direction: column; }
            .jpg-gif-btn { flex: none; }
            .jpg-gif-preview-content { flex-direction: column; }
            .jpg-gif-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .jpg-gif-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .jpg-gif-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .jpg-gif-related-tool-name { font-size: 0.875rem; }
            .jpg-gif-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .jpg-gif-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .jpg-gif-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .jpg-gif-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .jpg-gif-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .jpg-gif-upload-area:hover { background-color: var(--card-bg); }
        .jpg-gif-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        [data-theme="dark"] .jpg-gif-color-warning { background-color: #451a03; border-color: #92400e; color: #fbbf24; }
    </style>
</head>
<body>
    <div class="jpg-gif-widget-container">
        <h1 class="jpg-gif-widget-title">JPG to GIF Converter</h1>
        <p class="jpg-gif-widget-description">
            Convert JPG images to GIF format for web compatibility and simple graphics. Perfect for logos, icons, and images with limited colors.
        </p>
        
        <div class="jpg-gif-upload-area" id="uploadArea">
            <div class="jpg-gif-upload-icon">📁</div>
            <div class="jpg-gif-upload-text">Click to select JPG image or drag & drop</div>
            <div class="jpg-gif-upload-subtext">Supports JPG/JPEG files (Max 10MB)</div>
            <input type="file" id="fileInput" class="jpg-gif-file-input" accept=".jpg,.jpeg">
        </div>

        <div class="jpg-gif-color-warning" id="colorWarning">
            <strong>Color Limitation Notice:</strong> GIF format is limited to 256 colors. Complex JPG photos with many colors may experience some color reduction during conversion.
        </div>

        <div class="jpg-gif-preview" id="previewSection">
            <h3 class="jpg-gif-preview-title">Preview & Comparison</h3>
            <div class="jpg-gif-preview-content">
                <div class="jpg-gif-preview-item">
                    <div class="jpg-gif-preview-label">Original JPG</div>
                    <img id="originalImage" class="jpg-gif-preview-image" alt="Original JPG" />
                    <div class="jpg-gif-file-info" id="originalInfo"></div>
                </div>
                <div class="jpg-gif-preview-item">
                    <div class="jpg-gif-preview-label">Converted GIF</div>
                    <img id="convertedImage" class="jpg-gif-preview-image" alt="Converted GIF" />
                    <div class="jpg-gif-file-info" id="convertedInfo"></div>
                </div>
            </div>
        </div>

        <div class="jpg-gif-buttons">
            <button id="convertBtn" class="jpg-gif-btn jpg-gif-btn-primary" disabled>
                Convert to GIF
            </button>
            <button id="downloadBtn" class="jpg-gif-btn jpg-gif-btn-success" disabled>
                Download GIF
            </button>
            <button id="resetBtn" class="jpg-gif-btn jpg-gif-btn-secondary">
                Reset
            </button>
        </div>

        <div class="jpg-gif-related-tools">
            <h3 class="jpg-gif-related-tools-title">Related Tools</h3>
            <div class="jpg-gif-related-tools-grid">
                <a href="https://www.webtoolskit.org/p/png-to-gif.html" class="jpg-gif-related-tool-item" rel="noopener">
                    <div class="jpg-gif-related-tool-icon">
                        <i class="fas fa-undo-alt"></i>
                    </div>
                    <div class="jpg-gif-related-tool-name">PNG to GIF</div>
                </a>

                <a href="https://www.webtoolskit.org/p/image-converter_23.html" class="jpg-gif-related-tool-item" rel="noopener">
                    <div class="jpg-gif-related-tool-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="jpg-gif-related-tool-name">Image Converter</div>
                </a>

                <a href="https://www.webtoolskit.org/p/jpg-to-png.html" class="jpg-gif-related-tool-item" rel="noopener">
                    <div class="jpg-gif-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="jpg-gif-related-tool-name">JPG to PNG</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert JPG to GIF Online - Web-Compatible Graphics</h2>
            <p>Our <strong>JPG to GIF Converter</strong> transforms JPG images into web-compatible GIF format, perfect for simple graphics, logos, and images that need broad browser support. While GIF is limited to 256 colors, it offers excellent compatibility and animation support for web graphics.</p>

            <p>Converting JPG to GIF is ideal when you need maximum web compatibility, transparency support, or when working with simple graphics that don't require millions of colors. Our tool processes conversions locally in your browser, ensuring privacy while delivering optimized results.</p>

            <h3>Frequently Asked Questions About JPG to GIF Conversion</h3>

            <h4>Can you convert JPG to GIF?</h4>
            <p>Yes, you can easily convert JPG to GIF using our free online converter. The conversion transforms JPG images into web-compatible GIF format, though GIF is limited to 256 colors so complex photos may experience some color reduction.</p>

            <h4>How to convert JPG to GIF on iPhone?</h4>
            <p>Use our web-based converter on your iPhone by opening Safari, uploading your JPG image, and downloading the converted GIF. No app installation required - works directly in your mobile browser with full functionality.</p>

            <h4>When might you want to use a GIF instead of a JPEG?</h4>
            <p>Use GIF instead of JPEG for simple graphics with few colors, images requiring transparency, animations, logos, icons, or when you need maximum browser compatibility. GIF works better for graphics with solid colors and sharp edges.</p>

            <h4>How does a GIF differ from a JPG or PNG?</h4>
            <p>GIF is limited to 256 colors and supports animation and transparency, making it ideal for simple graphics. JPG supports millions of colors with lossy compression for photos. PNG offers lossless compression with full color support and alpha transparency.</p>

            <h4>Can a JPEG file be animated?</h4>
            <p>No, JPEG files cannot be animated. JPEG is a static image format designed for photographs. For animations, you need formats like GIF (simple animations), APNG (high-quality animations), or video formats like MP4 or WebM.</p>
        </div>

        <div class="jpg-gif-features">
            <h3 class="jpg-gif-features-title">Key Features</h3>
            <ul class="jpg-gif-features-list">
                <li class="jpg-gif-features-item">Convert JPG to GIF instantly</li>
                <li class="jpg-gif-features-item">Web-compatible output</li>
                <li class="jpg-gif-features-item">Mobile browser support</li>
                <li class="jpg-gif-features-item">Transparency support</li>
                <li class="jpg-gif-features-item">Client-side processing for privacy</li>
                <li class="jpg-gif-features-item">Universal browser compatibility</li>
                <li class="jpg-gif-features-item">Real-time preview</li>
                <li class="jpg-gif-features-item">Optimized for simple graphics</li>
            </ul>
        </div>
    </div>

    <script>
        // JPG to GIF Converter Tool - Self-contained IIFE
        (function() {
            'use strict';

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                colorWarning: () => document.getElementById('colorWarning'),
                previewSection: () => document.getElementById('previewSection'),
                originalImage: () => document.getElementById('originalImage'),
                convertedImage: () => document.getElementById('convertedImage'),
                originalInfo: () => document.getElementById('originalInfo'),
                convertedInfo: () => document.getElementById('convertedInfo'),
                convertBtn: () => document.getElementById('convertBtn'),
                downloadBtn: () => document.getElementById('downloadBtn'),
                resetBtn: () => document.getElementById('resetBtn')
            };

            let originalFile = null;
            let convertedBlob = null;

            function init() {
                setupEventListeners();
            }

            function setupEventListeners() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();
                const convertBtn = elements.convertBtn();
                const downloadBtn = elements.downloadBtn();
                const resetBtn = elements.resetBtn();

                // File upload events
                uploadArea.addEventListener('click', () => fileInput.click());
                fileInput.addEventListener('change', handleFileSelect);

                // Drag and drop events
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('drop', handleDrop);

                // Button events
                convertBtn.addEventListener('click', convertImage);
                downloadBtn.addEventListener('click', downloadImage);
                resetBtn.addEventListener('click', resetTool);
            }

            function handleFileSelect(event) {
                const file = event.target.files[0];
                if (file) processFile(file);
            }

            function handleDragOver(event) {
                event.preventDefault();
                elements.uploadArea().classList.add('dragover');
            }

            function handleDragLeave(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
            }

            function handleDrop(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
                const files = event.dataTransfer.files;
                if (files.length > 0) processFile(files[0]);
            }

            function processFile(file) {
                if (!file.type.includes('jpeg') && !file.type.includes('jpg')) {
                    alert('Please select a JPG/JPEG image file.');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB.');
                    return;
                }

                originalFile = file;
                displayOriginalImage();
                elements.convertBtn().disabled = false;
                elements.colorWarning().style.display = 'block';
            }

            function displayOriginalImage() {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const originalImage = elements.originalImage();
                    originalImage.src = e.target.result;
                    
                    const originalInfo = elements.originalInfo();
                    originalInfo.textContent = `${originalFile.name} (${formatFileSize(originalFile.size)})`;
                    
                    elements.previewSection().style.display = 'block';
                };
                reader.readAsDataURL(originalFile);
            }

            function convertImage() {
                if (!originalFile) return;

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    // Convert to GIF format (note: browser support for GIF creation is limited)
                    // This will create a static GIF from the JPG
                    canvas.toBlob((blob) => {
                        convertedBlob = blob;
                        displayConvertedImage();
                        elements.downloadBtn().disabled = false;
                    }, 'image/gif');
                };

                img.src = URL.createObjectURL(originalFile);
            }

            function displayConvertedImage() {
                const convertedImage = elements.convertedImage();
                convertedImage.src = URL.createObjectURL(convertedBlob);
                
                const convertedInfo = elements.convertedInfo();
                const fileName = originalFile.name.replace(/\.[^/.]+$/, '') + '.gif';
                const sizeChange = convertedBlob.size > originalFile.size ? 'increase' : 'decrease';
                const sizeRatio = ((convertedBlob.size / originalFile.size) * 100).toFixed(1);
                convertedInfo.innerHTML = `${fileName} (${formatFileSize(convertedBlob.size)})<br><small style="color: ${sizeChange === 'increase' ? '#dc3545' : '#28a745'};">Size ${sizeChange}: ${sizeRatio}%</small>`;
            }

            function downloadImage() {
                if (!convertedBlob) return;

                const link = document.createElement('a');
                link.href = URL.createObjectURL(convertedBlob);
                link.download = originalFile.name.replace(/\.[^/.]+$/, '') + '.gif';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            function resetTool() {
                originalFile = null;
                convertedBlob = null;
                elements.fileInput().value = '';
                elements.previewSection().style.display = 'none';
                elements.colorWarning().style.display = 'none';
                elements.convertBtn().disabled = true;
                elements.downloadBtn().disabled = true;
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', init);
        })();
    </script>
</body>
</html>