<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>

  <!-- Performance and Core Web Vitals optimization -->
  <meta name="format-detection" content="telephone=no"/>
  <meta name="theme-color" content="#0047AB"/>
  <meta name="color-scheme" content="light dark"/>
  <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"/>
  <meta name="generator" content="WebToolsKit"/>
  <meta name="referrer" content="no-referrer-when-downgrade"/>

  <title>Contact Us | WebToolsKit</title>
  <meta name="description" content="Get in touch with WebToolsKit. Contact us for support, feedback, or questions about our free online tools and utilities."/>
  <meta name="keywords" content="contact webtoolskit, support, feedback, online tools help, web utilities contact"/>
  <meta name="author" content="WebToolsKit"/>
  <link rel="canonical" href="https://www.webtoolskit.org/p/contact-us.html"/>

  <!-- Enhanced Open Graph Meta Tags -->
  <meta property="og:title" content="Contact Us | WebToolsKit"/>
  <meta property="og:description" content="Get in touch with WebToolsKit. Contact us for support, feedback, or questions about our free online tools and utilities."/>
  <meta property="og:url" content="https://www.webtoolskit.org/p/contact-us.html"/>
  <meta property="og:type" content="website"/>
  <meta property="og:locale" content="en_US"/>

  <!-- Enhanced Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary"/>
  <meta name="twitter:title" content="Contact Us | WebToolsKit"/>
  <meta name="twitter:description" content="Get in touch with WebToolsKit. Contact us for support, feedback, or questions about our free online tools and utilities."/>
  <meta name="twitter:site" content="@webtoolskit"/>

  <!-- Enhanced Favicon and App Icons -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico"/>
  <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png"/>
  <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png"/>
  <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png"/>

  <!-- Performance Optimization -->
  <link rel="preconnect" href="https://fonts.googleapis.com"/>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""/>
  <link rel="preconnect" href="https://docs.google.com"/>

  <!-- Accessibility and Performance Enhancements -->
  <meta name="mobile-web-app-capable" content="yes"/>
  <meta name="apple-mobile-web-app-capable" content="yes"/>
  <meta name="apple-mobile-web-app-status-bar-style" content="default"/>
  <meta name="apple-mobile-web-app-title" content="WebToolsKit"/>
  <!-- Skip to main content for accessibility -->
  <style>
    .skip-link {
      position: absolute;
      top: -40px;
      left: 6px;
      background: var(--primary-color);
      color: white;
      padding: 8px;
      text-decoration: none;
      border-radius: 0 0 4px 4px;
      z-index: 10000;
      font-weight: 600;
    }
    .skip-link:focus {
      top: 0;
    }

    /* Screen reader only class for accessibility */
    .sr-only {
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    }

    /* Base styles matching the template */
    :root {
      --primary-color: #0047AB;
      --secondary-color: #4338ca;
      --text-color: #111827;
      --text-color-light: #4b5563;
      --background-color: #fff;
      --background-color-alt: #f3f4f6;
      --border-color: #e5e7eb;
      --card-bg: #fff;
      --font-family: 'Inter', sans-serif;
      --font-size-base: 16px;
      --line-height-base: 1.5;
      --spacing-xs: .25rem;
      --spacing-sm: .5rem;
      --spacing-md: 1rem;
      --spacing-lg: 1.5rem;
      --spacing-xl: 2rem;
      --spacing-2xl: 3rem;
      --border-radius-sm: .25rem;
      --border-radius-md: .375rem;
      --border-radius-lg: .5rem;
      --transition-base: all .3s ease;
    }

    /* Dark mode support */
    [data-theme="dark"] {
      --primary-color: #60a5fa;
      --secondary-color: #818cf8;
      --text-color: #ffffff;
      --text-color-light: #d1d5db;
      --background-color: #111827;
      --background-color-alt: #1f2937;
      --border-color: #374151;
      --card-bg: #1f2937;
    }

    /* Base styles */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: var(--font-family);
      font-size: var(--font-size-base);
      line-height: var(--line-height-base);
      color: var(--text-color);
      background-color: var(--background-color);
      padding: 0;
      margin: 0;
      width: 100%;
    }

    .container {
      width: 100%;
      max-width: 1500px; /* Increased from 1200px for maximum writing space */
      margin: 0 auto;
      padding: 0;
    }

    /* Contact page specific styles - Optimized spacing */
    .contact-page {
      padding: var(--spacing-xl) 0; /* Reduced from 2xl to minimize vertical space */
      width: 100%;
    }

    .contact-header {
      text-align: center;
      margin-bottom: var(--spacing-2xl);
      padding: 0 var(--spacing-md);
    }

    .contact-title {
      font-size: 2.5rem; /* Increased from 2rem */
      margin-bottom: var(--spacing-md); /* Increased from sm */
      color: var(--text-color);
      font-weight: 700;
      position: relative;
      display: inline-block;
    }

    .contact-title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      border-radius: 2px;
    }

    .contact-description {
      color: var(--text-color-light);
      max-width: 800px; /* Increased from 700px */
      margin: 0 auto var(--spacing-xl); /* Increased from lg */
      line-height: 1.7; /* Increased from 1.6 */
      font-size: 1.1rem; /* Added font size */
    }

    .contact-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 0 var(--spacing-xs); /* Reduced from md to minimize horizontal margins */
    }

    .contact-form-container {
      width: 100%;
      background-color: var(--card-bg);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-lg) var(--spacing-md); /* Reduced from xl to minimize vertical space */
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); /* Enhanced shadow */
      border: 1px solid var(--border-color);
      position: relative;
      overflow: hidden;
      margin-bottom: var(--spacing-xl); /* Reduced from 2xl to minimize vertical space */
    }

    .contact-form-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 5px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    }

    .contact-info {
      width: 100%;
      background-color: var(--card-bg);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-lg); /* Reduced from xl to minimize vertical space */
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); /* Enhanced shadow */
      border: 1px solid var(--border-color);
      position: relative;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: var(--spacing-md); /* Reduced from lg to minimize wasted space */
    }

    .contact-info::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 5px;
      background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
    }

    .contact-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg); /* Increased from md */
    }

    .form-group {
      margin-bottom: var(--spacing-lg); /* Increased from md */
    }

    .form-label {
      display: block;
      margin-bottom: var(--spacing-sm);
      font-weight: 600; /* Increased from 500 */
      color: var(--text-color);
      font-size: 1.05rem; /* Added font size */
    }

    .form-input,
    .form-textarea {
      width: 100%;
      padding: 1rem; /* Increased from 0.75rem */
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius-md);
      background-color: var(--background-color);
      color: var(--text-color);
      transition: var(--transition-base);
      font-family: var(--font-family);
      font-size: 1rem;
    }

    .form-input:focus,
    .form-textarea:focus {
      border-color: var(--primary-color);
      outline: none;
      box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
    }

    .form-textarea {
      min-height: 180px; /* Increased from 150px */
      resize: vertical;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.9rem 2rem; /* Increased from 0.75rem 1.5rem */
      border-radius: var(--border-radius-md);
      font-weight: 600; /* Increased from 500 */
      cursor: pointer;
      transition: var(--transition-base);
      border: none;
      font-size: 1.1rem; /* Increased from 1rem */
      text-align: center;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: #fff;
      position: relative;
      overflow: hidden;
      z-index: 1;
    }

    .btn-primary::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: var(--secondary-color);
      transition: all 0.4s ease;
      z-index: -1;
    }

    .btn-primary:hover {
      transform: translateY(-3px); /* Increased from -2px */
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    }

    .btn-primary:hover::before {
      left: 0;
    }

    .info-item {
      margin-bottom: var(--spacing-md); /* Reduced from xl */
      position: relative;
      padding-left: 45px; /* Added padding for icon */
      flex: 1 1 30%; /* Make info items take up equal space in a row */
      min-width: 250px; /* Ensure minimum width for readability */
    }

    .info-icon {
      position: absolute;
      left: 0;
      top: 0;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: rgba(0, 71, 171, 0.1);
      color: var(--primary-color);
      font-size: 1.1rem;
    }

    .info-title {
      font-size: 1.2rem; /* Increased from 1.1rem */
      font-weight: 600;
      margin-bottom: var(--spacing-sm);
      color: var(--primary-color);
    }

    .info-content {
      color: var(--text-color-light);
      line-height: 1.7; /* Increased from 1.6 */
      font-size: 1rem;
    }

    .social-icons {
      display: flex;
      margin-top: var(--spacing-md);
    }

    .social-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(0, 71, 171, 0.1);
      color: var(--primary-color);
      margin-right: var(--spacing-md);
      transition: var(--transition-base);
      font-size: 1.1rem;
    }

    .social-icon:hover {
      background-color: var(--primary-color);
      color: white;
      transform: translateY(-3px);
    }

    .success-message {
      display: none;
      padding: var(--spacing-lg); /* Increased from md */
      background-color: #d1fae5;
      border-radius: var(--border-radius-md);
      color: #065f46;
      margin-top: var(--spacing-lg); /* Increased from md */
      text-align: center;
      font-size: 1.1rem;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    /* Google Form styling */
    .google-form-wrapper {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .google-form-iframe {
      width: 100%;
      min-height: 650px;
      border: none;
      overflow: hidden;
      display: block;
    }

    /* Responsive styles */
    @media (max-width: 992px) {
      .contact-container {
        gap: var(--spacing-xl);
      }
    }

    @media (max-width: 768px) {
      .contact-title {
        font-size: 2.2rem;
      }

      .info-item {
        flex: 1 1 45%;
      }
    }

    @media (max-width: 576px) {
      .contact-form-container,
      .contact-info {
        padding: var(--spacing-lg); /* Adjusted padding */
      }

      .contact-title {
        font-size: 1.8rem;
      }

      .contact-description {
        font-size: 1rem;
      }

      .google-form-iframe {
        min-height: 750px; /* Further increased for better use of mobile space */
      }

      .info-item {
        flex: 1 1 100%;
      }
    }
  </style>
</head>
<body role="document">
  <!-- Skip to main content for accessibility -->
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <div class="container">
    <main class="contact-page" id="main-content" role="main">
      <header class="contact-header">
        <h1 class="contact-title" id="contact-title">Contact Us</h1>
        <p class="contact-description">Have a question or feedback? We'd love to hear from you. Fill out the form below and we'll get back to you as soon as possible.</p>
      </header>

      <div class="contact-container">
        <section class="contact-form-container" aria-labelledby="contact-form-title">
          <h2 id="contact-form-title" class="sr-only">Contact Form</h2>
          <div class="google-form-wrapper">
            <iframe src="https://docs.google.com/forms/d/e/1FAIpQLSdHQXKrAnNcMdl0-J7YINNjG8ENR4O2VMregtY5yfSM-RQiUQ/viewform?embedded=true"
                    width="100%"
                    height="850"
                    frameborder="0"
                    marginheight="0"
                    marginwidth="0"
                    title="WebToolsKit Contact Form - Submit your questions and feedback"
                    loading="lazy"
                    class="google-form-iframe"
                    aria-label="Contact form for WebToolsKit support and feedback">
              <p>Loading contact form... If this form doesn't load, please email us <NAME_EMAIL></p>
            </iframe>
          </div>
        </section>

        <section class="contact-info" aria-labelledby="contact-info-title">
          <h2 id="contact-info-title" class="sr-only">Contact Information</h2>
          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-envelope" aria-hidden="true"></i>
            </div>
            <h3 class="info-title">Email Us</h3>
            <p class="info-content">For general inquiries: <br>
              <a href="mailto:<EMAIL>" aria-label="Send email to WebToolsKit support"><EMAIL></a>
            </p>
          </div>

          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-clock" aria-hidden="true"></i>
            </div>
            <h3 class="info-title">Response Time</h3>
            <p class="info-content">We typically respond to all inquiries within 24-48 hours during business days.</p>
          </div>

          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-share-alt" aria-hidden="true"></i>
            </div>
            <h3 class="info-title">Follow Us</h3>
            <p class="info-content">Stay connected with us on social media for updates and new tools.</p>
            <nav class="social-icons" aria-label="Social media links">
              <a href="https://facebook.com/webtoolskit" class="social-icon" aria-label="Follow us on Facebook" rel="noopener" target="_blank">
                <i class="fab fa-facebook-f" aria-hidden="true"></i>
              </a>
              <a href="https://twitter.com/webtoolskit" class="social-icon" aria-label="Follow us on Twitter" rel="noopener" target="_blank">
                <i class="fab fa-twitter" aria-hidden="true"></i>
              </a>
              <a href="https://instagram.com/webtoolskit" class="social-icon" aria-label="Follow us on Instagram" rel="noopener" target="_blank">
                <i class="fab fa-instagram" aria-hidden="true"></i>
              </a>
              <a href="https://linkedin.com/company/webtoolskit" class="social-icon" aria-label="Connect with us on LinkedIn" rel="noopener" target="_blank">
                <i class="fab fa-linkedin-in" aria-hidden="true"></i>
              </a>
            </nav>
          </div>
        </section>
      </div>
    </main>
  </div>

  <!-- Schema.org structured data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact WebToolsKit",
    "description": "Contact page for WebToolsKit - Free Online Web Tools for Developers & Designers",
    "url": "https://webtoolskit.org/p/contact.html",
    "contactPoint": {
      "@type": "ContactPoint",
      "email": "<EMAIL>",
      "contactType": "customer service"
    }
  }
  </script>


</body>
</html>
