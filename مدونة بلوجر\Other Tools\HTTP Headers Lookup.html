<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free HTTP Headers Lookup - Check Website HTTP Response Headers Online</title>
    <meta name="description" content="Analyze HTTP response headers of any website with our free HTTP Headers Lookup tool. Check security headers, server information, and SEO-related headers instantly.">
    <meta name="keywords" content="http headers lookup, http headers checker, response headers, security headers, server headers, website headers analyzer">
    <link rel="canonical" href="https://www.webtoolskit.org/p/http-headers-lookup.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free HTTP Headers Lookup - Check Website HTTP Response Headers Online",
        "description": "Analyze HTTP response headers of any website with our free HTTP Headers Lookup tool. Check security headers, server information, and SEO-related headers instantly.",
        "url": "https://www.webtoolskit.org/p/http-headers-lookup.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "HTTP Headers Lookup",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "HTTP response header analysis",
                "Security header checking",
                "Server information lookup",
                "SEO header analysis",
                "Website header inspection"
            ]
        },
        "potentialAction": [
            { "@type": "SearchAction", "name": "Lookup HTTP Headers" },
            { "@type": "AnalyzeAction", "name": "Analyze Website Headers" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I check HTTP headers of a website?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Enter the website URL in the field above and click 'Lookup Headers'. Our tool will fetch and display all HTTP response headers including server information, security headers, caching directives, and content type information."
          }
        },
        {
          "@type": "Question",
          "name": "What are HTTP response headers and why are they important?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "HTTP response headers are metadata sent by web servers with each response. They contain important information about the server, security policies, caching instructions, content type, and more. They're crucial for SEO, security, and performance optimization."
          }
        },
        {
          "@type": "Question",
          "name": "How can I view security headers of a website?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our tool automatically identifies and highlights security-related headers like Content-Security-Policy, X-Frame-Options, Strict-Transport-Security, and X-Content-Type-Options. These headers help protect websites from various security threats."
          }
        },
        {
          "@type": "Question",
          "name": "What HTTP headers should I check for SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Important SEO headers include Status Code, Content-Type, Content-Encoding, Cache-Control, Last-Modified, ETag, and Canonical headers. These affect how search engines crawl, index, and cache your website content."
          }
        },
        {
          "@type": "Question",
          "name": "How do I analyze server response headers?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Enter any website URL and our tool will show server headers like Server type, response status, content encoding, and performance-related headers. This helps with debugging, optimization, and understanding server configuration."
          }
        }
      ]
    }
    </script>

    <style>
        /* HTTP Headers Lookup Widget - Simplified & Template Compatible */
        .http-headers-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .http-headers-widget-container * { box-sizing: border-box; }

        .http-headers-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .http-headers-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .http-headers-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .http-headers-field {
            display: flex;
            flex-direction: column;
        }

        .http-headers-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .http-headers-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .http-headers-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .http-headers-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .http-headers-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .http-headers-btn:hover { transform: translateY(-2px); }

        .http-headers-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .http-headers-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .http-headers-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .http-headers-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .http-headers-btn-success {
            background-color: #10b981;
            color: white;
        }

        .http-headers-btn-success:hover {
            background-color: #059669;
        }

        .http-headers-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .http-headers-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .http-headers-loading {
            text-align: center;
            color: var(--text-color-light);
            padding: var(--spacing-lg);
            font-style: italic;
        }

        .http-headers-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 500px;
        }

        .http-headers-header-item {
            margin-bottom: var(--spacing-sm);
            padding: var(--spacing-xs) 0;
            border-bottom: 1px solid var(--border-color);
        }

        .http-headers-header-name {
            font-weight: 600;
            color: var(--primary-color);
            display: inline-block;
            min-width: 200px;
        }

        .http-headers-header-value {
            color: var(--text-color);
            word-break: break-all;
        }

        .http-headers-status {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            text-align: center;
        }

        .http-headers-status-code {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-xs);
        }

        .status-200 { color: #10b981; }
        .status-300 { color: #f59e0b; }
        .status-400 { color: #ef4444; }
        .status-500 { color: #dc2626; }

        .http-headers-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .http-headers-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        @media (max-width: 768px) {
            .http-headers-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .http-headers-widget-title { font-size: 1.875rem; }
            .http-headers-buttons { flex-direction: column; }
            .http-headers-btn { flex: none; }
            .http-headers-header-name { min-width: 120px; display: block; }
        }

        [data-theme="dark"] .http-headers-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .http-headers-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .http-headers-output::selection { background-color: var(--primary-color); color: white; }

        .http-headers-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="whatsapp-link-generator"] .http-headers-related-tool-icon { background: linear-gradient(145deg, #25D366, #128C7E); }
        a[href*="paypal-link-generator"] .http-headers-related-tool-icon { background: linear-gradient(145deg, #0070BA, #005EA6); }
        a[href*="password-generator"] .http-headers-related-tool-icon { background: linear-gradient(145deg, #0EA5E9, #0284C7); }

        .http-headers-related-tool-item:hover .http-headers-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="whatsapp-link-generator"]:hover .http-headers-related-tool-icon { background: linear-gradient(145deg, #34d399, #10b981); }
        a[href*="paypal-link-generator"]:hover .http-headers-related-tool-icon { background: linear-gradient(145deg, #0ea5e9, #0284c7); }
        a[href*="password-generator"]:hover .http-headers-related-tool-icon { background: linear-gradient(145deg, #38bdf8, #0ea5e9); }

        .http-headers-related-tool-item { box-shadow: none; border: none; }
        .http-headers-related-tool-item:hover { box-shadow: none; border: none; }
        .http-headers-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .http-headers-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .http-headers-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .http-headers-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .http-headers-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .http-headers-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .http-headers-related-tool-item:hover .http-headers-related-tool-name { color: var(--primary-color); }

        .http-headers-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .http-headers-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .http-headers-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .http-headers-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .http-headers-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .http-headers-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .http-headers-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .http-headers-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .http-headers-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .http-headers-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .http-headers-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .http-headers-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .http-headers-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .http-headers-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="http-headers-widget-container">
        <h1 class="http-headers-widget-title">HTTP Headers Lookup</h1>
        <p class="http-headers-widget-description">
            Analyze HTTP response headers of any website. Check security headers, server information, caching directives, and SEO-related headers instantly.
        </p>

        <form class="http-headers-form">
            <div class="http-headers-field">
                <label for="websiteUrl" class="http-headers-label">Website URL:</label>
                <input
                    type="url"
                    id="websiteUrl"
                    class="http-headers-input"
                    placeholder="https://example.com"
                />
            </div>
        </form>

        <div class="http-headers-buttons">
            <button class="http-headers-btn http-headers-btn-primary" onclick="HTTPHeadersLookup.lookup()">
                Lookup Headers
            </button>
            <button class="http-headers-btn http-headers-btn-secondary" onclick="HTTPHeadersLookup.clear()">
                Clear Results
            </button>
            <button class="http-headers-btn http-headers-btn-success" onclick="HTTPHeadersLookup.copy()">
                Copy Headers
            </button>
        </div>

        <div class="http-headers-result">
            <h3 class="http-headers-result-title">HTTP Response Headers:</h3>
            <div class="http-headers-output" id="headersOutput">Enter a website URL and click "Lookup Headers" to analyze HTTP response headers...</div>
        </div>

        <div class="http-headers-related-tools">
            <h3 class="http-headers-related-tools-title">Related Tools</h3>
            <div class="http-headers-related-tools-grid">
                <a href="/p/whatsapp-link-generator.html" class="http-headers-related-tool-item" rel="noopener">
                    <div class="http-headers-related-tool-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="http-headers-related-tool-name">WhatsApp Link Generator</div>
                </a>

                <a href="/p/paypal-link-generator.html" class="http-headers-related-tool-item" rel="noopener">
                    <div class="http-headers-related-tool-icon">
                        <i class="fab fa-paypal"></i>
                    </div>
                    <div class="http-headers-related-tool-name">PayPal Link Generator</div>
                </a>

                <a href="/p/password-generator.html" class="http-headers-related-tool-item" rel="noopener">
                    <div class="http-headers-related-tool-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="http-headers-related-tool-name">Password Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional HTTP Headers Lookup for Website Analysis</h2>
            <p>Our <strong>HTTP Headers Lookup</strong> tool provides comprehensive analysis of website HTTP response headers, essential for developers, SEO professionals, and security analysts. Examine server configurations, security policies, caching directives, and performance optimizations to understand how websites communicate with browsers and search engines.</p>
            <p>Perfect for web developers, system administrators, SEO specialists, and security professionals who need to analyze website headers for debugging, optimization, and security assessment. The tool reveals critical information about server technology, security implementations, and content delivery configurations.</p>

            <h3>How to Use the HTTP Headers Lookup Tool</h3>
            <ol>
                <li><strong>Enter Website URL:</strong> Input the complete URL of the website you want to analyze (including https://).</li>
                <li><strong>Lookup Headers:</strong> Click "Lookup Headers" to fetch and analyze the HTTP response headers.</li>
                <li><strong>Review Results:</strong> Examine the status code, server information, security headers, and caching directives.</li>
                <li><strong>Copy Headers:</strong> Use "Copy Headers" to save the results for documentation or further analysis.</li>
            </ol>

            <h3>Frequently Asked Questions About HTTP Headers Analysis</h3>

            <h4>How do I check HTTP headers of a website?</h4>
            <p>Enter the website URL in the field above and click 'Lookup Headers'. Our tool will fetch and display all HTTP response headers including server information, security headers, caching directives, and content type information.</p>

            <h4>What are HTTP response headers and why are they important?</h4>
            <p>HTTP response headers are metadata sent by web servers with each response. They contain important information about the server, security policies, caching instructions, content type, and more. They're crucial for SEO, security, and performance optimization.</p>

            <h4>How can I view security headers of a website?</h4>
            <p>Our tool automatically identifies and highlights security-related headers like Content-Security-Policy, X-Frame-Options, Strict-Transport-Security, and X-Content-Type-Options. These headers help protect websites from various security threats.</p>

            <h4>What HTTP headers should I check for SEO?</h4>
            <p>Important SEO headers include Status Code, Content-Type, Content-Encoding, Cache-Control, Last-Modified, ETag, and Canonical headers. These affect how search engines crawl, index, and cache your website content.</p>

            <h4>How do I analyze server response headers?</h4>
            <p>Enter any website URL and our tool will show server headers like Server type, response status, content encoding, and performance-related headers. This helps with debugging, optimization, and understanding server configuration.</p>
        </div>

        <div class="http-headers-features">
            <h3 class="http-headers-features-title">Key Features:</h3>
            <ul class="http-headers-features-list">
                <li class="http-headers-features-item" style="margin-bottom: 0.3em;">Complete HTTP Header Analysis</li>
                <li class="http-headers-features-item" style="margin-bottom: 0.3em;">Security Header Detection</li>
                <li class="http-headers-features-item" style="margin-bottom: 0.3em;">Server Information Lookup</li>
                <li class="http-headers-features-item" style="margin-bottom: 0.3em;">SEO Header Analysis</li>
                <li class="http-headers-features-item" style="margin-bottom: 0.3em;">Response Status Checking</li>
                <li class="http-headers-features-item" style="margin-bottom: 0.3em;">Performance Header Review</li>
                <li class="http-headers-features-item">100% Free and Instant</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="http-headers-notification" id="headersNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                urlInput: () => document.getElementById('websiteUrl'),
                output: () => document.getElementById('headersOutput'),
                notification: () => document.getElementById('headersNotification')
            };

            function validateUrl(url) {
                try {
                    const urlObj = new URL(url);
                    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
                } catch (e) {
                    return false;
                }
            }

            function normalizeUrl(url) {
                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    return 'https://' + url;
                }
                return url;
            }

            function getStatusClass(status) {
                if (status >= 200 && status < 300) return 'status-200';
                if (status >= 300 && status < 400) return 'status-300';
                if (status >= 400 && status < 500) return 'status-400';
                if (status >= 500) return 'status-500';
                return '';
            }

            function formatHeaders(headers, status, statusText) {
                let html = '';

                // Status information
                html += `<div class="http-headers-status">`;
                html += `<div class="http-headers-status-code ${getStatusClass(status)}">${status} ${statusText}</div>`;
                html += `<div>Response Status</div>`;
                html += `</div>`;

                // Headers
                const headerEntries = Object.entries(headers).sort(([a], [b]) => a.localeCompare(b));

                headerEntries.forEach(([name, value]) => {
                    html += `<div class="http-headers-header-item">`;
                    html += `<span class="http-headers-header-name">${name}:</span> `;
                    html += `<span class="http-headers-header-value">${value}</span>`;
                    html += `</div>`;
                });

                return html;
            }

            function formatHeadersForCopy(headers, status, statusText) {
                let text = `HTTP Response Headers\n`;
                text += `Status: ${status} ${statusText}\n`;
                text += `${'='.repeat(50)}\n\n`;

                const headerEntries = Object.entries(headers).sort(([a], [b]) => a.localeCompare(b));

                headerEntries.forEach(([name, value]) => {
                    text += `${name}: ${value}\n`;
                });

                return text;
            }

            async function fetchHeaders(url) {
                // Note: Due to CORS restrictions, this is a simplified implementation
                // In a real-world scenario, you'd need a backend proxy or use a CORS proxy service

                try {
                    const response = await fetch(url, {
                        method: 'HEAD',
                        mode: 'cors'
                    });

                    const headers = {};
                    response.headers.forEach((value, key) => {
                        headers[key] = value;
                    });

                    return {
                        status: response.status,
                        statusText: response.statusText,
                        headers: headers
                    };
                } catch (error) {
                    // Fallback: Try with GET request
                    try {
                        const response = await fetch(url, {
                            method: 'GET',
                            mode: 'cors'
                        });

                        const headers = {};
                        response.headers.forEach((value, key) => {
                            headers[key] = value;
                        });

                        return {
                            status: response.status,
                            statusText: response.statusText,
                            headers: headers
                        };
                    } catch (fallbackError) {
                        throw new Error('Unable to fetch headers due to CORS restrictions. This tool works best with websites that allow cross-origin requests.');
                    }
                }
            }

            window.HTTPHeadersLookup = {
                currentHeaders: null,

                async lookup() {
                    const url = elements.urlInput().value.trim();
                    const output = elements.output();

                    if (!url) {
                        output.innerHTML = '<div style="color: #dc2626;">Please enter a website URL to lookup HTTP headers.</div>';
                        return;
                    }

                    const normalizedUrl = normalizeUrl(url);

                    if (!validateUrl(normalizedUrl)) {
                        output.innerHTML = '<div style="color: #dc2626;">Please enter a valid URL (e.g., https://example.com).</div>';
                        return;
                    }

                    // Show loading state
                    output.innerHTML = '<div class="http-headers-loading">🔍 Fetching HTTP headers...</div>';

                    try {
                        const result = await fetchHeaders(normalizedUrl);
                        this.currentHeaders = result;

                        const formattedHeaders = formatHeaders(result.headers, result.status, result.statusText);
                        output.innerHTML = formattedHeaders;

                    } catch (error) {
                        output.innerHTML = `<div style="color: #dc2626;">Error: ${error.message}</div>`;
                        this.currentHeaders = null;
                    }
                },

                clear() {
                    elements.urlInput().value = '';
                    elements.output().innerHTML = 'Enter a website URL and click "Lookup Headers" to analyze HTTP response headers...';
                    this.currentHeaders = null;
                },

                copy() {
                    if (!this.currentHeaders) {
                        alert('No headers to copy. Please lookup headers first.');
                        return;
                    }

                    const text = formatHeadersForCopy(this.currentHeaders.headers, this.currentHeaders.status, this.currentHeaders.statusText);

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message = '✓ Copied to clipboard!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Enter key shortcut
                elements.urlInput().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        HTTPHeadersLookup.lookup();
                    }
                });

                // Global keyboard shortcut
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        HTTPHeadersLookup.lookup();
                    }
                });

                // Sample URL for demonstration (uncomment to test)
                // elements.urlInput().value = 'https://httpbin.org/get';
            });
        })();
    </script>
</body>
</html>
