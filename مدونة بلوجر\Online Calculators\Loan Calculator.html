<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Loan Calculator - Estimate Your Monthly Payments</title>
    <meta name="description" content="Use our free and accurate loan calculator to estimate your monthly payments, total interest, and amortization for mortgages, car loans, or personal loans.">
    <meta name="keywords" content="loan calculator, mortgage calculator, monthly payment calculator, loan amortization, personal loan calculator, car loan calculator">
    <link rel="canonical" href="https://www.webtoolskit.org/p/loan-calculator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Loan Calculator - Estimate Your Monthly Payments",
        "description": "Use our free and accurate loan calculator to estimate your monthly payments, total interest, and amortization for mortgages, car loans, or personal loans.",
        "url": "https://www.webtoolskit.org/p/loan-calculator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-23",
        "dateModified": "2025-06-23",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Loan Calculator",
            "applicationCategory": "FinanceApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Monthly payment calculation",
                "Total interest calculation",
                "Total loan repayment cost",
                "Supports various loan types (mortgage, auto, personal)"
            ]
        },
        "potentialAction": {
             "@type": "Action",
             "name": "Calculate Loan Payment"
        }
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I calculate how much of a loan I can afford?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To determine how much loan you can afford, financial experts recommend using the debt-to-income (DTI) ratio. Lenders typically prefer a DTI ratio below 36-43%. You can use our loan calculator by entering different loan amounts to see what the monthly payment would be, then check if that payment fits comfortably within your monthly budget and keeps your DTI in a healthy range."
          }
        },
        {
          "@type": "Question",
          "name": "What is the formula to calculate a mortgage payment?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The standard formula to calculate a fixed monthly mortgage payment is M = P * [r(1+r)^n] / [(1+r)^n - 1]. In this formula, 'M' is the monthly payment, 'P' is the principal loan amount, 'r' is the monthly interest rate (annual rate divided by 12), and 'n' is the total number of payments (loan term in years multiplied by 12). Our calculator uses this formula to give you an accurate payment estimate."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate monthly installments for a personal loan?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Calculating monthly installments for a personal loan uses the same formula as a mortgage or auto loan. Simply input the total loan amount, the annual interest rate (APR), and the loan term (in years) into our Loan Calculator. The tool will instantly compute your fixed monthly installment."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate how much a loan payment will be?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The easiest way is to use our Loan Calculator. Enter the three key variables: 1) the total amount you want to borrow, 2) the annual interest rate, and 3) the duration of the loan (the term). Click the 'Calculate Loan' button, and the tool will instantly show you the exact monthly payment."
          }
        },
        {
          "@type": "Question",
          "name": "How much would a $20,000 loan cost per month?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The monthly cost of a $20,000 loan depends entirely on the interest rate and the loan term. For example, a $20,000 loan over 5 years (60 months) at a 7% interest rate would cost approximately $396 per month. If the term was extended to 7 years, the payment would drop to around $299. Use our calculator to input your specific terms and find the exact monthly cost."
          }
        }
      ]
    }
    </script>


    <style>
        /* Loan Calculator Widget - Simplified & Template Compatible */
        .loan-calculator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .loan-calculator-widget-container * { box-sizing: border-box; }

        .loan-calculator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .loan-calculator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .loan-calculator-input-group {
            margin-bottom: var(--spacing-lg);
        }

        .loan-calculator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .loan-calculator-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .loan-calculator-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .loan-calculator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .loan-calculator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .loan-calculator-btn:hover { transform: translateY(-2px); }

        .loan-calculator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .loan-calculator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .loan-calculator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .loan-calculator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .loan-calculator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .loan-calculator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .loan-calculator-output {
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 120px;
            color: var(--text-color);
            line-height: 1.5;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .loan-calculator-output-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px dashed var(--border-color);
        }
        
        .loan-calculator-output-item:last-child {
            border-bottom: none;
        }

        .loan-calculator-output-label {
            font-weight: 600;
            color: var(--text-color-light);
        }
        
        .loan-calculator-output-value {
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--primary-color);
        }
        
        .loan-calculator-output-value.total {
            color: var(--text-color);
        }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .loan-calculator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .loan-calculator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .loan-calculator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .loan-calculator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            margin-bottom: 0.3em;
        }

        .loan-calculator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 4px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 600px) { 
            .loan-calculator-features-list { 
                columns: 1 !important; 
                -webkit-columns: 1 !important; 
                -moz-columns: 1 !important; 
            } 
        }

        .loan-calculator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="sales-tax-calculator"] .loan-calculator-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="margin-calculator"] .loan-calculator-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }
        a[href*="percentage-calculator"] .loan-calculator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .loan-calculator-related-tool-item:hover .loan-calculator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .loan-calculator-related-tool-item { box-shadow: none; border: none; }
        .loan-calculator-related-tool-item:hover { box-shadow: none; border: none; }
        .loan-calculator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .loan-calculator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .loan-calculator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .loan-calculator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .loan-calculator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .loan-calculator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .loan-calculator-related-tool-item:hover .loan-calculator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .loan-calculator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .loan-calculator-widget-title { font-size: 1.875rem; }
            .loan-calculator-buttons { flex-direction: column; }
            .loan-calculator-btn { flex: none; }
            .loan-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .loan-calculator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .loan-calculator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .loan-calculator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .loan-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .loan-calculator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .loan-calculator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .loan-calculator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .loan-calculator-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .loan-calculator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
    </style>
</head>
<body>
    <div class="loan-calculator-widget-container">
        <h1 class="loan-calculator-widget-title">Loan Calculator</h1>
        <p class="loan-calculator-widget-description">
            Instantly estimate your monthly payments, total interest, and total cost for any fixed-rate loan.
        </p>
        
        <div class="loan-calculator-input-group">
            <label for="loanAmountInput" class="loan-calculator-label">Loan Amount ($)</label>
            <input 
                id="loanAmountInput" 
                class="loan-calculator-input"
                type="number"
                placeholder="e.g., 250000"
            />
        </div>

        <div class="loan-calculator-input-group">
            <label for="interestRateInput" class="loan-calculator-label">Annual Interest Rate (%)</label>
            <input 
                id="interestRateInput" 
                class="loan-calculator-input"
                type="number"
                placeholder="e.g., 6.5"
            />
        </div>

        <div class="loan-calculator-input-group">
            <label for="loanTermInput" class="loan-calculator-label">Loan Term (Years)</label>
            <input 
                id="loanTermInput" 
                class="loan-calculator-input"
                type="number"
                placeholder="e.g., 30"
            />
        </div>

        <div class="loan-calculator-buttons">
            <button class="loan-calculator-btn loan-calculator-btn-primary" onclick="LoanCalculator.calculate()">
                Calculate Loan
            </button>
            <button class="loan-calculator-btn loan-calculator-btn-secondary" onclick="LoanCalculator.clear()">
                Clear All
            </button>
        </div>

        <div class="loan-calculator-result">
            <h3 class="loan-calculator-result-title">Loan Summary:</h3>
            <div class="loan-calculator-output" id="loanCalculatorOutput">
                Your loan calculation results will appear here...
            </div>
        </div>
        
        <div class="loan-calculator-related-tools">
            <h3 class="loan-calculator-related-tools-title">Related Tools</h3>
            <div class="loan-calculator-related-tools-grid">
                <a href="/p/sales-tax-calculator.html" class="loan-calculator-related-tool-item" rel="noopener">
                    <div class="loan-calculator-related-tool-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="loan-calculator-related-tool-name">Sales Tax Calculator</div>
                </a>

                <a href="/p/margin-calculator.html" class="loan-calculator-related-tool-item" rel="noopener">
                    <div class="loan-calculator-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="loan-calculator-related-tool-name">Margin Calculator</div>
                </a>

                <a href="/p/percentage-calculator.html" class="loan-calculator-related-tool-item" rel="noopener">
                    <div class="loan-calculator-related-tool-icon">
                        <i class="fas fa-percent"></i>
                    </div>
                    <div class="loan-calculator-related-tool-name">Percentage Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Make Informed Financial Decisions with Our Loan Calculator</h2>
            <p>Whether you're planning to buy a home, purchase a new car, or take out a personal loan, understanding the financial commitment is the first step. Our free <strong>Loan Calculator</strong> is a powerful tool designed to demystify loan repayments. By providing a clear breakdown of your potential monthly payments, total interest paid, and the overall cost of the loan, it empowers you to budget effectively and compare different loan offers with confidence.</p>
            <p>This tool eliminates complex manual calculations, giving you instant, accurate results. Simply enter the loan amount, annual interest rate, and the loan term, and our calculator will handle the rest. Use it to explore various scenarios—see how a shorter loan term increases monthly payments but saves you a significant amount in total interest, or how a small difference in interest rates can impact your costs over the life of the loan.</p>
            
            <h3>How to Use the Loan Calculator</h3>
            <ol>
                <li><strong>Enter Loan Amount:</strong> Input the total principal amount you plan to borrow.</li>
                <li><strong>Enter Annual Interest Rate:</strong> Provide the annual percentage rate (APR) offered by your lender.</li>
                <li><strong>Enter Loan Term:</strong> Specify the duration of the loan in years.</li>
                <li><strong>Calculate:</strong> Click the "Calculate Loan" button to see a detailed summary of your monthly payment, total interest, and total repayment amount.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Loan Calculations</h3>
            
            <h4>How do I calculate how much of a loan I can afford?</h4>
            <p>To determine how much loan you can afford, financial experts recommend using the debt-to-income (DTI) ratio. Lenders typically prefer a DTI ratio below 36-43%. You can use our loan calculator by entering different loan amounts to see what the monthly payment would be, then check if that payment fits comfortably within your monthly budget and keeps your DTI in a healthy range.</p>
            
            <h4>What is the formula to calculate a mortgage payment?</h4>
            <p>The standard formula to calculate a fixed monthly mortgage payment is <code>M = P * [r(1+r)^n] / [(1+r)^n - 1]</code>. In this formula, 'M' is the monthly payment, 'P' is the principal loan amount, 'r' is the monthly interest rate (annual rate divided by 12), and 'n' is the total number of payments (loan term in years multiplied by 12). Our calculator uses this formula to give you an accurate payment estimate.</p>
            
            <h4>How to calculate monthly installments for a personal loan?</h4>
            <p>Calculating monthly installments for a personal loan uses the same formula as a mortgage or auto loan. Simply input the total loan amount, the annual interest rate (APR), and the loan term (in years) into our Loan Calculator. The tool will instantly compute your fixed monthly installment.</p>
            
            <h4>How to calculate how much a loan payment will be?</h4>
            <p>The easiest way is to use our Loan Calculator. Enter the three key variables: 1) the total amount you want to borrow, 2) the annual interest rate, and 3) the duration of the loan (the term). Click the 'Calculate Loan' button, and the tool will instantly show you the exact monthly payment.</p>
            
            <h4>How much would a $20,000 loan cost per month?</h4>
            <p>The monthly cost of a $20,000 loan depends entirely on the interest rate and the loan term. For example, a $20,000 loan over 5 years (60 months) at a 7% interest rate would cost approximately $396 per month. If the term was extended to 7 years, the payment would drop to around $299. Use our calculator to input your specific terms and find the exact monthly cost.</p>
        </div>

        <div class="loan-calculator-features">
            <h3 class="loan-calculator-features-title">Key Features:</h3>
            <ul class="loan-calculator-features-list">
                <li class="loan-calculator-features-item">Calculate monthly payments</li>
                <li class="loan-calculator-features-item">See total interest paid</li>
                <li class="loan-calculator-features-item">View total repayment cost</li>
                <li class="loan-calculator-features-item">Supports any fixed-rate loan</li>
                <li class="loan-calculator-features-item">Instant and accurate results</li>
                <li class="loan-calculator-features-item">Simple, clean interface</li>
                <li class="loan-calculator-features-item">Mobile-responsive design</li>
                <li class="loan-calculator-features-item">100% free and secure</li>
            </ul>
        </div>
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                amount: () => document.getElementById('loanAmountInput'),
                rate: () => document.getElementById('interestRateInput'),
                term: () => document.getElementById('loanTermInput'),
                output: () => document.getElementById('loanCalculatorOutput')
            };
            
            const formatCurrency = (num) => {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 2
                }).format(num);
            };

            window.LoanCalculator = {
                calculate() {
                    const outputEl = elements.output();
                    const P = parseFloat(elements.amount().value);
                    const annualRate = parseFloat(elements.rate().value);
                    const termYears = parseFloat(elements.term().value);

                    if (isNaN(P) || P <= 0 || isNaN(annualRate) || annualRate < 0 || isNaN(termYears) || termYears <= 0) {
                        outputEl.innerHTML = '<span style="color: #dc2626;">Please enter valid positive numbers in all fields.</span>';
                        return;
                    }

                    const r = annualRate / 12 / 100; // Monthly interest rate
                    const n = termYears * 12; // Total number of payments

                    if (r === 0) { // Handle 0% interest case
                        const monthlyPayment = P / n;
                        const totalPayment = P;
                        const totalInterest = 0;
                        this.displayResults(monthlyPayment, totalInterest, totalPayment);
                        return;
                    }

                    const M = P * (r * Math.pow(1 + r, n)) / (Math.pow(1 + r, n) - 1);
                    const totalPayment = M * n;
                    const totalInterest = totalPayment - P;
                    
                    this.displayResults(M, totalInterest, totalPayment);
                },

                displayResults(monthlyPayment, totalInterest, totalPayment) {
                    const outputEl = elements.output();
                    outputEl.style.color = '';
                    outputEl.innerHTML = `
                        <div class="loan-calculator-output-item">
                            <span class="loan-calculator-output-label">Monthly Payment:</span>
                            <span class="loan-calculator-output-value">${formatCurrency(monthlyPayment)}</span>
                        </div>
                        <div class="loan-calculator-output-item">
                            <span class="loan-calculator-output-label">Total Interest Paid:</span>
                            <span class="loan-calculator-output-value total">${formatCurrency(totalInterest)}</span>
                        </div>
                        <div class="loan-calculator-output-item">
                            <span class="loan-calculator-output-label">Total Repayment:</span>
                            <span class="loan-calculator-output-value total">${formatCurrency(totalPayment)}</span>
                        </div>
                    `;
                },

                clear() {
                    elements.amount().value = '';
                    elements.rate().value = '';
                    elements.term().value = '';
                    elements.output().innerHTML = 'Your loan calculation results will appear here...';
                    elements.output().style.color = '';
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Handle Enter key on input fields to trigger calculation
                const inputs = [elements.amount(), elements.rate(), elements.term()];
                inputs.forEach(input => {
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            LoanCalculator.calculate();
                        }
                    });
                });
            });
        })();
    </script>
</body>
</html>