<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Search Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #0047AB;
            --text-color: #111827;
            --text-color-light: #4b5563;
            --background-color: #fff;
            --border-color: #e5e7eb;
            --header-bg: #fff;
            --card-bg: #fff;
        }

        [data-theme="dark"] {
            --primary-color: #60a5fa;
            --text-color: #ffffff;
            --text-color-light: #d1d5db;
            --background-color: #111827;
            --border-color: #374151;
            --header-bg: #111827;
            --card-bg: #1f2937;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            transition: all 0.3s ease;
        }

        .header {
            background-color: var(--header-bg);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .header-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-icon:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 71, 171, 0.3);
        }

        /* Page Search Styles */
        .page-search-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: var(--header-bg);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem;
            z-index: 1000;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .page-search-container.active {
            transform: translateY(0);
        }

        .page-search-input-wrapper {
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }

        .page-search-input {
            width: 100%;
            padding: 12px 50px 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 25px;
            font-size: 16px;
            background-color: var(--background-color);
            color: var(--text-color);
            outline: none;
            transition: border-color 0.3s ease;
        }

        .page-search-input:focus {
            border-color: var(--primary-color);
        }

        .page-search-close {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text-color-light);
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .page-search-close:hover {
            background-color: var(--border-color);
            color: var(--text-color);
        }

        .page-search-results {
            margin-top: 10px;
            font-size: 14px;
            color: var(--text-color-light);
            text-align: center;
        }

        /* Search Highlight Styles */
        .search-highlight {
            background-color: #ffeb3b;
            color: #000;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        [data-theme="dark"] .search-highlight {
            background-color: #ffc107;
            color: #000;
        }

        .search-highlight.current {
            background-color: #ff5722;
            color: white;
        }

        [data-theme="dark"] .search-highlight.current {
            background-color: #ff5722;
            color: white;
        }

        .content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .content h1, .content h2, .content h3 {
            margin-bottom: 1rem;
            color: var(--text-color);
        }

        .content p {
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .theme-toggle {
            margin-left: 0.5rem;
        }

        .dark-icon {
            display: block;
        }

        .light-icon {
            display: none;
        }

        [data-theme="dark"] .dark-icon {
            display: none;
        }

        [data-theme="dark"] .light-icon {
            display: block;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">WebToolsKit</div>
            <div class="header-buttons">
                <div class="page-search-toggle">
                    <button aria-label="Search current page" class="btn-icon" id="page-search-toggle" type="button">
                        <i aria-hidden="true" class="fas fa-search"></i>
                    </button>
                </div>
                <div class="theme-toggle">
                    <button aria-label="Toggle dark mode" class="btn-icon" id="theme-toggle" type="button">
                        <i aria-hidden="true" class="fas fa-moon dark-icon"></i>
                        <i aria-hidden="true" class="fas fa-sun light-icon"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Page Search Container -->
    <div class="page-search-container" id="page-search-container">
        <div class="page-search-input-wrapper">
            <input aria-label="Search current page" class="page-search-input" id="page-search-input" placeholder="Search current page..." type="text">
            <button aria-label="Close search" class="page-search-close" id="page-search-close" type="button">
                <i aria-hidden="true" class="fas fa-times"></i>
            </button>
        </div>
        <div class="page-search-results" id="page-search-results"></div>
    </div>

    <main class="content">
        <h1>Page Search Test</h1>
        <p>This is a test page to demonstrate the page search functionality. You can search for words like "search", "functionality", "test", or "page" to see how the highlighting works.</p>
        
        <h2>Features</h2>
        <p>The search functionality includes:</p>
        <ul>
            <li>Real-time search as you type</li>
            <li>Highlighting of matching text</li>
            <li>Navigation between matches with Enter key</li>
            <li>Current match indicator</li>
            <li>No results message when no matches found</li>
        </ul>

        <h3>How to Use</h3>
        <p>Click the search icon in the header to open the search box. Start typing to search within the current page content. Press Enter to navigate between matches, or Escape to close the search.</p>

        <h2>Sample Content</h2>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.</p>
        
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>

        <h3>More Test Content</h3>
        <p>This paragraph contains various words that you can search for to test the search functionality. Try searching for "paragraph", "words", "search", or "test" to see the highlighting in action.</p>
    </main>

    <script>
        // Theme toggle functionality
        function getTheme() {
            const savedTheme = localStorage.getItem("theme");
            return savedTheme || (window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light");
        }

        function setTheme(theme) {
            document.documentElement.setAttribute("data-theme", theme);
            localStorage.setItem("theme", theme);
        }

        function toggleTheme() {
            const currentTheme = getTheme();
            const newTheme = currentTheme === "dark" ? "light" : "dark";
            setTheme(newTheme);
        }

        // Set initial theme
        setTheme(getTheme());

        document.addEventListener('DOMContentLoaded', function() {
            // Theme toggle
            const themeToggleBtn = document.getElementById('theme-toggle');
            if (themeToggleBtn) {
                themeToggleBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleTheme();
                });
            }

            // Page Search Functionality
            const pageSearchToggle = document.getElementById('page-search-toggle');
            const pageSearchContainer = document.getElementById('page-search-container');
            const pageSearchInput = document.getElementById('page-search-input');
            const pageSearchClose = document.getElementById('page-search-close');
            const pageSearchResults = document.getElementById('page-search-results');
            
            let searchHighlights = [];
            let currentHighlightIndex = -1;

            // Toggle search container
            if (pageSearchToggle && pageSearchContainer) {
                pageSearchToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    pageSearchContainer.classList.toggle('active');
                    if (pageSearchContainer.classList.contains('active')) {
                        setTimeout(() => pageSearchInput.focus(), 100);
                    } else {
                        clearSearchHighlights();
                    }
                });
            }

            // Close search
            if (pageSearchClose) {
                pageSearchClose.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    pageSearchContainer.classList.remove('active');
                    clearSearchHighlights();
                });
            }

            // Search input functionality
            if (pageSearchInput) {
                pageSearchInput.addEventListener('input', function() {
                    const query = this.value.trim();
                    if (query.length >= 2) {
                        performPageSearch(query);
                    } else {
                        clearSearchHighlights();
                        updateSearchResults('');
                    }
                });

                pageSearchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        navigateToNextHighlight();
                    } else if (e.key === 'Escape') {
                        pageSearchContainer.classList.remove('active');
                        clearSearchHighlights();
                    }
                });
            }

            // Clear highlights function
            function clearSearchHighlights() {
                searchHighlights.forEach(highlight => {
                    const parent = highlight.parentNode;
                    parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
                    parent.normalize();
                });
                searchHighlights = [];
                currentHighlightIndex = -1;
            }

            // Perform search function
            function performPageSearch(query) {
                clearSearchHighlights();
                
                const searchableElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, td, th, span, div:not(.page-search-container):not(.page-search-container *)');
                let matchCount = 0;
                
                const regex = new RegExp(`(${escapeRegExp(query)})`, 'gi');
                
                searchableElements.forEach(element => {
                    if (element.children.length === 0 && element.textContent.trim()) {
                        const text = element.textContent;
                        if (regex.test(text)) {
                            const highlightedHTML = text.replace(regex, '<span class="search-highlight">$1</span>');
                            element.innerHTML = highlightedHTML;
                            
                            const highlights = element.querySelectorAll('.search-highlight');
                            highlights.forEach(highlight => {
                                searchHighlights.push(highlight);
                                matchCount++;
                            });
                        }
                    }
                });
                
                updateSearchResults(matchCount > 0 ? `${matchCount} match${matchCount !== 1 ? 'es' : ''} found` : 'No results found');
                
                if (matchCount > 0) {
                    currentHighlightIndex = 0;
                    scrollToHighlight(0);
                }
            }

            // Navigate to next highlight
            function navigateToNextHighlight() {
                if (searchHighlights.length > 0) {
                    currentHighlightIndex = (currentHighlightIndex + 1) % searchHighlights.length;
                    scrollToHighlight(currentHighlightIndex);
                }
            }

            // Scroll to highlight
            function scrollToHighlight(index) {
                if (searchHighlights[index]) {
                    // Remove current class from all highlights
                    searchHighlights.forEach(h => h.classList.remove('current'));
                    
                    // Add current class to active highlight
                    searchHighlights[index].classList.add('current');
                    
                    // Scroll to highlight
                    searchHighlights[index].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                    
                    updateSearchResults(`${index + 1} of ${searchHighlights.length} match${searchHighlights.length !== 1 ? 'es' : ''}`);
                }
            }

            // Update search results display
            function updateSearchResults(message) {
                if (pageSearchResults) {
                    pageSearchResults.textContent = message;
                }
            }

            // Escape regex special characters
            function escapeRegExp(string) {
                return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            }

            // Close search when clicking outside
            document.addEventListener('click', function(e) {
                if (pageSearchContainer && pageSearchContainer.classList.contains('active')) {
                    if (!pageSearchContainer.contains(e.target) && !pageSearchToggle.contains(e.target)) {
                        pageSearchContainer.classList.remove('active');
                        clearSearchHighlights();
                    }
                }
            });

            // Close search on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && pageSearchContainer && pageSearchContainer.classList.contains('active')) {
                    pageSearchContainer.classList.remove('active');
                    clearSearchHighlights();
                }
            });
        });
    </script>
</body>
</html>
