<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pace Converter - Convert Running Pace Between Mile and Kilometer</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Pace Converter - Convert Running Pace Between Mile and Kilometer",
        "description": "Instantly convert your running or training pace between minutes per mile (min/mi) and minutes per kilometer (min/km). Free online tool for athletes and runners.",
        "url": "https://www.webtoolskit.org/p/pace-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-16",
        "dateModified": "2025-06-16",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Pace Converter",
            "applicationCategory": "HealthApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Pace Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert pace to speed?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Pace (time/distance) and speed (distance/time) are inverses. To convert pace to speed, you first need your pace in minutes per unit (mile or km). The formula is: Speed = 60 / Pace (in minutes). For example, if your pace is 8 minutes per mile, your speed is 60 ÷ 8 = 7.5 miles per hour (mph). If your pace is 5 minutes per kilometer, your speed is 60 ÷ 5 = 12 kilometers per hour (km/h)."
          }
        },
        {
          "@type": "Question",
          "name": "What is pace vs speed?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Pace and speed measure performance but in different ways. Pace measures the time it takes to cover a set distance, like 'minutes per mile' or 'minutes per kilometer'. A lower pace number is faster. Speed measures the distance covered in a set amount of time, like 'miles per hour' (mph) or 'kilometers per hour' (km/h). A higher speed number is faster."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate pace per mile?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate your pace per mile, divide your total running time (in minutes) by the total distance you ran (in miles). For example, if you ran 3 miles in 24 minutes, your pace is 24 minutes ÷ 3 miles = 8 minutes per mile. If your time includes seconds, convert it to a decimal first. For a 25 minute, 30 second run over 3 miles, the calculation is 25.5 minutes ÷ 3 miles = 8.5 minutes per mile, which is 8 minutes and 30 seconds per mile."
          }
        },
        {
          "@type": "Question",
          "name": "What is the pace conversion between distances?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The conversion relies on the fact that 1 mile is approximately 1.60934 kilometers. To convert a min/mile pace to a min/km pace, you divide the total seconds of your pace by 1.60934. To convert a min/km pace to a min/mile pace, you multiply the total seconds of your pace by 1.60934. Our Pace Converter handles this math for you automatically."
          }
        },
        {
          "@type": "Question",
          "name": "Are treadmill distances and paces accurate?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Treadmill distances and paces are generally good for consistent training but may not be perfectly accurate. Factors like lack of regular calibration, belt wear, and not accounting for real-world conditions like wind or hills can create discrepancies. They are reliable for tracking progress in a controlled environment, but you might notice a difference compared to GPS-tracked outdoor runs."
          }
        }
      ]
    }
    </script>

    <style>
        /* Pace Converter Widget - Simplified & Template Compatible */
        .pace-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .pace-converter-widget-container * { box-sizing: border-box; }

        .pace-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .pace-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .pace-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
            grid-column: 1 / -1; /* Span full width on mobile */
        }

        .pace-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .pace-converter-time-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }

        .pace-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .pace-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }
        
        .pace-converter-input[readonly] {
            background-color: var(--border-color);
            cursor: not-allowed;
        }

        .pace-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 150px;
        }

        .pace-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .pace-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .pace-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .pace-converter-btn:hover { transform: translateY(-2px); }

        .pace-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .pace-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .pace-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .pace-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .pace-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .pace-converter-btn-success:hover {
            background-color: #059669;
        }

        .pace-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .pace-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .pace-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .pace-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .pace-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .pace-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .pace-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .pace-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .pace-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .pace-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .pace-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="speed-converter"] .pace-converter-related-tool-icon { background: linear-gradient(145deg, #4F46E5, #4338CA); }
        a[href*="length-converter"] .pace-converter-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="time-converter"] .pace-converter-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }
        
        .pace-converter-related-tool-item:hover .pace-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="speed-converter"]:hover .pace-converter-related-tool-icon { background: linear-gradient(145deg, #5b52f6, #4f46e5); }
        a[href*="length-converter"]:hover .pace-converter-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="time-converter"]:hover .pace-converter-related-tool-icon { background: linear-gradient(145deg, #7476f2, #6366f1); }
        
        .pace-converter-related-tool-item { box-shadow: none; border: none; }
        .pace-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .pace-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .pace-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .pace-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .pace-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .pace-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .pace-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .pace-converter-related-tool-item:hover .pace-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .pace-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .pace-converter-widget-title { font-size: 1.875rem; }
            .pace-converter-buttons { flex-direction: column; }
            .pace-converter-btn { flex: none; }
            .pace-converter-input-group { grid-template-columns: 1fr; }
            .pace-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .pace-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .pace-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .pace-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .pace-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .pace-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .pace-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .pace-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .pace-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .pace-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .pace-converter-output::selection { background-color: var(--primary-color); color: white; }
        .pace-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .pace-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="pace-converter-widget-container">
        <h1 class="pace-converter-widget-title">Pace Converter</h1>
        <p class="pace-converter-widget-description">
            Quickly convert your running, cycling, or training pace between minutes per mile and minutes per kilometer.
        </p>
        
        <div class="pace-converter-input-group">
            <label for="paceFromMinutes" class="pace-converter-label">From Pace:</label>
            <div class="pace-converter-time-inputs">
                <input type="number" id="paceFromMinutes" class="pace-converter-input" placeholder="Minutes" min="0" step="1">
                <input type="number" id="paceFromSeconds" class="pace-converter-input" placeholder="Seconds" min="0" max="59" step="1">
            </div>
            <select id="paceFromUnit" class="pace-converter-select">
                <option value="min_per_mi" selected>per Mile (/mi)</option>
                <option value="min_per_km">per Kilometer (/km)</option>
            </select>
        </div>

        <div class="pace-converter-input-group">
            <label for="paceToMinutes" class="pace-converter-label">To Pace:</label>
            <div class="pace-converter-time-inputs">
                <input type="number" id="paceToMinutes" class="pace-converter-input" readonly placeholder="Minutes">
                <input type="number" id="paceToSeconds" class="pace-converter-input" readonly placeholder="Seconds">
            </div>
            <select id="paceToUnit" class="pace-converter-select">
                <option value="min_per_mi">per Mile (/mi)</option>
                <option value="min_per_km" selected>per Kilometer (/km)</option>
            </select>
        </div>

        <div class="pace-converter-buttons">
            <button class="pace-converter-btn pace-converter-btn-primary" onclick="PaceConverter.convert()">
                Convert Pace
            </button>
            <button class="pace-converter-btn pace-converter-btn-secondary" onclick="PaceConverter.clear()">
                Clear All
            </button>
            <button class="pace-converter-btn pace-converter-btn-success" onclick="PaceConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="pace-converter-result">
            <h3 class="pace-converter-result-title">Conversion Result:</h3>
            <div class="pace-converter-output" id="paceConverterOutput">
                Your converted pace will appear here...
            </div>
        </div>

        <div class="pace-converter-related-tools">
            <h3 class="pace-converter-related-tools-title">Related Tools</h3>
            <div class="pace-converter-related-tools-grid">
                <a href="/p/speed-converter.html" class="pace-converter-related-tool-item" rel="noopener">
                    <div class="pace-converter-related-tool-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="pace-converter-related-tool-name">Speed Converter</div>
                </a>
                <a href="/p/length-converter.html" class="pace-converter-related-tool-item" rel="noopener">
                    <div class="pace-converter-related-tool-icon">
                        <i class="fas fa-ruler-horizontal"></i>
                    </div>
                    <div class="pace-converter-related-tool-name">Length Converter</div>
                </a>
                <a href="/p/time-converter.html" class="pace-converter-related-tool-item" rel="noopener">
                    <div class="pace-converter-related-tool-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="pace-converter-related-tool-name">Time Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Free Pace Converter for Runners and Athletes</h2>
            <p>Whether you're training for a marathon, a 5K, or just trying to improve your personal best, understanding your pace is crucial. Our free <strong>Pace Converter</strong> is a simple yet powerful tool designed for runners, cyclists, and athletes of all levels. It allows you to instantly convert your pace between different units of distance, primarily minutes per mile (min/mi) and minutes per kilometer (min/km). This is especially useful when following a training plan or participating in an event that uses a different measurement system than you're used to.</p>
            <p>By providing a clear and accurate conversion, our tool helps you stay on track with your goals, understand your performance better, and compare your times with others from around the world. No more complex mental math during your run—just a quick, reliable result.</p>

            <h3>How to Use the Pace Converter</h3>
            <ol>
                <li><strong>Enter Your Pace:</strong> In the "From Pace" section, type your pace into the "Minutes" and "Seconds" fields.</li>
                <li><strong>Select Your Unit:</strong> Choose whether your entered pace is "per Mile" or "per Kilometer" from the dropdown menu.</li>
                <li><strong>Choose Target Unit:</strong> In the "To Pace" section, select the unit you want to convert to.</li>
                <li><strong>Convert:</strong> Click the "Convert Pace" button. Your converted pace will instantly appear in the "To Pace" fields and the result summary below.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button for an easy way to save or share your converted pace.</li>
            </ol>

            <h3>Frequently Asked Questions About Pace Conversion</h3>
            
            <h4>How to convert pace to speed?</h4>
            <p>Pace (time/distance) and speed (distance/time) are inverses. To convert pace to speed, you first need your pace in minutes per unit (mile or km). The formula is: Speed = 60 / Pace (in minutes). For example, if your pace is 8 minutes per mile, your speed is 60 ÷ 8 = 7.5 miles per hour (mph). If your pace is 5 minutes per kilometer, your speed is 60 ÷ 5 = 12 kilometers per hour (km/h).</p>

            <h4>What is pace vs speed?</h4>
            <p>Pace and speed measure performance but in different ways. Pace measures the time it takes to cover a set distance, like 'minutes per mile' or 'minutes per kilometer'. A lower pace number is faster. Speed measures the distance covered in a set amount of time, like 'miles per hour' (mph) or 'kilometers per hour' (km/h). A higher speed number is faster.</p>

            <h4>How to calculate pace per mile?</h4>
            <p>To calculate your pace per mile, divide your total running time (in minutes) by the total distance you ran (in miles). For example, if you ran 3 miles in 24 minutes, your pace is 24 minutes ÷ 3 miles = 8 minutes per mile. If your time includes seconds, convert it to a decimal first. For a 25 minute, 30 second run over 3 miles, the calculation is 25.5 minutes ÷ 3 miles = 8.5 minutes per mile, which is 8 minutes and 30 seconds per mile.</p>

            <h4>What is the pace conversion between distances?</h4>
            <p>The conversion relies on the fact that 1 mile is approximately 1.60934 kilometers. To convert a min/mile pace to a min/km pace, you divide the total seconds of your pace by 1.60934. To convert a min/km pace to a min/mile pace, you multiply the total seconds of your pace by 1.60934. Our Pace Converter handles this math for you automatically.</p>

            <h4>Are treadmill distances and paces accurate?</h4>
            <p>Treadmill distances and paces are generally good for consistent training but may not be perfectly accurate. Factors like lack of regular calibration, belt wear, and not accounting for real-world conditions like wind or hills can create discrepancies. They are reliable for tracking progress in a controlled environment, but you might notice a difference compared to GPS-tracked outdoor runs.</p>
        </div>

        <div class="pace-converter-features">
            <h3 class="pace-converter-features-title">Key Features:</h3>
            <ul class="pace-converter-features-list">
                <li class="pace-converter-features-item" style="margin-bottom: 0.3em;">Mile to Kilometer pace conversion</li>
                <li class="pace-converter-features-item" style="margin-bottom: 0.3em;">Instant and accurate calculations</li>
                <li class="pace-converter-features-item" style="margin-bottom: 0.3em;">Simple two-field time input</li>
                <li class="pace-converter-features-item" style="margin-bottom: 0.3em;">One-click copy for results</li>
                <li class="pace-converter-features-item" style="margin-bottom: 0.3em;">Perfect for race training</li>
                <li class="pace-converter-features-item" style="margin-bottom: 0.3em;">Clean, mobile-friendly design</li>
                <li class="pace-converter-features-item">Completely free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="pace-converter-notification" id="paceConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Pace Converter
        (function() {
            'use strict';

            const KM_PER_MILE = 1.609344;

            const elements = {
                fromMinutes: () => document.getElementById('paceFromMinutes'),
                fromSeconds: () => document.getElementById('paceFromSeconds'),
                toMinutes: () => document.getElementById('paceToMinutes'),
                toSeconds: () => document.getElementById('paceToSeconds'),
                fromUnit: () => document.getElementById('paceFromUnit'),
                toUnit: () => document.getElementById('paceToUnit'),
                output: () => document.getElementById('paceConverterOutput'),
                notification: () => document.getElementById('paceConverterNotification')
            };

            window.PaceConverter = {
                convert() {
                    const fromMinutesVal = parseInt(elements.fromMinutes().value, 10) || 0;
                    const fromSecondsVal = parseInt(elements.fromSeconds().value, 10) || 0;
                    const fromUnit = elements.fromUnit().value;
                    const toUnit = elements.toUnit().value;
                    const output = elements.output();

                    if ((fromMinutesVal === 0 && fromSecondsVal === 0) || fromSecondsVal >= 60) {
                        output.textContent = 'Please enter a valid pace (e.g., 8 min 30 sec).';
                        output.style.color = '#dc2626';
                        elements.toMinutes().value = '';
                        elements.toSeconds().value = '';
                        return;
                    }
                    
                    output.style.color = '';

                    const totalInputSeconds = (fromMinutesVal * 60) + fromSecondsVal;
                    let resultSeconds;

                    if (fromUnit === toUnit) {
                        resultSeconds = totalInputSeconds;
                    } else if (fromUnit === 'min_per_mi' && toUnit === 'min_per_km') {
                        resultSeconds = totalInputSeconds / KM_PER_MILE;
                    } else if (fromUnit === 'min_per_km' && toUnit === 'min_per_mi') {
                        resultSeconds = totalInputSeconds * KM_PER_MILE;
                    } else {
                        resultSeconds = totalInputSeconds;
                    }

                    const resultMinutes = Math.floor(resultSeconds / 60);
                    const resultRemainingSeconds = Math.round(resultSeconds % 60);

                    elements.toMinutes().value = resultMinutes;
                    elements.toSeconds().value = resultRemainingSeconds;

                    const fromUnitText = this.getUnitName(fromUnit);
                    const toUnitText = this.getUnitName(toUnit);
                    output.textContent = `${fromMinutesVal}m ${fromSecondsVal}s ${fromUnitText} = ${resultMinutes}m ${resultRemainingSeconds}s ${toUnitText}`;
                },

                getUnitName(unit) {
                    return unit === 'min_per_mi' ? 'per mile' : 'per kilometer';
                },

                clear() {
                    elements.fromMinutes().value = '';
                    elements.fromSeconds().value = '';
                    elements.toMinutes().value = '';
                    elements.toSeconds().value = '';
                    elements.output().textContent = 'Your converted pace will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const toMinutes = elements.toMinutes().value;
                    const toSeconds = elements.toSeconds().value;
                    if (!toMinutes && !toSeconds) return;

                    const text = `${toMinutes}m ${toSeconds}s ${this.getUnitName(elements.toUnit().value)}`;
                    
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                const formInputs = [elements.fromMinutes(), elements.fromSeconds()];
                
                formInputs.forEach(input => {
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            PaceConverter.convert();
                        }
                    });
                });
            });
        })();
    </script>
</body>
</html>