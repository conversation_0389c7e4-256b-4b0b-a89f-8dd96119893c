<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pressure Converter - Convert PSI, Bar, Pascals, and More</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Pressure Converter - Convert PSI, Bar, Pascals, and More",
        "description": "Instantly convert between various pressure units like PSI, bar, pascals, atmospheres, and torr. Free online tool for engineers, scientists, and hobbyists.",
        "url": "https://www.webtoolskit.org/p/pressure-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-17",
        "dateModified": "2025-06-17",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Pressure Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Pressure Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the formula for pressure from weight?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The fundamental formula for pressure is Pressure (P) = Force (F) ÷ Area (A). When the force is caused by weight, the formula becomes P = Weight ÷ Area. Since weight is mass (m) times the acceleration due to gravity (g), you can also write it as P = (m × g) / A. For example, a 10 kg block on a 0.5 square meter surface exerts a pressure of (10 kg × 9.81 m/s²) / 0.5 m² = 196.2 Pascals."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert PSI to weight?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You cannot directly convert PSI (Pounds per Square Inch) to weight (like pounds or kilograms) because they measure different physical quantities. PSI is a unit of pressure (force per area), while pounds are a unit of force or mass. However, you can use pressure to calculate the total force over a specific area using the formula: Total Force (in pounds) = Pressure (in PSI) × Area (in square inches). For example, a pressure of 30 PSI over a 10 square inch area results in a total force of 30 × 10 = 300 pounds-force."
          }
        },
        {
          "@type": "Question",
          "name": "What are the conversions for pressure?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Common pressure conversions include: 1 standard atmosphere (atm) = 14.696 PSI = 1.01325 bar = 101,325 Pascals (Pa) = 760 Torr. 1 bar = 14.504 PSI = 100,000 Pa. 1 PSI = 6,894.76 Pa. Our pressure converter tool handles these and other conversions automatically for you."
          }
        },
        {
          "@type": "Question",
          "name": "What is the main purpose of a current to pressure converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A current-to-pressure (I/P) converter, also known as an I/P transducer, is a device used in industrial process control. Its main purpose is to translate a standard electrical control signal (typically 4-20 milliamps) into a proportional pneumatic pressure output (typically 3-15 PSI). This allows electronic control systems to operate pneumatic equipment like control valves, actuators, and dampers, effectively bridging the gap between electronic and pneumatic systems."
          }
        },
        {
          "@type": "Question",
          "name": "What are the three formulas of pressure?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Three fundamental formulas used to calculate pressure in different contexts are: 1. The Basic Formula: P = F / A (Pressure = Force divided by Area), which defines pressure as a force distributed over a surface. 2. Hydrostatic Pressure: P = ρgh (Pressure = density × gravity × height), which calculates the pressure exerted by a column of fluid at a certain depth. 3. Ideal Gas Law: P = nRT / V, which describes the pressure of a gas in relation to its amount (n), temperature (T), and volume (V)."
          }
        }
      ]
    }
    </script>

    <style>
        /* Pressure Converter Widget - Simplified & Template Compatible */
        .pressure-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .pressure-converter-widget-container * { box-sizing: border-box; }

        .pressure-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .pressure-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .pressure-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .pressure-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .pressure-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .pressure-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .pressure-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .pressure-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .pressure-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .pressure-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .pressure-converter-btn:hover { transform: translateY(-2px); }

        .pressure-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .pressure-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .pressure-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .pressure-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .pressure-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .pressure-converter-btn-success:hover {
            background-color: #059669;
        }

        .pressure-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .pressure-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .pressure-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .pressure-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .pressure-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .pressure-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .pressure-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .pressure-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .pressure-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .pressure-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .pressure-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="area-converter"] .pressure-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="weight-converter"] .pressure-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="volume-converter"] .pressure-converter-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }

        .pressure-converter-related-tool-item:hover .pressure-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="area-converter"]:hover .pressure-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        a[href*="weight-converter"]:hover .pressure-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        a[href*="volume-converter"]:hover .pressure-converter-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        
        .pressure-converter-related-tool-item { box-shadow: none; border: none; }
        .pressure-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .pressure-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .pressure-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .pressure-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .pressure-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .pressure-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .pressure-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .pressure-converter-related-tool-item:hover .pressure-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .pressure-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .pressure-converter-widget-title { font-size: 1.875rem; }
            .pressure-converter-buttons { flex-direction: column; }
            .pressure-converter-btn { flex: none; }
            .pressure-converter-input-group { grid-template-columns: 1fr; }
            .pressure-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .pressure-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .pressure-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .pressure-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .pressure-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .pressure-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .pressure-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .pressure-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .pressure-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .pressure-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .pressure-converter-output::selection { background-color: var(--primary-color); color: white; }
        .pressure-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .pressure-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="pressure-converter-widget-container">
        <h1 class="pressure-converter-widget-title">Pressure Converter</h1>
        <p class="pressure-converter-widget-description">
            Convert between PSI, bar, pascals, atmospheres, and other common pressure units for engineering, science, or hobbyist projects.
        </p>
        
        <div class="pressure-converter-input-group">
            <label for="pressureFromInput" class="pressure-converter-label">From:</label>
            <input 
                type="number" 
                id="pressureFromInput" 
                class="pressure-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="pressureFromUnit" class="pressure-converter-select">
                <option value="psi">PSI (lbf/in²)</option>
                <option value="bar" selected>Bar</option>
                <option value="pa">Pascal (Pa)</option>
                <option value="kpa">Kilopascal (kPa)</option>
                <option value="mpa">Megapascal (MPa)</option>
                <option value="atm">Atmosphere (atm)</option>
                <option value="torr">Torr</option>
            </select>
        </div>

        <div class="pressure-converter-input-group">
            <label for="pressureToInput" class="pressure-converter-label">To:</label>
            <input 
                type="number" 
                id="pressureToInput" 
                class="pressure-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="pressureToUnit" class="pressure-converter-select">
                <option value="psi" selected>PSI (lbf/in²)</option>
                <option value="bar">Bar</option>
                <option value="pa">Pascal (Pa)</option>
                <option value="kpa">Kilopascal (kPa)</option>
                <option value="mpa">Megapascal (MPa)</option>
                <option value="atm">Atmosphere (atm)</option>
                <option value="torr">Torr</option>
            </select>
        </div>

        <div class="pressure-converter-buttons">
            <button class="pressure-converter-btn pressure-converter-btn-primary" onclick="PressureConverter.convert()">
                Convert Pressure
            </button>
            <button class="pressure-converter-btn pressure-converter-btn-secondary" onclick="PressureConverter.clear()">
                Clear All
            </button>
            <button class="pressure-converter-btn pressure-converter-btn-success" onclick="PressureConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="pressure-converter-result">
            <h3 class="pressure-converter-result-title">Conversion Result:</h3>
            <div class="pressure-converter-output" id="pressureConverterOutput">
                Your converted pressure will appear here...
            </div>
        </div>

        <div class="pressure-converter-related-tools">
            <h3 class="pressure-converter-related-tools-title">Related Tools</h3>
            <div class="pressure-converter-related-tools-grid">
                <a href="/p/area-converter.html" class="pressure-converter-related-tool-item" rel="noopener">
                    <div class="pressure-converter-related-tool-icon">
                        <i class="fas fa-vector-square"></i>
                    </div>
                    <div class="pressure-converter-related-tool-name">Area Converter</div>
                </a>
                <a href="/p/weight-converter.html" class="pressure-converter-related-tool-item" rel="noopener">
                    <div class="pressure-converter-related-tool-icon">
                        <i class="fas fa-weight-hanging"></i>
                    </div>
                    <div class="pressure-converter-related-tool-name">Weight Converter</div>
                </a>
                <a href="/p/volume-converter.html" class="pressure-converter-related-tool-item" rel="noopener">
                    <div class="pressure-converter-related-tool-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="pressure-converter-related-tool-name">Volume Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Fast and Accurate Pressure Unit Conversion</h2>
            <p>From inflating car tires to conducting a scientific experiment, pressure measurements are a part of many technical and everyday tasks. Our free <strong>Pressure Converter</strong> simplifies the process of converting between different units of pressure. Whether you're working with PSI (pounds per square inch), bar, Pascals (Pa), or atmospheres (atm), this tool provides instant and accurate results, eliminating the need for manual calculations and complex formulas.</p>
            <p>This converter is an essential utility for engineers, mechanics, scientists, divers, and anyone who needs to quickly switch between pressure measurement systems. With support for the most common units, including kilopascals (kPa), megapascals (MPa), and torr, you have a comprehensive tool at your fingertips.</p>

            <h3>How to Use the Pressure Converter</h3>
            <ol>
                <li><strong>Enter Your Value:</strong> Type the number you wish to convert into the "From" input field.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., bar) from the first dropdown and your target unit (e.g., PSI) from the second.</li>
                <li><strong>Convert:</strong> Click the "Convert Pressure" button to see the result instantly displayed in the "To" field and the summary box below.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to quickly copy the converted value to your clipboard for use elsewhere.</li>
            </ol>

            <h3>Frequently Asked Questions About Pressure Conversion</h3>
            
            <h4>What is the formula for pressure from weight?</h4>
            <p>The fundamental formula for pressure is Pressure (P) = Force (F) ÷ Area (A). When the force is caused by weight, the formula becomes P = Weight ÷ Area. Since weight is mass (m) times the acceleration due to gravity (g), you can also write it as P = (m × g) / A. For example, a 10 kg block on a 0.5 square meter surface exerts a pressure of (10 kg × 9.81 m/s²) / 0.5 m² = 196.2 Pascals.</p>

            <h4>How to convert PSI to weight?</h4>
            <p>You cannot directly convert PSI (Pounds per Square Inch) to weight (like pounds or kilograms) because they measure different physical quantities. PSI is a unit of pressure (force per area), while pounds are a unit of force or mass. However, you can use pressure to calculate the total force over a specific area using the formula: Total Force (in pounds) = Pressure (in PSI) × Area (in square inches). For example, a pressure of 30 PSI over a 10 square inch area results in a total force of 30 × 10 = 300 pounds-force.</p>

            <h4>What are the conversions for pressure?</h4>
            <p>Common pressure conversions include: 1 standard atmosphere (atm) = 14.696 PSI = 1.01325 bar = 101,325 Pascals (Pa) = 760 Torr. 1 bar = 14.504 PSI = 100,000 Pa. 1 PSI = 6,894.76 Pa. Our pressure converter tool handles these and other conversions automatically for you.</p>
            
            <h4>What is the main purpose of a current to pressure converter?</h4>
            <p>A current-to-pressure (I/P) converter, also known as an I/P transducer, is a device used in industrial process control. Its main purpose is to translate a standard electrical control signal (typically 4-20 milliamps) into a proportional pneumatic pressure output (typically 3-15 PSI). This allows electronic control systems to operate pneumatic equipment like control valves, actuators, and dampers, effectively bridging the gap between electronic and pneumatic systems.</p>

            <h4>What are the three formulas of pressure?</h4>
            <p>Three fundamental formulas used to calculate pressure in different contexts are: 1. The Basic Formula: P = F / A (Pressure = Force divided by Area), which defines pressure as a force distributed over a surface. 2. Hydrostatic Pressure: P = ρgh (Pressure = density × gravity × height), which calculates the pressure exerted by a column of fluid at a certain depth. 3. Ideal Gas Law: P = nRT / V, which describes the pressure of a gas in relation to its amount (n), temperature (T), and volume (V).</p>
        </div>

        <div class="pressure-converter-features">
            <h3 class="pressure-converter-features-title">Key Features:</h3>
            <ul class="pressure-converter-features-list">
                <li class="pressure-converter-features-item" style="margin-bottom: 0.3em;">Supports 7 common pressure units</li>
                <li class="pressure-converter-features-item" style="margin-bottom: 0.3em;">High precision decimal results</li>
                <li class="pressure-converter-features-item" style="margin-bottom: 0.3em;">User-friendly and intuitive interface</li>
                <li class="pressure-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="pressure-converter-features-item" style="margin-bottom: 0.3em;">Real-time conversion feedback</li>
                <li class="pressure-converter-features-item" style="margin-bottom: 0.3em;">Mobile-first responsive design</li>
                <li class="pressure-converter-features-item">No server-side processing</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="pressure-converter-notification" id="pressureConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Pressure Converter
        (function() {
            'use strict';

            // Conversion factors to Pascals (Pa)
            const conversionFactors = {
                'psi': 6894.757,
                'bar': 100000,
                'pa': 1,
                'kpa': 1000,
                'mpa': 1000000,
                'atm': 101325,
                'torr': 133.322
            };

            const elements = {
                fromInput: () => document.getElementById('pressureFromInput'),
                toInput: () => document.getElementById('pressureToInput'),
                fromUnit: () => document.getElementById('pressureFromUnit'),
                toUnit: () => document.getElementById('pressureToUnit'),
                output: () => document.getElementById('pressureConverterOutput'),
                notification: () => document.getElementById('pressureConverterNotification')
            };

            window.PressureConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to Pascals first, then to target unit
                    const valueInPascals = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInPascals / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1000000 || Math.abs(value) < 0.000001) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toFixed(10)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = {
                        'psi': 'PSI', 'bar': 'bar', 'pa': 'Pa', 'kpa': 'kPa', 'mpa': 'MPa', 'atm': 'atm', 'torr': 'Torr'
                    };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted pressure will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        PressureConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>