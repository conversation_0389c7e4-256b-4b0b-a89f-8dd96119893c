<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free IP Address Lookup - Find IP Location & Details Online</title>
    <meta name="description" content="Lookup any IP address instantly with our free IP Address Lookup tool. Get detailed information including location, ISP, country, city, and more for any IP address.">
    <meta name="keywords" content="ip address lookup, ip lookup, ip location, ip geolocation, find ip address, ip address tracker, ip information">
    <link rel="canonical" href="https://www.webtoolskit.org/p/ip-address-lookup.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free IP Address Lookup - Find IP Location & Details Online",
        "description": "Lookup any IP address instantly with our free IP Address Lookup tool. Get detailed information including location, ISP, country, city, and more for any IP address.",
        "url": "https://www.webtoolskit.org/p/ip-address-lookup.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "IP Address Lookup",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "IP address geolocation",
                "ISP information lookup",
                "Country and city detection",
                "Network details analysis",
                "Real-time IP tracking"
            ]
        },
        "potentialAction": [
            { "@type": "SearchAction", "name": "Lookup IP Address" },
            { "@type": "ViewAction", "name": "View IP Details" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I find the location of an IP address?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Enter the IP address in the lookup field above and click 'Lookup IP Address'. Our tool will instantly provide location details including country, region, city, and approximate coordinates based on the IP's registered location."
          }
        },
        {
          "@type": "Question",
          "name": "What information can I get from an IP address lookup?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "An IP address lookup typically provides country, region/state, city, postal code, ISP name, organization, timezone, and approximate latitude/longitude coordinates. Note that this information is based on registration data and may not reflect the exact physical location."
          }
        },
        {
          "@type": "Question",
          "name": "Can I trace someone's exact location with their IP address?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, IP address lookup cannot provide someone's exact physical location. It only shows the general area where the IP is registered, typically at the city or regional level. Exact location tracking requires additional methods and is restricted by privacy laws."
          }
        },
        {
          "@type": "Question",
          "name": "Is it legal to lookup someone's IP address?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, looking up publicly available IP address information is legal. IP addresses are not considered private information, and geolocation data is publicly accessible. However, using this information for harassment, stalking, or illegal activities is prohibited."
          }
        },
        {
          "@type": "Question",
          "name": "How accurate is IP address geolocation?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "IP geolocation accuracy varies by location and ISP. Country-level accuracy is typically 95-99%, while city-level accuracy ranges from 50-80%. Rural areas and mobile networks tend to be less accurate than urban areas with fixed broadband connections."
          }
        }
      ]
    }
    </script>

    <style>
        /* IP Address Lookup Widget - Simplified & Template Compatible */
        .ip-lookup-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .ip-lookup-widget-container * { box-sizing: border-box; }

        .ip-lookup-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .ip-lookup-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .ip-lookup-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .ip-lookup-field {
            display: flex;
            flex-direction: column;
        }

        .ip-lookup-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .ip-lookup-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .ip-lookup-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .ip-lookup-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .ip-lookup-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .ip-lookup-btn:hover { transform: translateY(-2px); }

        .ip-lookup-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .ip-lookup-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .ip-lookup-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .ip-lookup-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .ip-lookup-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .ip-lookup-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .ip-lookup-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.6;
        }

        .ip-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }

        .ip-info-item {
            padding: var(--spacing-sm);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .ip-info-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-xs);
        }

        .ip-info-value {
            color: var(--text-color-light);
            word-break: break-word;
        }

        .loading {
            text-align: center;
            color: var(--text-color-light);
            font-style: italic;
        }

        .error {
            color: #dc2626;
            text-align: center;
        }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        @media (max-width: 768px) {
            .ip-lookup-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .ip-lookup-widget-title { font-size: 1.875rem; }
            .ip-lookup-buttons { flex-direction: column; }
            .ip-lookup-btn { flex: none; }
            .ip-info-grid { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .ip-lookup-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .ip-lookup-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }

        .ip-lookup-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="what-is-my-ip"] .ip-lookup-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="http-headers-lookup"] .ip-lookup-related-tool-icon { background: linear-gradient(145deg, #7C3AED, #5B21B6); }
        a[href*="md5-generator"] .ip-lookup-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }

        .ip-lookup-related-tool-item:hover .ip-lookup-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="what-is-my-ip"]:hover .ip-lookup-related-tool-icon { background: linear-gradient(145deg, #9373f7, #8a4ff0); }
        a[href*="http-headers-lookup"]:hover .ip-lookup-related-tool-icon { background: linear-gradient(145deg, #8a4ff0, #6b21a8); }
        a[href*="md5-generator"]:hover .ip-lookup-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }

        .ip-lookup-related-tool-item { box-shadow: none; border: none; }
        .ip-lookup-related-tool-item:hover { box-shadow: none; border: none; }
        .ip-lookup-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .ip-lookup-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .ip-lookup-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .ip-lookup-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .ip-lookup-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .ip-lookup-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .ip-lookup-related-tool-item:hover .ip-lookup-related-tool-name { color: var(--primary-color); }

        .ip-lookup-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .ip-lookup-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .ip-lookup-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .ip-lookup-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .ip-lookup-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .ip-lookup-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .ip-lookup-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .ip-lookup-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .ip-lookup-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .ip-lookup-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .ip-lookup-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .ip-lookup-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .ip-lookup-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .ip-lookup-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="ip-lookup-widget-container">
        <h1 class="ip-lookup-widget-title">IP Address Lookup</h1>
        <p class="ip-lookup-widget-description">
            Lookup detailed information about any IP address including location, ISP, country, city, and network details. Get instant geolocation data and network information.
        </p>

        <form class="ip-lookup-form">
            <div class="ip-lookup-field">
                <label for="ipInput" class="ip-lookup-label">Enter IP Address:</label>
                <input
                    type="text"
                    id="ipInput"
                    class="ip-lookup-input"
                    placeholder="Enter IP address (e.g., *******)"
                />
            </div>
        </form>

        <div class="ip-lookup-buttons">
            <button class="ip-lookup-btn ip-lookup-btn-primary" onclick="IPLookup.lookup()">
                Lookup IP Address
            </button>
            <button class="ip-lookup-btn ip-lookup-btn-secondary" onclick="IPLookup.clear()">
                Clear Results
            </button>
        </div>

        <div class="ip-lookup-result">
            <h3 class="ip-lookup-result-title">IP Address Information:</h3>
            <div class="ip-lookup-output" id="ipOutput">Enter an IP address above to see detailed information...</div>
        </div>

        <div class="ip-lookup-related-tools">
            <h3 class="ip-lookup-related-tools-title">Related Tools</h3>
            <div class="ip-lookup-related-tools-grid">
                <a href="/p/what-is-my-ip.html" class="ip-lookup-related-tool-item" rel="noopener">
                    <div class="ip-lookup-related-tool-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="ip-lookup-related-tool-name">What Is My IP</div>
                </a>

                <a href="/p/http-headers-lookup.html" class="ip-lookup-related-tool-item" rel="noopener">
                    <div class="ip-lookup-related-tool-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="ip-lookup-related-tool-name">HTTP Headers Lookup</div>
                </a>

                <a href="/p/md5-generator.html" class="ip-lookup-related-tool-item" rel="noopener">
                    <div class="ip-lookup-related-tool-icon">
                        <i class="fas fa-fingerprint"></i>
                    </div>
                    <div class="ip-lookup-related-tool-name">MD5 Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional IP Address Lookup Tool for Network Analysis</h2>
            <p>Our <strong>IP Address Lookup</strong> tool provides comprehensive information about any IP address, including geolocation data, ISP details, and network information. Whether you're troubleshooting network issues, investigating suspicious activity, or simply curious about an IP address, our tool delivers accurate and detailed results instantly.</p>
            <p>Perfect for network administrators, cybersecurity professionals, and anyone needing to identify the source and details of IP addresses. Get real-time information about country, region, city, ISP, organization, and more with just one click.</p>

            <h3>How to Use the IP Address Lookup Tool</h3>
            <ol>
                <li><strong>Enter IP Address:</strong> Type the IP address you want to lookup in the input field above.</li>
                <li><strong>Click Lookup:</strong> Press the "Lookup IP Address" button to retrieve detailed information.</li>
                <li><strong>View Results:</strong> Review the comprehensive information including location, ISP, and network details.</li>
            </ol>

            <h3>Frequently Asked Questions About IP Address Lookup</h3>

            <h4>How do I find the location of an IP address?</h4>
            <p>Enter the IP address in the lookup field above and click 'Lookup IP Address'. Our tool will instantly provide location details including country, region, city, and approximate coordinates based on the IP's registered location.</p>

            <h4>What information can I get from an IP address lookup?</h4>
            <p>An IP address lookup typically provides country, region/state, city, postal code, ISP name, organization, timezone, and approximate latitude/longitude coordinates. Note that this information is based on registration data and may not reflect the exact physical location.</p>

            <h4>Can I trace someone's exact location with their IP address?</h4>
            <p>No, IP address lookup cannot provide someone's exact physical location. It only shows the general area where the IP is registered, typically at the city or regional level. Exact location tracking requires additional methods and is restricted by privacy laws.</p>

            <h4>Is it legal to lookup someone's IP address?</h4>
            <p>Yes, looking up publicly available IP address information is legal. IP addresses are not considered private information, and geolocation data is publicly accessible. However, using this information for harassment, stalking, or illegal activities is prohibited.</p>

            <h4>How accurate is IP address geolocation?</h4>
            <p>IP geolocation accuracy varies by location and ISP. Country-level accuracy is typically 95-99%, while city-level accuracy ranges from 50-80%. Rural areas and mobile networks tend to be less accurate than urban areas with fixed broadband connections.</p>
        </div>

        <div class="ip-lookup-features">
            <h3 class="ip-lookup-features-title">Key Features:</h3>
            <ul class="ip-lookup-features-list">
                <li class="ip-lookup-features-item" style="margin-bottom: 0.3em;">Real-time IP Geolocation</li>
                <li class="ip-lookup-features-item" style="margin-bottom: 0.3em;">ISP and Organization Details</li>
                <li class="ip-lookup-features-item" style="margin-bottom: 0.3em;">Country and City Information</li>
                <li class="ip-lookup-features-item" style="margin-bottom: 0.3em;">Network Range Analysis</li>
                <li class="ip-lookup-features-item" style="margin-bottom: 0.3em;">Timezone Detection</li>
                <li class="ip-lookup-features-item" style="margin-bottom: 0.3em;">Mobile-Friendly Interface</li>
                <li class="ip-lookup-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('ipInput'),
                output: () => document.getElementById('ipOutput')
            };

            function isValidIP(ip) {
                const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
                const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
                return ipv4Regex.test(ip) || ipv6Regex.test(ip);
            }

            function displayIPInfo(data) {
                const output = elements.output();

                if (!data || data.status === 'fail') {
                    output.innerHTML = '<div class="error">Unable to retrieve information for this IP address. Please check the IP and try again.</div>';
                    return;
                }

                const infoItems = [
                    { label: 'IP Address', value: data.query || 'N/A' },
                    { label: 'Country', value: data.country || 'N/A' },
                    { label: 'Country Code', value: data.countryCode || 'N/A' },
                    { label: 'Region', value: data.regionName || 'N/A' },
                    { label: 'Region Code', value: data.region || 'N/A' },
                    { label: 'City', value: data.city || 'N/A' },
                    { label: 'ZIP Code', value: data.zip || 'N/A' },
                    { label: 'Latitude', value: data.lat ? data.lat.toString() : 'N/A' },
                    { label: 'Longitude', value: data.lon ? data.lon.toString() : 'N/A' },
                    { label: 'Timezone', value: data.timezone || 'N/A' },
                    { label: 'ISP', value: data.isp || 'N/A' },
                    { label: 'Organization', value: data.org || 'N/A' },
                    { label: 'AS Number', value: data.as || 'N/A' }
                ];

                const gridHTML = infoItems.map(item => `
                    <div class="ip-info-item">
                        <div class="ip-info-label">${item.label}:</div>
                        <div class="ip-info-value">${item.value}</div>
                    </div>
                `).join('');

                output.innerHTML = `<div class="ip-info-grid">${gridHTML}</div>`;
            }

            window.IPLookup = {
                async lookup() {
                    const input = elements.input().value.trim();
                    const output = elements.output();

                    if (!input) {
                        output.innerHTML = '<div class="error">Please enter an IP address to lookup.</div>';
                        return;
                    }

                    if (!isValidIP(input)) {
                        output.innerHTML = '<div class="error">Please enter a valid IP address (IPv4 or IPv6).</div>';
                        return;
                    }

                    output.innerHTML = '<div class="loading">Looking up IP address information...</div>';

                    try {
                        // Using ip-api.com service (free tier)
                        const response = await fetch(`http://ip-api.com/json/${input}?fields=status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,isp,org,as,query`);

                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }

                        const data = await response.json();
                        displayIPInfo(data);

                    } catch (error) {
                        console.error('Error fetching IP information:', error);

                        // Fallback: Display mock data for demonstration
                        const mockData = {
                            query: input,
                            country: 'United States',
                            countryCode: 'US',
                            region: 'CA',
                            regionName: 'California',
                            city: 'Mountain View',
                            zip: '94043',
                            lat: 37.4056,
                            lon: -122.0775,
                            timezone: 'America/Los_Angeles',
                            isp: 'Google LLC',
                            org: 'Google Public DNS',
                            as: 'AS15169 Google LLC'
                        };

                        output.innerHTML = `
                            <div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 12px; margin-bottom: 16px; color: #92400e;">
                                <strong>Demo Mode:</strong> Unable to fetch live data. Showing sample information below.
                            </div>
                        `;
                        displayIPInfo(mockData);
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().innerHTML = 'Enter an IP address above to see detailed information...';
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Enter key shortcut
                elements.input().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        IPLookup.lookup();
                    }
                });

                // Auto-detect user's IP on page load (optional)
                // Uncomment the following lines if you want to auto-populate with user's IP
                /*
                fetch('https://api.ipify.org?format=json')
                    .then(response => response.json())
                    .then(data => {
                        elements.input().placeholder = `Enter IP address (Your IP: ${data.ip})`;
                    })
                    .catch(() => {
                        // Silently fail if unable to detect user's IP
                    });
                */
            });
        })();
    </script>
</body>
</html>
