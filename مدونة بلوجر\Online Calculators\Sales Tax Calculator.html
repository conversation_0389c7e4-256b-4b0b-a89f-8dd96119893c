<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Tax Calculator Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Sales Tax Calculator - Calculate Tax Amount & Total Price",
        "description": "Calculate sales tax, total price including tax, or find the tax rate for any purchase. Free online tool with reverse tax calculation and state tax rates.",
        "url": "https://www.webtoolskit.org/p/sales-tax-calculator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Sales Tax Calculator",
            "applicationCategory": "FinanceApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CalculateAction", "name": "Calculate Sales Tax" },
            { "@type": "CopyAction", "name": "Copy Tax Calculation Results" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to calculate sales tax amount?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate sales tax amount, multiply the purchase price by the tax rate. For example, if an item costs $100 and the tax rate is 8.5%, the sales tax is $100 × 0.085 = $8.50. The formula is: Sales Tax = Purchase Price × Tax Rate."
          }
        },
        {
          "@type": "Question",
          "name": "How to find total price after tax?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To find the total price after tax, add the sales tax amount to the original price. You can also multiply the original price by (1 + tax rate). For example, with a $100 item and 8.5% tax: Total = $100 + $8.50 = $108.50, or Total = $100 × 1.085 = $108.50."
          }
        },
        {
          "@type": "Question",
          "name": "How to remove sales tax from a total?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To remove sales tax from a total price, divide the total by (1 + tax rate). For example, if the total is $108.50 and tax rate is 8.5%, the pre-tax price is $108.50 ÷ 1.085 = $100. Then subtract this from the total to get the tax amount: $108.50 - $100 = $8.50."
          }
        },
        {
          "@type": "Question",
          "name": "What states have no sales tax?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Five US states have no statewide sales tax: Alaska, Delaware, Montana, New Hampshire, and Oregon. However, some of these states allow local municipalities to impose sales taxes. For example, Alaska has no state sales tax but allows local sales taxes in certain areas."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate tax rate from total price?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate the tax rate from total price, use this formula: Tax Rate = (Total Price - Original Price) ÷ Original Price × 100%. For example, if an item originally costs $100 and the total is $108.50, the tax rate is ($108.50 - $100) ÷ $100 × 100% = 8.5%."
          }
        }
      ]
    }
    </script>


    <style>
        /* Sales Tax Calculator Widget - Simplified & Template Compatible */
        .sales-tax-calculator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .sales-tax-calculator-widget-container * { box-sizing: border-box; }

        .sales-tax-calculator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .sales-tax-calculator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .sales-tax-calculator-input-group {
            margin-bottom: var(--spacing-lg);
        }

        .sales-tax-calculator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .sales-tax-calculator-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .sales-tax-calculator-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .sales-tax-calculator-select {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            cursor: pointer;
        }

        .sales-tax-calculator-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .sales-tax-calculator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .sales-tax-calculator-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .sales-tax-calculator-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .sales-tax-calculator-radio {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .sales-tax-calculator-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .sales-tax-calculator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .sales-tax-calculator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .sales-tax-calculator-btn:hover { transform: translateY(-2px); }

        .sales-tax-calculator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .sales-tax-calculator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .sales-tax-calculator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .sales-tax-calculator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .sales-tax-calculator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .sales-tax-calculator-btn-success:hover {
            background-color: #059669;
        }

        .sales-tax-calculator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-md);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
            margin: var(--spacing-sm) 0;
        }

        .sales-tax-calculator-result-title {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .sales-tax-calculator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 120px;
            color: var(--text-color);
            line-height: 1.8;
            white-space: pre-wrap;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            margin: 0;
        }

        .sales-tax-calculator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .sales-tax-calculator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .sales-tax-calculator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .sales-tax-calculator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .sales-tax-calculator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sales-tax-calculator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .sales-tax-calculator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .sales-tax-calculator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="discount-calculator"] .sales-tax-calculator-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="percentage-calculator"] .sales-tax-calculator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="margin-calculator"] .sales-tax-calculator-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }

        .sales-tax-calculator-related-tool-item:hover .sales-tax-calculator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="discount-calculator"]:hover .sales-tax-calculator-related-tool-icon { background: linear-gradient(145deg, #f87171, #b91c1c); }
        a[href*="percentage-calculator"]:hover .sales-tax-calculator-related-tool-icon { background: linear-gradient(145deg, #9d6ff7, #8e4aee); }
        a[href*="margin-calculator"]:hover .sales-tax-calculator-related-tool-icon { background: linear-gradient(145deg, #f6a509, #c67006); }
        
        .sales-tax-calculator-related-tool-item { box-shadow: none; border: none; }
        .sales-tax-calculator-related-tool-item:hover { box-shadow: none; border: none; }
        .sales-tax-calculator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .sales-tax-calculator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .sales-tax-calculator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .sales-tax-calculator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .sales-tax-calculator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .sales-tax-calculator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .sales-tax-calculator-related-tool-item:hover .sales-tax-calculator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .sales-tax-calculator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .sales-tax-calculator-widget-title { font-size: 1.875rem; }
            .sales-tax-calculator-buttons { flex-direction: column; }
            .sales-tax-calculator-btn { flex: none; }
            .sales-tax-calculator-options { grid-template-columns: 1fr; }
            .sales-tax-calculator-grid { grid-template-columns: 1fr; }
            .sales-tax-calculator-output { 
                padding: var(--spacing-xs) var(--spacing-sm); 
                font-size: 0.875rem; 
                min-height: 100px; 
                line-height: 1.6; 
            }
            .sales-tax-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .sales-tax-calculator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .sales-tax-calculator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .sales-tax-calculator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .sales-tax-calculator-output { 
                padding: var(--spacing-xs) var(--spacing-xs); 
                font-size: 0.8rem; 
                min-height: 80px; 
                line-height: 1.5; 
            }
            .sales-tax-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .sales-tax-calculator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .sales-tax-calculator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .sales-tax-calculator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .sales-tax-calculator-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        [data-theme="dark"] .sales-tax-calculator-select:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .sales-tax-calculator-radio:focus, .sales-tax-calculator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .sales-tax-calculator-output::selection { background-color: var(--primary-color); color: white; }
        .sales-tax-calculator-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .sales-tax-calculator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="sales-tax-calculator-widget-container">
        <h1 class="sales-tax-calculator-widget-title">Sales Tax Calculator</h1>
        <p class="sales-tax-calculator-widget-description">
            Calculate sales tax, total price including tax, or find the tax rate for any purchase. Perfect for shopping, business transactions, and financial planning.
        </p>
        
        <div class="sales-tax-calculator-options">
            <div class="sales-tax-calculator-option">
                <input type="radio" id="calculateTax" name="calculationType" class="sales-tax-calculator-radio" value="calculateTax" checked>
                <label for="calculateTax" class="sales-tax-calculator-option-label">Calculate Tax Amount</label>
            </div>
            <div class="sales-tax-calculator-option">
                <input type="radio" id="calculateTotal" name="calculationType" class="sales-tax-calculator-radio" value="calculateTotal">
                <label for="calculateTotal" class="sales-tax-calculator-option-label">Calculate Total Price</label>
            </div>
            <div class="sales-tax-calculator-option">
                <input type="radio" id="removeTax" name="calculationType" class="sales-tax-calculator-radio" value="removeTax">
                <label for="removeTax" class="sales-tax-calculator-option-label">Remove Tax from Total</label>
            </div>
            <div class="sales-tax-calculator-option">
                <input type="radio" id="findTaxRate" name="calculationType" class="sales-tax-calculator-radio" value="findTaxRate">
                <label for="findTaxRate" class="sales-tax-calculator-option-label">Find Tax Rate</label>
            </div>
        </div>

        <div class="sales-tax-calculator-grid">
            <div class="sales-tax-calculator-input-group" id="priceGroup">
                <label for="price" class="sales-tax-calculator-label">Price:</label>
                <input 
                    type="number" 
                    id="price" 
                    class="sales-tax-calculator-input"
                    placeholder="Enter price amount"
                    step="0.01"
                    min="0"
                >
            </div>
            
            <div class="sales-tax-calculator-input-group" id="taxRateGroup">
                <label for="taxRate" class="sales-tax-calculator-label">Tax Rate (%):</label>
                <input 
                    type="number" 
                    id="taxRate" 
                    class="sales-tax-calculator-input"
                    placeholder="Enter tax rate"
                    step="0.01"
                    min="0"
                    max="100"
                >
            </div>
            
            <div class="sales-tax-calculator-input-group" id="totalPriceGroup" style="display: none;">
                <label for="totalPrice" class="sales-tax-calculator-label">Total Price:</label>
                <input 
                    type="number" 
                    id="totalPrice" 
                    class="sales-tax-calculator-input"
                    placeholder="Enter total price"
                    step="0.01"
                    min="0"
                >
            </div>
            
            <div class="sales-tax-calculator-input-group">
                <label for="statePreset" class="sales-tax-calculator-label">State Preset (Optional):</label>
                <select id="statePreset" class="sales-tax-calculator-select">
                    <option value="">Select a state</option>
                    <option value="0">No Sales Tax States</option>
                    <option value="2.9">Colorado - 2.9%</option>
                    <option value="6.35">Connecticut - 6.35%</option>
                    <option value="6">Florida - 6%</option>
                    <option value="4">Georgia - 4%</option>
                    <option value="4.712">Illinois - 4.712%</option>
                    <option value="7">Indiana - 7%</option>
                    <option value="6.5">Nevada - 6.5%</option>
                    <option value="6.625">New Jersey - 6.625%</option>
                    <option value="4">New York - 4%</option>
                    <option value="4.75">North Carolina - 4.75%</option>
                    <option value="5.75">Ohio - 5.75%</option>
                    <option value="4.225">Pennsylvania - 4.225%</option>
                    <option value="6.25">Texas - 6.25%</option>
                    <option value="6.85">Virginia - 6.85%</option>
                    <option value="6.5">Washington - 6.5%</option>
                </select>
            </div>
        </div>

        <div class="sales-tax-calculator-buttons">
            <button class="sales-tax-calculator-btn sales-tax-calculator-btn-primary" onclick="SalesTaxCalculator.calculate()">
                Calculate Tax
            </button>
            <button class="sales-tax-calculator-btn sales-tax-calculator-btn-secondary" onclick="SalesTaxCalculator.clear()">
                Clear All
            </button>
            <button class="sales-tax-calculator-btn sales-tax-calculator-btn-success" onclick="SalesTaxCalculator.copy()">
                Copy Result
            </button>
        </div>

        <div class="sales-tax-calculator-result">
            <h3 class="sales-tax-calculator-result-title">Tax Calculation Results:</h3>
            <div class="sales-tax-calculator-output" id="salesTaxCalculatorOutput">
                Your tax calculation results will appear here...
            </div>
        </div>

        <div class="sales-tax-calculator-related-tools">
            <h3 class="sales-tax-calculator-related-tools-title">Related Tools</h3>
            <div class="sales-tax-calculator-related-tools-grid">
                <a href="/p/discount-calculator.html" class="sales-tax-calculator-related-tool-item" rel="noopener">
                    <div class="sales-tax-calculator-related-tool-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="sales-tax-calculator-related-tool-name">Discount Calculator</div>
                </a>

                <a href="/p/percentage-calculator.html" class="sales-tax-calculator-related-tool-item" rel="noopener">
                    <div class="sales-tax-calculator-related-tool-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="sales-tax-calculator-related-tool-name">Percentage Calculator</div>
                </a>

                <a href="/p/margin-calculator.html" class="sales-tax-calculator-related-tool-item" rel="noopener">
                    <div class="sales-tax-calculator-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="sales-tax-calculator-related-tool-name">Margin Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Simplify Your Tax Calculations with Our Sales Tax Calculator</h2>
            <p>A <strong>Sales Tax Calculator</strong> is an essential tool for consumers, business owners, and financial professionals who need to accurately calculate tax amounts, total prices, or determine tax rates. Whether you're shopping online, running a retail business, or managing finances, understanding sales tax calculations is crucial for budgeting and compliance. Our comprehensive calculator handles multiple calculation types including tax amount calculation, total price determination, reverse tax calculation, and tax rate finding.</p>
            <p>Sales tax varies significantly across different states and municipalities, making it challenging to calculate manually. Our tool includes preset tax rates for major states and allows custom tax rate entry for precise calculations. The calculator also supports reverse calculations, helping you determine the original price before tax when you only know the total amount.</p>
            
            <h3>How to Use the Sales Tax Calculator</h3>
            <ol>
                <li><strong>Choose Calculation Type:</strong> Select from four calculation modes - calculate tax amount, find total price, remove tax from total, or determine tax rate.</li>
                <li><strong>Enter Required Values:</strong> Input the price and tax rate, or total price depending on your calculation type. The interface adapts to show relevant fields.</li>
                <li><strong>Select State Preset (Optional):</strong> Choose from common state tax rates for quick calculations, or enter your custom tax rate for precise results.</li>
                <li><strong>Calculate and Review:</strong> Click "Calculate Tax" to get detailed results including tax amount, total price, and percentage breakdowns with clear explanations.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Sales Tax</h3>
            
            <h4>How to calculate sales tax amount?</h4>
            <p>To calculate sales tax amount, multiply the purchase price by the tax rate. For example, if an item costs $100 and the tax rate is 8.5%, the sales tax is $100 × 0.085 = $8.50. The formula is: Sales Tax = Purchase Price × Tax Rate.</p>
            
            <h4>How to find total price after tax?</h4>
            <p>To find the total price after tax, add the sales tax amount to the original price. You can also multiply the original price by (1 + tax rate). For example, with a $100 item and 8.5% tax: Total = $100 + $8.50 = $108.50, or Total = $100 × 1.085 = $108.50.</p>
            
            <h4>How to remove sales tax from a total?</h4>
            <p>To remove sales tax from a total price, divide the total by (1 + tax rate). For example, if the total is $108.50 and tax rate is 8.5%, the pre-tax price is $108.50 ÷ 1.085 = $100. Then subtract this from the total to get the tax amount: $108.50 - $100 = $8.50.</p>
            
            <h4>What states have no sales tax?</h4>
            <p>Five US states have no statewide sales tax: Alaska, Delaware, Montana, New Hampshire, and Oregon. However, some of these states allow local municipalities to impose sales taxes. For example, Alaska has no state sales tax but allows local sales taxes in certain areas.</p>
            
            <h4>How to calculate tax rate from total price?</h4>
            <p>To calculate the tax rate from total price, use this formula: Tax Rate = (Total Price - Original Price) ÷ Original Price × 100%. For example, if an item originally costs $100 and the total is $108.50, the tax rate is ($108.50 - $100) ÷ $100 × 100% = 8.5%.</p>
        </div>


        <div class="sales-tax-calculator-features">
            <h3 class="sales-tax-calculator-features-title">Key Features:</h3>
            <ul class="sales-tax-calculator-features-list">
                <li class="sales-tax-calculator-features-item" style="margin-bottom: 0.3em;">Multiple calculation modes</li>
                <li class="sales-tax-calculator-features-item" style="margin-bottom: 0.3em;">State tax rate presets</li>
                <li class="sales-tax-calculator-features-item" style="margin-bottom: 0.3em;">Reverse tax calculation</li>
                <li class="sales-tax-calculator-features-item" style="margin-bottom: 0.3em;">Tax rate finder</li>
                <li class="sales-tax-calculator-features-item" style="margin-bottom: 0.3em;">Detailed breakdown results</li>
                <li class="sales-tax-calculator-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="sales-tax-calculator-features-item">One-click copy functionality</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="sales-tax-calculator-notification" id="salesTaxCalculatorNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Sales Tax Calculator
        (function() {
            'use strict';

            const elements = {
                price: () => document.getElementById('price'),
                taxRate: () => document.getElementById('taxRate'),
                totalPrice: () => document.getElementById('totalPrice'),
                statePreset: () => document.getElementById('statePreset'),
                priceGroup: () => document.getElementById('priceGroup'),
                taxRateGroup: () => document.getElementById('taxRateGroup'),
                totalPriceGroup: () => document.getElementById('totalPriceGroup'),
                calculationType: () => document.querySelector('input[name="calculationType"]:checked'),
                output: () => document.getElementById('salesTaxCalculatorOutput'),
                notification: () => document.getElementById('salesTaxCalculatorNotification')
            };

            window.SalesTaxCalculator = {
                calculate() {
                    const output = elements.output();
                    const calculationType = elements.calculationType().value;
                    
                    const price = parseFloat(elements.price().value) || 0;
                    const taxRate = parseFloat(elements.taxRate().value) || 0;
                    const totalPrice = parseFloat(elements.totalPrice().value) || 0;

                    // Validate inputs based on calculation type
                    if (!this.validateInputs(calculationType, price, taxRate, totalPrice)) {
                        output.textContent = 'Please enter valid numbers for all required fields.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const results = this.performCalculation(calculationType, price, taxRate, totalPrice);
                    output.textContent = results;
                },

                validateInputs(type, price, taxRate, totalPrice) {
                    switch (type) {
                        case 'calculateTax':
                        case 'calculateTotal':
                            return price > 0 && taxRate >= 0;
                        case 'removeTax':
                            return totalPrice > 0 && taxRate >= 0;
                        case 'findTaxRate':
                            return price > 0 && totalPrice > price;
                        default:
                            return false;
                    }
                },

                performCalculation(type, price, taxRate, totalPrice) {
                    let results = [];
                    
                    switch (type) {
                        case 'calculateTax':
                            return this.calculateTaxAmount(price, taxRate);
                        case 'calculateTotal':
                            return this.calculateTotalPrice(price, taxRate);
                        case 'removeTax':
                            return this.removeTaxFromTotal(totalPrice, taxRate);
                        case 'findTaxRate':
                            return this.findTaxRate(price, totalPrice);
                        default:
                            return 'Invalid calculation type selected.';
                    }
                },

                calculateTaxAmount(price, taxRate) {
                    const taxAmount = price * (taxRate / 100);
                    const totalWithTax = price + taxAmount;
                    
                    let results = [];
                    results.push(`SALES TAX CALCULATION`);
                    results.push(`=======================================`);
                    results.push('');
                    results.push(`Input Values:`);
                    results.push(`  Original Price: $${price.toFixed(2)}`);
                    results.push(`  Tax Rate: ${taxRate}%`);
                    results.push('');
                    results.push(`Calculation:`);
                    results.push(`  Sales Tax = Price x Tax Rate`);
                    results.push(`  Sales Tax = $${price.toFixed(2)} x ${taxRate}%`);
                    results.push(`  Sales Tax = $${price.toFixed(2)} x ${(taxRate/100).toFixed(4)}`);
                    results.push(`  Sales Tax = $${taxAmount.toFixed(2)}`);
                    results.push('');
                    results.push(`RESULTS:`);
                    results.push(`  - Sales Tax Amount: $${taxAmount.toFixed(2)}`);
                    results.push(`  - Original Price: $${price.toFixed(2)}`);
                    results.push(`  - Total Price (with tax): $${totalWithTax.toFixed(2)}`);
                    results.push('');
                    results.push(`Summary:`);
                    results.push(`  You will pay $${taxAmount.toFixed(2)} in sales tax`);
                    results.push(`  on a $${price.toFixed(2)} purchase at ${taxRate}% tax rate.`);
                    
                    return results.join('\n');
                },

                calculateTotalPrice(price, taxRate) {
                    const taxAmount = price * (taxRate / 100);
                    const totalWithTax = price + taxAmount;
                    
                    let results = [];
                    results.push(`TOTAL PRICE CALCULATION`);
                    results.push(`=======================================`);
                    results.push('');
                    results.push(`Input Values:`);
                    results.push(`  Original Price: $${price.toFixed(2)}`);
                    results.push(`  Tax Rate: ${taxRate}%`);
                    results.push('');
                    results.push(`Calculation:`);
                    results.push(`  Total Price = Price + (Price x Tax Rate)`);
                    results.push(`  Total Price = Price x (1 + Tax Rate)`);
                    results.push(`  Total Price = $${price.toFixed(2)} x (1 + ${(taxRate/100).toFixed(4)})`);
                    results.push(`  Total Price = $${price.toFixed(2)} x ${(1 + taxRate/100).toFixed(4)}`);
                    results.push(`  Total Price = $${totalWithTax.toFixed(2)}`);
                    results.push('');
                    results.push(`RESULTS:`);
                    results.push(`  - Original Price: $${price.toFixed(2)}`);
                    results.push(`  - Sales Tax Amount: $${taxAmount.toFixed(2)}`);
                    results.push(`  - Total Price (with tax): $${totalWithTax.toFixed(2)}`);
                    results.push('');
                    results.push(`Summary:`);
                    results.push(`  The total price including ${taxRate}% sales tax`);
                    results.push(`  is $${totalWithTax.toFixed(2)} for a $${price.toFixed(2)} item.`);
                    
                    return results.join('\n');
                },

                removeTaxFromTotal(totalPrice, taxRate) {
                    const priceBeforeTax = totalPrice / (1 + taxRate / 100);
                    const taxAmount = totalPrice - priceBeforeTax;
                    
                    let results = [];
                    results.push(`REMOVE TAX FROM TOTAL`);
                    results.push(`=======================================`);
                    results.push('');
                    results.push(`Input Values:`);
                    results.push(`  Total Price (with tax): $${totalPrice.toFixed(2)}`);
                    results.push(`  Tax Rate: ${taxRate}%`);
                    results.push('');
                    results.push(`Calculation:`);
                    results.push(`  Price Before Tax = Total Price / (1 + Tax Rate)`);
                    results.push(`  Price Before Tax = $${totalPrice.toFixed(2)} / (1 + ${(taxRate/100).toFixed(4)})`);
                    results.push(`  Price Before Tax = $${totalPrice.toFixed(2)} / ${(1 + taxRate/100).toFixed(4)}`);
                    results.push(`  Price Before Tax = $${priceBeforeTax.toFixed(2)}`);
                    results.push('');
                    results.push(`  Tax Amount = Total Price - Price Before Tax`);
                    results.push(`  Tax Amount = $${totalPrice.toFixed(2)} - $${priceBeforeTax.toFixed(2)}`);
                    results.push(`  Tax Amount = $${taxAmount.toFixed(2)}`);
                    results.push('');
                    results.push(`RESULTS:`);
                    results.push(`  - Price Before Tax: $${priceBeforeTax.toFixed(2)}`);
                    results.push(`  - Sales Tax Amount: $${taxAmount.toFixed(2)}`);
                    results.push(`  - Total Price (with tax): $${totalPrice.toFixed(2)}`);
                    results.push('');
                    results.push(`Summary:`);
                    results.push(`  The original price was $${priceBeforeTax.toFixed(2)}`);
                    results.push(`  with $${taxAmount.toFixed(2)} in sales tax at ${taxRate}% rate.`);
                    
                    return results.join('\n');
                },

                findTaxRate(price, totalPrice) {
                    const taxAmount = totalPrice - price;
                    const taxRate = (taxAmount / price) * 100;
                    
                    let results = [];
                    results.push(`FIND TAX RATE`);
                    results.push(`=======================================`);
                    results.push('');
                    results.push(`Input Values:`);
                    results.push(`  Original Price: $${price.toFixed(2)}`);
                    results.push(`  Total Price (with tax): $${totalPrice.toFixed(2)}`);
                    results.push('');
                    results.push(`Calculation:`);
                    results.push(`  Tax Amount = Total Price - Original Price`);
                    results.push(`  Tax Amount = $${totalPrice.toFixed(2)} - $${price.toFixed(2)}`);
                    results.push(`  Tax Amount = $${taxAmount.toFixed(2)}`);
                    results.push('');
                    results.push(`  Tax Rate = (Tax Amount / Original Price) x 100%`);
                    results.push(`  Tax Rate = ($${taxAmount.toFixed(2)} / $${price.toFixed(2)}) x 100%`);
                    results.push(`  Tax Rate = ${(taxAmount/price).toFixed(4)} x 100%`);
                    results.push(`  Tax Rate = ${taxRate.toFixed(2)}%`);
                    results.push('');
                    results.push(`RESULTS:`);
                    results.push(`  - Original Price: $${price.toFixed(2)}`);
                    results.push(`  - Sales Tax Amount: $${taxAmount.toFixed(2)}`);
                    results.push(`  - Tax Rate: ${taxRate.toFixed(2)}%`);
                    results.push(`  - Total Price (with tax): $${totalPrice.toFixed(2)}`);
                    results.push('');
                    results.push(`Summary:`);
                    results.push(`  The tax rate is ${taxRate.toFixed(2)}% based on`);
                    results.push(`  the difference between prices.`);
                    
                    return results.join('\n');
                },

                updateUI() {
                    const calculationType = elements.calculationType().value;
                    const priceGroup = elements.priceGroup();
                    const taxRateGroup = elements.taxRateGroup();
                    const totalPriceGroup = elements.totalPriceGroup();
                    
                    // Reset visibility
                    priceGroup.style.display = 'block';
                    taxRateGroup.style.display = 'block';
                    totalPriceGroup.style.display = 'none';
                    
                    // Update labels and visibility based on calculation type
                    switch (calculationType) {
                        case 'calculateTax':
                            elements.price().parentElement.querySelector('label').textContent = 'Price:';
                            elements.price().placeholder = 'Enter price amount';
                            break;
                        case 'calculateTotal':
                            elements.price().parentElement.querySelector('label').textContent = 'Price:';
                            elements.price().placeholder = 'Enter price amount';
                            break;
                        case 'removeTax':
                            totalPriceGroup.style.display = 'block';
                            elements.totalPrice().parentElement.querySelector('label').textContent = 'Total Price (with tax):';
                            elements.totalPrice().placeholder = 'Enter total price including tax';
                            break;
                        case 'findTaxRate':
                            totalPriceGroup.style.display = 'block';
                            taxRateGroup.style.display = 'none';
                            elements.price().parentElement.querySelector('label').textContent = 'Original Price:';
                            elements.price().placeholder = 'Enter original price';
                            elements.totalPrice().parentElement.querySelector('label').textContent = 'Total Price (with tax):';
                            elements.totalPrice().placeholder = 'Enter total price including tax';
                            break;
                    }
                },

                clear() {
                    elements.price().value = '';
                    elements.taxRate().value = '';
                    elements.totalPrice().value = '';
                    elements.statePreset().value = '';
                    document.getElementById('calculateTax').checked = true;
                    this.updateUI();
                    elements.output().textContent = 'Your tax calculation results will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your tax calculation results will appear here...', 'Please enter valid numbers for all required fields.', 'Invalid calculation type selected.'].includes(text)) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                // Handle calculation type changes
                const calculationTypes = document.querySelectorAll('input[name="calculationType"]');
                calculationTypes.forEach(radio => {
                    radio.addEventListener('change', function() {
                        SalesTaxCalculator.updateUI();
                    });
                });

                // Handle state preset changes
                elements.statePreset().addEventListener('change', function() {
                    if (this.value && this.value !== '0') {
                        elements.taxRate().value = this.value;
                    } else if (this.value === '0') {
                        elements.taxRate().value = '0';
                    }
                });

                // Initialize UI
                SalesTaxCalculator.updateUI();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key for calculation
                const inputs = document.querySelectorAll('.sales-tax-calculator-input');
                inputs.forEach(input => {
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                            e.preventDefault();
                            SalesTaxCalculator.calculate();
                        }
                    });
                });
            });
        })();
    </script>
</body>
</html>