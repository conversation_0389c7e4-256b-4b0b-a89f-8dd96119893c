<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free VTT to SRT Converter - Convert WebVTT to SRT Online</title>
    <meta name="description" content="Convert VTT subtitle files to SRT format instantly with our free VTT to SRT Converter. Perfect for video editing, media players, and subtitle management.">
    <meta name="keywords" content="vtt to srt, vtt to srt converter, webvtt to srt, subtitle converter, video subtitles, srt converter">
    <link rel="canonical" href="https://www.webtoolskit.org/p/vtt-to-srt.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free VTT to SRT Converter - Convert WebVTT to SRT Online",
        "description": "Convert VTT subtitle files to SRT format instantly with our free VTT to SRT Converter. Perfect for video editing, media players, and subtitle management.",
        "url": "https://www.webtoolskit.org/p/vtt-to-srt.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "VTT to SRT Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "VTT to SRT conversion",
                "WebVTT subtitle processing",
                "Video editing utility",
                "Media player compatibility",
                "Subtitle format conversion"
            ]
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert VTT to SRT" },
            { "@type": "DownloadAction", "name": "Download SRT File" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I convert VTT to SRT subtitle format?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Paste your VTT subtitle content into the input field above and click 'Convert to SRT'. Our tool will instantly convert the WebVTT format to SRT format, which you can then copy or download for use with video players and editing software."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between VTT and SRT files?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "VTT (WebVTT) is designed for web browsers and HTML5 video, supporting styling and positioning. SRT (SubRip) is a simpler format widely supported by video players and editing software. SRT uses numbered sequences while VTT uses timestamps with 'WEBVTT' header."
          }
        },
        {
          "@type": "Question",
          "name": "Can I use SRT files instead of VTT for videos?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, SRT files work with most video players, media software, and streaming platforms. While VTT is preferred for web browsers, SRT has broader compatibility with desktop video players, mobile apps, and video editing software."
          }
        },
        {
          "@type": "Question",
          "name": "How do I convert WebVTT subtitles to SRT?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our converter handles the technical differences automatically. It removes the WEBVTT header, converts timestamp formats (00:00.000 to 00:00,000), adds sequence numbers, and ensures proper SRT formatting for maximum compatibility."
          }
        },
        {
          "@type": "Question",
          "name": "Which subtitle format is better VTT or SRT?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "It depends on your use case. VTT is better for web videos with advanced styling needs, while SRT is better for broader compatibility with video players, editing software, and streaming platforms. SRT is more universally supported."
          }
        }
      ]
    }
    </script>

    <style>
        /* VTT to SRT Converter Widget - Simplified & Template Compatible */
        .vtt-srt-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .vtt-srt-widget-container * { box-sizing: border-box; }

        .vtt-srt-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .vtt-srt-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .vtt-srt-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .vtt-srt-field {
            display: flex;
            flex-direction: column;
        }

        .vtt-srt-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .vtt-srt-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            resize: vertical;
            min-height: 200px;
        }

        .vtt-srt-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .vtt-srt-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .vtt-srt-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .vtt-srt-btn:hover { transform: translateY(-2px); }

        .vtt-srt-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .vtt-srt-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .vtt-srt-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .vtt-srt-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .vtt-srt-btn-success {
            background-color: #10b981;
            color: white;
        }

        .vtt-srt-btn-success:hover {
            background-color: #059669;
        }

        .vtt-srt-btn-download {
            background-color: #8b5cf6;
            color: white;
        }

        .vtt-srt-btn-download:hover {
            background-color: #7c3aed;
        }

        .vtt-srt-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .vtt-srt-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .vtt-srt-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 400px;
        }

        .vtt-srt-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .vtt-srt-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        @media (max-width: 768px) {
            .vtt-srt-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .vtt-srt-widget-title { font-size: 1.875rem; }
            .vtt-srt-buttons { flex-direction: column; }
            .vtt-srt-btn { flex: none; }
        }

        [data-theme="dark"] .vtt-srt-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .vtt-srt-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .vtt-srt-output::selection { background-color: var(--primary-color); color: white; }

        .vtt-srt-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="srt-to-vtt"] .vtt-srt-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="youtube-thumbnail-downloader"] .vtt-srt-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="base64-encode"] .vtt-srt-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }

        .vtt-srt-related-tool-item:hover .vtt-srt-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="srt-to-vtt"]:hover .vtt-srt-related-tool-icon { background: linear-gradient(145deg, #f56565, #e53e3e); }
        a[href*="youtube-thumbnail-downloader"]:hover .vtt-srt-related-tool-icon { background: linear-gradient(145deg, #38d9a9, #20c997); }
        a[href*="base64-encode"]:hover .vtt-srt-related-tool-icon { background: linear-gradient(145deg, #fbbf24, #f59e0b); }

        .vtt-srt-related-tool-item { box-shadow: none; border: none; }
        .vtt-srt-related-tool-item:hover { box-shadow: none; border: none; }
        .vtt-srt-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .vtt-srt-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .vtt-srt-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .vtt-srt-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .vtt-srt-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .vtt-srt-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .vtt-srt-related-tool-item:hover .vtt-srt-related-tool-name { color: var(--primary-color); }

        .vtt-srt-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .vtt-srt-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .vtt-srt-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .vtt-srt-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .vtt-srt-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .vtt-srt-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .vtt-srt-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .vtt-srt-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .vtt-srt-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .vtt-srt-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .vtt-srt-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .vtt-srt-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .vtt-srt-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .vtt-srt-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="vtt-srt-widget-container">
        <h1 class="vtt-srt-widget-title">VTT to SRT Converter</h1>
        <p class="vtt-srt-widget-description">
            Convert WebVTT subtitle files to SRT format instantly. Perfect for video editing, media players, and subtitle management across different platforms.
        </p>

        <form class="vtt-srt-form">
            <div class="vtt-srt-field">
                <label for="vttInput" class="vtt-srt-label">Paste VTT Content:</label>
                <textarea
                    id="vttInput"
                    class="vtt-srt-textarea"
                    placeholder="Paste your VTT subtitle content here...

Example:
WEBVTT

00:00.000 --> 00:02.000
Hello, welcome to our video!

00:02.000 --> 00:05.000
This is a sample subtitle."
                ></textarea>
            </div>
        </form>

        <div class="vtt-srt-buttons">
            <button class="vtt-srt-btn vtt-srt-btn-primary" onclick="VTTtoSRTConverter.convert()">
                Convert to SRT
            </button>
            <button class="vtt-srt-btn vtt-srt-btn-secondary" onclick="VTTtoSRTConverter.clear()">
                Clear All
            </button>
            <button class="vtt-srt-btn vtt-srt-btn-success" onclick="VTTtoSRTConverter.copy()">
                Copy SRT
            </button>
            <button class="vtt-srt-btn vtt-srt-btn-download" onclick="VTTtoSRTConverter.download()">
                Download SRT
            </button>
        </div>

        <div class="vtt-srt-result">
            <h3 class="vtt-srt-result-title">Converted SRT Content:</h3>
            <div class="vtt-srt-output" id="srtOutput">Your converted SRT subtitle content will appear here...</div>
        </div>

        <div class="vtt-srt-related-tools">
            <h3 class="vtt-srt-related-tools-title">Related Tools</h3>
            <div class="vtt-srt-related-tools-grid">
                <a href="/p/srt-to-vtt.html" class="vtt-srt-related-tool-item" rel="noopener">
                    <div class="vtt-srt-related-tool-icon">
                        <i class="fas fa-file-video"></i>
                    </div>
                    <div class="vtt-srt-related-tool-name">SRT to VTT</div>
                </a>

                <a href="/p/youtube-thumbnail-downloader.html" class="vtt-srt-related-tool-item" rel="noopener">
                    <div class="vtt-srt-related-tool-icon">
                        <i class="fab fa-youtube"></i>
                    </div>
                    <div class="vtt-srt-related-tool-name">YouTube Thumbnail Downloader</div>
                </a>

                <a href="/p/base64-encode.html" class="vtt-srt-related-tool-item" rel="noopener">
                    <div class="vtt-srt-related-tool-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="vtt-srt-related-tool-name">Base64 Encode</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional VTT to SRT Converter for Video Production</h2>
            <p>Our <strong>VTT to SRT Converter</strong> transforms WebVTT subtitle files into SRT format instantly, making your subtitles compatible with a wide range of video players, editing software, and streaming platforms. Whether you're working on video production, content creation, or media distribution, our tool ensures seamless subtitle format conversion.</p>
            <p>Perfect for content creators, video editors, and media professionals who need to convert web-based VTT subtitles to the universally supported SRT format. The tool handles all technical formatting differences automatically, ensuring your subtitles work across different platforms and applications.</p>

            <h3>How to Use the VTT to SRT Converter</h3>
            <ol>
                <li><strong>Paste VTT Content:</strong> Copy your WebVTT subtitle content and paste it into the input field above.</li>
                <li><strong>Convert Format:</strong> Click "Convert to SRT" to transform the VTT format to SRT format instantly.</li>
                <li><strong>Copy or Download:</strong> Use "Copy SRT" to copy the result or "Download SRT" to save as a file.</li>
                <li><strong>Use in Projects:</strong> Import the converted SRT file into your video editing software or media player.</li>
            </ol>

            <h3>Frequently Asked Questions About VTT to SRT Conversion</h3>

            <h4>How do I convert VTT to SRT subtitle format?</h4>
            <p>Paste your VTT subtitle content into the input field above and click 'Convert to SRT'. Our tool will instantly convert the WebVTT format to SRT format, which you can then copy or download for use with video players and editing software.</p>

            <h4>What is the difference between VTT and SRT files?</h4>
            <p>VTT (WebVTT) is designed for web browsers and HTML5 video, supporting styling and positioning. SRT (SubRip) is a simpler format widely supported by video players and editing software. SRT uses numbered sequences while VTT uses timestamps with 'WEBVTT' header.</p>

            <h4>Can I use SRT files instead of VTT for videos?</h4>
            <p>Yes, SRT files work with most video players, media software, and streaming platforms. While VTT is preferred for web browsers, SRT has broader compatibility with desktop video players, mobile apps, and video editing software.</p>

            <h4>How do I convert WebVTT subtitles to SRT?</h4>
            <p>Our converter handles the technical differences automatically. It removes the WEBVTT header, converts timestamp formats (00:00.000 to 00:00,000), adds sequence numbers, and ensures proper SRT formatting for maximum compatibility.</p>

            <h4>Which subtitle format is better VTT or SRT?</h4>
            <p>It depends on your use case. VTT is better for web videos with advanced styling needs, while SRT is better for broader compatibility with video players, editing software, and streaming platforms. SRT is more universally supported.</p>
        </div>

        <div class="vtt-srt-features">
            <h3 class="vtt-srt-features-title">Key Features:</h3>
            <ul class="vtt-srt-features-list">
                <li class="vtt-srt-features-item" style="margin-bottom: 0.3em;">Instant VTT to SRT Conversion</li>
                <li class="vtt-srt-features-item" style="margin-bottom: 0.3em;">WebVTT Format Processing</li>
                <li class="vtt-srt-features-item" style="margin-bottom: 0.3em;">Video Editing Compatibility</li>
                <li class="vtt-srt-features-item" style="margin-bottom: 0.3em;">Media Player Support</li>
                <li class="vtt-srt-features-item" style="margin-bottom: 0.3em;">Download SRT Files</li>
                <li class="vtt-srt-features-item" style="margin-bottom: 0.3em;">Mobile-Friendly Interface</li>
                <li class="vtt-srt-features-item">100% Free and Accurate</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="vtt-srt-notification" id="vttSrtNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                vttInput: () => document.getElementById('vttInput'),
                srtOutput: () => document.getElementById('srtOutput'),
                notification: () => document.getElementById('vttSrtNotification')
            };

            function convertVTTtoSRT(vttContent) {
                if (!vttContent || !vttContent.trim()) {
                    throw new Error('Please provide VTT content to convert');
                }

                let lines = vttContent.trim().split('\n');
                let srtContent = '';
                let subtitleIndex = 1;
                let i = 0;

                // Skip WEBVTT header and any initial metadata
                while (i < lines.length && (lines[i].trim() === '' || lines[i].trim().startsWith('WEBVTT') || lines[i].trim().startsWith('NOTE'))) {
                    i++;
                }

                while (i < lines.length) {
                    // Skip empty lines
                    while (i < lines.length && lines[i].trim() === '') {
                        i++;
                    }

                    if (i >= lines.length) break;

                    // Look for timestamp line (contains -->)
                    if (lines[i].includes('-->')) {
                        // Add subtitle index
                        srtContent += subtitleIndex + '\n';

                        // Convert timestamp format from VTT to SRT
                        let timestampLine = lines[i].trim();
                        // Convert from 00:00.000 --> 00:02.000 to 00:00:00,000 --> 00:00:02,000
                        timestampLine = timestampLine.replace(/(\d{2}):(\d{2})\.(\d{3})/g, '$1:$2:00,$3');
                        // Handle cases where VTT might already have hours
                        timestampLine = timestampLine.replace(/(\d{2}):(\d{2}):(\d{2})\.(\d{3})/g, '$1:$2:$3,$4');

                        srtContent += timestampLine + '\n';
                        i++;

                        // Collect subtitle text lines
                        let subtitleText = '';
                        while (i < lines.length && lines[i].trim() !== '' && !lines[i].includes('-->')) {
                            if (subtitleText !== '') {
                                subtitleText += '\n';
                            }
                            // Remove VTT-specific tags and styling
                            let cleanLine = lines[i].trim()
                                .replace(/<[^>]*>/g, '') // Remove HTML tags
                                .replace(/\{[^}]*\}/g, '') // Remove CSS styling
                                .replace(/&lt;/g, '<')
                                .replace(/&gt;/g, '>')
                                .replace(/&amp;/g, '&');
                            subtitleText += cleanLine;
                            i++;
                        }

                        srtContent += subtitleText + '\n\n';
                        subtitleIndex++;
                    } else {
                        // Skip non-timestamp lines (might be cue identifiers)
                        i++;
                    }
                }

                return srtContent.trim();
            }

            window.VTTtoSRTConverter = {
                convert() {
                    const vttContent = elements.vttInput().value;
                    const output = elements.srtOutput();

                    try {
                        const srtContent = convertVTTtoSRT(vttContent);
                        output.textContent = srtContent;
                        output.style.color = '';
                    } catch (error) {
                        output.textContent = `Error: ${error.message}`;
                        output.style.color = '#dc2626';
                    }
                },

                clear() {
                    elements.vttInput().value = '';
                    elements.srtOutput().textContent = 'Your converted SRT subtitle content will appear here...';
                    elements.srtOutput().style.color = '';
                },

                copy() {
                    const text = elements.srtOutput().textContent;
                    if (text === 'Your converted SRT subtitle content will appear here...' || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                download() {
                    const text = elements.srtOutput().textContent;
                    if (text === 'Your converted SRT subtitle content will appear here...' || text.startsWith('Error:')) return;

                    const blob = new Blob([text], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'subtitles.srt';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    this.showNotification('Downloaded!');
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message = '✓ Copied to clipboard!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Auto-convert when user pastes content
                elements.vttInput().addEventListener('paste', function() {
                    setTimeout(() => {
                        if (this.value.trim()) {
                            VTTtoSRTConverter.convert();
                        }
                    }, 100);
                });

                // Enter key shortcut
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        VTTtoSRTConverter.convert();
                    }
                });

                // Sample VTT content for demonstration
                const sampleVTT = `WEBVTT

00:00.000 --> 00:02.000
Hello, welcome to our video!

00:02.000 --> 00:05.000
This is a sample subtitle.

00:05.000 --> 00:08.000
You can paste your VTT content above.`;

                // Uncomment to load sample content on page load
                // elements.vttInput().value = sampleVTT;
                // VTTtoSRTConverter.convert();
            });
        })();
    </script>
</body>
</html>
