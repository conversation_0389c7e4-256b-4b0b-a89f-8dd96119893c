<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word to Number Converter - Convert Text to Numbers Online</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Word to Number Converter - Convert Text to Numbers Online",
        "description": "Easily convert written number words like 'one hundred fifty' back into numeric digits (150). Our free tool is perfect for parsing text, verifying figures, and data entry tasks.",
        "url": "https://www.webtoolskit.org/p/word-to-number-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-10",
        "dateModified": "2025-06-20",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Word to Number Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Words to Number" },
            { "@type": "CopyAction", "name": "Copy Numeric Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How can I convert text to numbers?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Converting text to numbers typically means translating written number words (e.g., 'two hundred fifty-five') into their numeric digit form (255). This is the exact function of our Word to Number Converter. You simply type the words into the tool, and it parses them to produce the correct digits, which is useful for data entry, programming, and verifying financial amounts."
          }
        },
        {
          "@type": "Question",
          "name": "How to turn words into numbers in Excel?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Excel does not have a native function for converting number words into digits. The task usually requires a custom script using VBA (Visual Basic for Applications) or installing a third-party add-in. For a quick and easy solution without any software installation, you can use a web-based tool like this one."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert name into number?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "This tool is designed to convert number words (like 'one', 'two', 'hundred') into numbers, not personal names. Converting a name like 'John Smith' into a number is usually associated with numerology, which involves assigning a numeric value to each letter (e.g., A=1, B=2) and summing them up. That is a different type of calculation that this tool does not perform."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate a number based on a name?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Calculating a number based on a name is a practice found in numerology, not standard mathematics or data conversion. It involves a system where each letter of the alphabet corresponds to a number. You would spell out the name and add up the values of its letters to get a final number. Our converter specifically handles number words like 'fifty-four' and does not calculate values from names."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert characters into numeric?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The phrase 'convert characters into numeric' can mean two things. First, it can mean converting number words into digits, which is what this tool does (e.g., 'one' becomes '1'). Second, in programming, it can mean finding the numeric code that represents a character, such as its ASCII or Unicode value (e.g., the character 'A' has an ASCII value of 65). Our tool focuses on the first definition."
          }
        }
      ]
    }
    </script>

    <style>
        /* Word to Number Converter Widget - Simplified & Template Compatible */
        .word-to-number-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .word-to-number-converter-widget-container * { box-sizing: border-box; }

        .word-to-number-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .word-to-number-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .word-to-number-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .word-to-number-converter-input-group {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .word-to-number-converter-textarea {
            width: 100%;
            min-height: 120px;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            resize: vertical;
        }

        .word-to-number-converter-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .word-to-number-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .word-to-number-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .word-to-number-converter-btn:hover { transform: translateY(-2px); }

        .word-to-number-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .word-to-number-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .word-to-number-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .word-to-number-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .word-to-number-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .word-to-number-converter-btn-success:hover {
            background-color: #059669;
        }

        .word-to-number-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .word-to-number-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .word-to-number-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 1.125rem;
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .word-to-number-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .word-to-number-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .word-to-number-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .word-to-number-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .word-to-number-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .word-to-number-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .word-to-number-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .word-to-number-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="number-to-word-converter"] .word-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="roman-numerals-to-number"] .word-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="number-to-roman-numerals"] .word-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .word-to-number-converter-related-tool-item:hover .word-to-number-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="number-to-word-converter"]:hover .word-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #2dd4bf, #14b8a6); }
        a[href*="roman-numerals-to-number"]:hover .word-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        a[href*="number-to-roman-numerals"]:hover .word-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        
        .word-to-number-converter-related-tool-item { box-shadow: none; border: none; }
        .word-to-number-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .word-to-number-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .word-to-number-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .word-to-number-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .word-to-number-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .word-to-number-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .word-to-number-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .word-to-number-converter-related-tool-item:hover .word-to-number-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .word-to-number-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .word-to-number-converter-widget-title { font-size: 1.875rem; }
            .word-to-number-converter-buttons { flex-direction: column; }
            .word-to-number-converter-btn { flex: none; }
            .word-to-number-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .word-to-number-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .word-to-number-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .word-to-number-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .word-to-number-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .word-to-number-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .word-to-number-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .word-to-number-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .word-to-number-converter-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .word-to-number-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .word-to-number-converter-output::selection { background-color: var(--primary-color); color: white; }
        .word-to-number-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .word-to-number-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="word-to-number-converter-widget-container">
        <h1 class="word-to-number-converter-widget-title">Word to Number Converter</h1>
        <p class="word-to-number-converter-widget-description">
            Easily convert written number words, such as 'one hundred twenty-three', back into their numeric digit form (123).
        </p>
        
        <div class="word-to-number-converter-input-group">
            <label for="wordInput" class="word-to-number-converter-label">Enter Words:</label>
            <textarea
                id="wordInput" 
                class="word-to-number-converter-textarea"
                placeholder="e.g., one thousand two hundred thirty-four point five six"
            ></textarea>
        </div>

        <div class="word-to-number-converter-buttons">
            <button class="word-to-number-converter-btn word-to-number-converter-btn-primary" onclick="WordToNumberConverter.convert()">
                Convert to Number
            </button>
            <button class="word-to-number-converter-btn word-to-number-converter-btn-secondary" onclick="WordToNumberConverter.clear()">
                Clear All
            </button>
            <button class="word-to-number-converter-btn word-to-number-converter-btn-success" onclick="WordToNumberConverter.copy()">
                Copy Number
            </button>
        </div>

        <div class="word-to-number-converter-result">
            <h3 class="word-to-number-converter-result-title">Result in Numbers:</h3>
            <div class="word-to-number-converter-output" id="numberOutput">
                Your numeric result will appear here...
            </div>
        </div>

        <div class="word-to-number-converter-related-tools">
            <h3 class="word-to-number-converter-related-tools-title">Related Tools</h3>
            <div class="word-to-number-converter-related-tools-grid">
                <a href="/p/number-to-word-converter.html" class="word-to-number-converter-related-tool-item" rel="noopener">
                    <div class="word-to-number-converter-related-tool-icon">
                        <i class="fas fa-spell-check"></i>
                    </div>
                    <div class="word-to-number-converter-related-tool-name">Number to Word Converter</div>
                </a>
                <a href="/p/roman-numerals-to-number.html" class="word-to-number-converter-related-tool-item" rel="noopener">
                    <div class="word-to-number-converter-related-tool-icon">
                        <i class="fas fa-list-ol"></i>
                    </div>
                    <div class="word-to-number-converter-related-tool-name">Roman to Number Converter</div>
                </a>
                <a href="/p/number-to-roman-numerals.html" class="word-to-number-converter-related-tool-item" rel="noopener">
                    <div class="word-to-number-converter-related-tool-icon">
                        <i class="fas fa-columns"></i>
                    </div>
                    <div class="word-to-number-converter-related-tool-name">Number to Roman Numerals</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert Written Numbers to Digits with Ease</h2>
            <p>While we often convert numbers to words for formal documents, the reverse is also a common necessity. If you're working with transcribed audio, scanned documents, or data entry from written records, you may need to turn number words back into digits. Our free <strong>Word to Number Converter</strong> is a powerful tool that accurately parses English number phrases and converts them into their numerical form.</p>
            <p>This tool can interpret complex number phrases, including those with millions, thousands, and decimals. It's an invaluable asset for data analysts, financial clerks, programmers, and anyone who needs to quickly and accurately digitize written numerical data. Save time and reduce manual entry errors with this simple online utility.</p>

            <h3>How to Use the Word to Number Converter</h3>
            <ol>
                <li><strong>Enter the Words:</strong> Type or paste the written number (e.g., "five hundred forty-two") into the text area.</li>
                <li><strong>Convert:</strong> Click the "Convert to Number" button to process the text.</li>
                <li><strong>Get the Result:</strong> The tool will instantly display the corresponding numeric digits in the result box.</li>
                <li><strong>Copy the Number:</strong> Use the "Copy Number" button to copy the result to your clipboard.</li>
            </ol>

            <h3>Frequently Asked Questions About Converting Text to Numbers</h3>
            
            <h4>How can I convert text to numbers?</h4>
            <p>Converting text to numbers typically means translating written number words (e.g., 'two hundred fifty-five') into their numeric digit form (255). This is the exact function of our Word to Number Converter. You simply type the words into the tool, and it parses them to produce the correct digits, which is useful for data entry, programming, and verifying financial amounts.</p>

            <h4>How to turn words into numbers in Excel?</h4>
            <p>Excel does not have a native function for converting number words into digits. The task usually requires a custom script using VBA (Visual Basic for Applications) or installing a third-party add-in. For a quick and easy solution without any software installation, you can use a web-based tool like this one.</p>

            <h4>How to convert name into number?</h4>
            <p>This tool is designed to convert number words (like 'one', 'two', 'hundred') into numbers, not personal names. Converting a name like 'John Smith' into a number is usually associated with numerology, which involves assigning a numeric value to each letter (e.g., A=1, B=2) and summing them up. That is a different type of calculation that this tool does not perform.</p>

            <h4>How to calculate a number based on a name?</h4>
            <p>Calculating a number based on a name is a practice found in numerology, not standard mathematics or data conversion. It involves a system where each letter of the alphabet corresponds to a number. You would spell out the name and add up the values of its letters to get a final number. Our converter specifically handles number words like 'fifty-four' and does not calculate values from names.</p>

            <h4>How to convert characters into numeric?</h4>
            <p>The phrase 'convert characters into numeric' can mean two things. First, it can mean converting number words into digits, which is what this tool does (e.g., 'one' becomes '1'). Second, in programming, it can mean finding the numeric code that represents a character, such as its ASCII or Unicode value (e.g., the character 'A' has an ASCII value of 65). Our tool focuses on the first definition.</p>
        </div>

        <div class="word-to-number-converter-features">
            <h3 class="word-to-number-converter-features-title">Key Features:</h3>
            <ul class="word-to-number-converter-features-list">
                <li class="word-to-number-converter-features-item" style="margin-bottom: 0.3em;">Handles large numbers (billions+)</li>
                <li class="word-to-number-converter-features-item" style="margin-bottom: 0.3em;">Supports decimal points</li>
                <li class="word-to-number-converter-features-item" style="margin-bottom: 0.3em;">Ideal for data entry & validation</li>
                <li class="word-to-number-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="word-to-number-converter-features-item" style="margin-bottom: 0.3em;">Accurate text parsing</li>
                <li class="word-to-number-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side conversion</li>
                <li class="word-to-number-converter-features-item">100% free and private to use</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="word-to-number-converter-notification" id="wordToNumberConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Word to Number Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('wordInput'),
                output: () => document.getElementById('numberOutput'),
                notification: () => document.getElementById('wordToNumberConverterNotification')
            };
            
            const numberMap = {
                'zero': 0, 'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5, 'six': 6, 'seven': 7, 'eight': 8, 'nine': 9,
                'ten': 10, 'eleven': 11, 'twelve': 12, 'thirteen': 13, 'fourteen': 14, 'fifteen': 15, 'sixteen': 16, 'seventeen': 17, 'eighteen': 18, 'nineteen': 19,
                'twenty': 20, 'thirty': 30, 'forty': 40, 'fifty': 50, 'sixty': 60, 'seventy': 70, 'eighty': 80, 'ninety': 90
            };
            
            const scaleMap = { 'hundred': 100, 'thousand': 1000, 'million': 1000000, 'billion': 1000000000, 'trillion': 1000000000000 };

            window.WordToNumberConverter = {
                convert() {
                    const outputEl = elements.output();
                    let text = elements.input().value.trim();

                    if (!text) {
                        outputEl.textContent = 'Please enter some words to convert.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }
                    
                    outputEl.style.color = '';
                    
                    try {
                        let [integerPart, decimalPart] = text.toLowerCase().split('point');
                        
                        const integerNum = this.parseIntegerWords(integerPart);
                        let result = integerNum.toString();

                        if (decimalPart) {
                            const decimalNumStr = this.parseDecimalWords(decimalPart.trim());
                            result += '.' + decimalNumStr;
                        }

                        outputEl.textContent = result;

                    } catch (e) {
                        outputEl.textContent = e.message;
                        outputEl.style.color = '#dc2626';
                    }
                },
                
                parseIntegerWords(text) {
                    const words = text.replace(/[\s-]+/g, ' ').replace(/,
/g, '').replace(/ and /g, ' ').trim().split(' ');
                    
                    if (words.length === 1 && words[0] === '') return 0;
                    
                    let total = 0;
                    let current = 0;

                    words.forEach(word => {
                        if (numberMap[word] !== undefined) {
                            current += numberMap[word];
                        } else if (word === 'hundred') {
                            current *= 100;
                        } else if (scaleMap[word] !== undefined) {
                            total += current * scaleMap[word];
                            current = 0;
                        } else {
                           throw new Error(`Invalid word: '${word}'`);
                        }
                    });

                    return total + current;
                },

                parseDecimalWords(text) {
                    const words = text.trim().split(/\s+/);
                    let decimalStr = '';
                    words.forEach(word => {
                        if (numberMap[word] !== undefined && numberMap[word] < 10) {
                            decimalStr += numberMap[word];
                        } else {
                           throw new Error(`Invalid decimal word: '${word}'`);
                        }
                    });
                    return decimalStr;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your numeric result will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (!text || text.includes('...')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const inputEl = elements.input();
                inputEl.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        WordToNumberConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>