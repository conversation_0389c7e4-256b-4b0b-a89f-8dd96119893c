<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free WhatsApp Link Generator - Create Click-to-Chat Links Online</title>
    <meta name="description" content="Generate WhatsApp click-to-chat links instantly with our free WhatsApp Link Generator. Create direct WhatsApp links with pre-filled messages for business and personal use.">
    <meta name="keywords" content="whatsapp link generator, whatsapp click to chat, whatsapp link, wa.me link, whatsapp business link, whatsapp message link">
    <link rel="canonical" href="https://www.webtoolskit.org/p/whatsapp-link-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free WhatsApp Link Generator - Create Click-to-Chat Links Online",
        "description": "Generate WhatsApp click-to-chat links instantly with our free WhatsApp Link Generator. Create direct WhatsApp links with pre-filled messages for business and personal use.",
        "url": "https://www.webtoolskit.org/p/whatsapp-link-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "WhatsApp Link Generator",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "WhatsApp click-to-chat links",
                "Pre-filled message creation",
                "Business communication tool",
                "Direct WhatsApp access",
                "Mobile and desktop compatible"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate WhatsApp Link" },
            { "@type": "CopyAction", "name": "Copy WhatsApp Link" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I create a WhatsApp link with a pre-filled message?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Enter the phone number (with country code) and your desired message in the fields above, then click 'Generate WhatsApp Link'. The tool will create a wa.me link that opens WhatsApp with your message pre-filled when clicked."
          }
        },
        {
          "@type": "Question",
          "name": "What is a WhatsApp click-to-chat link?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A WhatsApp click-to-chat link (wa.me link) allows users to start a conversation with a specific phone number without saving the contact first. It can include a pre-filled message and works on both mobile and desktop devices."
          }
        },
        {
          "@type": "Question",
          "name": "Can I create WhatsApp links without saving contacts?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, WhatsApp click-to-chat links allow users to message any phone number directly without adding it to their contacts. This is perfect for business customer service, marketing campaigns, and one-time communications."
          }
        },
        {
          "@type": "Question",
          "name": "How do I generate WhatsApp links for business?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Enter your business phone number with country code and create a professional pre-filled message like 'Hi, I'm interested in your services.' Use the generated link on your website, social media, or marketing materials to enable direct customer contact."
          }
        },
        {
          "@type": "Question",
          "name": "Do WhatsApp links work on both mobile and desktop?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, WhatsApp links work on mobile devices (opening the WhatsApp app) and desktop computers (opening WhatsApp Web or the desktop app). They provide a seamless experience across all platforms."
          }
        }
      ]
    }
    </script>

    <style>
        /* WhatsApp Link Generator Widget - Simplified & Template Compatible */
        .whatsapp-link-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .whatsapp-link-widget-container * { box-sizing: border-box; }

        .whatsapp-link-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .whatsapp-link-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .whatsapp-link-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .whatsapp-link-field {
            display: flex;
            flex-direction: column;
        }

        .whatsapp-link-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .whatsapp-link-input,
        .whatsapp-link-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .whatsapp-link-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .whatsapp-link-input:focus,
        .whatsapp-link-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .whatsapp-link-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .whatsapp-link-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .whatsapp-link-btn:hover { transform: translateY(-2px); }

        .whatsapp-link-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .whatsapp-link-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .whatsapp-link-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .whatsapp-link-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .whatsapp-link-btn-success {
            background-color: #10b981;
            color: white;
        }

        .whatsapp-link-btn-success:hover {
            background-color: #059669;
        }

        .whatsapp-link-btn-whatsapp {
            background-color: #25d366;
            color: white;
        }

        .whatsapp-link-btn-whatsapp:hover {
            background-color: #128c7e;
        }

        .whatsapp-link-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .whatsapp-link-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .whatsapp-link-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .whatsapp-link-preview {
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
        }

        .whatsapp-link-preview-title {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .whatsapp-link-preview-content {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .whatsapp-link-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .whatsapp-link-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        @media (max-width: 768px) {
            .whatsapp-link-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .whatsapp-link-widget-title { font-size: 1.875rem; }
            .whatsapp-link-buttons { flex-direction: column; }
            .whatsapp-link-btn { flex: none; }
        }

        [data-theme="dark"] .whatsapp-link-input:focus,
        [data-theme="dark"] .whatsapp-link-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .whatsapp-link-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .whatsapp-link-output::selection { background-color: var(--primary-color); color: white; }

        .whatsapp-link-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="password-generator"] .whatsapp-link-related-tool-icon { background: linear-gradient(145deg, #0EA5E9, #0284C7); }
        a[href*="base64-encode"] .whatsapp-link-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="md5-generator"] .whatsapp-link-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }

        .whatsapp-link-related-tool-item:hover .whatsapp-link-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="password-generator"]:hover .whatsapp-link-related-tool-icon { background: linear-gradient(145deg, #38bdf8, #0ea5e9); }
        a[href*="base64-encode"]:hover .whatsapp-link-related-tool-icon { background: linear-gradient(145deg, #fbbf24, #f59e0b); }
        a[href*="md5-generator"]:hover .whatsapp-link-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }

        .whatsapp-link-related-tool-item { box-shadow: none; border: none; }
        .whatsapp-link-related-tool-item:hover { box-shadow: none; border: none; }
        .whatsapp-link-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .whatsapp-link-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .whatsapp-link-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .whatsapp-link-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .whatsapp-link-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .whatsapp-link-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .whatsapp-link-related-tool-item:hover .whatsapp-link-related-tool-name { color: var(--primary-color); }

        .whatsapp-link-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .whatsapp-link-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .whatsapp-link-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .whatsapp-link-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .whatsapp-link-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .whatsapp-link-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .whatsapp-link-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .whatsapp-link-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .whatsapp-link-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .whatsapp-link-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .whatsapp-link-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .whatsapp-link-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .whatsapp-link-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .whatsapp-link-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="whatsapp-link-widget-container">
        <h1 class="whatsapp-link-widget-title">WhatsApp Link Generator</h1>
        <p class="whatsapp-link-widget-description">
            Create WhatsApp click-to-chat links with pre-filled messages. Perfect for business customer service, marketing campaigns, and direct communication.
        </p>

        <form class="whatsapp-link-form">
            <div class="whatsapp-link-field">
                <label for="phoneNumber" class="whatsapp-link-label">Phone Number (with country code):</label>
                <input
                    type="tel"
                    id="phoneNumber"
                    class="whatsapp-link-input"
                    placeholder="e.g., +1234567890 or 1234567890"
                />
            </div>
            <div class="whatsapp-link-field">
                <label for="messageText" class="whatsapp-link-label">Pre-filled Message (optional):</label>
                <textarea
                    id="messageText"
                    class="whatsapp-link-textarea"
                    placeholder="Enter your message here (optional)..."
                ></textarea>
            </div>
        </form>

        <div class="whatsapp-link-buttons">
            <button class="whatsapp-link-btn whatsapp-link-btn-primary" onclick="WhatsAppLinkGenerator.generate()">
                Generate WhatsApp Link
            </button>
            <button class="whatsapp-link-btn whatsapp-link-btn-secondary" onclick="WhatsAppLinkGenerator.clear()">
                Clear All
            </button>
            <button class="whatsapp-link-btn whatsapp-link-btn-success" onclick="WhatsAppLinkGenerator.copy()">
                Copy Link
            </button>
            <button class="whatsapp-link-btn whatsapp-link-btn-whatsapp" onclick="WhatsAppLinkGenerator.test()">
                Test Link
            </button>
        </div>

        <div class="whatsapp-link-result">
            <h3 class="whatsapp-link-result-title">Generated WhatsApp Link:</h3>
            <div class="whatsapp-link-output" id="whatsappOutput">Click "Generate WhatsApp Link" to create your link...</div>

            <div class="whatsapp-link-preview" id="linkPreview" style="display: none;">
                <div class="whatsapp-link-preview-title">Link Preview:</div>
                <div class="whatsapp-link-preview-content" id="previewContent"></div>
            </div>
        </div>

        <div class="whatsapp-link-related-tools">
            <h3 class="whatsapp-link-related-tools-title">Related Tools</h3>
            <div class="whatsapp-link-related-tools-grid">
                <a href="/p/password-generator.html" class="whatsapp-link-related-tool-item" rel="noopener">
                    <div class="whatsapp-link-related-tool-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="whatsapp-link-related-tool-name">Password Generator</div>
                </a>

                <a href="/p/base64-encode.html" class="whatsapp-link-related-tool-item" rel="noopener">
                    <div class="whatsapp-link-related-tool-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="whatsapp-link-related-tool-name">Base64 Encode</div>
                </a>

                <a href="/p/md5-generator.html" class="whatsapp-link-related-tool-item" rel="noopener">
                    <div class="whatsapp-link-related-tool-icon">
                        <i class="fas fa-fingerprint"></i>
                    </div>
                    <div class="whatsapp-link-related-tool-name">MD5 Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional WhatsApp Link Generator for Business Communication</h2>
            <p>Our <strong>WhatsApp Link Generator</strong> creates click-to-chat links that enable direct communication with your customers without requiring them to save your contact first. Perfect for businesses, customer service, marketing campaigns, and personal use, these wa.me links work seamlessly across mobile and desktop platforms.</p>
            <p>Ideal for business owners, marketers, and customer service teams who want to streamline communication. Generate WhatsApp links with pre-filled messages to improve customer engagement, reduce friction in the contact process, and enhance your marketing effectiveness across websites, social media, and digital campaigns.</p>

            <h3>How to Use the WhatsApp Link Generator</h3>
            <ol>
                <li><strong>Enter Phone Number:</strong> Input the phone number with country code (e.g., +1234567890).</li>
                <li><strong>Add Message (Optional):</strong> Create a pre-filled message that will appear when users click the link.</li>
                <li><strong>Generate Link:</strong> Click "Generate WhatsApp Link" to create your wa.me link instantly.</li>
                <li><strong>Copy and Use:</strong> Copy the link and use it on your website, social media, or marketing materials.</li>
            </ol>

            <h3>Frequently Asked Questions About WhatsApp Link Generation</h3>

            <h4>How do I create a WhatsApp link with a pre-filled message?</h4>
            <p>Enter the phone number (with country code) and your desired message in the fields above, then click 'Generate WhatsApp Link'. The tool will create a wa.me link that opens WhatsApp with your message pre-filled when clicked.</p>

            <h4>What is a WhatsApp click-to-chat link?</h4>
            <p>A WhatsApp click-to-chat link (wa.me link) allows users to start a conversation with a specific phone number without saving the contact first. It can include a pre-filled message and works on both mobile and desktop devices.</p>

            <h4>Can I create WhatsApp links without saving contacts?</h4>
            <p>Yes, WhatsApp click-to-chat links allow users to message any phone number directly without adding it to their contacts. This is perfect for business customer service, marketing campaigns, and one-time communications.</p>

            <h4>How do I generate WhatsApp links for business?</h4>
            <p>Enter your business phone number with country code and create a professional pre-filled message like 'Hi, I'm interested in your services.' Use the generated link on your website, social media, or marketing materials to enable direct customer contact.</p>

            <h4>Do WhatsApp links work on both mobile and desktop?</h4>
            <p>Yes, WhatsApp links work on mobile devices (opening the WhatsApp app) and desktop computers (opening WhatsApp Web or the desktop app). They provide a seamless experience across all platforms.</p>
        </div>

        <div class="whatsapp-link-features">
            <h3 class="whatsapp-link-features-title">Key Features:</h3>
            <ul class="whatsapp-link-features-list">
                <li class="whatsapp-link-features-item" style="margin-bottom: 0.3em;">Click-to-Chat Link Generation</li>
                <li class="whatsapp-link-features-item" style="margin-bottom: 0.3em;">Pre-filled Message Support</li>
                <li class="whatsapp-link-features-item" style="margin-bottom: 0.3em;">Business Communication Tool</li>
                <li class="whatsapp-link-features-item" style="margin-bottom: 0.3em;">Mobile and Desktop Compatible</li>
                <li class="whatsapp-link-features-item" style="margin-bottom: 0.3em;">No Contact Saving Required</li>
                <li class="whatsapp-link-features-item" style="margin-bottom: 0.3em;">Marketing Campaign Ready</li>
                <li class="whatsapp-link-features-item">100% Free and Instant</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="whatsapp-link-notification" id="whatsappNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                phoneInput: () => document.getElementById('phoneNumber'),
                messageInput: () => document.getElementById('messageText'),
                output: () => document.getElementById('whatsappOutput'),
                preview: () => document.getElementById('linkPreview'),
                previewContent: () => document.getElementById('previewContent'),
                notification: () => document.getElementById('whatsappNotification')
            };

            function formatPhoneNumber(phone) {
                // Remove all non-digit characters
                let cleaned = phone.replace(/\D/g, '');

                // If it doesn't start with a country code, assume it needs one
                if (!cleaned.startsWith('1') && cleaned.length === 10) {
                    // For US numbers, add +1
                    cleaned = '1' + cleaned;
                }

                return cleaned;
            }

            function generateWhatsAppLink(phone, message) {
                const formattedPhone = formatPhoneNumber(phone);
                let link = `https://wa.me/${formattedPhone}`;

                if (message && message.trim()) {
                    const encodedMessage = encodeURIComponent(message.trim());
                    link += `?text=${encodedMessage}`;
                }

                return link;
            }

            function updatePreview(phone, message) {
                const preview = elements.preview();
                const previewContent = elements.previewContent();

                if (phone.trim()) {
                    const formattedPhone = formatPhoneNumber(phone);
                    let previewText = `Phone: +${formattedPhone}`;

                    if (message && message.trim()) {
                        previewText += `\nMessage: "${message.trim()}"`;
                    } else {
                        previewText += `\nMessage: (No pre-filled message)`;
                    }

                    previewContent.textContent = previewText;
                    preview.style.display = 'block';
                } else {
                    preview.style.display = 'none';
                }
            }

            window.WhatsAppLinkGenerator = {
                generate() {
                    const phone = elements.phoneInput().value.trim();
                    const message = elements.messageInput().value;
                    const output = elements.output();

                    if (!phone) {
                        output.textContent = 'Please enter a phone number to generate a WhatsApp link.';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                        return;
                    }

                    // Basic phone number validation
                    const phoneDigits = phone.replace(/\D/g, '');
                    if (phoneDigits.length < 7) {
                        output.textContent = 'Please enter a valid phone number with at least 7 digits.';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                        return;
                    }

                    try {
                        const whatsappLink = generateWhatsAppLink(phone, message);
                        output.textContent = whatsappLink;
                        output.style.color = '';
                        updatePreview(phone, message);
                    } catch (error) {
                        output.textContent = 'Error generating WhatsApp link. Please check your input.';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                    }
                },

                clear() {
                    elements.phoneInput().value = '';
                    elements.messageInput().value = '';
                    elements.output().textContent = 'Click "Generate WhatsApp Link" to create your link...';
                    elements.output().style.color = '';
                    elements.preview().style.display = 'none';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Click "Generate WhatsApp Link" to create your link...' || text.includes('Please enter') || text.includes('Error')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                test() {
                    const text = elements.output().textContent;
                    if (text === 'Click "Generate WhatsApp Link" to create your link...' || text.includes('Please enter') || text.includes('Error')) {
                        alert('Please generate a WhatsApp link first.');
                        return;
                    }

                    // Open the WhatsApp link in a new tab
                    window.open(text, '_blank', 'noopener,noreferrer');
                    this.showNotification('Link opened!');
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message = '✓ Copied to clipboard!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Real-time preview updates
                elements.phoneInput().addEventListener('input', function() {
                    const phone = this.value.trim();
                    const message = elements.messageInput().value;
                    if (phone) {
                        updatePreview(phone, message);
                    } else {
                        elements.preview().style.display = 'none';
                    }
                });

                elements.messageInput().addEventListener('input', function() {
                    const phone = elements.phoneInput().value.trim();
                    const message = this.value;
                    if (phone) {
                        updatePreview(phone, message);
                    }
                });

                // Enter key shortcuts
                elements.phoneInput().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        WhatsAppLinkGenerator.generate();
                    }
                });

                elements.messageInput().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        WhatsAppLinkGenerator.generate();
                    }
                });

                // Global keyboard shortcut
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        WhatsAppLinkGenerator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
