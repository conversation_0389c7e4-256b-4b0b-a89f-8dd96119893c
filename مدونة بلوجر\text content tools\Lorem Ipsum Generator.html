<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lorem Ipsum Generator Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Lorem Ipsum Generator - Create Placeholder Text Instantly",
        "description": "Generate Lorem Ipsum placeholder text instantly. Free online tool with customizable options, multiple text types, and one-click copying.",
        "url": "https://www.webtoolskit.org/p/lorem-ipsum-generator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Lorem Ipsum Generator",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Lorem Ipsum Text" },
            { "@type": "CopyAction", "name": "Copy Generated Text" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the full Lorem Ipsum text?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The most common Lorem Ipsum passage is: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.' This text is derived from sections 1.10.32 and 1.10.33 of Cicero's 'de Finibus Bonorum et Malorum'."
          }
        },
        {
          "@type": "Question",
          "name": "How to generate Lorem Ipsum?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using an online Lorem Ipsum generator is the easiest way. Simply select the type of text you need (e.g., paragraphs, sentences, or words), specify the quantity, and click the 'Generate' button. The tool instantly creates the placeholder text for you to copy and paste into your project."
          }
        },
        {
          "@type": "Question",
          "name": "Is Lorem Ipsum fake Latin?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Lorem Ipsum is best described as pseudo-Latin or garbled Latin. It originates from a real Latin text by Cicero, but the words have been altered, with parts removed and added, making it nonsensical. While the words are Latin-based, the resulting text does not have any real meaning, which is ideal for placeholder content."
          }
        },
        {
          "@type": "Question",
          "name": "Why does everyone use Lorem Ipsum?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Designers and developers use Lorem Ipsum for several key reasons. First, it has a normal-looking distribution of letters and word lengths, which makes a design layout look more realistic than using repetitive text like 'Your text here.' Second, because it's nonsensical, it prevents viewers from being distracted by the content, allowing them to focus purely on the visual design and typography. It's a long-standing industry standard for creating mockups and wireframes."
          }
        },
        {
          "@type": "Question",
          "name": "What does “Lorem ipsum dolor sit amet” mean in English?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The phrase 'Lorem ipsum dolor sit amet' itself is not directly translatable because it is a scrambled version of a Latin text. The original source phrase from Cicero's work is 'Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet...', which translates to 'Nor is there anyone who loves or pursues or desires to obtain pain of itself, because it is pain...' The 'Lorem Ipsum' text we use today is a corrupted form of this original passage."
          }
        }
      ]
    }
    </script>


    <style>
        /* Mobile-First Reset */
        * {
            box-sizing: border-box;
        }

        html {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Lorem Ipsum Generator - Enhanced */
        .widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
            width: 100%;
            box-sizing: border-box;
        }

        .widget-container * { box-sizing: border-box; }

        .widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .option {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .label {
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .input, .select {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-family: var(--font-family);
        }

        .input:focus, .select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
            min-height: 44px; /* Minimum touch target size */
            touch-action: manipulation; /* Prevents double-tap zoom */
        }

        .btn:hover { transform: translateY(-2px); }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover { background-color: var(--border-color); }

        .btn-success {
            background-color: #10b981;
            color: white;
        }

        .btn-success:hover { background-color: #059669; }

        .result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: var(--font-size-base);
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .notification.show { transform: translateX(0); }

        /* === START: Standardized Related Tools Section === */
        .related-tools {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .related-tools-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-xl);
            font-size: 1.5rem;
            font-weight: 700;
            text-align: center;
        }
        
        .related-tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
            justify-items: center;
        }
        
        .related-tool-item {
            box-shadow: none;
            border: none;
            text-align: center;
            text-decoration: none;
            color: inherit;
            transition: var(--transition-base);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            display: block;
            width: 100%;
            max-width: 160px;
        }
        
        .related-tool-item:hover {
            transform: translateY(0);
            background-color: transparent;
        }
        
        .related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px; /* Modern "squircle" shape */
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        /* Specific icon colors */
        a[href*="text-to-slug"] .related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="word-counter"] .related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="case-converter"] .related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }

        .related-tool-item:hover .related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        /* Specific icon hover colors */
        a[href*="text-to-slug"]:hover .related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="word-counter"]:hover .related-tool-icon { background: linear-gradient(145deg, #14b8a6, #10B981); }
        a[href*="case-converter"]:hover .related-tool-icon { background: linear-gradient(145deg, #f472b6, #EC4899); }

        .related-tool-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            margin-top: var(--spacing-sm);
            line-height: 1.3;
        }

        .related-tool-item:hover .related-tool-name {
            color: var(--primary-color);
        }
        /* === END: Standardized Related Tools Section === */
        
        /* SEO Content Styles */
        .lorem-seo-content {
            margin-top: var(--spacing-xl);
            padding: var(--spacing-lg) 0;
            color: var(--text-color-light);
            line-height: 1.7;
            border-top: 1px solid var(--border-color);
        }
        .lorem-seo-content h2, .lorem-seo-content h3, .lorem-seo-content h4 {
            color: var(--text-color);
            margin-top: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }
        .lorem-seo-content h2 { font-size: 1.75rem; font-weight: 700; }
        .lorem-seo-content h3 { font-size: 1.5rem; font-weight: 600; }
        .lorem-seo-content h4 { font-size: 1.25rem; font-weight: 600; }
        .lorem-seo-content p { margin-bottom: var(--spacing-md); }
        .lorem-seo-content ul { padding-left: 20px; }
        .lorem-seo-content li { margin-bottom: var(--spacing-sm); }
        .lorem-seo-content a { color: var(--primary-color); text-decoration: none; }
        .lorem-seo-content a:hover { text-decoration: underline; }

        /* Key Features Section - Enhanced */
        .lorem-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .lorem-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .lorem-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .lorem-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            margin-bottom: 0.3em;
        }

        .lorem-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        /* Mobile-First Responsive Design */
        @media (max-width: 768px) {
            .widget-container {
                margin: 10px;
                padding: 20px;
            }

            .widget-title {
                font-size: 1.75rem;
            }

            .widget-description {
                font-size: 1rem;
                margin-bottom: 20px;
            }

            .buttons {
                flex-direction: column;
                gap: 12px;
            }

            .btn {
                flex: none;
                min-width: auto;
                width: 100%;
            }

            .options {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .input, .select {
                padding: 15px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .output {
                padding: 15px;
                font-size: 14px;
            }
            
            .related-tools-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: var(--spacing-md);
            }
            
            .related-tool-item {
                padding: var(--spacing-md);
                max-width: none;
            }

            .related-tool-icon {
                width: 64px;
                height: 64px;
                font-size: 2rem;
                border-radius: 16px;
            }

            .related-tool-name {
                font-size: 0.875rem;
            }


            .lorem-features-list {
                columns: 1;
                -webkit-columns: 1;
                -moz-columns: 1;
            }
        }

        @media (max-width: 480px) {
            .widget-container {
                margin: 5px;
                padding: 15px;
            }

            .widget-title {
                font-size: 1.5rem;
            }
            
            .related-tools-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: var(--spacing-sm);
            }

            .related-tool-item {
                padding: var(--spacing-sm);
                max-width: none;
            }

            .related-tool-icon {
                width: 56px;
                height: 56px;
                font-size: 1.75rem;
                border-radius: 12px;
            }

            .related-tool-name {
                font-size: 0.75rem;
            }

            .notification {
                top: 10px;
                right: 10px;
                left: 10px;
                transform: translateY(-100px);
                text-align: center;
            }

            .notification.show {
                transform: translateY(0);
            }
        }

        /* Focus & Accessibility */
        .btn:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .output::selection {
            background-color: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="widget-container">
        <h1 class="widget-title">Lorem Ipsum Generator</h1>
        <p class="widget-description">
            Generate placeholder text for your designs and layouts. Choose from paragraphs, sentences, or words.
        </p>

        <div class="options">
            <div class="option">
                <label for="type" class="label">Generate:</label>
                <select id="type" class="select">
                    <option value="paragraphs">Paragraphs</option>
                    <option value="sentences">Sentences</option>
                    <option value="words">Words</option>
                </select>
            </div>
            <div class="option">
                <label for="count" class="label">Count:</label>
                <input type="number" id="count" class="input" value="3" min="1" max="50">
            </div>
            <div class="option">
                <label for="start" class="label">Start with "Lorem ipsum":</label>
                <select id="start" class="select">
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                </select>
            </div>
        </div>

        <div class="buttons">
            <button class="btn btn-primary" onclick="Tool.generate()">Generate Text</button>
            <button class="btn btn-secondary" onclick="Tool.clear()">Clear All</button>
            <button class="btn btn-success" onclick="Tool.copy()">Copy Result</button>
        </div>

        <div class="result">
            <h3 class="result-title">Generated Text:</h3>
            <div class="output" id="output">Your generated Lorem Ipsum text will appear here...</div>
        </div>

        <div class="related-tools">
            <h3 class="related-tools-title">Related Tools</h3>
            <div class="related-tools-grid">
                <a href="/p/text-to-slug_30.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="related-tool-name">Text to Slug</div>
                </a>

                <a href="/p/word-counter.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="related-tool-name">Word Counter</div>
                </a>

                <a href="/p/case-converter.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-text-height"></i>
                    </div>
                    <div class="related-tool-name">Case Converter</div>
                </a>
            </div>
        </div>

        <div class="lorem-seo-content">
            <h2>Understanding the Lorem Ipsum Generator</h2>
            <p>A <strong>Lorem Ipsum Generator</strong> is an essential tool for designers, developers, and content creators who need placeholder text (also known as "dummy text") to visualize layouts and typographic styles. Instead of using distracting real content or repetitive phrases like "content here," Lorem Ipsum provides text that mimics the natural flow and word distribution of the English language, allowing for a more realistic preview of a final design.</p>
            <p>This generator is completely free, runs in your browser, and requires no software installation. You can customize the output to fit your exact needs, from a single sentence to multiple paragraphs of text.</p>

            <h3>How to Use Our Lorem Ipsum Generator</h3>
            <p>Generating placeholder text is simple and takes just a few clicks. Follow these steps:</p>
            <ol>
                <li><strong>Select the Type:</strong> In the "Generate" dropdown, choose whether you want to create paragraphs, sentences, or individual words.</li>
                <li><strong>Specify the Count:</strong> Enter the number of paragraphs, sentences, or words you need in the "Count" field.</li>
                <li><strong>Choose the Starting Phrase:</strong> Decide if you want your text to begin with the classic "Lorem ipsum dolor sit amet..." by selecting 'Yes' or 'No'.</li>
                <li><strong>Generate and Copy:</strong> Click the "Generate Text" button to see the result. You can then use the "Copy Result" button to instantly copy the text to your clipboard.</li>
            </ol>

            <h3>Frequently Asked Questions About Lorem Ipsum Generator</h3>
            <h4>What is the full Lorem Ipsum text?</h4>
            <p>While our tool generates random variations, the most famous and traditional Lorem Ipsum passage is: <em>"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat..."</em> This text is a scrambled version of a passage from "de Finibus Bonorum et Malorum" (The Extremes of Good and Evil), a Latin text written by Cicero in 45 BC.</p>

            <h4>Is Lorem Ipsum fake Latin?</h4>
            <p>Yes, it's best described as "pseudo-Latin" or "garbled Latin." While it uses real Latin words, it's been intentionally altered, with words added, removed, and rearranged to make it nonsensical. This is its key feature—it looks like real text but doesn't distract the viewer with meaning, keeping the focus entirely on the design.</p>

            <h4>Why does everyone use Lorem Ipsum?</h4>
            <p>Lorem Ipsum has been the industry-standard dummy text for centuries. Its popularity stems from a few key advantages:</p>
            <ul>
                <li><strong>Focus on Design:</strong> Since the text is meaningless, it forces viewers to concentrate on the layout, typography, and visual hierarchy of a design rather than the content itself.</li>
                <li><strong>Natural Look:</strong> It has a more-or-less normal distribution of letters and word lengths, which makes the layout look as it would with real content.</li>
                <li><strong>Industry Standard:</strong> Its widespread use means that clients and collaborators instantly recognize it as placeholder content.</li>
            </ul>

            <h4>What does “Lorem ipsum dolor sit amet” mean in English?</h4>
            <p>Directly, the phrase "Lorem ipsum dolor sit amet" has no meaning. It is a corrupted version of a sentence from Cicero's original work. The original text read, <em>"Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet..."</em>, which translates to "Nor is there anyone who loves or pursues or desires to obtain pain of itself, because it is pain..." The placeholder text we use today is a fragment of this, with the word "dolorem" (pain) becoming "Lorem."</p>
        </div>


        <div class="lorem-features">
            <h3 class="lorem-features-title">Key Features:</h3>
            <ul class="lorem-features-list">
                <li class="lorem-features-item">Generate Lorem Ipsum instantly</li>
                <li class="lorem-features-item">Customizable paragraph and word count</li>
                <li class="lorem-features-item">Multiple text generation types</li>
                <li class="lorem-features-item">Real-time preview</li>
                <li class="lorem-features-item">One-click copy to clipboard</li>
                <li class="lorem-features-item">Mobile-responsive design</li>
                <li class="lorem-features-item">No data sent to servers</li>
                <li class="lorem-features-item">Free to use, no registration required</li>
            </ul>
        </div>
    </div>

    <div class="notification" id="notification">✓ Copied to clipboard!</div>

    <script>
        // Ultra Simplified Lorem Ipsum Generator
        (function() {
            'use strict';

            const words = ['lorem','ipsum','dolor','sit','amet','consectetur','adipiscing','elit','sed','do','eiusmod','tempor','incididunt','ut','labore','et','dolore','magna','aliqua','enim','ad','minim','veniam','quis','nostrud','exercitation','ullamco','laboris','nisi','aliquip','ex','ea','commodo','consequat','duis','aute','irure','in','reprehenderit','voluptate','velit','esse','cillum','fugiat','nulla','pariatur','excepteur','sint','occaecat','cupidatat','non','proident','sunt','culpa','qui','officia','deserunt','mollit','anim','id','est','laborum'];

            window.Tool = {
                generate() {
                    const type = document.getElementById('type').value;
                    const count = parseInt(document.getElementById('count').value) || 1;
                    const startWithLorem = document.getElementById('start').value === 'true';

                    let result = '';
                    if (type === 'words') {
                        result = this.makeWords(count, startWithLorem);
                    } else if (type === 'sentences') {
                        result = this.makeSentences(count, startWithLorem);
                    } else {
                        result = this.makeParagraphs(count, startWithLorem);
                    }

                    document.getElementById('output').textContent = result;
                },

                makeWords(count, startWithLorem) {
                    const result = [];
                    if (startWithLorem) {
                        result.push('Lorem', 'ipsum');
                        count -= 2;
                    }
                    for (let i = 0; i < count; i++) {
                        result.push(words[Math.floor(Math.random() * words.length)]);
                    }
                    return result.join(' ') + '.';
                },

                makeSentences(count, startWithLorem) {
                    const sentences = [];
                    for (let i = 0; i < count; i++) {
                        const len = Math.floor(Math.random() * 10) + 8;
                        const sentenceWords = [];

                        if (i === 0 && startWithLorem) {
                            sentenceWords.push('Lorem', 'ipsum');
                            for (let j = 2; j < len; j++) {
                                sentenceWords.push(words[Math.floor(Math.random() * words.length)]);
                            }
                        } else {
                            for (let j = 0; j < len; j++) {
                                sentenceWords.push(words[Math.floor(Math.random() * words.length)]);
                            }
                        }

                        sentenceWords[0] = sentenceWords[0].charAt(0).toUpperCase() + sentenceWords[0].slice(1);
                        sentences.push(sentenceWords.join(' ') + '.');
                    }
                    return sentences.join(' ');
                },

                makeParagraphs(count, startWithLorem) {
                    const paragraphs = [];
                    for (let i = 0; i < count; i++) {
                        const sentenceCount = Math.floor(Math.random() * 4) + 3;
                        const sentences = [];

                        for (let j = 0; j < sentenceCount; j++) {
                            const len = Math.floor(Math.random() * 10) + 8;
                            const sentenceWords = [];

                            if (i === 0 && j === 0 && startWithLorem) {
                                sentenceWords.push('Lorem', 'ipsum');
                                for (let k = 2; k < len; k++) {
                                    sentenceWords.push(words[Math.floor(Math.random() * words.length)]);
                                }
                            } else {
                                for (let k = 0; k < len; k++) {
                                    sentenceWords.push(words[Math.floor(Math.random() * words.length)]);
                                }
                            }

                            sentenceWords[0] = sentenceWords[0].charAt(0).toUpperCase() + sentenceWords[0].slice(1);
                            sentences.push(sentenceWords.join(' ') + '.');
                        }
                        paragraphs.push(sentences.join(' '));
                    }
                    return paragraphs.join('\n\n');
                },

                clear() {
                    document.getElementById('output').textContent = 'Your generated Lorem Ipsum text will appear here...';
                },

                copy() {
                    const text = document.getElementById('output').textContent;
                    if (text === 'Your generated Lorem Ipsum text will appear here...') return;

                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(text).then(() => this.notify());
                    } else {
                        const el = document.createElement('textarea');
                        el.value = text;
                        el.style.cssText = 'position:fixed;left:-999px;top:-999px';
                        document.body.appendChild(el);
                        el.select();
                        document.execCommand('copy');
                        document.body.removeChild(el);
                        this.notify();
                    }
                },

                notify() {
                    const n = document.getElementById('notification');
                    n.classList.add('show');
                    setTimeout(() => n.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Removed auto-generation - now only generates when button is clicked
            });
        })();
    </script>
</body>
</html>