<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary to Text Converter Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Binary to Text Converter - Decode Binary Code Online",
        "description": "Convert binary code to readable text instantly. Free online tool with real-time conversion, bulk processing, and one-click copying.",
        "url": "https://www.webtoolskit.org/p/binary-to-text.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Binary to Text Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Binary to Text" },
            { "@type": "CopyAction", "name": "Copy Decoded Text" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What does 01001000 01100101 01101100 01101100 01101111 00100001 mean?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The binary code 01001000 01100101 01101100 01101100 01101111 00100001 translates to 'Hello!' in text. Each 8-bit binary sequence represents a character: 01001000 = H, 01100101 = e, 01101100 = l, 01101100 = l, 01101111 = o, 00100001 = !."
          }
        },
        {
          "@type": "Question",
          "name": "What is the binary translator 010011110110111001100101?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The binary sequence 010011110110111001100101 translates to 'One' in text. This is a 24-bit sequence where each 8-bit group represents a character: 01001111 = O, 01101110 = n, 01100101 = e."
          }
        },
        {
          "@type": "Question",
          "name": "What is Hello World in binary converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "'Hello World' in binary is: 01001000 01100101 01101100 01101100 01101111 00100000 01010111 01101111 01110010 01101100 01100100. Each character is represented by its 8-bit ASCII binary equivalent."
          }
        },
        {
          "@type": "Question",
          "name": "What character does the binary 01000001 represent?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The binary 01000001 represents the character 'A' (uppercase letter A). This is the ASCII code 65 in binary format, which corresponds to the capital letter A in the standard ASCII character set."
          }
        },
        {
          "@type": "Question",
          "name": "What does 0100100001101001 mean in binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The binary sequence 0100100001101001 translates to 'Hi' in text. Breaking it down: 01001000 = H (ASCII 72) and 01101001 = i (ASCII 105)."
          }
        }
      ]
    }
    </script>


    <style>
        /* Binary to Text Widget - Simplified & Template Compatible */
        .binary-to-text-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .binary-to-text-widget-container * { box-sizing: border-box; }

        .binary-to-text-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .binary-to-text-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .binary-to-text-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .binary-to-text-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .binary-to-text-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .binary-to-text-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .binary-to-text-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .binary-to-text-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .binary-to-text-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .binary-to-text-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .binary-to-text-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .binary-to-text-btn:hover { transform: translateY(-2px); }

        .binary-to-text-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .binary-to-text-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .binary-to-text-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .binary-to-text-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .binary-to-text-btn-success {
            background-color: #10b981;
            color: white;
        }

        .binary-to-text-btn-success:hover {
            background-color: #059669;
        }

        .binary-to-text-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .binary-to-text-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .binary-to-text-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .binary-to-text-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .binary-to-text-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .binary-to-text-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .binary-to-text-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .binary-to-text-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .binary-to-text-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .binary-to-text-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .binary-to-text-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="text-to-binary"] .binary-to-text-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="binary-to-decimal"] .binary-to-text-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="binary-encoder"] .binary-to-text-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .binary-to-text-related-tool-item:hover .binary-to-text-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="text-to-binary"]:hover .binary-to-text-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="binary-to-decimal"]:hover .binary-to-text-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="binary-encoder"]:hover .binary-to-text-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .binary-to-text-related-tool-item { box-shadow: none; border: none; }
        .binary-to-text-related-tool-item:hover { box-shadow: none; border: none; }
        .binary-to-text-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .binary-to-text-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .binary-to-text-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .binary-to-text-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .binary-to-text-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .binary-to-text-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .binary-to-text-related-tool-item:hover .binary-to-text-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .binary-to-text-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .binary-to-text-widget-title { font-size: 1.875rem; }
            .binary-to-text-buttons { flex-direction: column; }
            .binary-to-text-btn { flex: none; }
            .binary-to-text-options { grid-template-columns: 1fr; }
            .binary-to-text-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .binary-to-text-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .binary-to-text-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .binary-to-text-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .binary-to-text-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .binary-to-text-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .binary-to-text-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .binary-to-text-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .binary-to-text-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .binary-to-text-checkbox:focus, .binary-to-text-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .binary-to-text-output::selection { background-color: var(--primary-color); color: white; }
        .binary-to-text-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .binary-to-text-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="binary-to-text-widget-container">
        <h1 class="binary-to-text-widget-title">Binary to Text Converter</h1>
        <p class="binary-to-text-widget-description">
            Convert binary code to readable text instantly. Decode binary data, understand binary messages, and analyze binary patterns with ease.
        </p>
        
        <div class="binary-to-text-input-group">
            <label for="binaryToTextInput" class="binary-to-text-label">Enter binary code:</label>
            <textarea 
                id="binaryToTextInput" 
                class="binary-to-text-textarea"
                placeholder="Enter binary code here (e.g., 01001000 01100101 01101100 01101100 01101111)..."
                rows="4"
            ></textarea>
        </div>

        <div class="binary-to-text-options">
            <div class="binary-to-text-option">
                <input type="checkbox" id="binaryAutoSpaces" class="binary-to-text-checkbox" checked>
                <label for="binaryAutoSpaces" class="binary-to-text-option-label">Auto-detect spaces</label>
            </div>
            <div class="binary-to-text-option">
                <input type="checkbox" id="binaryStrictMode" class="binary-to-text-checkbox">
                <label for="binaryStrictMode" class="binary-to-text-option-label">Strict mode (8-bit only)</label>
            </div>
            <div class="binary-to-text-option">
                <input type="checkbox" id="binaryShowCodes" class="binary-to-text-checkbox">
                <label for="binaryShowCodes" class="binary-to-text-option-label">Show ASCII codes</label>
            </div>
            <div class="binary-to-text-option">
                <input type="checkbox" id="binaryIgnoreInvalid" class="binary-to-text-checkbox" checked>
                <label for="binaryIgnoreInvalid" class="binary-to-text-option-label">Ignore invalid characters</label>
            </div>
        </div>

        <div class="binary-to-text-buttons">
            <button class="binary-to-text-btn binary-to-text-btn-primary" onclick="BinaryToTextConverter.convert()">
                Convert to Text
            </button>
            <button class="binary-to-text-btn binary-to-text-btn-secondary" onclick="BinaryToTextConverter.clear()">
                Clear All
            </button>
            <button class="binary-to-text-btn binary-to-text-btn-success" onclick="BinaryToTextConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="binary-to-text-result">
            <h3 class="binary-to-text-result-title">Decoded Text:</h3>
            <div class="binary-to-text-output" id="binaryToTextOutput">
                Your decoded text will appear here...
            </div>
        </div>

        <div class="binary-to-text-related-tools">
            <h3 class="binary-to-text-related-tools-title">Related Tools</h3>
            <div class="binary-to-text-related-tools-grid">
                <a href="/p/text-to-binary.html" class="binary-to-text-related-tool-item" rel="noopener">
                    <div class="binary-to-text-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="binary-to-text-related-tool-name">Text to Binary</div>
                </a>

                <a href="/p/binary-to-decimal.html" class="binary-to-text-related-tool-item" rel="noopener">
                    <div class="binary-to-text-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="binary-to-text-related-tool-name">Binary to Decimal</div>
                </a>

                <a href="/p/binary-encoder.html" class="binary-to-text-related-tool-item" rel="noopener">
                    <div class="binary-to-text-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="binary-to-text-related-tool-name">Binary Encoder</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Decode Binary Messages with Our Binary to Text Converter</h2>
            <p>Binary code is the fundamental language of computers, using only 0s and 1s to represent all information. Our <strong>Binary to Text</strong> converter makes it easy to decode binary messages back into readable text. Whether you're studying computer science, working with programming, or simply curious about binary messages, this tool provides instant and accurate binary-to-text conversion.</p>
            <p>Each character in text corresponds to a specific binary pattern, typically 8 bits (1 byte) long. For example, the letter 'A' is represented as 01000001 in binary, which equals 65 in decimal ASCII. Our converter automatically handles these conversions, supporting various binary formats and providing options for different decoding preferences.</p>
            
            <h3>How to Use the Binary to Text Converter</h3>
            <ol>
                <li><strong>Enter Binary Code:</strong> Paste your binary sequence into the input field. You can include spaces or enter continuous binary digits.</li>
                <li><strong>Configure Options:</strong> Choose whether to auto-detect spaces, use strict 8-bit mode, show ASCII codes, or ignore invalid characters.</li>
                <li><strong>Convert and Copy:</strong> Click "Convert to Text" to decode your binary. The result appears instantly, and you can copy it with one click.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Binary to Text</h3>
            
            <h4>What does 01001000 01100101 01101100 01101100 01101111 00100001 mean?</h4>
            <p>The binary code 01001000 01100101 01101100 01101100 01101111 00100001 translates to 'Hello!' in text. Each 8-bit binary sequence represents a character: 01001000 = H, 01100101 = e, 01101100 = l, 01101100 = l, 01101111 = o, 00100001 = !.</p>
            
            <h4>What is the binary translator 010011110110111001100101?</h4>
            <p>The binary sequence 010011110110111001100101 translates to 'One' in text. This is a 24-bit sequence where each 8-bit group represents a character: 01001111 = O, 01101110 = n, 01100101 = e.</p>
            
            <h4>What is Hello World in binary converter?</h4>
            <p>'Hello World' in binary is: 01001000 01100101 01101100 01101100 01101111 00100000 01010111 01101111 01110010 01101100 01100100. Each character is represented by its 8-bit ASCII binary equivalent.</p>
            
            <h4>What character does the binary 01000001 represent?</h4>
            <p>The binary 01000001 represents the character 'A' (uppercase letter A). This is the ASCII code 65 in binary format, which corresponds to the capital letter A in the standard ASCII character set.</p>
            
            <h4>What does 0100100001101001 mean in binary?</h4>
            <p>The binary sequence 0100100001101001 translates to 'Hi' in text. Breaking it down: 01001000 = H (ASCII 72) and 01101001 = i (ASCII 105).</p>
        </div>


        <div class="binary-to-text-features">
            <h3 class="binary-to-text-features-title">Key Features:</h3>
            <ul class="binary-to-text-features-list">
                <li class="binary-to-text-features-item" style="margin-bottom: 0.3em;">Instant binary-to-text conversion</li>
                <li class="binary-to-text-features-item" style="margin-bottom: 0.3em;">Auto-detect binary spacing</li>
                <li class="binary-to-text-features-item" style="margin-bottom: 0.3em;">Support for various binary formats</li>
                <li class="binary-to-text-features-item" style="margin-bottom: 0.3em;">ASCII code display option</li>
                <li class="binary-to-text-features-item" style="margin-bottom: 0.3em;">Strict 8-bit mode</li>
                <li class="binary-to-text-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="binary-to-text-features-item">Real-time error handling</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="binary-to-text-notification" id="binaryToTextNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Binary to Text Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('binaryToTextInput'),
                output: () => document.getElementById('binaryToTextOutput'),
                notification: () => document.getElementById('binaryToTextNotification')
            };

            window.BinaryToTextConverter = {
                convert() {
                    const input = elements.input();
                    const output = elements.output();
                    const binaryText = input.value;

                    if (!binaryText.trim()) {
                        output.textContent = 'Please enter binary code to convert.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        autoSpaces: document.getElementById('binaryAutoSpaces').checked,
                        strictMode: document.getElementById('binaryStrictMode').checked,
                        showCodes: document.getElementById('binaryShowCodes').checked,
                        ignoreInvalid: document.getElementById('binaryIgnoreInvalid').checked
                    };

                    try {
                        const result = this.processBinary(binaryText, options);
                        output.textContent = result || 'No valid binary data found';
                    } catch (error) {
                        output.textContent = `Error: ${error.message}`;
                        output.style.color = '#dc2626';
                    }
                },

                processBinary(binaryText, options) {
                    // Clean the input - remove spaces and non-binary characters
                    let cleanBinary = binaryText.replace(/[^01\s]/g, '');
                    
                    if (options.ignoreInvalid) {
                        cleanBinary = cleanBinary.replace(/[^01]/g, '');
                    } else {
                        cleanBinary = cleanBinary.replace(/\s+/g, '');
                    }

                    if (!cleanBinary) {
                        throw new Error('No valid binary digits found');
                    }

                    // Auto-detect spaces or split into 8-bit chunks
                    let binaryChunks = [];
                    if (options.autoSpaces && /\s/.test(binaryText)) {
                        binaryChunks = binaryText.split(/\s+/).filter(chunk => chunk.length > 0);
                    } else {
                        // Split into 8-bit chunks
                        for (let i = 0; i < cleanBinary.length; i += 8) {
                            const chunk = cleanBinary.substr(i, 8);
                            if (chunk.length === 8 || !options.strictMode) {
                                binaryChunks.push(chunk);
                            }
                        }
                    }

                    let result = '';
                    let details = [];

                    for (let chunk of binaryChunks) {
                        if (options.strictMode && chunk.length !== 8) {
                            continue;
                        }

                        const decimal = parseInt(chunk, 2);
                        if (isNaN(decimal) || decimal < 0 || decimal > 255) {
                            continue;
                        }

                        const character = String.fromCharCode(decimal);
                        result += character;

                        if (options.showCodes) {
                            details.push(`${chunk} = ${character} (${decimal})`);
                        }
                    }

                    if (options.showCodes && details.length > 0) {
                        return `${result}\n\nASCII Details:\n${details.join('\n')}`;
                    }

                    return result;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your decoded text will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your decoded text will appear here...', 'Please enter binary code to convert.', 'No valid binary data found'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                const checkboxes = document.querySelectorAll('.binary-to-text-checkbox');

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        BinaryToTextConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>