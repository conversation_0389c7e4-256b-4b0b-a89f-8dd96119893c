<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Converter - HEX, RGB, HSL, and HSV Converter</title>
    <meta name="description" content="Instantly convert colors between HEX, RGB, HSL, and HSV formats with our advanced online Color Converter. Features a live color picker and real-time updates.">
    <meta name="keywords" content="color converter, hex to rgb, rgb to hex, hsl to hex, color picker, css colors, color code converter, hex to hsl">
    <link rel="canonical" href="https://www.webtoolskit.org/p/color-converter.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Color Converter - HEX, RGB, HSL, and HSV Converter",
        "description": "Instantly convert colors between HEX, RGB, HSL, and HSV formats with our advanced online Color Converter. Features a live color picker and real-time updates.",
        "url": "https://www.webtoolskit.org/p/color-converter.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Color Converter",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "HEX to RGB Conversion",
                "RGB to HSL Conversion",
                "HSL to HSV Conversion",
                "Live Color Picker",
                "Alpha/Opacity Control",
                "Real-time Updates",
                "One-Click Copy for all formats"
            ]
        }
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a color converter used for?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A color converter is a tool used by web designers, developers, and digital artists to translate color values from one format (like HEX) to another (like RGB or HSL). This is essential because different applications and programming languages require specific color formats to render colors correctly."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert a HEX color code to RGB?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert a HEX code like #FF5733 to RGB, you split the code into three pairs (FF, 57, 33). Each pair represents Red, Green, and Blue, respectively. You then convert each hexadecimal pair to its decimal equivalent: FF becomes 255, 57 becomes 87, and 33 becomes 51. So, #FF5733 is rgb(255, 87, 51). Our tool automates this process instantly."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between HSL and HSV color models?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Both HSL (Hue, Saturation, Lightness) and HSV (Hue, Saturation, Value) are cylindrical representations of the RGB color model. The main difference is in how they define brightness. In HSL, maximum Lightness (L=100%) is always pure white. In HSV, maximum Value (V=100%) represents the brightest version of the chosen hue, not necessarily white. HSL is often considered more intuitive for human color selection."
          }
        },
        {
          "@type": "Question",
          "name": "What does the alpha value in RGBA and HSLA mean?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The 'A' in RGBA and HSLA stands for 'alpha,' which represents the color's opacity. It's a value between 0.0 (completely transparent) and 1.0 (completely opaque). An alpha value of 0.5 would make the color 50% transparent, allowing background elements to show through. Our tool's opacity slider lets you adjust this value easily."
          }
        },
        {
          "@type": "Question",
          "name": "How do I use a color picker to find a color code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Simply click on the color swatch in our tool. This will open your browser's native color picker. You can then visually select any color you want. Once you choose a color, our tool will instantly update all the text fields (HEX, RGB, HSL, etc.) to show the corresponding codes for your selection."
          }
        }
      ]
    }
    </script>

    <style>
        .color-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .color-converter-widget-container * { box-sizing: border-box; }

        .color-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .color-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .color-converter-main-layout {
            display: grid;
            grid-template-columns: 1fr 120px;
            gap: var(--spacing-xl);
            align-items: center;
            margin-bottom: var(--spacing-xl);
        }

        .color-converter-inputs { display: flex; flex-direction: column; gap: var(--spacing-lg); }

        .color-converter-preview-wrapper { display: flex; flex-direction: column; align-items: center; gap: var(--spacing-sm); }
        .color-converter-preview {
            width: 100px;
            height: 100px;
            border-radius: var(--border-radius-lg);
            border: 2px solid var(--border-color);
            cursor: pointer;
            transition: transform 0.2s;
        }
        .color-converter-preview:hover { transform: scale(1.05); }
        .color-converter-picker-input {
            width: 0;
            height: 0;
            padding: 0;
            border: none;
            opacity: 0;
            position: absolute;
        }
        
        .color-converter-field-group { display: flex; flex-direction: column; gap: var(--spacing-sm); }
        .color-converter-label { display: block; font-weight: 600; color: var(--text-color); margin-bottom: -5px; }
        .color-converter-input-row { display: flex; gap: var(--spacing-md); align-items: center; }
        .color-converter-input {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            width: 100%;
        }
        .color-converter-input:focus { outline: none; border-color: var(--primary-color); }
        .color-converter-copy-btn {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            border: 1px solid var(--border-color);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border-radius: var(--border-radius-md);
            transition: var(--transition-base);
            white-space: nowrap;
        }
        .color-converter-copy-btn:hover { background-color: var(--border-color); }

        .color-converter-slider {
            -webkit-appearance: none;
            width: 100%;
            height: 8px;
            background: linear-gradient(90deg, transparent, var(--text-color));
            border-radius: 5px;
            outline: none;
            opacity: 0.7;
            transition: opacity .2s;
        }
        .color-converter-slider:hover { opacity: 1; }
        .color-converter-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: var(--primary-color);
            cursor: pointer;
            border-radius: 50%;
            border: 2px solid var(--card-bg);
        }
        .color-converter-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: var(--primary-color);
            cursor: pointer;
            border-radius: 50%;
            border: 2px solid var(--card-bg);
        }
        .color-converter-slider-value { font-weight: 600; min-width: 40px; text-align: right; }

        .color-converter-notification {
            position: fixed; top: 20px; right: 20px; background-color: #10b981; color: white;
            padding: var(--spacing-md) var(--spacing-lg); border-radius: var(--border-radius-md);
            font-weight: 600; z-index: 10000; transform: translateX(400px); transition: var(--transition-base);
        }
        .color-converter-notification.show { transform: translateX(0); }
        
        .seo-content, .color-converter-related-tools, .color-converter-features {
            margin-top: var(--spacing-xl); padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }
        .seo-content { color: var(--text-color-light); line-height: 1.7; }
        .seo-content h2, .seo-content h3 { color: var(--text-color); margin-bottom: var(--spacing-md); }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        .color-converter-related-tools-title, .color-converter-features-title {
            color: var(--text-color); margin-bottom: var(--spacing-xl);
            font-size: 1.5rem; font-weight: 700; text-align: center;
        }
        .color-converter-features-title { text-align: left; font-size: 1.25rem; margin-bottom: var(--spacing-md); }
        
        .color-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); justify-items: center; }
        .color-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; display: block; width: 100%; max-width: 160px; }
        .color-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .color-converter-related-tool-item:hover .color-converter-related-tool-name { color: var(--primary-color); }
        .color-converter-related-tool-icon {
            width: 72px; height: 72px; margin: 0 auto var(--spacing-sm); border-radius: 18px;
            display: flex; align-items: center; justify-content: center; font-size: 2.25rem; color: white;
            transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0,0,0,0.12);
        }
        .color-converter-related-tool-item:hover .color-converter-related-tool-icon { transform: translateY(-5px) scale(1.05); }
        a[href*="hex-to-rgb"] .color-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="rgb-to-hex"] .color-converter-related-tool-icon { background: linear-gradient(145deg, #F97316, #EA580C); }
        a[href*="password-generator"] .color-converter-related-tool-icon { background: linear-gradient(145deg, #0EA5E9, #0284C7); }

        .color-converter-features-list { list-style: none; padding: 0; margin: 0; columns: 2; }
        .color-converter-features-item { padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg); color: var(--text-color-light); position: relative; break-inside: avoid; }
        .color-converter-features-item:before {
            content: ""; position: absolute; left: 0; top: calc(var(--spacing-sm) + 2px);
            width: 12px; height: 6px; border-left: 2px solid #10b981; border-bottom: 2px solid #10b981; transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .color-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .color-converter-widget-title { font-size: 1.875rem; }
            .color-converter-main-layout { grid-template-columns: 1fr; }
            .color-converter-preview-wrapper { margin-bottom: var(--spacing-lg); }
            .color-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
        }
        @media (max-width: 600px) { .color-converter-features-list { columns: 1; } }

    </style>
</head>
<body>
    <div class="color-converter-widget-container">
        <h1 class="color-converter-widget-title">Color Converter</h1>
        <p class="color-converter-widget-description">
            A real-time tool for designers and developers to convert between HEX, RGB, HSL, and HSV color formats with a live color picker.
        </p>

        <div class="color-converter-main-layout">
            <div class="color-converter-inputs">
                <!-- HEX -->
                <div class="color-converter-field-group">
                    <label for="hexInput" class="color-converter-label">HEX</label>
                    <div class="color-converter-input-row">
                        <input type="text" id="hexInput" class="color-converter-input" maxlength="7">
                        <button class="color-converter-copy-btn" onclick="ColorConverterTool.copy('hex')">Copy</button>
                    </div>
                </div>
                <!-- RGB -->
                <div class="color-converter-field-group">
                    <label for="rInput" class="color-converter-label">RGB (Red, Green, Blue)</label>
                    <div class="color-converter-input-row">
                        <input type="number" id="rInput" class="color-converter-input" min="0" max="255">
                        <input type="number" id="gInput" class="color-converter-input" min="0" max="255">
                        <input type="number" id="bInput" class="color-converter-input" min="0" max="255">
                        <button class="color-converter-copy-btn" onclick="ColorConverterTool.copy('rgb')">Copy</button>
                    </div>
                </div>
                <!-- HSL -->
                <div class="color-converter-field-group">
                    <label for="hInput" class="color-converter-label">HSL (Hue, Saturation, Lightness)</label>
                    <div class="color-converter-input-row">
                        <input type="number" id="hInput" class="color-converter-input" min="0" max="360">
                        <input type="number" id="sInput" class="color-converter-input" min="0" max="100">
                        <input type="number" id="lInput" class="color-converter-input" min="0" max="100">
                        <button class="color-converter-copy-btn" onclick="ColorConverterTool.copy('hsl')">Copy</button>
                    </div>
                </div>
                <!-- Alpha/Opacity -->
                <div class="color-converter-field-group">
                    <label for="alphaSlider" class="color-converter-label">Opacity</label>
                    <div class="color-converter-input-row">
                        <input type="range" id="alphaSlider" class="color-converter-slider" min="0" max="1" step="0.01">
                        <span id="alphaValue" class="color-converter-slider-value">1.00</span>
                    </div>
                </div>
            </div>
            <div class="color-converter-preview-wrapper">
                <div id="colorPreview" class="color-converter-preview" onclick="document.getElementById('colorPicker').click()"></div>
                <input type="color" id="colorPicker" class="color-converter-picker-input">
                <span>Click to pick</span>
            </div>
        </div>

        <div class="color-converter-related-tools">
            <h3 class="color-converter-related-tools-title">Related Tools</h3>
            <div class="color-converter-related-tools-grid">
                <a href="/p/hex-to-rgb-converter.html" class="color-converter-related-tool-item" rel="noopener">
                    <div class="color-converter-related-tool-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="color-converter-related-tool-name">HEX to RGB</div>
                </a>
                <a href="/p/rgb-to-hex-converter.html" class="color-converter-related-tool-item" rel="noopener">
                    <div class="color-converter-related-tool-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="color-converter-related-tool-name">RGB to HEX</div>
                </a>
                <a href="/p/password-generator.html" class="color-converter-related-tool-item" rel="noopener">
                    <div class="color-converter-related-tool-icon"><i class="fas fa-key"></i></div>
                    <div class="color-converter-related-tool-name">Password Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>The Ultimate Color Conversion Tool for Creatives</h2>
            <p>For any professional in the digital space, from web developers in the U.S. to graphic designers worldwide, color is a fundamental language. However, this language has many dialects: HEX, RGB, HSL, and more. Our <strong>Color Converter</strong> acts as a universal translator, allowing you to seamlessly switch between these formats in real-time. This ensures your color palettes are consistent, accurate, and perfectly implemented across every platform and application.</p>
            <p>This tool is designed for speed and precision. Whether you need to grab a HEX code from a design mockup and convert it to RGBA for CSS, or you want to adjust the lightness of a color using HSL sliders, our converter provides instant feedback. The integrated color picker and opacity control give you complete command over every aspect of your color selection.</p>

            <h3>How to Use the Color Converter</h3>
            <ol>
                <li><strong>Select a Color:</strong> You can start in three ways: click the color swatch to use the visual color picker, type a value into any of the input fields (e.g., `#3498db` in HEX), or adjust the H, S, L, or RGB sliders.</li>
                <li><strong>Watch the Real-Time Updates:</strong> As you make a change, all other color formats will update instantly to match your selection.</li>
                <li><strong>Adjust Opacity:</strong> Use the "Opacity" slider to change the alpha value of your color, which will be reflected in the RGBA and HSLA values.</li>
                <li><strong>Copy the Code:</strong> Click the "Copy" button next to any format to instantly save the color code to your clipboard.</li>
            </ol>

            <h3>Frequently Asked Questions</h3>
            <h4>What is a color converter used for?</h4>
            <p>A color converter is a tool used by web designers, developers, and digital artists to translate color values from one format (like HEX) to another (like RGB or HSL). This is essential because different applications and programming languages require specific color formats to render colors correctly.</p>
            <h4>How do you convert a HEX color code to RGB?</h4>
            <p>To convert a HEX code like #FF5733 to RGB, you split the code into three pairs (FF, 57, 33). Each pair represents Red, Green, and Blue, respectively. You then convert each hexadecimal pair to its decimal equivalent: FF becomes 255, 57 becomes 87, and 33 becomes 51. So, #FF5733 is rgb(255, 87, 51). Our tool automates this process instantly.</p>
            <h4>What is the difference between HSL and HSV color models?</h4>
            <p>Both HSL (Hue, Saturation, Lightness) and HSV (Hue, Saturation, Value) are cylindrical representations of the RGB color model. The main difference is in how they define brightness. In HSL, maximum Lightness (L=100%) is always pure white. In HSV, maximum Value (V=100%) represents the brightest version of the chosen hue, not necessarily white. HSL is often considered more intuitive for human color selection.</p>
            <h4>What does the alpha value in RGBA and HSLA mean?</h4>
            <p>The 'A' in RGBA and HSLA stands for 'alpha,' which represents the color's opacity. It's a value between 0.0 (completely transparent) and 1.0 (completely opaque). An alpha value of 0.5 would make the color 50% transparent, allowing background elements to show through. Our tool's opacity slider lets you adjust this value easily.</p>
            <h4>How do I use a color picker to find a color code?</h4>
            <p>Simply click on the color swatch in our tool. This will open your browser's native color picker. You can then visually select any color you want. Once you choose a color, our tool will instantly update all the text fields (HEX, RGB, HSL, etc.) to show the corresponding codes for your selection.</p>
        </div>

        <div class="color-converter-features">
            <h3 class="color-converter-features-title">Key Features:</h3>
            <ul class="color-converter-features-list">
                <li class="color-converter-features-item">Live Color Picker</li>
                <li class="color-converter-features-item">Real-Time Two-Way Conversion</li>
                <li class="color-converter-features-item">HEX, RGB, HSL, RGBA, HSLA</li>
                <li class="color-converter-features-item">Opacity (Alpha) Slider</li>
                <li class="color-converter-features-item">One-Click Copy for All Formats</li>
                <li class="color-converter-features-item">Clean, Responsive UI</li>
                <li class="color-converter-features-item">100% Free & Client-Side</li>
            </ul>
        </div>
    </div>

    <div class="color-converter-notification" id="colorNotification">✓ Copied to clipboard!</div>

    <script>
        (function() {
            'use strict';
            const elements = {
                picker: document.getElementById('colorPicker'),
                preview: document.getElementById('colorPreview'),
                hex: document.getElementById('hexInput'),
                r: document.getElementById('rInput'),
                g: document.getElementById('gInput'),
                b: document.getElementById('bInput'),
                h: document.getElementById('hInput'),
                s: document.getElementById('sInput'),
                l: document.getElementById('lInput'),
                alphaSlider: document.getElementById('alphaSlider'),
                alphaValue: document.getElementById('alphaValue'),
                notification: document.getElementById('colorNotification'),
            };

            let isUpdating = false;

            const updateColor = (source) => {
                if (isUpdating) return;
                isUpdating = true;

                let r, g, b, h, s, l, hex;
                const a = parseFloat(elements.alphaSlider.value);

                try {
                    switch (source) {
                        case 'picker':
                            hex = elements.picker.value;
                            ({ r, g, b } = hexToRgb(hex));
                            break;
                        case 'hex':
                            hex = elements.hex.value;
                            if (!/^#([0-9a-f]{3}){1,2}$/i.test(hex)) throw new Error('Invalid HEX');
                            ({ r, g, b } = hexToRgb(hex));
                            break;
                        case 'rgb':
                            r = parseInt(elements.r.value);
                            g = parseInt(elements.g.value);
                            b = parseInt(elements.b.value);
                            if (isNaN(r) || isNaN(g) || isNaN(b)) throw new Error('Invalid RGB');
                            hex = rgbToHex(r, g, b);
                            break;
                        case 'hsl':
                            h = parseInt(elements.h.value);
                            s = parseInt(elements.s.value);
                            l = parseInt(elements.l.value);
                            if (isNaN(h) || isNaN(s) || isNaN(l)) throw new Error('Invalid HSL');
                            ({ r, g, b } = hslToRgb(h, s, l));
                            hex = rgbToHex(r, g, b);
                            break;
                        case 'alpha':
                            hex = elements.hex.value;
                             ({ r, g, b } = hexToRgb(hex));
                            break;
                    }

                    if (r === undefined) throw new Error('Update failed');
                    
                    ({ h, s, l } = rgbToHsl(r, g, b));

                    elements.picker.value = hex;
                    elements.preview.style.backgroundColor = `rgba(${r}, ${g}, ${b}, ${a})`;
                    
                    if (source !== 'hex') elements.hex.value = hex;
                    if (source !== 'rgb') {
                        elements.r.value = r;
                        elements.g.value = g;
                        elements.b.value = b;
                    }
                    if (source !== 'hsl') {
                        elements.h.value = Math.round(h);
                        elements.s.value = Math.round(s);
                        elements.l.value = Math.round(l);
                    }
                    elements.alphaValue.textContent = a.toFixed(2);
                    elements.alphaSlider.style.background = `linear-gradient(90deg, transparent, ${hex})`;
                } catch(e) {
                   // Silently fail for invalid intermediate input
                } finally {
                    isUpdating = false;
                }
            };
            
            // Conversion Functions
            const hexToRgb = (hex) => {
                let r = 0, g = 0, b = 0;
                if (hex.length == 4) {
                    r = "0x" + hex[1] + hex[1];
                    g = "0x" + hex[2] + hex[2];
                    b = "0x" + hex[3] + hex[3];
                } else if (hex.length == 7) {
                    r = "0x" + hex[1] + hex[2];
                    g = "0x" + hex[3] + hex[4];
                    b = "0x" + hex[5] + hex[6];
                }
                return { r: +r, g: +g, b: +b };
            };

            const rgbToHex = (r, g, b) => {
                r = Math.min(255, Math.max(0, r));
                g = Math.min(255, Math.max(0, g));
                b = Math.min(255, Math.max(0, b));
                return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
            };

            const rgbToHsl = (r, g, b) => {
                r /= 255; g /= 255; b /= 255;
                let max = Math.max(r, g, b), min = Math.min(r, g, b);
                let h, s, l = (max + min) / 2;
                if (max == min) { h = s = 0; } 
                else {
                    let d = max - min;
                    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                    switch (max) {
                        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                        case g: h = (b - r) / d + 2; break;
                        case b: h = (r - g) / d + 4; break;
                    }
                    h /= 6;
                }
                return { h: h * 360, s: s * 100, l: l * 100 };
            };

            const hslToRgb = (h, s, l) => {
                h /= 360; s /= 100; l /= 100;
                let r, g, b;
                if (s == 0) { r = g = b = l; } 
                else {
                    const hue2rgb = (p, q, t) => {
                        if (t < 0) t += 1; if (t > 1) t -= 1;
                        if (t < 1/6) return p + (q - p) * 6 * t;
                        if (t < 1/2) return q;
                        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                        return p;
                    };
                    let q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                    let p = 2 * l - q;
                    r = hue2rgb(p, q, h + 1/3);
                    g = hue2rgb(p, q, h);
                    b = hue2rgb(p, q, h - 1/3);
                }
                return { r: Math.round(r * 255), g: Math.round(g * 255), b: Math.round(b * 255) };
            };
            
            window.ColorConverterTool = {
                copy(type) {
                    let text;
                    const a = parseFloat(elements.alphaSlider.value);
                    switch(type) {
                        case 'hex': text = elements.hex.value; break;
                        case 'rgb': text = a < 1 ? `rgba(${elements.r.value}, ${elements.g.value}, ${elements.b.value}, ${a.toFixed(2)})` : `rgb(${elements.r.value}, ${elements.g.value}, ${elements.b.value})`; break;
                        case 'hsl': text = a < 1 ? `hsla(${elements.h.value}, ${elements.s.value}%, ${elements.l.value}%, ${a.toFixed(2)})` : `hsl(${elements.h.value}, ${elements.s.value}%, ${elements.l.value}%)`; break;
                    }
                    navigator.clipboard.writeText(text).then(() => {
                        elements.notification.classList.add('show');
                        setTimeout(() => elements.notification.classList.remove('show'), 2500);
                    });
                }
            };

            // Event Listeners
            elements.picker.addEventListener('input', () => updateColor('picker'));
            elements.hex.addEventListener('input', () => updateColor('hex'));
            ['r', 'g', 'b'].forEach(c => elements[c].addEventListener('input', () => updateColor('rgb')));
            ['h', 's', 'l'].forEach(c => elements[c].addEventListener('input', () => updateColor('hsl')));
            elements.alphaSlider.addEventListener('input', () => updateColor('alpha'));

            // Initial Load
            updateColor('hex');
            elements.hex.value = '#3498DB';
            updateColor('hex');

        })();
    </script>
</body>
</html>