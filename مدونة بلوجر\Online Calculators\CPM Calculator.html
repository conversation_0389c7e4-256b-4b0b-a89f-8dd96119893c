<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPM Calculator Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free CPM Calculator - Calculate Cost Per Mille & Ad Spend",
        "description": "Calculate CPM (Cost Per Mille), ad spend, and impressions instantly. Free online CPM calculator for digital advertising campaigns and marketing budget planning.",
        "url": "https://www.webtoolskit.org/p/cpm-calculator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "CPM Calculator",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CalculateAction", "name": "Calculate CPM" },
            { "@type": "CalculateAction", "name": "Calculate Ad Spend" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I calculate my CPM?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate your CPM, divide your total ad spend by the number of impressions, then multiply by 1,000. Formula: CPM = (Total Cost ÷ Impressions) × 1,000. For example, if you spend $100 on 50,000 impressions: CPM = ($100 ÷ 50,000) × 1,000 = $2.00 per thousand impressions."
          }
        },
        {
          "@type": "Question",
          "name": "What is the formula for calculating CPM?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The CPM formula is: CPM = (Total Advertising Cost ÷ Total Impressions) × 1,000. This calculates the cost per 1,000 impressions. You can also rearrange the formula to find Total Cost = (CPM × Impressions) ÷ 1,000, or Impressions = (Total Cost × 1,000) ÷ CPM."
          }
        },
        {
          "@type": "Question",
          "name": "What does $10 per CPM mean?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "$10 per CPM means you pay $10 for every 1,000 impressions (views) of your advertisement. This is the cost to show your ad to 1,000 people. If your campaign gets 10,000 impressions at $10 CPM, you would pay $100 total for the advertising campaign."
          }
        },
        {
          "@type": "Question",
          "name": "What is a good CPM rate?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A good CPM rate varies by industry and platform. Generally, $1-$3 CPM is considered good for display ads, $6-$12 for social media ads, and $10-$30 for video ads. Premium placements and targeted audiences typically have higher CPMs. The key is ensuring your CPM allows for profitable conversions based on your conversion rates and customer lifetime value."
          }
        },
        {
          "@type": "Question",
          "name": "What is the cost per mille CPM when you spend $50 on 10000 impressions?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "When you spend $50 on 10,000 impressions, your CPM is $5.00. Calculation: CPM = ($50 ÷ 10,000) × 1,000 = $5.00 per thousand impressions. This means you're paying $5 for every 1,000 times your ad is displayed to users."
          }
        }
      ]
    }
    </script>


    <style>
        /* CPM Calculator Widget - Simplified & Template Compatible */
        .cpm-calculator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .cpm-calculator-widget-container * { box-sizing: border-box; }

        .cpm-calculator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .cpm-calculator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .cpm-calculator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .cpm-calculator-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .cpm-calculator-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .cpm-calculator-input-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .cpm-calculator-input-group {
            display: flex;
            flex-direction: column;
        }

        .cpm-calculator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .cpm-calculator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .cpm-calculator-btn:hover { transform: translateY(-2px); }

        .cpm-calculator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .cpm-calculator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .cpm-calculator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .cpm-calculator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .cpm-calculator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .cpm-calculator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .cpm-calculator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .cpm-calculator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .cpm-calculator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .cpm-calculator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .cpm-calculator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .cpm-calculator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .cpm-calculator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .cpm-calculator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .cpm-calculator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="margin-calculator"] .cpm-calculator-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="percentage-calculator"] .cpm-calculator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="roi-calculator"] .cpm-calculator-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }

        .cpm-calculator-related-tool-item:hover .cpm-calculator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="margin-calculator"]:hover .cpm-calculator-related-tool-icon { background: linear-gradient(145deg, #059669, #047857); }
        a[href*="percentage-calculator"]:hover .cpm-calculator-related-tool-icon { background: linear-gradient(145deg, #7C3AED, #6D28D9); }
        a[href*="roi-calculator"]:hover .cpm-calculator-related-tool-icon { background: linear-gradient(145deg, #D97706, #B45309); }
        
        .cpm-calculator-related-tool-item { box-shadow: none; border: none; }
        .cpm-calculator-related-tool-item:hover { box-shadow: none; border: none; }
        .cpm-calculator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .cpm-calculator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .cpm-calculator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .cpm-calculator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .cpm-calculator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .cpm-calculator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .cpm-calculator-related-tool-item:hover .cpm-calculator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .cpm-calculator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .cpm-calculator-widget-title { font-size: 1.875rem; }
            .cpm-calculator-buttons { flex-direction: column; }
            .cpm-calculator-btn { flex: none; }
            .cpm-calculator-input-row { grid-template-columns: 1fr; gap: var(--spacing-md); }
            .cpm-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .cpm-calculator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .cpm-calculator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .cpm-calculator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .cpm-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .cpm-calculator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .cpm-calculator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .cpm-calculator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .cpm-calculator-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .cpm-calculator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .cpm-calculator-output::selection { background-color: var(--primary-color); color: white; }
        .cpm-calculator-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .cpm-calculator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="cpm-calculator-widget-container">
        <h1 class="cpm-calculator-widget-title">CPM Calculator</h1>
        <p class="cpm-calculator-widget-description">
            Calculate CPM (Cost Per Mille), ad spend, and impressions for your digital advertising campaigns.
        </p>
        
        <div class="cpm-calculator-input-row">
            <div class="cpm-calculator-input-group">
                <label for="cpmCalculatorCost" class="cpm-calculator-label">Total Ad Spend ($):</label>
                <input 
                    type="number" 
                    id="cpmCalculatorCost" 
                    class="cpm-calculator-input"
                    placeholder="Enter total advertising cost..."
                    step="0.01"
                    min="0"
                />
            </div>
            
            <div class="cpm-calculator-input-group">
                <label for="cpmCalculatorImpressions" class="cpm-calculator-label">Total Impressions:</label>
                <input 
                    type="number" 
                    id="cpmCalculatorImpressions" 
                    class="cpm-calculator-input"
                    placeholder="Enter total impressions..."
                    step="1"
                    min="0"
                />
            </div>
        </div>

        <div class="cpm-calculator-buttons">
            <button class="cpm-calculator-btn cpm-calculator-btn-primary" onclick="CPMCalculator.calculate()">
                Calculate CPM
            </button>
            <button class="cpm-calculator-btn cpm-calculator-btn-secondary" onclick="CPMCalculator.clear()">
                Clear All
            </button>
        </div>

        <div class="cpm-calculator-result">
            <h3 class="cpm-calculator-result-title">CPM Calculation:</h3>
            <div class="cpm-calculator-output" id="cpmCalculatorOutput">
                Your CPM calculation will appear here...
            </div>
        </div>

        <div class="cpm-calculator-related-tools">
            <h3 class="cpm-calculator-related-tools-title">Related Tools</h3>
            <div class="cpm-calculator-related-tools-grid">
                <a href="/p/margin-calculator.html" class="cpm-calculator-related-tool-item" rel="noopener">
                    <div class="cpm-calculator-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="cpm-calculator-related-tool-name">Margin Calculator</div>
                </a>

                <a href="/p/percentage-calculator.html" class="cpm-calculator-related-tool-item" rel="noopener">
                    <div class="cpm-calculator-related-tool-icon">
                        <i class="fas fa-percent"></i>
                    </div>
                    <div class="cpm-calculator-related-tool-name">Percentage Calculator</div>
                </a>

                <a href="/p/roi-calculator.html" class="cpm-calculator-related-tool-item" rel="noopener">
                    <div class="cpm-calculator-related-tool-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="cpm-calculator-related-tool-name">ROI Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional CPM Calculator for Digital Advertising</h2>
            <p>Understanding CPM (Cost Per Mille) is essential for digital marketers and advertisers who want to optimize their advertising spend and measure campaign efficiency. Our <strong>CPM calculator</strong> helps you determine the cost per thousand impressions, calculate total ad spend, or estimate the number of impressions you can achieve with your budget. Whether you're running display ads, social media campaigns, or video advertising, CPM calculations are crucial for budget planning and performance analysis.</p>
            <p>CPM serves as a key performance indicator that allows you to compare the cost-effectiveness of different advertising channels and campaigns. By understanding your CPM rates, you can make informed decisions about budget allocation, targeting strategies, and campaign optimization to maximize your advertising ROI.</p>
            
            <h3>How to Use the CPM Calculator</h3>
            <ol>
                <li><strong>Enter Total Ad Spend:</strong> Input the total amount you've spent or plan to spend on your advertising campaign.</li>
                <li><strong>Enter Total Impressions:</strong> Input the number of impressions your ads received or are expected to receive.</li>
                <li><strong>Calculate CPM:</strong> Click "Calculate CPM" to see your cost per thousand impressions and related metrics.</li>
            </ol>
        
            <h3>Frequently Asked Questions About CPM</h3>
            
            <h4>How do I calculate my CPM?</h4>
            <p>To calculate your CPM, divide your total ad spend by the number of impressions, then multiply by 1,000. Formula: CPM = (Total Cost ÷ Impressions) × 1,000. For example, if you spend $100 on 50,000 impressions: CPM = ($100 ÷ 50,000) × 1,000 = $2.00 per thousand impressions.</p>
            
            <h4>What is the formula for calculating CPM?</h4>
            <p>The CPM formula is: CPM = (Total Advertising Cost ÷ Total Impressions) × 1,000. This calculates the cost per 1,000 impressions. You can also rearrange the formula to find Total Cost = (CPM × Impressions) ÷ 1,000, or Impressions = (Total Cost × 1,000) ÷ CPM.</p>
            
            <h4>What does $10 per CPM mean?</h4>
            <p>$10 per CPM means you pay $10 for every 1,000 impressions (views) of your advertisement. This is the cost to show your ad to 1,000 people. If your campaign gets 10,000 impressions at $10 CPM, you would pay $100 total for the advertising campaign.</p>
            
            <h4>What is a good CPM rate?</h4>
            <p>A good CPM rate varies by industry and platform. Generally, $1-$3 CPM is considered good for display ads, $6-$12 for social media ads, and $10-$30 for video ads. Premium placements and targeted audiences typically have higher CPMs. The key is ensuring your CPM allows for profitable conversions based on your conversion rates and customer lifetime value.</p>
            
            <h4>What is the cost per mille CPM when you spend $50 on 10000 impressions?</h4>
            <p>When you spend $50 on 10,000 impressions, your CPM is $5.00. Calculation: CPM = ($50 ÷ 10,000) × 1,000 = $5.00 per thousand impressions. This means you're paying $5 for every 1,000 times your ad is displayed to users.</p>
        </div>


        <div class="cpm-calculator-features">
            <h3 class="cpm-calculator-features-title">Key Features:</h3>
            <ul class="cpm-calculator-features-list">
                <li class="cpm-calculator-features-item" style="margin-bottom: 0.3em;">Accurate CPM calculation</li>
                <li class="cpm-calculator-features-item" style="margin-bottom: 0.3em;">Cost per impression metrics</li>
                <li class="cpm-calculator-features-item" style="margin-bottom: 0.3em;">Campaign budget planning</li>
                <li class="cpm-calculator-features-item" style="margin-bottom: 0.3em;">Advertising ROI analysis</li>
                <li class="cpm-calculator-features-item" style="margin-bottom: 0.3em;">Real-time calculations</li>
                <li class="cpm-calculator-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="cpm-calculator-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="cpm-calculator-notification" id="cpmCalculatorNotification">
        ✓ Calculation completed!
    </div>

    <script>
        // Simplified CPM Calculator
        (function() {
            'use strict';

            const elements = {
                costInput: () => document.getElementById('cpmCalculatorCost'),
                impressionsInput: () => document.getElementById('cpmCalculatorImpressions'),
                output: () => document.getElementById('cpmCalculatorOutput'),
                notification: () => document.getElementById('cpmCalculatorNotification')
            };

            window.CPMCalculator = {
                calculate() {
                    const cost = parseFloat(elements.costInput().value);
                    const impressions = parseFloat(elements.impressionsInput().value);
                    const output = elements.output();

                    if (!cost || !impressions) {
                        output.innerHTML = 'Please enter both total ad spend and total impressions.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    if (cost < 0 || impressions < 0) {
                        output.innerHTML = 'Please enter positive values only.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    if (impressions === 0) {
                        output.innerHTML = 'Impressions cannot be zero.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const cpm = (cost / impressions) * 1000;
                    const costPerImpression = cost / impressions;

                    // Determine CPM rating
                    let cpmRating = '';
                    if (cpm <= 3) {
                        cpmRating = 'Excellent (Low Cost)';
                    } else if (cpm <= 6) {
                        cpmRating = 'Good';
                    } else if (cpm <= 12) {
                        cpmRating = 'Average';
                    } else if (cpm <= 25) {
                        cpmRating = 'High (Premium)';
                    } else {
                        cpmRating = 'Very High';
                    }

                    output.innerHTML = `
                        <strong>CPM (Cost Per Mille):</strong> $${cpm.toFixed(2)}<br>
                        <strong>Cost Per Impression:</strong> $${costPerImpression.toFixed(4)}<br>
                        <strong>Total Ad Spend:</strong> $${cost.toFixed(2)}<br>
                        <strong>Total Impressions:</strong> ${impressions.toLocaleString()}<br>
                        <strong>CPM Rating:</strong> ${cpmRating}
                    `;

                    this.showNotification();
                },

                clear() {
                    elements.costInput().value = '';
                    elements.impressionsInput().value = '';
                    elements.output().innerHTML = 'Your CPM calculation will appear here...';
                    elements.output().style.color = '';
                    
                    this.showNotification('Calculator cleared');
                },

                showNotification(message = '✓ Calculation completed!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const costInput = elements.costInput();
                const impressionsInput = elements.impressionsInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Removed auto-calculate functionality - now only calculates when button is clicked

                // Handle Enter key
                [costInput, impressionsInput].forEach(input => {
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            CPMCalculator.calculate();
                        }
                    });
                });
            });
        })();
    </script>
</body>
</html>