<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Roman Numerals to Number Converter - Convert Roman to Arabic</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Roman Numerals to Number Converter",
        "description": "Easily convert any Roman numeral, like MMXXIV, into its corresponding number (2024). A free online tool for students, historians, and enthusiasts.",
        "url": "https://www.webtoolskit.org/p/roman-numerals-to-number.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-04",
        "dateModified": "2025-06-04",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Roman Numerals to Number Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Roman Numerals to Number" },
            { "@type": "CopyAction", "name": "Copy Numeric Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert Roman numerals to numbers?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert a Roman numeral, read it from left to right. If a symbol has a value greater than or equal to the symbol to its right, add its value. If a symbol's value is less than the one to its right, subtract its value. For example, in 'XI', X (10) is greater than I (1), so you add them: 10 + 1 = 11. In 'IX', I (1) is less than X (10), so you subtract: 10 - 1 = 9."
          }
        },
        {
          "@type": "Question",
          "name": "What does XXVIII mean in numbers?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To find the value of XXVIII, you add the value of each symbol together because they are in descending order: X (10) + X (10) + V (5) + I (1) + I (1) + I (1) = 28. So, XXVIII is the number 28."
          }
        },
        {
          "@type": "Question",
          "name": "Why is 1999 not written as MIM in Roman numerals?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The Roman numeral system has specific rules for subtraction. You can only subtract a power of ten (I, X, C) from the next two higher values (I from V and X; X from L and C; C from D and M). 'MIM' for 1999 (1000 + (1000-1)) breaks this rule. The correct way is to break the number down into its parts: 1000 (M) + 900 (CM) + 90 (XC) + 9 (IX), which combines to form MCMXCIX."
          }
        },
        {
          "@type": "Question",
          "name": "What is the Roman numeral for 2025?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To write 2025 in Roman numerals, you break it down by place value: 2000 is MM, 20 is XX, and 5 is V. Combining these gives you MMXXV."
          }
        },
        {
          "@type": "Question",
          "name": "Why did people stop using Roman numerals?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "People stopped using Roman numerals for daily calculations primarily because the system is inefficient for arithmetic. It lacks a concept for zero and a place-value system, which makes multiplication and division extremely difficult compared to the Arabic numeral system (0, 1, 2, 3...) that we use today. The Arabic system's simplicity and efficiency made it superior for trade, science, and mathematics."
          }
        }
      ]
    }
    </script>

    <style>
        /* Roman to Number Converter Widget - Simplified & Template Compatible */
        .roman-to-number-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .roman-to-number-converter-widget-container * { box-sizing: border-box; }

        .roman-to-number-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .roman-to-number-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .roman-to-number-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .roman-to-number-converter-input-group {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .roman-to-number-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            text-transform: uppercase;
        }

        .roman-to-number-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .roman-to-number-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .roman-to-number-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .roman-to-number-converter-btn:hover { transform: translateY(-2px); }

        .roman-to-number-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .roman-to-number-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .roman-to-number-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .roman-to-number-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .roman-to-number-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .roman-to-number-converter-btn-success:hover {
            background-color: #059669;
        }

        .roman-to-number-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .roman-to-number-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .roman-to-number-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 1.5rem;
            font-weight: bold;
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
            text-align: center;
        }

        .roman-to-number-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .roman-to-number-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .roman-to-number-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .roman-to-number-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .roman-to-number-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .roman-to-number-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .roman-to-number-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .roman-to-number-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="number-to-roman-numerals"] .roman-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="word-to-number-converter"] .roman-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #F97316, #EA580C); }
        a[href*="number-to-word-converter"] .roman-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }

        .roman-to-number-converter-related-tool-item:hover .roman-to-number-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="number-to-roman-numerals"]:hover .roman-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        a[href*="word-to-number-converter"]:hover .roman-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #fb923c, #f97316); }
        a[href*="number-to-word-converter"]:hover .roman-to-number-converter-related-tool-icon { background: linear-gradient(145deg, #2dd4bf, #14b8a6); }
        
        .roman-to-number-converter-related-tool-item { box-shadow: none; border: none; }
        .roman-to-number-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .roman-to-number-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .roman-to-number-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .roman-to-number-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .roman-to-number-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .roman-to-number-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .roman-to-number-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .roman-to-number-converter-related-tool-item:hover .roman-to-number-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .roman-to-number-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .roman-to-number-converter-widget-title { font-size: 1.875rem; }
            .roman-to-number-converter-buttons { flex-direction: column; }
            .roman-to-number-converter-btn { flex: none; }
            .roman-to-number-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .roman-to-number-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .roman-to-number-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .roman-to-number-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .roman-to-number-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .roman-to-number-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .roman-to-number-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .roman-to-number-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .roman-to-number-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .roman-to-number-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .roman-to-number-converter-output::selection { background-color: var(--primary-color); color: white; }
        .roman-to-number-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .roman-to-number-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="roman-to-number-converter-widget-container">
        <h1 class="roman-to-number-converter-widget-title">Roman Numerals to Number Converter</h1>
        <p class="roman-to-number-converter-widget-description">
            Instantly convert any Roman numeral into its modern number equivalent. Perfect for translating dates, chapter numbers, and more.
        </p>
        
        <div class="roman-to-number-converter-input-group">
            <label for="romanInput" class="roman-to-number-converter-label">Enter a Roman Numeral:</label>
            <input 
                type="text" 
                id="romanInput" 
                class="roman-to-number-converter-input"
                placeholder="e.g., MMXXIV"
            />
        </div>

        <div class="roman-to-number-converter-buttons">
            <button class="roman-to-number-converter-btn roman-to-number-converter-btn-primary" onclick="RomanToNumberConverter.convert()">
                Convert to Number
            </button>
            <button class="roman-to-number-converter-btn roman-to-number-converter-btn-secondary" onclick="RomanToNumberConverter.clear()">
                Clear All
            </button>
            <button class="roman-to-number-converter-btn roman-to-number-converter-btn-success" onclick="RomanToNumberConverter.copy()">
                Copy Number
            </button>
        </div>

        <div class="roman-to-number-converter-result">
            <h3 class="roman-to-number-converter-result-title">Result in Numbers:</h3>
            <div class="roman-to-number-converter-output" id="numberOutput">
                Your number will appear here...
            </div>
        </div>

        <div class="roman-to-number-converter-related-tools">
            <h3 class="roman-to-number-converter-related-tools-title">Related Tools</h3>
            <div class="roman-to-number-converter-related-tools-grid">
                <a href="/p/number-to-roman-numerals.html" class="roman-to-number-converter-related-tool-item" rel="noopener">
                    <div class="roman-to-number-converter-related-tool-icon">
                        <i class="fas fa-columns"></i>
                    </div>
                    <div class="roman-to-number-converter-related-tool-name">Number to Roman Numerals</div>
                </a>
                <a href="/p/word-to-number-converter.html" class="roman-to-number-converter-related-tool-item" rel="noopener">
                    <div class="roman-to-number-converter-related-tool-icon">
                        <i class="fas fa-keyboard"></i>
                    </div>
                    <div class="roman-to-number-converter-related-tool-name">Word to Number Converter</div>
                </a>
                <a href="/p/number-to-word-converter.html" class="roman-to-number-converter-related-tool-item" rel="noopener">
                    <div class="roman-to-number-converter-related-tool-icon">
                        <i class="fas fa-spell-check"></i>
                    </div>
                    <div class="roman-to-number-converter-related-tool-name">Number to Word Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Translate Roman Numerals to Numbers Instantly</h2>
            <p>Ever come across a Roman numeral like 'MCMXCIX' and wondered what number it represents? Our free <strong>Roman Numerals to Number Converter</strong> is designed to solve that problem instantly. This tool is perfect for students, historians, and anyone curious about the ancient numbering system. Simply enter a Roman numeral, and our converter will translate it into the familiar Arabic number we use today.</p>
            <p>From deciphering the copyright date on a movie to understanding the numbering of a Super Bowl, this converter provides a quick and accurate translation. It correctly interprets standard Roman numerals, including those using subtractive notation (like IV for 4), giving you a reliable result every time.</p>

            <h3>How to Use the Roman to Number Converter</h3>
            <ol>
                <li><strong>Enter a Roman Numeral:</strong> Type any valid Roman numeral (e.g., MMXXIV) into the input field.</li>
                <li><strong>Convert:</strong> Click the "Convert to Number" button.</li>
                <li><strong>Get the Result:</strong> The tool will instantly display the corresponding number in the result box.</li>
                <li><strong>Copy the Number:</strong> Use the "Copy Number" button to copy the result for your notes or research.</li>
            </ol>

            <h3>Frequently Asked Questions About Roman Numerals</h3>

            <h4>How do you convert Roman numerals to numbers?</h4>
            <p>To convert a Roman numeral, read it from left to right. If a symbol has a value greater than or equal to the symbol to its right, add its value. If a symbol's value is less than the one to its right, subtract its value. For example, in 'XI', X (10) is greater than I (1), so you add them: 10 + 1 = 11. In 'IX', I (1) is less than X (10), so you subtract: 10 - 1 = 9.</p>

            <h4>What does XXVIII mean in numbers?</h4>
            <p>To find the value of XXVIII, you add the value of each symbol together because they are in descending order: X (10) + X (10) + V (5) + I (1) + I (1) + I (1) = 28. So, XXVIII is the number 28.</p>

            <h4>Why is 1999 not written as MIM in Roman numerals?</h4>
            <p>The Roman numeral system has specific rules for subtraction. You can only subtract a power of ten (I, X, C) from the next two higher values (I from V and X; X from L and C; C from D and M). 'MIM' for 1999 (1000 + (1000-1)) breaks this rule. The correct way is to break the number down into its parts: 1000 (M) + 900 (CM) + 90 (XC) + 9 (IX), which combines to form MCMXCIX.</p>

            <h4>What is the Roman numeral for 2025?</h4>
            <p>To write 2025 in Roman numerals, you break it down by place value: 2000 is MM, 20 is XX, and 5 is V. Combining these gives you MMXXV.</p>

            <h4>Why did people stop using Roman numerals?</h4>
            <p>People stopped using Roman numerals for daily calculations primarily because the system is inefficient for arithmetic. It lacks a concept for zero and a place-value system, which makes multiplication and division extremely difficult compared to the Arabic numeral system (0, 1, 2, 3...) that we use today. The Arabic system's simplicity and efficiency made it superior for trade, science, and mathematics.</p>
        </div>

        <div class="roman-to-number-converter-features">
            <h3 class="roman-to-number-converter-features-title">Key Features:</h3>
            <ul class="roman-to-number-converter-features-list">
                <li class="roman-to-number-converter-features-item" style="margin-bottom: 0.3em;">Accurate Roman numeral parsing</li>
                <li class="roman-to-number-converter-features-item" style="margin-bottom: 0.3em;">Handles subtractive notation</li>
                <li class="roman-to-number-converter-features-item" style="margin-bottom: 0.3em;">Validates input for correctness</li>
                <li class="roman-to-number-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="roman-to-number-converter-features-item" style="margin-bottom: 0.3em;">Great for historical dates</li>
                <li class="roman-to-number-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side conversion</li>
                <li class="roman-to-number-converter-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="roman-to-number-converter-notification" id="romanToNumberConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Roman Numerals to Number Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('romanInput'),
                output: () => document.getElementById('numberOutput'),
                notification: () => document.getElementById('romanToNumberConverterNotification')
            };
            
            const romanMap = { 'I': 1, 'V': 5, 'X': 10, 'L': 50, 'C': 100, 'D': 500, 'M': 1000 };
            const validationRegex = /^M{0,3}(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})$/;

            window.RomanToNumberConverter = {
                convert() {
                    const outputEl = elements.output();
                    const inputEl = elements.input();
                    const romanStr = inputEl.value.trim().toUpperCase();

                    outputEl.textContent = ''; // Clear previous content

                    if (!romanStr) {
                        outputEl.textContent = 'Please enter a Roman numeral.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }

                    if (!validationRegex.test(romanStr)) {
                        outputEl.textContent = 'Invalid Roman numeral format.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }
                    
                    outputEl.style.color = '';
                    
                    let result = 0;
                    for (let i = 0; i < romanStr.length; i++) {
                        const currentVal = romanMap[romanStr[i]];
                        const nextVal = romanMap[romanStr[i + 1]];

                        if (nextVal && currentVal < nextVal) {
                            result -= currentVal;
                        } else {
                            result += currentVal;
                        }
                    }
                    
                    outputEl.textContent = result;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your number will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (!text || text.includes('...')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const inputEl = elements.input();
                inputEl.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        RomanToNumberConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>