<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Power Converter - Convert Watts, Kilowatts, Horsepower & More</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Power Converter - Convert Watts, Kilowatts, Horsepower & More",
        "description": "Instantly convert between various power units like watts (W), kilowatts (kW), horsepower (hp), and more. A free online tool for engineering, physics, and technical calculations.",
        "url": "https://www.webtoolskit.org/p/power-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-10",
        "dateModified": "2025-06-20",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Power Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Power Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a power converter used for?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A physical power converter is an electrical device that changes one form of electrical power to another. This can include converting AC to DC (a rectifier), DC to AC (an inverter), changing the voltage or frequency of AC power, or regulating DC voltage. They are essential for allowing electronics to connect to different power sources safely and efficiently."
          }
        },
        {
          "@type": "Question",
          "name": "What is the primary function of a power converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The primary function of a power converter is to process and control the flow of electrical energy by supplying voltages and currents in a form that is optimally suited for the user's load. In short, it adapts a power source to a power load, ensuring compatibility and efficiency."
          }
        },
        {
          "@type": "Question",
          "name": "How to choose a power converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To choose a physical power converter, you must consider several factors: 1. Input/Output Voltage: Ensure it matches your power source and device. 2. Wattage: The converter must be rated to handle the total power (in watts) of the device(s) you'll be using. It's wise to choose a converter with a higher wattage rating than your device needs. 3. Type: Determine if you need an AC-DC, DC-AC, or voltage converter. 4. Plug Type: Ensure it's compatible with the outlets you'll be using."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between a power converter and a transformer?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A transformer is a specific type of power converter that only works with AC (alternating current) and changes voltage levels through electromagnetic induction. A 'power converter' is a broader term that can include transformers, but also more complex electronic circuits that can convert AC to DC, DC to AC, or DC to DC, often involving active components like transistors and capacitors."
          }
        },
        {
          "@type": "Question",
          "name": "How do I know if I need a converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You need a physical power converter if your electronic device's required voltage and/or current type (AC/DC) does not match the power source. For travel, check your device's power label. If it says 'INPUT: 100-240V', it is multi-voltage and doesn't need a voltage converter (just a plug adapter). If it shows a single voltage (e.g., '120V'), you will need a converter to use it in a region with a different standard (e.g., 230V)."
          }
        }
      ]
    }
    </script>

    <style>
        /* Power Converter Widget - Simplified & Template Compatible */
        .power-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .power-converter-widget-container * { box-sizing: border-box; }

        .power-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .power-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .power-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .power-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .power-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .power-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .power-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .power-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .power-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .power-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .power-converter-btn:hover { transform: translateY(-2px); }

        .power-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .power-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .power-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .power-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .power-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .power-converter-btn-success:hover {
            background-color: #059669;
        }

        .power-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .power-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .power-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .power-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .power-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .power-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .power-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .power-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .power-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .power-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .power-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="voltage-converter"] .power-converter-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="current-converter"] .power-converter-related-tool-icon { background: linear-gradient(145deg, #3B82F6, #2563EB); }
        a[href*="energy-converter"] .power-converter-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }

        .power-converter-related-tool-item:hover .power-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="voltage-converter"]:hover .power-converter-related-tool-icon { background: linear-gradient(145deg, #fbbd24, #f59e0b); }
        a[href*="current-converter"]:hover .power-converter-related-tool-icon { background: linear-gradient(145deg, #60a5fa, #3b82f6); }
        a[href*="energy-converter"]:hover .power-converter-related-tool-icon { background: linear-gradient(145deg, #2dd4bf, #14b8a6); }
        
        .power-converter-related-tool-item { box-shadow: none; border: none; }
        .power-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .power-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .power-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .power-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .power-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .power-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .power-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .power-converter-related-tool-item:hover .power-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .power-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .power-converter-widget-title { font-size: 1.875rem; }
            .power-converter-buttons { flex-direction: column; }
            .power-converter-btn { flex: none; }
            .power-converter-input-group { grid-template-columns: 1fr; }
            .power-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .power-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .power-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .power-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .power-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .power-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .power-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .power-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .power-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .power-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .power-converter-output::selection { background-color: var(--primary-color); color: white; }
        .power-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .power-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="power-converter-widget-container">
        <h1 class="power-converter-widget-title">Power Converter</h1>
        <p class="power-converter-widget-description">
            Quickly convert power units such as Watts, Kilowatts, and Horsepower for your engineering, scientific, or automotive calculations.
        </p>
        
        <div class="power-converter-input-group">
            <label for="powerFromInput" class="power-converter-label">From:</label>
            <input 
                type="number" 
                id="powerFromInput" 
                class="power-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="powerFromUnit" class="power-converter-select">
                <option value="w" selected>Watt (W)</option>
                <option value="kw">Kilowatt (kW)</option>
                <option value="mw">Megawatt (MW)</option>
                <option value="hp">Horsepower (hp)</option>
                <option value="hp_metric">Horsepower (metric)</option>
            </select>
        </div>

        <div class="power-converter-input-group">
            <label for="powerToInput" class="power-converter-label">To:</label>
            <input 
                type="number" 
                id="powerToInput" 
                class="power-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="powerToUnit" class="power-converter-select">
                <option value="w">Watt (W)</option>
                <option value="kw">Kilowatt (kW)</option>
                <option value="mw">Megawatt (MW)</option>
                <option value="hp" selected>Horsepower (hp)</option>
                <option value="hp_metric">Horsepower (metric)</option>
            </select>
        </div>

        <div class="power-converter-buttons">
            <button class="power-converter-btn power-converter-btn-primary" onclick="PowerConverter.convert()">
                Convert Power
            </button>
            <button class="power-converter-btn power-converter-btn-secondary" onclick="PowerConverter.clear()">
                Clear All
            </button>
            <button class="power-converter-btn power-converter-btn-success" onclick="PowerConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="power-converter-result">
            <h3 class="power-converter-result-title">Conversion Result:</h3>
            <div class="power-converter-output" id="powerConverterOutput">
                Your converted power will appear here...
            </div>
        </div>

        <div class="power-converter-related-tools">
            <h3 class="power-converter-related-tools-title">Related Tools</h3>
            <div class="power-converter-related-tools-grid">
                <a href="/p/voltage-converter.html" class="power-converter-related-tool-item" rel="noopener">
                    <div class="power-converter-related-tool-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <div class="power-converter-related-tool-name">Voltage Converter</div>
                </a>
                <a href="/p/current-converter.html" class="power-converter-related-tool-item" rel="noopener">
                    <div class="power-converter-related-tool-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="power-converter-related-tool-name">Current Converter</div>
                </a>
                <a href="/p/energy-converter.html" class="power-converter-related-tool-item" rel="noopener">
                    <div class="power-converter-related-tool-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="power-converter-related-tool-name">Energy Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Instant Power Unit Conversion Tool</h2>
            <p>Power, the rate at which energy is used or work is done, is a fundamental concept in physics and engineering. It's measured in various units depending on the industry and application. Our free <strong>Power Converter</strong> is designed to bridge the gap between these units, providing a seamless way to convert values. Whether you're an engineer working with kilowatts, an automotive enthusiast discussing horsepower, or a student learning about watts, this tool ensures you get accurate conversions in an instant.</p>
            <p>This utility supports the most common power units, including Watts (W), Kilowatts (kW), Megawatts (MW), mechanical Horsepower (hp), and metric Horsepower. Eliminate manual errors and save valuable time on your projects with our simple and reliable converter.</p>

            <h3>How to Use the Power Converter</h3>
            <ol>
                <li><strong>Enter Power Value:</strong> Type the number you want to convert into the "From" input field.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., Kilowatts) and your target unit (e.g., Horsepower) from the dropdown menus.</li>
                <li><strong>Convert:</strong> Click the "Convert Power" button to see the precise result calculated instantly.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to quickly copy the converted value for use in your calculations, reports, or documents.</li>
            </ol>

            <h3>Frequently Asked Questions About Power Conversion</h3>
            
            <h4>What is a power converter used for?</h4>
            <p>A physical power converter is an electrical device that changes one form of electrical power to another. This can include converting AC to DC (a rectifier), DC to AC (an inverter), changing the voltage or frequency of AC power, or regulating DC voltage. They are essential for allowing electronics to connect to different power sources safely and efficiently.</p>

            <h4>What is the primary function of a power converter?</h4>
            <p>The primary function of a power converter is to process and control the flow of electrical energy by supplying voltages and currents in a form that is optimally suited for the user's load. In short, it adapts a power source to a power load, ensuring compatibility and efficiency.</p>

            <h4>How to choose a power converter?</h4>
            <p>To choose a physical power converter, you must consider several factors: 1. Input/Output Voltage: Ensure it matches your power source and device. 2. Wattage: The converter must be rated to handle the total power (in watts) of the device(s) you'll be using. It's wise to choose a converter with a higher wattage rating than your device needs. 3. Type: Determine if you need an AC-DC, DC-AC, or voltage converter. 4. Plug Type: Ensure it's compatible with the outlets you'll be using.</p>

            <h4>What is the difference between a power converter and a transformer?</h4>
            <p>A transformer is a specific type of power converter that only works with AC (alternating current) and changes voltage levels through electromagnetic induction. A 'power converter' is a broader term that can include transformers, but also more complex electronic circuits that can convert AC to DC, DC to AC, or DC to DC, often involving active components like transistors and capacitors.</p>

            <h4>How do I know if I need a converter?</h4>
            <p>You need a physical power converter if your electronic device's required voltage and/or current type (AC/DC) does not match the power source. For travel, check your device's power label. If it says 'INPUT: 100-240V', it is multi-voltage and doesn't need a voltage converter (just a plug adapter). If it shows a single voltage (e.g., '120V'), you will need a converter to use it in a region with a different standard (e.g., 230V).</p>
        </div>

        <div class="power-converter-features">
            <h3 class="power-converter-features-title">Key Features:</h3>
            <ul class="power-converter-features-list">
                <li class="power-converter-features-item" style="margin-bottom: 0.3em;">Watts, kW, MW, and hp units</li>
                <li class="power-converter-features-item" style="margin-bottom: 0.3em;">High-precision conversions</li>
                <li class="power-converter-features-item" style="margin-bottom: 0.3em;">Mechanical & metric horsepower</li>
                <li class="power-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="power-converter-features-item" style="margin-bottom: 0.3em;">Fast client-side calculation</li>
                <li class="power-converter-features-item" style="margin-bottom: 0.3em;">Fully responsive for all devices</li>
                <li class="power-converter-features-item">Completely free to use</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="power-converter-notification" id="powerConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Power Converter
        (function() {
            'use strict';

            // Conversion factors to Watts (W)
            const conversionFactors = {
                'w': 1,
                'kw': 1000,
                'mw': 1000000,
                'hp': 745.699872,
                'hp_metric': 735.49875
            };

            const elements = {
                fromInput: () => document.getElementById('powerFromInput'),
                toInput: () => document.getElementById('powerToInput'),
                fromUnit: () => document.getElementById('powerFromUnit'),
                toUnit: () => document.getElementById('powerToUnit'),
                output: () => document.getElementById('powerConverterOutput'),
                notification: () => document.getElementById('powerConverterNotification')
            };

            window.PowerConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to Watts first, then to target unit
                    const valueInWatts = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInWatts / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-6 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(10)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'w': 'W', 'kw': 'kW', 'mw': 'MW', 'hp': 'hp', 'hp_metric': 'hp(M)' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted power will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        PowerConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>