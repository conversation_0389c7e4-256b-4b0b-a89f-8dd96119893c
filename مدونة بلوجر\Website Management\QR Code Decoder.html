<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Decoder - Read QR Codes from Images Online</title>
    <meta name="description" content="Instantly decode a QR code from an image. Upload or drag-and-drop a file to read the content of any QR code online, for free.">
    <link rel="canonical" href="https://www.webtoolskit.org/p/qr-code-decoder.html">
    <!-- Font Awesome CDN for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "QR Code Decoder - Read QR Codes from Images Online",
        "description": "Instantly decode a QR code from an image. Upload or drag-and-drop a file to read the content of any QR code online, for free.",
        "url": "https://www.webtoolskit.org/p/qr-code-decoder.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-07-02",
        "dateModified": "2025-07-02",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "QR Code Decoder",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Decode QR Code from Image" },
            { "@type": "CopyAction", "name": "Copy Decoded Content" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a QR code decoder?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A QR code decoder, or QR code reader, is a tool that can scan and interpret the information stored within a QR code from an image file. Unlike a phone camera app, an online decoder allows you to upload an image (like a screenshot or a saved photo) to extract the data, such as a URL, text, or contact information."
          }
        },
        {
          "@type": "Question",
          "name": "How do you decode a QR code from a picture?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To decode a QR code from a picture, you can use an online decoder like this one. Simply click the upload area to select your image file, or drag and drop it into the designated zone. The tool will automatically scan the image, find the QR code, and display the decoded content."
          }
        },
        {
          "@type": "Question",
          "name": "Can you read a QR code from a screenshot?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, absolutely. You can take a screenshot of a QR code on your screen and upload the screenshot image to an online QR code decoder. The tool will process the image and extract the information just as it would from any other picture."
          }
        },
        {
          "@type": "Question",
          "name": "What information can a QR code hold?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A QR code can hold various types of information, including website URLs, plain text, Wi-Fi network credentials (SSID and password), contact information (vCard), calendar events, email addresses, phone numbers, and geolocation coordinates."
          }
        },
        {
          "@type": "Question",
          "name": "Is it safe to scan any QR code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "While QR codes themselves are safe, the content they link to might not be. Be cautious when scanning QR codes from untrusted sources, as they can lead to malicious websites (phishing) or prompt unwanted actions. An online decoder can add a layer of safety by showing you the destination URL or text before you visit or use it."
          }
        }
      ]
    }
    </script>


    <style>
        /* QR Code Decoder Widget - Simplified & Template Compatible */
        .qr-code-decoder-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .qr-code-decoder-widget-container * { box-sizing: border-box; }

        .qr-code-decoder-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .qr-code-decoder-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }
        
        .qr-code-decoder-upload-area {
            border: 3px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            cursor: pointer;
            transition: var(--transition-base);
            margin-bottom: var(--spacing-lg);
        }
        
        .qr-code-decoder-upload-area.active {
            border-color: var(--primary-color);
            background-color: var(--background-color-alt);
        }
        
        .qr-code-decoder-upload-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }
        
        .qr-code-decoder-upload-text {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .qr-code-decoder-upload-hint {
            color: var(--text-color-light);
            font-size: 0.9rem;
        }
        
        #qrCodeFileInput {
            display: none;
        }
        
        #qrImagePreviewContainer {
            text-align: center;
            margin-bottom: var(--spacing-lg);
        }
        
        #qrImagePreview {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 2px solid var(--border-color);
        }

        .qr-code-decoder-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .qr-code-decoder-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .qr-code-decoder-btn:hover { transform: translateY(-2px); }

        .qr-code-decoder-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .qr-code-decoder-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .qr-code-decoder-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .qr-code-decoder-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .qr-code-decoder-btn-success {
            background-color: #10b981;
            color: white;
        }

        .qr-code-decoder-btn-success:hover {
            background-color: #059669;
        }

        .qr-code-decoder-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .qr-code-decoder-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .qr-code-decoder-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            white-space: pre-wrap;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .qr-code-decoder-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .qr-code-decoder-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .qr-code-decoder-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .qr-code-decoder-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .qr-code-decoder-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .qr-code-decoder-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .qr-code-decoder-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .qr-code-decoder-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="qr-code-generator"] .qr-code-decoder-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="url-parser"] .qr-code-decoder-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="url-decode"] .qr-code-decoder-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }


        .qr-code-decoder-related-tool-item:hover .qr-code-decoder-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="qr-code-generator"]:hover .qr-code-decoder-related-tool-icon { background: linear-gradient(145deg, #9333ea, #8B5CF6); }
        a[href*="url-parser"]:hover .qr-code-decoder-related-tool-icon { background: linear-gradient(145deg, #f472b6, #EC4899); }
        a[href*="url-decode"]:hover .qr-code-decoder-related-tool-icon { background: linear-gradient(145deg, #f472b6, #EC4899); }
        
        .qr-code-decoder-related-tool-item { box-shadow: none; border: none; }
        .qr-code-decoder-related-tool-item:hover { box-shadow: none; border: none; }
        .qr-code-decoder-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .qr-code-decoder-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .qr-code-decoder-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .qr-code-decoder-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .qr-code-decoder-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .qr-code-decoder-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .qr-code-decoder-related-tool-item:hover .qr-code-decoder-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .qr-code-decoder-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .qr-code-decoder-widget-title { font-size: 1.875rem; }
            .qr-code-decoder-buttons { flex-direction: column; }
            .qr-code-decoder-btn { flex: none; }
            .qr-code-decoder-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .qr-code-decoder-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .qr-code-decoder-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .qr-code-decoder-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .qr-code-decoder-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .qr-code-decoder-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .qr-code-decoder-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .qr-code-decoder-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .qr-code-decoder-upload-area.active { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .qr-code-decoder-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .qr-code-decoder-output::selection { background-color: var(--primary-color); color: white; }
        @media (max-width: 600px) { .qr-code-decoder-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="qr-code-decoder-widget-container">
        <h1 class="qr-code-decoder-widget-title">QR Code Decoder</h1>
        <p class="qr-code-decoder-widget-description">
            Have a QR code in an image? Upload it here to instantly read its contents. Decode QR codes from screenshots, photos, and any image file.
        </p>
        
        <label for="qrCodeFileInput" class="qr-code-decoder-upload-area" id="qrCodeUploadArea">
            <div class="qr-code-decoder-upload-icon">
                <i class="fas fa-upload"></i>
            </div>
            <div class="qr-code-decoder-upload-text">
                Drag & Drop an image or Click to Upload
            </div>
            <div class="qr-code-decoder-upload-hint">
                Supports PNG, JPG, GIF, WebP, etc.
            </div>
        </label>
        <input type="file" id="qrCodeFileInput" accept="image/*">
        
        <div id="qrImagePreviewContainer" style="display: none;">
            <img id="qrImagePreview" src="" alt="QR Code Preview">
        </div>
        
        <canvas id="qrCanvas" style="display: none;"></canvas>

        <div class="qr-code-decoder-result">
            <h3 class="qr-code-decoder-result-title">Decoded Content:</h3>
            <div class="qr-code-decoder-output" id="qrCodeOutput">
                Upload an image to see the decoded content...
            </div>
        </div>
        
        <div class="qr-code-decoder-buttons" style="margin-top: 1.5rem;">
            <button class="qr-code-decoder-btn qr-code-decoder-btn-success" onclick="QrCodeDecoder.copy()">
                Copy Result
            </button>
            <button class="qr-code-decoder-btn qr-code-decoder-btn-secondary" onclick="QrCodeDecoder.clear()">
                Clear All
            </button>
        </div>

        <div class="qr-code-decoder-related-tools">
            <h3 class="qr-code-decoder-related-tools-title">Related Tools</h3>
            <div class="qr-code-decoder-related-tools-grid">
                <a href="/p/qr-code-generator.html" class="qr-code-decoder-related-tool-item" rel="noopener">
                    <div class="qr-code-decoder-related-tool-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="qr-code-decoder-related-tool-name">QR Code Generator</div>
                </a>
                <a href="/p/url-parser.html" class="qr-code-decoder-related-tool-item" rel="noopener">
                    <div class="qr-code-decoder-related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="qr-code-decoder-related-tool-name">URL Parser</div>
                </a>
                <a href="/p/url-decode.html" class="qr-code-decoder-related-tool-item" rel="noopener">
                    <div class="qr-code-decoder-related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="qr-code-decoder-related-tool-name">URL Decode</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Read QR Codes from any Image File</h2>
            <p>While your phone's camera is great for scanning QR codes in the real world, what do you do when the code is in an image on your computer? Our online <strong>QR Code Decoder</strong> solves this problem. This free tool allows you to upload any image file—whether it's a screenshot, a downloaded photo, or a document—and instantly extracts the information stored within the QR code. It's a simple, fast, and secure way to read QR codes without needing a physical camera.</p>
            
            <h3>How to Use the QR Code Decoder</h3>
            <ol>
                <li><strong>Upload Your Image:</strong> Drag and drop your image file onto the upload area, or click it to select a file from your device.</li>
                <li><strong>Automatic Decoding:</strong> The tool will immediately scan the image for a QR code.</li>
                <li><strong>View and Copy the Content:</strong> If a QR code is found, its content (like a URL or text) will be displayed in the result box, ready for you to copy.</li>
            </ol>
            
            <h3>Practical Use Cases</h3>
            <p>This tool is perfect for various situations, such as reading a QR code from a website you're browsing, extracting information from a code sent in an email, or accessing a Wi-Fi password from a photo of a router. By showing you the content before you access it, it also provides an extra layer of security, helping you avoid potentially malicious links.</p>
        
            <h3>Frequently Asked Questions About QR Code Decoder</h3>
            
            <h4>What is a QR code decoder?</h4>
            <p>A QR code decoder, or QR code reader, is a tool that can scan and interpret the information stored within a QR code from an image file. Unlike a phone camera app, an online decoder allows you to upload an image (like a screenshot or a saved photo) to extract the data, such as a URL, text, or contact information.</p>
            
            <h4>How do you decode a QR code from a picture?</h4>
            <p>To decode a QR code from a picture, you can use an online decoder like this one. Simply click the upload area to select your image file, or drag and drop it into the designated zone. The tool will automatically scan the image, find the QR code, and display the decoded content.</p>
            
            <h4>Can you read a QR code from a screenshot?</h4>
            <p>Yes, absolutely. You can take a screenshot of a QR code on your screen and upload the screenshot image to an online QR code decoder. The tool will process the image and extract the information just as it would from any other picture.</p>
            
            <h4>What information can a QR code hold?</h4>
            <p>A QR code can hold various types of information, including website URLs, plain text, Wi-Fi network credentials (SSID and password), contact information (vCard), calendar events, email addresses, phone numbers, and geolocation coordinates.</p>
            
            <h4>Is it safe to scan any QR code?</h4>
            <p>While QR codes themselves are safe, the content they link to might not be. Be cautious when scanning QR codes from untrusted sources, as they can lead to malicious websites (phishing) or prompt unwanted actions. An online decoder can add a layer of safety by showing you the destination URL or text before you visit or use it.</p>
        </div>

        <div class="qr-code-decoder-features">
            <h3 class="qr-code-decoder-features-title">Key Features:</h3>
            <ul class="qr-code-decoder-features-list">
                <li class="qr-code-decoder-features-item">Decode from image upload</li>
                <li class="qr-code-decoder-features-item">Drag-and-drop support</li>
                <li class="qr-code-decoder-features-item">Instant, automatic decoding</li>
                <li class="qr-code-decoder-features-item">Supports all QR code types</li>
                <li class="qr-code-decoder-features-item">Secure client-side processing</li>
                <li class="qr-code-decoder-features-item">One-click copy result</li>
                <li class="qr-code-decoder-features-item">Mobile-friendly design</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="qr-code-decoder-notification" id="qrCodeDecoderNotification">
        ✓ Copied to clipboard!
    </div>

    <!-- jsQR Library for Decoding (CDN) -->
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>

    <script>
        // QR Code Decoder
        (function() {
            'use strict';

            const elements = {
                uploadArea: () => document.getElementById('qrCodeUploadArea'),
                fileInput: () => document.getElementById('qrCodeFileInput'),
                output: () => document.getElementById('qrCodeOutput'),
                notification: () => document.getElementById('qrCodeDecoderNotification'),
                previewContainer: () => document.getElementById('qrImagePreviewContainer'),
                previewImage: () => document.getElementById('qrImagePreview'),
                canvas: () => document.getElementById('qrCanvas')
            };

            window.QrCodeDecoder = {
                init() {
                    const uploadArea = elements.uploadArea();
                    const fileInput = elements.fileInput();
                    
                    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                        uploadArea.addEventListener(eventName, this.preventDefaults, false);
                    });

                    ['dragenter', 'dragover'].forEach(eventName => {
                        uploadArea.addEventListener(eventName, () => uploadArea.classList.add('active'), false);
                    });

                    ['dragleave', 'drop'].forEach(eventName => {
                        uploadArea.addEventListener(eventName, () => uploadArea.classList.remove('active'), false);
                    });

                    uploadArea.addEventListener('drop', this.handleDrop, false);
                    fileInput.addEventListener('change', this.handleFileSelect, false);
                },

                preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                },

                handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;
                    if (files.length > 0) {
                        QrCodeDecoder.handleFile(files[0]);
                    }
                },
                
                handleFileSelect(e) {
                    if (e.target.files.length > 0) {
                        QrCodeDecoder.handleFile(e.target.files[0]);
                    }
                },

                handleFile(file) {
                    if (!file.type.startsWith('image/')) {
                        elements.output().textContent = 'Error: Please upload an image file.';
                        elements.output().style.color = '#dc2626';
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        elements.previewImage().src = e.target.result;
                        elements.previewContainer().style.display = 'block';
                        elements.output().textContent = 'Processing...';
                        elements.output().style.color = '';
                        
                        const image = new Image();
                        image.onload = () => {
                            const canvas = elements.canvas();
                            const ctx = canvas.getContext('2d');
                            canvas.width = image.width;
                            canvas.height = image.height;
                            ctx.drawImage(image, 0, 0, image.width, image.height);
                            
                            const imageData = ctx.getImageData(0, 0, image.width, image.height);
                            const code = jsQR(imageData.data, imageData.width, imageData.height);
                            
                            if (code) {
                                elements.output().textContent = code.data;
                            } else {
                                elements.output().textContent = 'No QR code found in the image.';
                                elements.output().style.color = '#dc2626';
                            }
                        };
                        image.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                },

                clear() {
                    elements.fileInput().value = '';
                    elements.output().textContent = 'Upload an image to see the decoded content...';
                    elements.output().style.color = '';
                    elements.previewContainer().style.display = 'none';
                    elements.previewImage().src = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Upload an image to see the decoded content...', 'Processing...', 'No QR code found in the image.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }
                
                QrCodeDecoder.init();
            });
        })();
    </script>
</body>
</html>