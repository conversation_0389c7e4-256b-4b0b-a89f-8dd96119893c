<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accurate Calorie Calculator - Daily Calorie Needs for Weight Loss</title>
    <meta name="description" content="Use our free and accurate Calorie Calculator to estimate your daily calorie needs for weight loss, maintenance, or gain based on your age, gender, and activity level.">
    <meta name="keywords" content="calorie calculator, daily calorie needs, calculate calories, weight loss calculator, calorie intake calculator, calorie deficit">
    <link rel="canonical" href="https://www.webtoolskit.org/p/calorie-calculator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Accurate Calorie Calculator - Daily Calorie Needs for Weight Loss",
        "description": "Use our free and accurate Calorie Calculator to estimate your daily calorie needs for weight loss, maintenance, or gain based on your age, gender, and activity level.",
        "url": "https://www.webtoolskit.org/p/calorie-calculator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-20",
        "dateModified": "2025-06-24",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Calorie Calculator",
            "applicationCategory": "HealthApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Calculates daily calorie needs based on the Mifflin-St Jeor equation",
                "Provides estimates for weight loss, maintenance, and weight gain",
                "Adjusts for various activity levels",
                "Personalized results based on age, gender, height, and weight"
            ]
        },
        "potentialAction": {
             "@type": "Action",
             "name": "Calculate Daily Calories"
        }
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I figure out my calorie intake to lose weight?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To lose weight, you need to consume fewer calories than your body burns. This is called a calorie deficit. A general rule is that a deficit of 500 calories per day leads to about 1 pound of weight loss per week. Use our Calorie Calculator to find your maintenance calories (TDEE), then select a 'Weight Loss' goal to see your recommended daily intake."
          }
        },
        {
          "@type": "Question",
          "name": "How do you calculate a calorie deficit?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A calorie deficit is calculated by subtracting your daily calorie intake from your Total Daily Energy Expenditure (TDEE). The formula is: TDEE - Daily Calorie Intake = Calorie Deficit. Our calculator first finds your TDEE and then shows you the calorie targets needed to achieve different levels of deficit for weight loss."
          }
        },
        {
          "@type": "Question",
          "name": "What is the most trusted calorie calculator?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The most trusted calorie calculators are those that use scientifically recognized formulas, like the Mifflin-St Jeor equation, which is considered one of the most accurate methods for estimating Basal Metabolic Rate (BMR). Our calculator uses this equation along with activity multipliers to provide a reliable estimate of your daily calorie needs."
          }
        },
        {
          "@type": "Question",
          "name": "How many calories should I eat a day by age to lose weight?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Calorie needs are not determined by age alone. They depend on a combination of factors including age, gender, height, weight, and activity level. For example, a very active 40-year-old may need more calories than a sedentary 25-year-old. The best way to get a personalized estimate is to enter all your details into a comprehensive calorie calculator like this one."
          }
        },
        {
          "@type": "Question",
          "name": "Can I trust a calorie calculator?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can trust a well-made calorie calculator to provide a strong scientific estimate and an excellent starting point for your diet. However, it's an estimation, not a medical prescription. Individual metabolisms can vary slightly. It's best to use the result as a guideline, monitor your progress, and adjust your intake as needed. Always consult a healthcare professional for personalized medical advice."
          }
        }
      ]
    }
    </script>


    <style>
        /* Calorie Calculator Widget - Simplified & Template Compatible */
        .calorie-calculator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .calorie-calculator-widget-container * { box-sizing: border-box; }

        .calorie-calculator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .calorie-calculator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }
        
        .calorie-calculator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .calorie-calculator-input-group {
            margin-bottom: 0;
        }

        .calorie-calculator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .calorie-calculator-input, .calorie-calculator-select {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .calorie-calculator-input:focus, .calorie-calculator-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }
        
        .calorie-calculator-height-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }

        .calorie-calculator-radio-group {
            display: flex;
            gap: var(--spacing-lg);
            margin-top: var(--spacing-sm);
        }

        .calorie-calculator-radio-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .calorie-calculator-radio {
            accent-color: var(--primary-color);
            cursor: pointer;
            width: 18px;
            height: 18px;
        }
        
        .calorie-calculator-radio-label {
            font-weight: 500;
            cursor: pointer;
        }

        .calorie-calculator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .calorie-calculator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .calorie-calculator-btn:hover { transform: translateY(-2px); }

        .calorie-calculator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .calorie-calculator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .calorie-calculator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .calorie-calculator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .calorie-calculator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .calorie-calculator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
            text-align: center;
        }

        .calorie-calculator-output {
            color: var(--text-color);
            line-height: 1.5;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
            text-align: center;
        }
        
        .calorie-calculator-result-section {
            background-color: var(--card-bg);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }
        
        .calorie-calculator-result-label {
            font-weight: 600;
            color: var(--text-color-light);
            font-size: 0.9rem;
        }
        
        .calorie-calculator-result-value {
            font-weight: 800;
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .calorie-calculator-disclaimer {
            font-size: 0.8rem;
            color: var(--text-color-light);
            margin-top: var(--spacing-md);
            text-align: center;
        }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }
        
        .calorie-calculator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .calorie-calculator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .calorie-calculator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .calorie-calculator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            margin-bottom: 0.3em;
        }

        .calorie-calculator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 4px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 600px) { 
            .calorie-calculator-features-list { 
                columns: 1 !important; 
                -webkit-columns: 1 !important; 
                -moz-columns: 1 !important; 
            } 
        }

        .calorie-calculator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="tdee-calculator"] .calorie-calculator-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="age-calculator"] .calorie-calculator-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="percentage-calculator"] .calorie-calculator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .calorie-calculator-related-tool-item:hover .calorie-calculator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .calorie-calculator-related-tool-item { box-shadow: none; border: none; }
        .calorie-calculator-related-tool-item:hover { box-shadow: none; border: none; }
        .calorie-calculator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .calorie-calculator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .calorie-calculator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .calorie-calculator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .calorie-calculator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .calorie-calculator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .calorie-calculator-related-tool-item:hover .calorie-calculator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .calorie-calculator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .calorie-calculator-widget-title { font-size: 1.875rem; }
            .calorie-calculator-grid { grid-template-columns: 1fr; }
            .calorie-calculator-buttons { flex-direction: column; }
            .calorie-calculator-btn { flex: none; }
            .calorie-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .calorie-calculator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .calorie-calculator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .calorie-calculator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .calorie-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .calorie-calculator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .calorie-calculator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .calorie-calculator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .calorie-calculator-input:focus,
        [data-theme="dark"] .calorie-calculator-select:focus { 
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); 
        }
        .calorie-calculator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
    </style>
</head>
<body>
    <div class="calorie-calculator-widget-container">
        <h1 class="calorie-calculator-widget-title">Calorie Calculator</h1>
        <p class="calorie-calculator-widget-description">
            Estimate your daily calorie needs to maintain, lose, or gain weight with our scientifically-backed calculator.
        </p>

        <div class="calorie-calculator-grid">
            <div class="calorie-calculator-input-group">
                <label for="ageInput" class="calorie-calculator-label">Age</label>
                <input id="ageInput" type="number" class="calorie-calculator-input" placeholder="e.g., 35">
            </div>
            <div class="calorie-calculator-input-group">
                <label class="calorie-calculator-label">Gender</label>
                <div class="calorie-calculator-radio-group">
                    <div class="calorie-calculator-radio-option">
                        <input type="radio" id="genderMale" name="gender" class="calorie-calculator-radio" value="male" checked>
                        <label for="genderMale" class="calorie-calculator-radio-label">Male</label>
                    </div>
                    <div class="calorie-calculator-radio-option">
                        <input type="radio" id="genderFemale" name="gender" class="calorie-calculator-radio" value="female">
                        <label for="genderFemale" class="calorie-calculator-radio-label">Female</label>
                    </div>
                </div>
            </div>
            <div class="calorie-calculator-input-group">
                <label for="weightInput" class="calorie-calculator-label">Weight (lbs)</label>
                <input id="weightInput" type="number" class="calorie-calculator-input" placeholder="e.g., 160">
            </div>
            <div class="calorie-calculator-input-group">
                <label class="calorie-calculator-label">Height</label>
                <div class="calorie-calculator-height-inputs">
                    <input id="heightFtInput" type="number" class="calorie-calculator-input" placeholder="Feet">
                    <input id="heightInInput" type="number" class="calorie-calculator-input" placeholder="Inches">
                </div>
            </div>
            <div class="calorie-calculator-input-group">
                <label for="activityLevelSelect" class="calorie-calculator-label">Activity Level</label>
                <select id="activityLevelSelect" class="calorie-calculator-select">
                    <option value="1.2">Sedentary (little or no exercise)</option>
                    <option value="1.375">Lightly Active (light exercise/sports 1-3 days/week)</option>
                    <option value="1.55" selected>Moderately Active (moderate exercise/sports 3-5 days/week)</option>
                    <option value="1.725">Very Active (hard exercise/sports 6-7 days a week)</option>
                    <option value="1.9">Extra Active (very hard exercise/sports & physical job)</option>
                </select>
            </div>
             <div class="calorie-calculator-input-group">
                <label for="goalSelect" class="calorie-calculator-label">Your Goal</label>
                <select id="goalSelect" class="calorie-calculator-select">
                    <option value="0">Maintain weight</option>
                    <option value="-250">Mild weight loss (0.5 lb/week)</option>
                    <option value="-500" selected>Weight loss (1 lb/week)</option>
                    <option value="-1000">Extreme weight loss (2 lbs/week)</option>
                    <option value="250">Mild weight gain (0.5 lb/week)</option>
                    <option value="500">Weight gain (1 lb/week)</option>
                </select>
            </div>
        </div>
        
        <div class="calorie-calculator-buttons">
            <button class="calorie-calculator-btn calorie-calculator-btn-primary" onclick="CalorieCalculator.calculate()">
                Calculate Calories
            </button>
            <button class="calorie-calculator-btn calorie-calculator-btn-secondary" onclick="CalorieCalculator.clear()">
                Clear All
            </button>
        </div>

        <div class="calorie-calculator-result">
            <h3 class="calorie-calculator-result-title">Your Daily Calorie Needs</h3>
            <div class="calorie-calculator-output" id="calorieCalculatorOutput">
                Please fill out the form to see your results.
            </div>
            <p class="calorie-calculator-disclaimer">
                These results are an estimate. Consult with a doctor or registered dietitian for personalized advice.
            </p>
        </div>

        <div class="calorie-calculator-related-tools">
            <h3 class="calorie-calculator-related-tools-title">Related Tools</h3>
            <div class="calorie-calculator-related-tools-grid">
                <a href="/p/tdee-calculator.html" class="calorie-calculator-related-tool-item" rel="noopener">
                    <div class="calorie-calculator-related-tool-icon">
                        <i class="fas fa-dumbbell"></i>
                    </div>
                    <div class="calorie-calculator-related-tool-name">TDEE Calculator</div>
                </a>
                <a href="/p/age-calculator.html" class="calorie-calculator-related-tool-item" rel="noopener">
                    <div class="calorie-calculator-related-tool-icon">
                        <i class="fas fa-birthday-cake"></i>
                    </div>
                    <div class="calorie-calculator-related-tool-name">Age Calculator</div>
                </a>
                <a href="/p/percentage-calculator.html" class="calorie-calculator-related-tool-item" rel="noopener">
                    <div class="calorie-calculator-related-tool-icon">
                        <i class="fas fa-percent"></i>
                    </div>
                    <div class="calorie-calculator-related-tool-name">Percentage Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Understand Your Daily Calorie Needs</h2>
            <p>Achieving your health and fitness goals—whether it's losing weight, building muscle, or maintaining your current physique—starts with understanding your energy balance. Our <strong>Calorie Calculator</strong> is a robust tool designed to give you a personalized estimate of your daily calorie needs. By analyzing your age, gender, height, weight, and activity level, it provides a scientific starting point for your nutritional plan. It uses the trusted Mifflin-St Jeor equation to calculate your Basal Metabolic Rate (BMR), then applies an activity multiplier to determine your Total Daily Energy Expenditure (TDEE).</p>
            <p>Once you know your maintenance calories, you can make informed decisions. To lose weight, you need to be in a calorie deficit (consuming fewer calories than your TDEE). To gain weight, you need a calorie surplus. This calculator simplifies the process by showing you the exact targets for various goals, from mild weight loss to muscle gain.</p>
            
            <h3>How to Use the Calorie Calculator</h3>
            <ol>
                <li><strong>Enter Your Stats:</strong> Fill in your age, gender, weight, and height.</li>
                <li><strong>Select Activity Level:</strong> Choose the option that best describes your weekly physical activity.</li>
                <li><strong>Set Your Goal:</strong> Select whether you want to lose, maintain, or gain weight, and at what rate.</li>
                <li><strong>Calculate:</strong> Click the button to get an instant breakdown of your estimated daily calorie needs.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Calorie Calculation</h3>
            
            <h4>How do I figure out my calorie intake to lose weight?</h4>
            <p>To lose weight, you need to consume fewer calories than your body burns. This is called a calorie deficit. A general rule is that a deficit of 500 calories per day leads to about 1 pound of weight loss per week. Use our Calorie Calculator to find your maintenance calories (TDEE), then select a 'Weight Loss' goal to see your recommended daily intake.</p>
            
            <h4>How do you calculate a calorie deficit?</h4>
            <p>A calorie deficit is calculated by subtracting your daily calorie intake from your Total Daily Energy Expenditure (TDEE). The formula is: TDEE - Daily Calorie Intake = Calorie Deficit. Our calculator first finds your TDEE and then shows you the calorie targets needed to achieve different levels of deficit for weight loss.</p>

            <h4>What is the most trusted calorie calculator?</h4>
            <p>The most trusted calorie calculators are those that use scientifically recognized formulas, like the Mifflin-St Jeor equation, which is considered one of the most accurate methods for estimating Basal Metabolic Rate (BMR). Our calculator uses this equation along with activity multipliers to provide a reliable estimate of your daily calorie needs.</p>
            
            <h4>How many calories should I eat a day by age to lose weight?</h4>
            <p>Calorie needs are not determined by age alone. They depend on a combination of factors including age, gender, height, weight, and activity level. For example, a very active 40-year-old may need more calories than a sedentary 25-year-old. The best way to get a personalized estimate is to enter all your details into a comprehensive calorie calculator like this one.</p>
            
            <h4>Can I trust a calorie calculator?</h4>
            <p>Yes, you can trust a well-made calorie calculator to provide a strong scientific estimate and an excellent starting point for your diet. However, it's an estimation, not a medical prescription. Individual metabolisms can vary slightly. It's best to use the result as a guideline, monitor your progress, and adjust your intake as needed. Always consult a healthcare professional for personalized medical advice.</p>
        </div>
        
        <div class="calorie-calculator-features">
            <h3 class="calorie-calculator-features-title">Key Features:</h3>
            <ul class="calorie-calculator-features-list">
                <li class="calorie-calculator-features-item">Based on Mifflin-St Jeor formula</li>
                <li class="calorie-calculator-features-item">Personalized for age and gender</li>
                <li class="calorie-calculator-features-item">Adjustable activity levels</li>
                <li class="calorie-calculator-features-item">Specific weight loss/gain goals</li>
                <li class="calorie-calculator-features-item">Clear results breakdown</li>
                <li class="calorie-calculator-features-item">Calculates BMR and TDEE</li>
                <li class="calorie-calculator-features-item">Mobile-responsive design</li>
                <li class="calorie-calculator-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                age: () => document.getElementById('ageInput'),
                gender: () => document.querySelector('input[name="gender"]:checked'),
                weight: () => document.getElementById('weightInput'),
                heightFt: () => document.getElementById('heightFtInput'),
                heightIn: () => document.getElementById('heightInInput'),
                activity: () => document.getElementById('activityLevelSelect'),
                goal: () => document.getElementById('goalSelect'),
                output: () => document.getElementById('calorieCalculatorOutput')
            };

            window.CalorieCalculator = {
                calculate() {
                    const outputEl = elements.output();
                    
                    const age = parseInt(elements.age().value, 10);
                    const gender = elements.gender().value;
                    const weightLbs = parseFloat(elements.weight().value);
                    const heightFt = parseInt(elements.heightFt().value, 10);
                    const heightIn = parseInt(elements.heightIn().value, 10);
                    const activityFactor = parseFloat(elements.activity().value);
                    const goalModifier = parseInt(elements.goal().value, 10);

                    if (isNaN(age) || age <= 0 || isNaN(weightLbs) || weightLbs <= 0 || isNaN(heightFt) || heightFt < 0 || isNaN(heightIn) || heightIn < 0) {
                        outputEl.innerHTML = '<span style="color: #dc2626;">Please enter valid, positive numbers for all fields.</span>';
                        return;
                    }

                    // Conversions
                    const weightKg = weightLbs * 0.453592;
                    const totalHeightIn = (heightFt * 12) + heightIn;
                    const heightCm = totalHeightIn * 2.54;

                    // Calculate BMR using Mifflin-St Jeor
                    let bmr;
                    if (gender === 'male') {
                        bmr = 10 * weightKg + 6.25 * heightCm - 5 * age + 5;
                    } else { // female
                        bmr = 10 * weightKg + 6.25 * heightCm - 5 * age - 161;
                    }
                    
                    // Calculate TDEE (Total Daily Energy Expenditure)
                    const tdee = bmr * activityFactor;
                    
                    // Calculate final goal calories
                    const goalCalories = tdee + goalModifier;

                    this.displayResults(tdee, goalCalories);
                },

                displayResults(maintenance, goal) {
                    const outputEl = elements.output();
                    outputEl.innerHTML = `
                        <div class="calorie-calculator-result-section">
                            <div class="calorie-calculator-result-label">To Maintain Your Weight:</div>
                            <div class="calorie-calculator-result-value">${Math.round(maintenance).toLocaleString()} Calories/day</div>
                        </div>
                        <div class="calorie-calculator-result-section">
                            <div class="calorie-calculator-result-label">For Your Goal:</div>
                            <div class="calorie-calculator-result-value">${Math.round(goal).toLocaleString()} Calories/day</div>
                        </div>
                    `;
                },

                clear() {
                    elements.age().value = '';
                    elements.weight().value = '';
                    elements.heightFt().value = '';
                    elements.heightIn().value = '';
                    document.getElementById('genderMale').checked = true;
                    elements.activity().value = '1.55';
                    elements.goal().value = '-500';
                    elements.output().innerHTML = 'Please fill out the form to see your results.';
                }
            };
        })();
    </script>
</body>
</html>