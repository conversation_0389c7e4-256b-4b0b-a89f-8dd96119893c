<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Temperature Converter - Convert <PERSON>, <PERSON><PERSON><PERSON>heit, <PERSON><PERSON></title>
    <meta name="description" content="Instantly convert between temperature scales like Celsius, Fahrenheit, and Kelvin. A free and easy-to-use online tool for science, cooking, and weather.">
    <meta name="keywords" content="temperature converter, celsius to fahrenheit, fahrenheit to celsius, convert kelvin, C to F, F to C">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Temperature Converter - Celsius, Fahrenheit, and <PERSON><PERSON>",
        "description": "Convert between Celsius (°C), Fahrenheit (°F), and <PERSON><PERSON> (K) temperature scales instantly. A free, accurate online tool for various applications.",
        "url": "https://www.webtoolskit.org/p/temperature-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-25",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Temperature Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Temperature Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the cheat for converting Celsius to Fahrenheit?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A quick and easy cheat for converting Celsius to Fahrenheit is to double the Celsius temperature and add 30. For example, 15°C becomes (15 × 2) + 30 = 60°F. While the exact answer is 59°F, this mental trick is very close and useful for quick estimations."
          }
        },
        {
          "@type": "Question",
          "name": "What is the correct equation to convert Fahrenheit to Celsius?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The correct and precise formula to convert a temperature from Fahrenheit (°F) to Celsius (°C) is: C = (F - 32) × 5/9. You first subtract 32 from the Fahrenheit temperature, and then multiply the result by 5/9."
          }
        },
        {
          "@type": "Question",
          "name": "How do you mentally convert F to C?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To mentally convert Fahrenheit to Celsius, you can use a simple approximation: subtract 30 from the Fahrenheit temperature and then divide the result by 2. For example, for 70°F, you would do (70 - 30) / 2 = 20°C. The exact answer is 21.1°C, so this method provides a reasonably close estimate."
          }
        },
        {
          "@type": "Question",
          "name": "What is the formula of C to F?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The official formula to convert a temperature from Celsius (°C) to Fahrenheit (°F) is: F = (C × 9/5) + 32. First, multiply the Celsius temperature by 9/5 (or 1.8), and then add 32 to the result."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert F to C without a calculator?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The easiest way to convert Fahrenheit to Celsius without a calculator is using a mental shortcut. Subtract 30 from the Fahrenheit value, then divide by two. For instance, to convert 80°F, you would calculate (80 - 30) = 50, and then 50 / 2 = 25°C. This gives a fast and close approximation."
          }
        }
      ]
    }
    </script>

    <style>
        /* Temperature Converter Widget - Simplified & Template Compatible */
        .temperature-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .temperature-converter-widget-container * { box-sizing: border-box; }

        .temperature-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .temperature-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .temperature-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .temperature-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .temperature-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .temperature-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .temperature-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .temperature-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .temperature-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .temperature-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .temperature-converter-btn:hover { transform: translateY(-2px); }

        .temperature-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .temperature-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .temperature-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .temperature-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .temperature-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .temperature-converter-btn-success:hover {
            background-color: #059669;
        }

        .temperature-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .temperature-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .temperature-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .temperature-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .temperature-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .temperature-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .temperature-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .temperature-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .temperature-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .temperature-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .temperature-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="length-converter"] .temperature-converter-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="weight-converter"] .temperature-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="volume-converter"] .temperature-converter-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }

        .temperature-converter-related-tool-item:hover .temperature-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="length-converter"]:hover .temperature-converter-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="weight-converter"]:hover .temperature-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        a[href*="volume-converter"]:hover .temperature-converter-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        
        .temperature-converter-related-tool-item { box-shadow: none; border: none; }
        .temperature-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .temperature-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .temperature-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .temperature-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .temperature-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .temperature-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .temperature-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .temperature-converter-related-tool-item:hover .temperature-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .temperature-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .temperature-converter-widget-title { font-size: 1.875rem; }
            .temperature-converter-buttons { flex-direction: column; }
            .temperature-converter-btn { flex: none; }
            .temperature-converter-input-group { grid-template-columns: 1fr; }
            .temperature-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .temperature-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .temperature-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .temperature-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .temperature-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .temperature-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .temperature-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .temperature-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .temperature-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .temperature-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .temperature-converter-output::selection { background-color: var(--primary-color); color: white; }
        .temperature-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .temperature-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="temperature-converter-widget-container">
        <h1 class="temperature-converter-widget-title">Temperature Converter</h1>
        <p class="temperature-converter-widget-description">
            Quickly and accurately convert between Celsius, Fahrenheit, and Kelvin. A must-have tool for weather, cooking, and scientific work.
        </p>
        
        <div class="temperature-converter-input-group">
            <label for="temperatureFromInput" class="temperature-converter-label">From:</label>
            <input 
                type="number" 
                id="temperatureFromInput" 
                class="temperature-converter-input"
                placeholder="Enter temperature value..."
                step="any"
            />
            <select id="temperatureFromUnit" class="temperature-converter-select">
                <option value="C" selected>Celsius (°C)</option>
                <option value="F">Fahrenheit (°F)</option>
                <option value="K">Kelvin (K)</option>
            </select>
        </div>

        <div class="temperature-converter-input-group">
            <label for="temperatureToInput" class="temperature-converter-label">To:</label>
            <input 
                type="number" 
                id="temperatureToInput" 
                class="temperature-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="temperatureToUnit" class="temperature-converter-select">
                <option value="C">Celsius (°C)</option>
                <option value="F" selected>Fahrenheit (°F)</option>
                <option value="K">Kelvin (K)</option>
            </select>
        </div>

        <div class="temperature-converter-buttons">
            <button class="temperature-converter-btn temperature-converter-btn-primary" onclick="TemperatureConverter.convert()">
                Convert Temperature
            </button>
            <button class="temperature-converter-btn temperature-converter-btn-secondary" onclick="TemperatureConverter.clear()">
                Clear All
            </button>
            <button class="temperature-converter-btn temperature-converter-btn-success" onclick="TemperatureConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="temperature-converter-result">
            <h3 class="temperature-converter-result-title">Conversion Result:</h3>
            <div class="temperature-converter-output" id="temperatureConverterOutput">
                Your converted temperature will appear here...
            </div>
        </div>

        <div class="temperature-converter-related-tools">
            <h3 class="temperature-converter-related-tools-title">Related Tools</h3>
            <div class="temperature-converter-related-tools-grid">
                <a href="/p/length-converter.html" class="temperature-converter-related-tool-item" rel="noopener">
                    <div class="temperature-converter-related-tool-icon">
                        <i class="fas fa-ruler"></i>
                    </div>
                    <div class="temperature-converter-related-tool-name">Length Converter</div>
                </a>

                <a href="/p/weight-converter.html" class="temperature-converter-related-tool-item" rel="noopener">
                    <div class="temperature-converter-related-tool-icon">
                        <i class="fas fa-weight"></i>
                    </div>
                    <div class="temperature-converter-related-tool-name">Weight Converter</div>
                </a>

                <a href="/p/volume-converter.html" class="temperature-converter-related-tool-item" rel="noopener">
                    <div class="temperature-converter-related-tool-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="temperature-converter-related-tool-name">Volume Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Effortless Celsius, Fahrenheit, and Kelvin Conversions</h2>
            <p>Navigating between different temperature scales is a common need, whether you're following a recipe, checking the weather abroad, or working on a science project. Our <strong>Temperature Converter</strong> is a simple yet powerful tool that eliminates guesswork and provides precise conversions between Celsius (°C), Fahrenheit (°F), and Kelvin (K). With a clean interface and instant results, it's the only temperature converter you'll ever need.</p>
            <p>Unlike other unit conversions that use a simple multiplication factor, temperature scales like Celsius and Fahrenheit have different zero points, making the formulas more complex. This tool handles all the math for you, ensuring you get accurate results every time without having to remember formulas like `(F - 32) * 5/9`.</p>

            <h3>How to Use the Temperature Converter</h3>
            <ol>
                <li><strong>Enter Temperature:</strong> Type the numerical temperature value you wish to convert into the "From" input box.</li>
                <li><strong>Select Scale:</strong> Choose the starting scale (e.g., Fahrenheit) and the target scale (e.g., Celsius) from the dropdown menus.</li>
                <li><strong>Click Convert:</strong> Press the "Convert Temperature" button to see the result instantly.</li>
                <li><strong>Copy or Clear:</strong> Use the "Copy Result" button to save the value or "Clear All" to start over with a new conversion.</li>
            </ol>

            <h3>Frequently Asked Questions About Temperature Conversion</h3>

            <h4>What is the cheat for converting Celsius to Fahrenheit?</h4>
            <p>A quick and easy cheat for converting Celsius to Fahrenheit is to double the Celsius temperature and add 30. For example, 15°C becomes (15 × 2) + 30 = 60°F. While the exact answer is 59°F, this mental trick is very close and useful for quick estimations.</p>

            <h4>What is the correct equation to convert Fahrenheit to Celsius?</h4>
            <p>The correct and precise formula to convert a temperature from Fahrenheit (°F) to Celsius (°C) is: C = (F - 32) × 5/9. You first subtract 32 from the Fahrenheit temperature, and then multiply the result by 5/9.</p>

            <h4>How do you mentally convert F to C?</h4>
            <p>To mentally convert Fahrenheit to Celsius, you can use a simple approximation: subtract 30 from the Fahrenheit temperature and then divide the result by 2. For example, for 70°F, you would do (70 - 30) / 2 = 20°C. The exact answer is 21.1°C, so this method provides a reasonably close estimate.</p>

            <h4>What is the formula of C to F?</h4>
            <p>The official formula to convert a temperature from Celsius (°C) to Fahrenheit (°F) is: F = (C × 9/5) + 32. First, multiply the Celsius temperature by 9/5 (or 1.8), and then add 32 to the result.</p>

            <h4>How to convert F to C without a calculator?</h4>
            <p>The easiest way to convert Fahrenheit to Celsius without a calculator is using a mental shortcut. Subtract 30 from the Fahrenheit value, then divide by two. For instance, to convert 80°F, you would calculate (80 - 30) = 50, and then 50 / 2 = 25°C. This gives a fast and close approximation.</p>
        </div>

        <div class="temperature-converter-features">
            <h3 class="temperature-converter-features-title">Key Features:</h3>
            <ul class="temperature-converter-features-list">
                <li class="temperature-converter-features-item" style="margin-bottom: 0.3em;">Converts C, F, and K scales</li>
                <li class="temperature-converter-features-item" style="margin-bottom: 0.3em;">Handles negative temperatures</li>
                <li class="temperature-converter-features-item" style="margin-bottom: 0.3em;">Accurate formula-based logic</li>
                <li class="temperature-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="temperature-converter-features-item" style="margin-bottom: 0.3em;">Simple, user-friendly design</li>
                <li class="temperature-converter-features-item" style="margin-bottom: 0.3em;">Fully responsive on all devices</li>
                <li class="temperature-converter-features-item">100% private and secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="temperature-converter-notification" id="temperatureConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Temperature Converter
        (function() {
            'use strict';

            const elements = {
                fromInput: () => document.getElementById('temperatureFromInput'),
                toInput: () => document.getElementById('temperatureToInput'),
                fromUnit: () => document.getElementById('temperatureFromUnit'),
                toUnit: () => document.getElementById('temperatureToUnit'),
                output: () => document.getElementById('temperatureConverterOutput'),
                notification: () => document.getElementById('temperatureConverterNotification')
            };

            window.TemperatureConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit().value;
                    const toUnit = elements.toUnit().value;
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Step 1: Convert input value to Celsius as a base unit
                    let valueInCelsius;
                    switch (fromUnit) {
                        case 'F':
                            valueInCelsius = (value - 32) * 5 / 9;
                            break;
                        case 'K':
                            valueInCelsius = value - 273.15;
                            break;
                        case 'C':
                        default:
                            valueInCelsius = value;
                            break;
                    }

                    // Step 2: Convert from Celsius to the target unit
                    let convertedValue;
                    switch (toUnit) {
                        case 'F':
                            convertedValue = (valueInCelsius * 9 / 5) + 32;
                            break;
                        case 'K':
                            convertedValue = valueInCelsius + 273.15;
                            break;
                        case 'C':
                        default:
                            convertedValue = valueInCelsius;
                            break;
                    }
                    
                    const formattedResult = this.formatResult(convertedValue);
                    toInput.value = formattedResult;
                    output.textContent = `${value}°${this.getUnitName(fromUnit)} = ${formattedResult}°${this.getUnitName(toUnit)}`;
                },

                formatResult(value) {
                    // Show more precision for temperature
                    return parseFloat(value.toFixed(4)).toString();
                },

                getUnitName(unit) {
                    const unitNames = {
                        'C': 'Celsius',
                        'F': 'Fahrenheit',
                        'K': 'Kelvin'
                    };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted temperature will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        TemperatureConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>