<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Number to Roman Numerals Converter - Convert to Roman Numerals</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Number to Roman Numerals Converter",
        "description": "Easily convert any number into its correct Roman numeral representation. Ideal for students, historians, and tattoo designs. Supports numbers up to 3,999,999.",
        "url": "https://www.webtoolskit.org/p/number-to-roman-numerals.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-03",
        "dateModified": "2025-06-03",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Number to Roman Numerals Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Number to Roman Numerals" },
            { "@type": "CopyAction", "name": "Copy Roman Numeral Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert number to Roman Numerals?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The standard method is to work from left to right (largest values to smallest). For each Roman numeral value (like 1000 for M, 900 for CM, etc.), subtract it from your number as many times as possible and append the corresponding symbol. For example, to convert 1994: 1. Subtract 1000 (M), leaving 994. Result: 'M'. 2. Subtract 900 (CM), leaving 94. Result: 'MCM'. 3. Subtract 90 (XC), leaving 4. Result: 'MCMXC'. 4. Add 4 (IV). Final Result: 'MCMXCIV'."
          }
        },
        {
          "@type": "Question",
          "name": "What does MMXX mean in numbers?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To read a Roman numeral, you add the values of the symbols. MMXX breaks down into M + M + X + X. Since M = 1000 and X = 10, the calculation is 1000 + 1000 + 10 + 10 = 2020. Therefore, MMXX represents the number 2020."
          }
        },
        {
          "@type": "Question",
          "name": "Is 4 written as IV or IIII in Roman numerals?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The standard and correct way to write 4 is IV, using subtractive notation (5 minus 1). The form 'IIII' is an older, additive notation. While it is considered incorrect in modern usage, it is famously seen on clocks and watches (often called the 'watchmaker's four') for aesthetic balance on the clock face."
          }
        },
        {
          "@type": "Question",
          "name": "What is 2 million in Roman numerals?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To represent numbers in the thousands and millions, a bar (vinculum) is placed over a Roman numeral, which multiplies its value by 1,000. Since 2,000 is MM, the number 2,000,000 is written as MM with a bar over both letters: M̅M̅."
          }
        },
        {
          "@type": "Question",
          "name": "What is 999999 in Roman numerals?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using the vinculum (bar) rule, we break the number into two parts: 999,000 and 999. The Roman numeral for 999 is CMXCIX. Therefore, 999,000 is CMXCIX with a bar over it, and 999 is CMXCIX without a bar. The full representation is C̅M̅X̅C̅I̅X̅CMXCIX."
          }
        }
      ]
    }
    </script>

    <style>
        /* Number to Roman Converter Widget - Simplified & Template Compatible */
        .number-to-roman-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .number-to-roman-converter-widget-container * { box-sizing: border-box; }

        .number-to-roman-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .number-to-roman-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .number-to-roman-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .number-to-roman-converter-input-group {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .number-to-roman-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .number-to-roman-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .number-to-roman-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .number-to-roman-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .number-to-roman-converter-btn:hover { transform: translateY(-2px); }

        .number-to-roman-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .number-to-roman-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .number-to-roman-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .number-to-roman-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .number-to-roman-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .number-to-roman-converter-btn-success:hover {
            background-color: #059669;
        }

        .number-to-roman-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .number-to-roman-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .number-to-roman-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'Times New Roman', Times, serif;
            font-size: 1.5rem;
            font-weight: bold;
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
            text-align: center;
        }
        
        .number-to-roman-converter-output .overline {
            text-decoration: overline;
        }

        .number-to-roman-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .number-to-roman-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .number-to-roman-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .number-to-roman-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .number-to-roman-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .number-to-roman-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .number-to-roman-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .number-to-roman-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="roman-numerals-to-number"] .number-to-roman-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="number-to-word-converter"] .number-to-roman-converter-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="word-to-number-converter"] .number-to-roman-converter-related-tool-icon { background: linear-gradient(145deg, #F97316, #EA580C); }
        

        .number-to-roman-converter-related-tool-item:hover .number-to-roman-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="roman-numerals-to-number"]:hover .number-to-roman-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        a[href*="number-to-word-converter"]:hover .number-to-roman-converter-related-tool-icon { background: linear-gradient(145deg, #2dd4bf, #14b8a6); }
        a[href*="word-to-number-converter"]:hover .number-to-roman-converter-related-tool-icon { background: linear-gradient(145deg, #fb923c, #f97316); }
        
        .number-to-roman-converter-related-tool-item { box-shadow: none; border: none; }
        .number-to-roman-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .number-to-roman-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .number-to-roman-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .number-to-roman-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .number-to-roman-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .number-to-roman-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .number-to-roman-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .number-to-roman-converter-related-tool-item:hover .number-to-roman-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .number-to-roman-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .number-to-roman-converter-widget-title { font-size: 1.875rem; }
            .number-to-roman-converter-buttons { flex-direction: column; }
            .number-to-roman-converter-btn { flex: none; }
            .number-to-roman-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .number-to-roman-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .number-to-roman-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .number-to-roman-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .number-to-roman-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .number-to-roman-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .number-to-roman-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .number-to-roman-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .number-to-roman-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .number-to-roman-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .number-to-roman-converter-output::selection { background-color: var(--primary-color); color: white; }
        .number-to-roman-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .number-to-roman-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="number-to-roman-converter-widget-container">
        <h1 class="number-to-roman-converter-widget-title">Number to Roman Numerals Converter</h1>
        <p class="number-to-roman-converter-widget-description">
            Instantly convert any regular number into its correct Roman numeral equivalent. Perfect for dates, outlines, and historical curiosity.
        </p>
        
        <div class="number-to-roman-converter-input-group">
            <label for="numberInput" class="number-to-roman-converter-label">Enter a Number:</label>
            <input 
                type="number" 
                id="numberInput" 
                class="number-to-roman-converter-input"
                placeholder="e.g., 2024"
                min="1"
            />
        </div>

        <div class="number-to-roman-converter-buttons">
            <button class="number-to-roman-converter-btn number-to-roman-converter-btn-primary" onclick="NumberToRomanConverter.convert()">
                Convert to Roman
            </button>
            <button class="number-to-roman-converter-btn number-to-roman-converter-btn-secondary" onclick="NumberToRomanConverter.clear()">
                Clear All
            </button>
            <button class="number-to-roman-converter-btn number-to-roman-converter-btn-success" onclick="NumberToRomanConverter.copy()">
                Copy Roman Numeral
            </button>
        </div>

        <div class="number-to-roman-converter-result">
            <h3 class="number-to-roman-converter-result-title">Result in Roman Numerals:</h3>
            <div class="number-to-roman-converter-output" id="romanNumeralOutput">
                Your Roman numeral will appear here...
            </div>
        </div>

        <div class="number-to-roman-converter-related-tools">
            <h3 class="number-to-roman-converter-related-tools-title">Related Tools</h3>
            <div class="number-to-roman-converter-related-tools-grid">
                <a href="/p/roman-numerals-to-number.html" class="number-to-roman-converter-related-tool-item" rel="noopener">
                    <div class="number-to-roman-converter-related-tool-icon">
                        <i class="fas fa-list-ol"></i>
                    </div>
                    <div class="number-to-roman-converter-related-tool-name">Roman to Number Converter</div>
                </a>
                <a href="/p/number-to-word-converter.html" class="number-to-roman-converter-related-tool-item" rel="noopener">
                    <div class="number-to-roman-converter-related-tool-icon">
                        <i class="fas fa-spell-check"></i>
                    </div>
                    <div class="number-to-roman-converter-related-tool-name">Number to Word Converter</div>
                </a>
                <a href="/p/word-to-number-converter.html" class="number-to-roman-converter-related-tool-item" rel="noopener">
                    <div class="number-to-roman-converter-related-tool-icon">
                        <i class="fas fa-keyboard"></i>
                    </div>
                    <div class="number-to-roman-converter-related-tool-name">Word to Number Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>From Digits to Roman Symbols: An Easy Converter</h2>
            <p>The Roman numeral system, with its unique combination of letters, has fascinated people for centuries. While not used for daily arithmetic, it remains prominent in art, architecture, and formal contexts like copyright dates and the names of monarchs. Our free <strong>Number to Roman Numerals Converter</strong> is a simple tool that accurately translates any modern number into its classical Roman equivalent. Whether you're a student learning history, a designer looking for an elegant way to display a year, or just curious, this tool makes the conversion process effortless.</p>
            <p>This converter handles standard numbers and even larger values using the correct notation, ensuring an authentic and precise result every time. Simply enter your number and watch it transform into the ancient system of I, V, X, L, C, D, and M.</p>

            <h3>How to Use the Roman Numeral Converter</h3>
            <ol>
                <li><strong>Enter a Number:</strong> Type any positive integer into the input field.</li>
                <li><strong>Convert:</strong> Click the "Convert to Roman" button.</li>
                <li><strong>Get the Result:</strong> The tool will instantly display the corresponding Roman numeral in the result box below.</li>
                <li><strong>Copy the Numeral:</strong> Use the "Copy Roman Numeral" button to copy the result for your document, design, or project.</li>
            </ol>

            <h3>Frequently Asked Questions About Roman Numerals</h3>
            
            <h4>How to convert number to Roman Numerals?</h4>
            <p>The standard method is to work from left to right (largest values to smallest). For each Roman numeral value (like 1000 for M, 900 for CM, etc.), subtract it from your number as many times as possible and append the corresponding symbol. For example, to convert 1994: 1. Subtract 1000 (M), leaving 994. Result: 'M'. 2. Subtract 900 (CM), leaving 94. Result: 'MCM'. 3. Subtract 90 (XC), leaving 4. Result: 'MCMXC'. 4. Add 4 (IV). Final Result: 'MCMXCIV'.</p>

            <h4>What does MMXX mean in numbers?</h4>
            <p>To read a Roman numeral, you add the values of the symbols. MMXX breaks down into M + M + X + X. Since M = 1000 and X = 10, the calculation is 1000 + 1000 + 10 + 10 = 2020. Therefore, MMXX represents the number 2020.</p>

            <h4>Is 4 written as IV or IIII in Roman numerals?</h4>
            <p>The standard and correct way to write 4 is IV, using subtractive notation (5 minus 1). The form 'IIII' is an older, additive notation. While it is considered incorrect in modern usage, it is famously seen on clocks and watches (often called the 'watchmaker's four') for aesthetic balance on the clock face.</p>

            <h4>What is 2 million in Roman numerals?</h4>
            <p>To represent numbers in the thousands and millions, a bar (vinculum) is placed over a Roman numeral, which multiplies its value by 1,000. Since 2,000 is MM, the number 2,000,000 is written as MM with a bar over both letters: M̅M̅.</p>

            <h4>What is 999999 in Roman numerals?</h4>
            <p>Using the vinculum (bar) rule, we break the number into two parts: 999,000 and 999. The Roman numeral for 999 is CMXCIX. Therefore, 999,000 is CMXCIX with a bar over it, and 999 is CMXCIX without a bar. The full representation is C̅M̅X̅C̅I̅X̅CMXCIX.</p>
        </div>

        <div class="number-to-roman-converter-features">
            <h3 class="number-to-roman-converter-features-title">Key Features:</h3>
            <ul class="number-to-roman-converter-features-list">
                <li class="number-to-roman-converter-features-item" style="margin-bottom: 0.3em;">Handles numbers 1 to 3,999,999</li>
                <li class="number-to-roman-converter-features-item" style="margin-bottom: 0.3em;">Uses standard subtractive notation</li>
                <li class="number-to-roman-converter-features-item" style="margin-bottom: 0.3em;">Supports vinculum for large numbers</li>
                <li class="number-to-roman-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="number-to-roman-converter-features-item" style="margin-bottom: 0.3em;">Great for dates and outlines</li>
                <li class="number-to-roman-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side conversion</li>
                <li class="number-to-roman-converter-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="number-to-roman-converter-notification" id="numberToRomanConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Number to Roman Numerals Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('numberInput'),
                output: () => document.getElementById('romanNumeralOutput'),
                notification: () => document.getElementById('numberToRomanConverterNotification')
            };
            
            const romanMap = [
                { value: 1000, symbol: 'M' }, { value: 900, symbol: 'CM' },
                { value: 500, symbol: 'D' }, { value: 400, symbol: 'CD' },
                { value: 100, symbol: 'C' }, { value: 90, symbol: 'XC' },
                { value: 50, symbol: 'L' }, { value: 40, symbol: 'XL' },
                { value: 10, symbol: 'X' }, { value: 9, symbol: 'IX' },
                { value: 5, symbol: 'V' }, { value: 4, symbol: 'IV' },
                { value: 1, symbol: 'I' }
            ];

            window.NumberToRomanConverter = {
                convert() {
                    const outputEl = elements.output();
                    const inputEl = elements.input();
                    const num = parseInt(inputEl.value, 10);

                    outputEl.innerHTML = ''; // Clear previous content

                    if (isNaN(num) || inputEl.value.trim() === '') {
                        outputEl.textContent = 'Please enter a valid number.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }
                    if (num < 1 || num > 3999999) {
                        outputEl.textContent = 'Please enter a number between 1 and 3,999,999.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }

                    outputEl.style.color = '';
                    
                    const thousandsPart = Math.floor(num / 1000);
                    const remainderPart = num % 1000;
                    
                    let result = '';

                    if (thousandsPart > 0) {
                        const thousandsRoman = this.toStandardRoman(thousandsPart);
                        const overlineSpan = document.createElement('span');
                        overlineSpan.className = 'overline';
                        overlineSpan.textContent = thousandsRoman;
                        result += overlineSpan.outerHTML;
                    }

                    if (remainderPart > 0) {
                        result += this.toStandardRoman(remainderPart);
                    }
                    
                    outputEl.innerHTML = result;
                },
                
                toStandardRoman(n) {
                    let roman = '';
                    for (const { value, symbol } of romanMap) {
                        while (n >= value) {
                            roman += symbol;
                            n -= value;
                        }
                    }
                    return roman;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your Roman numeral will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (!text || text.includes('...')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const inputEl = elements.input();
                inputEl.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        NumberToRomanConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>