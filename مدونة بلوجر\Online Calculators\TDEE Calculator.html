<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TDEE Calculator - Find Your Total Daily Energy Expenditure</title>
    <meta name="description" content="Use our free TDEE calculator to accurately estimate your daily maintenance calories. Find your BMR and TDEE to set effective goals for weight loss or muscle gain.">
    <meta name="keywords" content="tdee calculator, total daily energy expenditure, maintenance calories, bmr calculator, calculate tdee, weight loss calories">
    <link rel="canonical" href="https://www.webtoolskit.org/p/tdee-calculator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "TDEE Calculator - Find Your Total Daily Energy Expenditure",
        "description": "Use our free TDEE calculator to accurately estimate your daily maintenance calories. Find your BMR and TDEE to set effective goals for weight loss or muscle gain.",
        "url": "https://www.webtoolskit.org/p/tdee-calculator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-20",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "TDEE Calculator",
            "applicationCategory": "HealthApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Calculates Total Daily Energy Expenditure (TDEE)",
                "Calculates Basal Metabolic Rate (BMR)",
                "Based on the accurate Mifflin-St Jeor formula",
                "Personalized results for weight management"
            ]
        },
        "potentialAction": {
             "@type": "Action",
             "name": "Calculate TDEE"
        }
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I calculate my actual TDEE?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A TDEE calculator provides a highly accurate estimate based on scientific formulas. To find your 'actual' or true TDEE, you would need to meticulously track your calorie intake and weight for 2-4 weeks. If your weight remains stable, your average daily calorie intake is your actual TDEE. This calculator gives you the best possible starting point without this manual effort."
          }
        },
        {
          "@type": "Question",
          "name": "Which TDEE calculator is most accurate?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The most accurate TDEE calculators use the Mifflin-St Jeor equation to determine your Basal Metabolic Rate (BMR), as it's considered the most reliable formula. Accuracy is then improved by correctly selecting your activity level. Our calculator uses this preferred method for the most precise estimation."
          }
        },
        {
          "@type": "Question",
          "name": "Can you trust TDEE calculators?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can trust a TDEE calculator as an excellent scientific starting point. It provides a very close estimate of your daily calorie needs. However, since every individual's metabolism is slightly different, it's best to use the result as a guideline, monitor your weight, and adjust your intake by 100-200 calories if needed."
          }
        },
        {
          "@type": "Question",
          "name": "Should I eat less than my TDEE to lose weight?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes. Your TDEE is your 'maintenance' calorie level—the amount you need to eat to stay the same weight. To lose weight, you must create a calorie deficit by consistently eating fewer calories than your TDEE. A deficit of 300-500 calories below your TDEE is a common and sustainable goal for weight loss."
          }
        },
        {
          "@type": "Question",
          "name": "What is the most accurate calorie calculator method?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The most accurate method for estimating calorie needs in a calculator is the Mifflin-St Jeor equation for calculating Basal Metabolic Rate (BMR), which is the calories your body burns at rest. This BMR is then multiplied by an activity factor to find your Total Daily Energy Expenditure (TDEE). This multi-step process is more accurate than simpler formulas."
          }
        }
      ]
    }
    </script>


    <style>
        /* TDEE Calculator Widget - Simplified & Template Compatible */
        .tdee-calculator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .tdee-calculator-widget-container * { box-sizing: border-box; }

        .tdee-calculator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .tdee-calculator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }
        
        .tdee-calculator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .tdee-calculator-input-group {
            margin-bottom: 0;
        }

        .tdee-calculator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .tdee-calculator-input, .tdee-calculator-select {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .tdee-calculator-input:focus, .tdee-calculator-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }
        
        .tdee-calculator-height-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }

        .tdee-calculator-radio-group {
            display: flex;
            gap: var(--spacing-lg);
            margin-top: var(--spacing-sm);
        }

        .tdee-calculator-radio-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .tdee-calculator-radio {
            accent-color: var(--primary-color);
            cursor: pointer;
            width: 18px;
            height: 18px;
        }
        
        .tdee-calculator-radio-label {
            font-weight: 500;
            cursor: pointer;
        }

        .tdee-calculator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .tdee-calculator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .tdee-calculator-btn:hover { transform: translateY(-2px); }

        .tdee-calculator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .tdee-calculator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .tdee-calculator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .tdee-calculator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .tdee-calculator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .tdee-calculator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
            text-align: center;
        }

        .tdee-calculator-output {
            color: var(--text-color);
            line-height: 1.5;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            text-align: center;
        }
        
        .tdee-calculator-result-section {
            background-color: var(--card-bg);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }
        
        .tdee-calculator-result-label {
            font-weight: 600;
            color: var(--text-color-light);
            font-size: 0.9rem;
        }
        
        .tdee-calculator-result-value {
            font-weight: 800;
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .tdee-calculator-disclaimer {
            font-size: 0.8rem;
            color: var(--text-color-light);
            margin-top: var(--spacing-md);
            text-align: center;
        }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }
        
        .tdee-calculator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .tdee-calculator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .tdee-calculator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .tdee-calculator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            margin-bottom: 0.3em;
        }

        .tdee-calculator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 4px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 600px) { 
            .tdee-calculator-features-list,
            .tdee-calculator-output { 
                columns: 1 !important; 
                -webkit-columns: 1 !important; 
                -moz-columns: 1 !important;
                grid-template-columns: 1fr;
            } 
        }

        .tdee-calculator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="calorie-calculator"] .tdee-calculator-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="age-calculator"] .tdee-calculator-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="percentage-calculator"] .tdee-calculator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .tdee-calculator-related-tool-item:hover .tdee-calculator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .tdee-calculator-related-tool-item { box-shadow: none; border: none; }
        .tdee-calculator-related-tool-item:hover { box-shadow: none; border: none; }
        .tdee-calculator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .tdee-calculator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .tdee-calculator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .tdee-calculator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .tdee-calculator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .tdee-calculator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .tdee-calculator-related-tool-item:hover .tdee-calculator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .tdee-calculator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .tdee-calculator-widget-title { font-size: 1.875rem; }
            .tdee-calculator-grid { grid-template-columns: 1fr; }
            .tdee-calculator-buttons { flex-direction: column; }
            .tdee-calculator-btn { flex: none; }
            .tdee-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .tdee-calculator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .tdee-calculator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .tdee-calculator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .tdee-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .tdee-calculator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .tdee-calculator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .tdee-calculator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .tdee-calculator-input:focus,
        [data-theme="dark"] .tdee-calculator-select:focus { 
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); 
        }
        .tdee-calculator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
    </style>
</head>
<body>
    <div class="tdee-calculator-widget-container">
        <h1 class="tdee-calculator-widget-title">TDEE Calculator</h1>
        <p class="tdee-calculator-widget-description">
            Find your Total Daily Energy Expenditure (TDEE) to understand your maintenance calories and set effective diet goals.
        </p>

        <div class="tdee-calculator-grid">
            <div class="tdee-calculator-input-group">
                <label for="ageInput" class="tdee-calculator-label">Age</label>
                <input id="ageInput" type="number" class="tdee-calculator-input" placeholder="e.g., 35">
            </div>
            <div class="tdee-calculator-input-group">
                <label class="tdee-calculator-label">Gender</label>
                <div class="tdee-calculator-radio-group">
                    <div class="tdee-calculator-radio-option">
                        <input type="radio" id="genderMale" name="gender" class="tdee-calculator-radio" value="male" checked>
                        <label for="genderMale" class="tdee-calculator-radio-label">Male</label>
                    </div>
                    <div class="tdee-calculator-radio-option">
                        <input type="radio" id="genderFemale" name="gender" class="tdee-calculator-radio" value="female">
                        <label for="genderFemale" class="tdee-calculator-radio-label">Female</label>
                    </div>
                </div>
            </div>
            <div class="tdee-calculator-input-group">
                <label for="weightInput" class="tdee-calculator-label">Weight (lbs)</label>
                <input id="weightInput" type="number" class="tdee-calculator-input" placeholder="e.g., 160">
            </div>
            <div class="tdee-calculator-input-group">
                <label class="tdee-calculator-label">Height</label>
                <div class="tdee-calculator-height-inputs">
                    <input id="heightFtInput" type="number" class="tdee-calculator-input" placeholder="Feet">
                    <input id="heightInInput" type="number" class="tdee-calculator-input" placeholder="Inches">
                </div>
            </div>
            <div class="tdee-calculator-input-group" style="grid-column: 1 / -1;">
                <label for="activityLevelSelect" class="tdee-calculator-label">Activity Level</label>
                <select id="activityLevelSelect" class="tdee-calculator-select">
                    <option value="1.2">Sedentary (little or no exercise)</option>
                    <option value="1.375">Lightly Active (light exercise/sports 1-3 days/week)</option>
                    <option value="1.55" selected>Moderately Active (moderate exercise/sports 3-5 days/week)</option>
                    <option value="1.725">Very Active (hard exercise/sports 6-7 days a week)</option>
                    <option value="1.9">Extra Active (very hard exercise/sports & physical job)</option>
                </select>
            </div>
        </div>
        
        <div class="tdee-calculator-buttons">
            <button class="tdee-calculator-btn tdee-calculator-btn-primary" onclick="TDEECalculator.calculate()">
                Calculate TDEE
            </button>
            <button class="tdee-calculator-btn tdee-calculator-btn-secondary" onclick="TDEECalculator.clear()">
                Clear All
            </button>
        </div>

        <div class="tdee-calculator-result">
            <h3 class="tdee-calculator-result-title">Your Estimated Energy Expenditure</h3>
            <div class="tdee-calculator-output" id="tdeeCalculatorOutput">
                Please fill out the form to see your results.
            </div>
            <p class="tdee-calculator-disclaimer">
                This is a scientific estimate. For personalized medical advice, consult a healthcare professional.
            </p>
        </div>

        <div class="tdee-calculator-related-tools">
            <h3 class="tdee-calculator-related-tools-title">Related Tools</h3>
            <div class="tdee-calculator-related-tools-grid">
                <a href="/p/calorie-calculator.html" class="tdee-calculator-related-tool-item" rel="noopener">
                    <div class="tdee-calculator-related-tool-icon">
                        <i class="fas fa-apple-alt"></i>
                    </div>
                    <div class="tdee-calculator-related-tool-name">Calorie Calculator</div>
                </a>
                <a href="/p/age-calculator.html" class="tdee-calculator-related-tool-item" rel="noopener">
                    <div class="tdee-calculator-related-tool-icon">
                        <i class="fas fa-birthday-cake"></i>
                    </div>
                    <div class="tdee-calculator-related-tool-name">Age Calculator</div>
                </a>
                <a href="/p/percentage-calculator.html" class="tdee-calculator-related-tool-item" rel="noopener">
                    <div class="tdee-calculator-related-tool-icon">
                        <i class="fas fa-percent"></i>
                    </div>
                    <div class="tdee-calculator-related-tool-name">Percentage Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>What is TDEE and Why Is It Important?</h2>
            <p>Your Total Daily Energy Expenditure (TDEE) is the total number of calories your body burns in a 24-hour period. This includes all activities: sleeping, eating, working, and exercising. Understanding your TDEE is the single most important step in taking control of your weight. It represents your 'maintenance calories'—the amount you need to consume to stay at your current weight. Our free <strong>TDEE Calculator</strong> uses the scientifically-backed Mifflin-St Jeor formula to provide an accurate estimate of this crucial number.</p>
            <p>Once you know your TDEE, you can set clear, effective goals. To lose fat, you must consume fewer calories than your TDEE (a calorie deficit). To gain muscle, you need to consume more (a calorie surplus). This calculator removes the guesswork and provides the foundational data you need for a successful diet plan.</p>
            
            <h3>How to Use the TDEE Calculator</h3>
            <ol>
                <li><strong>Enter Your Personal Details:</strong> Fill in your current age, gender, weight, and height.</li>
                <li><strong>Choose Your Activity Level:</strong> Be honest about your weekly exercise and activity to ensure an accurate result.</li>
                <li><strong>Calculate:</strong> Click the button to get an instant calculation of your Basal Metabolic Rate (BMR) and your TDEE.</li>
            </ol>
        
            <h3>Frequently Asked Questions About TDEE</h3>
            
            <h4>How do I calculate my actual TDEE?</h4>
            <p>A TDEE calculator provides a highly accurate estimate based on scientific formulas. To find your 'actual' or true TDEE, you would need to meticulously track your calorie intake and weight for 2-4 weeks. If your weight remains stable, your average daily calorie intake is your actual TDEE. This calculator gives you the best possible starting point without this manual effort.</p>
            
            <h4>Which TDEE calculator is most accurate?</h4>
            <p>The most accurate TDEE calculators use the Mifflin-St Jeor equation to determine your Basal Metabolic Rate (BMR), as it's considered the most reliable formula. Accuracy is then improved by correctly selecting your activity level. Our calculator uses this preferred method for the most precise estimation.</p>

            <h4>Can you trust TDEE calculators?</h4>
            <p>Yes, you can trust a TDEE calculator as an excellent scientific starting point. It provides a very close estimate of your daily calorie needs. However, since every individual's metabolism is slightly different, it's best to use the result as a guideline, monitor your weight, and adjust your intake by 100-200 calories if needed.</p>
            
            <h4>Should I eat less than my TDEE to lose weight?</h4>
            <p>Yes. Your TDEE is your 'maintenance' calorie level—the amount you need to eat to stay the same weight. To lose weight, you must create a calorie deficit by consistently eating fewer calories than your TDEE. A deficit of 300-500 calories below your TDEE is a common and sustainable goal for weight loss.</p>
            
            <h4>What is the most accurate calorie calculator method?</h4>
            <p>The most accurate method for estimating calorie needs in a calculator is the Mifflin-St Jeor equation for calculating Basal Metabolic Rate (BMR), which is the calories your body burns at rest. This BMR is then multiplied by an activity factor to find your Total Daily Energy Expenditure (TDEE). This multi-step process is more accurate than simpler formulas.</p>
        </div>
        
        <div class="tdee-calculator-features">
            <h3 class="tdee-calculator-features-title">Key Features:</h3>
            <ul class="tdee-calculator-features-list">
                <li class="tdee-calculator-features-item">Calculates TDEE (Maintenance)</li>
                <li class="tdee-calculator-features-item">Calculates BMR (Resting)</li>
                <li class="tdee-calculator-features-item">Uses Mifflin-St Jeor Formula</li>
                <li class="tdee-calculator-features-item">Personalized for age & gender</li>
                <li class="tdee-calculator-features-item">Variable activity multipliers</li>
                <li class="tdee-calculator-features-item">Clear BMR and TDEE results</li>
                <li class="tdee-calculator-features-item">Responsive on all devices</li>
                <li class="tdee-calculator-features-item">100% Free and confidential</li>
            </ul>
        </div>
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                age: () => document.getElementById('ageInput'),
                gender: () => document.querySelector('input[name="gender"]:checked'),
                weight: () => document.getElementById('weightInput'),
                heightFt: () => document.getElementById('heightFtInput'),
                heightIn: () => document.getElementById('heightInInput'),
                activity: () => document.getElementById('activityLevelSelect'),
                output: () => document.getElementById('tdeeCalculatorOutput')
            };

            window.TDEECalculator = {
                calculate() {
                    const outputEl = elements.output();
                    
                    const age = parseInt(elements.age().value, 10);
                    const gender = elements.gender().value;
                    const weightLbs = parseFloat(elements.weight().value);
                    const heightFt = parseInt(elements.heightFt().value, 10);
                    const heightIn = parseInt(elements.heightIn().value, 10);
                    const activityFactor = parseFloat(elements.activity().value);

                    if (isNaN(age) || age <= 0 || isNaN(weightLbs) || weightLbs <= 0 || isNaN(heightFt) || heightFt < 0 || isNaN(heightIn) || heightIn < 0) {
                        outputEl.innerHTML = '<span style="color: #dc2626; grid-column: 1 / -1;">Please enter valid, positive numbers for all fields.</span>';
                        return;
                    }

                    // Conversions
                    const weightKg = weightLbs * 0.453592;
                    const totalHeightIn = (heightFt * 12) + heightIn;
                    const heightCm = totalHeightIn * 2.54;

                    // Calculate BMR using Mifflin-St Jeor
                    let bmr;
                    if (gender === 'male') {
                        bmr = 10 * weightKg + 6.25 * heightCm - 5 * age + 5;
                    } else { // female
                        bmr = 10 * weightKg + 6.25 * heightCm - 5 * age - 161;
                    }
                    
                    // Calculate TDEE
                    const tdee = bmr * activityFactor;

                    this.displayResults(bmr, tdee);
                },

                displayResults(bmr, tdee) {
                    const outputEl = elements.output();
                    outputEl.innerHTML = `
                        <div class="tdee-calculator-result-section">
                            <div class="tdee-calculator-result-label">Basal Metabolic Rate (BMR)</div>
                            <div class="tdee-calculator-result-value">${Math.round(bmr).toLocaleString()}</div>
                        </div>
                        <div class="tdee-calculator-result-section">
                            <div class="tdee-calculator-result-label">Total Daily Energy Expenditure (TDEE)</div>
                            <div class="tdee-calculator-result-value">${Math.round(tdee).toLocaleString()}</div>
                        </div>
                    `;
                },

                clear() {
                    elements.age().value = '';
                    elements.weight().value = '';
                    elements.heightFt().value = '';
                    elements.heightIn().value = '';
                    document.getElementById('genderMale').checked = true;
                    elements.activity().value = '1.55';
                    elements.output().innerHTML = 'Please fill out the form to see your results.';
                }
            };
        })();
    </script>
</body>
</html>