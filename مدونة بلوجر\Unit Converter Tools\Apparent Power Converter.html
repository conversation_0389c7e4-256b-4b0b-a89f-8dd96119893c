<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apparent Power Converter - Convert VA, kVA, MVA Instantly</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Apparent Power Converter - Convert VA, kVA, MVA Instantly",
        "description": "Instantly convert between apparent power units like Volt-Amperes (VA), kilovolt-amperes (kVA), and megavolt-amperes (MVA). A free online tool for electrical engineers.",
        "url": "https://www.webtoolskit.org/p/apparent-power-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-12",
        "dateModified": "2025-06-18",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Apparent Power Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Apparent Power Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the purpose of apparent power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The main purpose of apparent power (measured in VA) is to represent the total power that must be supplied to a circuit, encompassing both the useful 'real power' (W) and the non-working 'reactive power' (VAR). It is used to correctly size electrical equipment like transformers, generators, and wiring, ensuring they can handle the total current without overheating, regardless of the power factor."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert real power to apparent power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You can convert real power (Watts) to apparent power (VA) using the power factor (PF) of the circuit. The formula is: Apparent Power (VA) = Real Power (W) / Power Factor. For example, if a motor consumes 800 W of real power at a power factor of 0.8, the apparent power is 800 W / 0.8 = 1000 VA or 1 kVA."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between apparent power and reactive power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Apparent power (S) is the total power in an AC circuit, representing the vector sum of real power (P) and reactive power (Q). Reactive power (Q) is just one component of this total power, specifically the energy that oscillates back and forth to sustain magnetic or electric fields. Think of it like a glass of beer: apparent power is the total volume of the glass, real power is the beer you drink, and reactive power is the foam on top."
          }
        },
        {
          "@type": "Question",
          "name": "Do you pay for real power or apparent power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Residential customers typically pay for real power, measured in kilowatt-hours (kWh). However, industrial and commercial customers often pay for both. Their bills may include demand charges based on the peak apparent power (kVA) drawn from the grid, as well as penalties for a low power factor. This encourages them to operate more efficiently by minimizing reactive power."
          }
        },
        {
          "@type": "Question",
          "name": "How to get apparent power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "There are two common ways to get apparent power (S). First, by multiplying the circuit's RMS voltage (V) by the RMS current (I): S = V × I. The result is in Volt-Amperes (VA). Second, if you know the real power (P) and reactive power (Q), you can calculate it using the power triangle formula, which is based on the Pythagorean theorem: S = √(P² + Q²)."
          }
        }
      ]
    }
    </script>

    <style>
        /* Apparent Power Converter Widget - Simplified & Template Compatible */
        .apparent-power-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .apparent-power-converter-widget-container * { box-sizing: border-box; }

        .apparent-power-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .apparent-power-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .apparent-power-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .apparent-power-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .apparent-power-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .apparent-power-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .apparent-power-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .apparent-power-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .apparent-power-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .apparent-power-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .apparent-power-converter-btn:hover { transform: translateY(-2px); }

        .apparent-power-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .apparent-power-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .apparent-power-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .apparent-power-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .apparent-power-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .apparent-power-converter-btn-success:hover {
            background-color: #059669;
        }

        .apparent-power-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .apparent-power-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .apparent-power-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .apparent-power-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .apparent-power-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .apparent-power-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .apparent-power-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .apparent-power-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .apparent-power-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .apparent-power-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .apparent-power-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="power-converter"] .apparent-power-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="reactive-power-converter"] .apparent-power-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="voltage-converter"] .apparent-power-converter-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }

        .apparent-power-converter-related-tool-item:hover .apparent-power-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="power-converter"]:hover .apparent-power-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        a[href*="reactive-power-converter"]:hover .apparent-power-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        a[href*="voltage-converter"]:hover .apparent-power-converter-related-tool-icon { background: linear-gradient(145deg, #fbbd24, #f59e0b); }
        
        .apparent-power-converter-related-tool-item { box-shadow: none; border: none; }
        .apparent-power-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .apparent-power-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .apparent-power-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .apparent-power-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .apparent-power-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .apparent-power-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .apparent-power-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .apparent-power-converter-related-tool-item:hover .apparent-power-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .apparent-power-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .apparent-power-converter-widget-title { font-size: 1.875rem; }
            .apparent-power-converter-buttons { flex-direction: column; }
            .apparent-power-converter-btn { flex: none; }
            .apparent-power-converter-input-group { grid-template-columns: 1fr; }
            .apparent-power-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .apparent-power-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .apparent-power-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .apparent-power-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .apparent-power-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .apparent-power-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .apparent-power-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .apparent-power-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .apparent-power-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .apparent-power-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .apparent-power-converter-output::selection { background-color: var(--primary-color); color: white; }
        .apparent-power-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .apparent-power-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="apparent-power-converter-widget-container">
        <h1 class="apparent-power-converter-widget-title">Apparent Power Converter</h1>
        <p class="apparent-power-converter-widget-description">
            A specialized tool for electrical engineers to convert apparent power units: Volt-Amperes (VA), kVA, and MVA.
        </p>
        
        <div class="apparent-power-converter-input-group">
            <label for="apparentPowerFromInput" class="apparent-power-converter-label">From:</label>
            <input 
                type="number" 
                id="apparentPowerFromInput" 
                class="apparent-power-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="apparentPowerFromUnit" class="apparent-power-converter-select">
                <option value="va" selected>Volt-Ampere (VA)</option>
                <option value="kva">Kilovolt-Ampere (kVA)</option>
                <option value="mva">Megavolt-Ampere (MVA)</option>
            </select>
        </div>

        <div class="apparent-power-converter-input-group">
            <label for="apparentPowerToInput" class="apparent-power-converter-label">To:</label>
            <input 
                type="number" 
                id="apparentPowerToInput" 
                class="apparent-power-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="apparentPowerToUnit" class="apparent-power-converter-select">
                <option value="va">Volt-Ampere (VA)</option>
                <option value="kva" selected>Kilovolt-Ampere (kVA)</option>
                <option value="mva">Megavolt-Ampere (MVA)</option>
            </select>
        </div>

        <div class="apparent-power-converter-buttons">
            <button class="apparent-power-converter-btn apparent-power-converter-btn-primary" onclick="ApparentPowerConverter.convert()">
                Convert Power
            </button>
            <button class="apparent-power-converter-btn apparent-power-converter-btn-secondary" onclick="ApparentPowerConverter.clear()">
                Clear All
            </button>
            <button class="apparent-power-converter-btn apparent-power-converter-btn-success" onclick="ApparentPowerConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="apparent-power-converter-result">
            <h3 class="apparent-power-converter-result-title">Conversion Result:</h3>
            <div class="apparent-power-converter-output" id="apparentPowerConverterOutput">
                Your converted apparent power will appear here...
            </div>
        </div>

        <div class="apparent-power-converter-related-tools">
            <h3 class="apparent-power-converter-related-tools-title">Related Tools</h3>
            <div class="apparent-power-converter-related-tools-grid">
                <a href="/p/power-converter.html" class="apparent-power-converter-related-tool-item" rel="noopener">
                    <div class="apparent-power-converter-related-tool-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="apparent-power-converter-related-tool-name">Power Converter</div>
                </a>
                <a href="/p/reactive-power-converter.html" class="apparent-power-converter-related-tool-item" rel="noopener">
                    <div class="apparent-power-converter-related-tool-icon">
                        <i class="fas fa-wave-square"></i>
                    </div>
                    <div class="apparent-power-converter-related-tool-name">Reactive Power Converter</div>
                </a>
                <a href="/p/voltage-converter.html" class="apparent-power-converter-related-tool-item" rel="noopener">
                    <div class="apparent-power-converter-related-tool-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <div class="apparent-power-converter-related-tool-name">Voltage Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert Apparent Power Units (VA, kVA, MVA)</h2>
            <p>In AC power systems, apparent power is a critical measurement that represents the total power load on a system. It's the combination of real power (which does work) and reactive power (which sustains magnetic fields). Our free <strong>Apparent Power Converter</strong> is a vital tool for electrical engineers, system designers, and technicians who need to perform quick and accurate conversions between Volt-Amperes (VA), kilovolt-amperes (kVA), and megavolt-amperes (MVA).</p>
            <p>Correctly calculating apparent power is essential for sizing transformers, uninterruptible power supplies (UPS), generators, and wiring. This tool removes the risk of manual error from your calculations, helping you ensure system safety and efficiency. Simply input your value and convert with a single click.</p>

            <h3>How to Use the Apparent Power Converter</h3>
            <ol>
                <li><strong>Enter Value:</strong> Type the numeric value of the apparent power you want to convert.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., VA) and your target unit (e.g., kVA) from the dropdown menus.</li>
                <li><strong>Convert:</strong> Click the "Convert Power" button to see the immediate result.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to easily save the converted value for your work.</li>
            </ol>

            <h3>Frequently Asked Questions About Apparent Power</h3>
            
            <h4>What is the purpose of apparent power?</h4>
            <p>The main purpose of apparent power (measured in VA) is to represent the total power that must be supplied to a circuit, encompassing both the useful 'real power' (W) and the non-working 'reactive power' (VAR). It is used to correctly size electrical equipment like transformers, generators, and wiring, ensuring they can handle the total current without overheating, regardless of the power factor.</p>

            <h4>How do you convert real power to apparent power?</h4>
            <p>You can convert real power (Watts) to apparent power (VA) using the power factor (PF) of the circuit. The formula is: Apparent Power (VA) = Real Power (W) / Power Factor. For example, if a motor consumes 800 W of real power at a power factor of 0.8, the apparent power is 800 W / 0.8 = 1000 VA or 1 kVA.</p>

            <h4>What is the difference between apparent power and reactive power?</h4>
            <p>Apparent power (S) is the total power in an AC circuit, representing the vector sum of real power (P) and reactive power (Q). Reactive power (Q) is just one component of this total power, specifically the energy that oscillates back and forth to sustain magnetic or electric fields. Think of it like a glass of beer: apparent power is the total volume of the glass, real power is the beer you drink, and reactive power is the foam on top.</p>

            <h4>Do you pay for real power or apparent power?</h4>
            <p>Residential customers typically pay for real power, measured in kilowatt-hours (kWh). However, industrial and commercial customers often pay for both. Their bills may include demand charges based on the peak apparent power (kVA) drawn from the grid, as well as penalties for a low power factor. This encourages them to operate more efficiently by minimizing reactive power.</p>

            <h4>How to get apparent power?</h4>
            <p>There are two common ways to get apparent power (S). First, by multiplying the circuit's RMS voltage (V) by the RMS current (I): S = V × I. The result is in Volt-Amperes (VA). Second, if you know the real power (P) and reactive power (Q), you can calculate it using the power triangle formula, which is based on the Pythagorean theorem: S = √(P² + Q²).</p>
        </div>

        <div class="apparent-power-converter-features">
            <h3 class="apparent-power-converter-features-title">Key Features:</h3>
            <ul class="apparent-power-converter-features-list">
                <li class="apparent-power-converter-features-item" style="margin-bottom: 0.3em;">Converts VA, kVA, and MVA</li>
                <li class="apparent-power-converter-features-item" style="margin-bottom: 0.3em;">High-precision results</li>
                <li class="apparent-power-converter-features-item" style="margin-bottom: 0.3em;">Essential for sizing equipment</li>
                <li class="apparent-power-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="apparent-power-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side calculations</li>
                <li class="apparent-power-converter-features-item" style="margin-bottom: 0.3em;">Responsive on all devices</li>
                <li class="apparent-power-converter-features-item">100% free and private to use</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="apparent-power-converter-notification" id="apparentPowerConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Apparent Power Converter
        (function() {
            'use strict';

            // Conversion factors to Volt-Amperes (VA)
            const conversionFactors = {
                'va': 1,
                'kva': 1000,
                'mva': 1000000
            };

            const elements = {
                fromInput: () => document.getElementById('apparentPowerFromInput'),
                toInput: () => document.getElementById('apparentPowerToInput'),
                fromUnit: () => document.getElementById('apparentPowerFromUnit'),
                toUnit: () => document.getElementById('apparentPowerToUnit'),
                output: () => document.getElementById('apparentPowerConverterOutput'),
                notification: () => document.getElementById('apparentPowerConverterNotification')
            };

            window.ApparentPowerConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to VA first, then to target unit
                    const valueInVA = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInVA / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-9 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(12)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'va': 'VA', 'kva': 'kVA', 'mva': 'MVA' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted apparent power will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        ApparentPowerConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>