<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Percentage Calculator - Calculate Percentages, Increase, Decrease & More</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Percentage Calculator - Calculate Percentages, Increase, Decrease & More",
        "description": "Calculate percentages, percentage increase, decrease, and find what percentage one number is of another. Free online percentage calculator with instant results.",
        "url": "https://www.webtoolskit.org/p/percentage-calculator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Percentage Calculator",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CalculateAction", "name": "Calculate Percentage" },
            { "@type": "CopyAction", "name": "Copy Percentage Results" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to use a calculator for percentage?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To use a percentage calculator, simply enter the numbers you want to calculate and select the type of calculation needed. For basic percentages, enter the part and the whole. For percentage increase/decrease, enter the original and new values. The calculator will instantly show the result."
          }
        },
        {
          "@type": "Question",
          "name": "How to quickly calculate percentages?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The quickest way to calculate percentages is using an online percentage calculator. Just enter your numbers and get instant results. For mental math, remember that 10% is always 1/10th of a number, 1% is 1/100th, and you can build from there."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate someone's percentage?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate someone's percentage (like a test score), divide their points earned by the total possible points, then multiply by 100. For example, if someone scored 85 out of 100, their percentage is (85 ÷ 100) × 100 = 85%."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate percentage between two people?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate the percentage difference between two people's scores or values, subtract the smaller value from the larger value, divide by the original value, then multiply by 100. This shows the percentage difference between their results."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate the percentage of a group of people?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate what percentage a specific group represents of a larger population, divide the number of people in the specific group by the total number of people, then multiply by 100. For example, if 25 out of 100 people are students, students represent 25% of the group."
          }
        }
      ]
    }
    </script>


    <style>
        /* Percentage Calculator Widget - Simplified & Template Compatible */
        .percentage-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .percentage-widget-container * { box-sizing: border-box; }

        .percentage-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .percentage-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .percentage-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .percentage-input-group {
            margin-bottom: var(--spacing-lg);
        }

        .percentage-tabs {
            display: flex;
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            margin-bottom: var(--spacing-lg);
            border: 2px solid var(--border-color);
        }

        .percentage-tab {
            flex: 1;
            padding: var(--spacing-md);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: none;
            cursor: pointer;
            transition: var(--transition-base);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .percentage-tab.active {
            background-color: var(--primary-color);
            color: white;
        }

        .percentage-tab:hover:not(.active) {
            background-color: var(--card-bg);
        }

        .percentage-input-wrapper {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .percentage-input-field {
            position: relative;
        }

        .percentage-input-field input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .percentage-input-field input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .percentage-calculation-type {
            display: none;
        }

        .percentage-calculation-type.active {
            display: block;
        }

        .percentage-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .percentage-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .percentage-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .percentage-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .percentage-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .percentage-btn {
            padding: 12px 24px;
            border: 2px solid transparent;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 140px;
            display: inline-block;
            text-align: center;
            text-decoration: none;
            box-sizing: border-box;
        }

        .percentage-btn:hover { transform: translateY(-2px); }

        .percentage-btn-primary {
            background-color: #0047AB;
            color: white;
            border: 2px solid #0047AB;
        }

        .percentage-btn-primary:hover {
            background-color: #003d96;
            border-color: #003d96;
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        /* Dark mode support for primary button */
        [data-theme="dark"] .percentage-btn-primary {
            background-color: #60a5fa;
            color: white;
            border-color: #60a5fa;
        }

        [data-theme="dark"] .percentage-btn-primary:hover {
            background-color: #3b82f6;
            border-color: #3b82f6;
            box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
        }

        .percentage-btn-secondary {
            background-color: #f8f9fa;
            color: #495057;
            border: 2px solid #dee2e6;
        }

        .percentage-btn-secondary:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
        }

        .percentage-btn-success {
            background-color: #10b981;
            color: white;
            border: 2px solid #10b981;
        }

        .percentage-btn-success:hover {
            background-color: #059669;
            border-color: #059669;
        }

        /* Dark mode support for buttons */
        [data-theme="dark"] .percentage-btn-secondary {
            background-color: #374151;
            color: #f9fafb;
            border-color: #6b7280;
        }

        [data-theme="dark"] .percentage-btn-secondary:hover {
            background-color: #4b5563;
            border-color: #9ca3af;
        }

        [data-theme="dark"] .percentage-btn-success {
            background-color: #10b981;
            color: white;
            border-color: #10b981;
        }

        [data-theme="dark"] .percentage-btn-success:hover {
            background-color: #059669;
            border-color: #059669;
        }

        [data-theme="dark"] .percentage-tab.active {
            background-color: #60a5fa;
        }

        .percentage-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .percentage-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .percentage-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            margin: 0;
        }

        .percentage-results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .percentage-result-item {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            text-align: center;
        }

        .percentage-result-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .percentage-result-label {
            font-size: 0.875rem;
            color: var(--text-color-light);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .percentage-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .percentage-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .percentage-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .percentage-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .percentage-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .percentage-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .percentage-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .percentage-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="average-calculator"] .percentage-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="margin-calculator"] .percentage-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="discount-calculator"] .percentage-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }

        .percentage-related-tool-item:hover .percentage-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="average-calculator"]:hover .percentage-related-tool-icon { background: linear-gradient(145deg, #f472b6, #ec4899); }
        a[href*="margin-calculator"]:hover .percentage-related-tool-icon { background: linear-gradient(145deg, #fbbf24, #f59e0b); }
        a[href*="discount-calculator"]:hover .percentage-related-tool-icon { background: linear-gradient(145deg, #34d399, #10b981); }
        
        .percentage-related-tool-item { box-shadow: none; border: none; }
        .percentage-related-tool-item:hover { box-shadow: none; border: none; }
        .percentage-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .percentage-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .percentage-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .percentage-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .percentage-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .percentage-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .percentage-related-tool-item:hover .percentage-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .percentage-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .percentage-widget-title { font-size: 1.875rem; }
            .percentage-buttons { flex-direction: column; }
            .percentage-btn { flex: none; }
            .percentage-options { grid-template-columns: 1fr; }
            .percentage-output { 
                padding: var(--spacing-xs) var(--spacing-sm); 
                font-size: 0.875rem; 
                min-height: 50px; 
                line-height: 1.4; 
            }
            .percentage-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .percentage-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .percentage-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .percentage-related-tool-name { font-size: 0.875rem; }
            .percentage-input-wrapper { grid-template-columns: 1fr; }
            .percentage-results-grid { grid-template-columns: repeat(2, 1fr); }
            .percentage-tabs { flex-direction: column; }
            .percentage-tab { font-size: 0.8rem; padding: var(--spacing-sm); }
        }

        @media (max-width: 480px) {
            .percentage-output { 
                padding: var(--spacing-xs) var(--spacing-xs); 
                font-size: 0.8rem; 
                min-height: 40px; 
                line-height: 1.3; 
            }
            .percentage-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .percentage-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .percentage-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .percentage-related-tool-name { font-size: 0.75rem; }
            .percentage-results-grid { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .percentage-input-field input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .percentage-checkbox:focus, .percentage-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .percentage-output::selection { background-color: var(--primary-color); color: white; }
        .percentage-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .percentage-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="percentage-widget-container">
        <h1 class="percentage-widget-title">Percentage Calculator</h1>
        <p class="percentage-widget-description">
            Calculate percentages, percentage increase, decrease, and find what percentage one number is of another. Get instant and accurate percentage calculations for any scenario.
        </p>
        
        <div class="percentage-tabs">
            <button class="percentage-tab active" onclick="PercentageCalculator.switchTab('basic')">Basic Percentage</button>
            <button class="percentage-tab" onclick="PercentageCalculator.switchTab('increase')">Percentage Change</button>
            <button class="percentage-tab" onclick="PercentageCalculator.switchTab('of-number')">X% of Number</button>
            <button class="percentage-tab" onclick="PercentageCalculator.switchTab('fraction')">Fraction to %</button>
        </div>

        <div id="basic-calc" class="percentage-calculation-type active">
            <div class="percentage-input-group">
                <label class="percentage-label">What percentage is X of Y?</label>
                <div class="percentage-input-wrapper">
                    <div class="percentage-input-field">
                        <input type="number" id="basicPart" placeholder="Enter the part (X)" step="any">
                    </div>
                    <div class="percentage-input-field">
                        <input type="number" id="basicWhole" placeholder="Enter the whole (Y)" step="any">
                    </div>
                </div>
            </div>
        </div>

        <div id="increase-calc" class="percentage-calculation-type">
            <div class="percentage-input-group">
                <label class="percentage-label">Percentage Increase/Decrease</label>
                <div class="percentage-input-wrapper">
                    <div class="percentage-input-field">
                        <input type="number" id="originalValue" placeholder="Original value" step="any">
                    </div>
                    <div class="percentage-input-field">
                        <input type="number" id="newValue" placeholder="New value" step="any">
                    </div>
                </div>
            </div>
        </div>

        <div id="of-number-calc" class="percentage-calculation-type">
            <div class="percentage-input-group">
                <label class="percentage-label">Calculate X% of a Number</label>
                <div class="percentage-input-wrapper">
                    <div class="percentage-input-field">
                        <input type="number" id="percentageAmount" placeholder="Enter percentage (%)" step="any">
                    </div>
                    <div class="percentage-input-field">
                        <input type="number" id="numberAmount" placeholder="Enter the number" step="any">
                    </div>
                </div>
            </div>
        </div>

        <div id="fraction-calc" class="percentage-calculation-type">
            <div class="percentage-input-group">
                <label class="percentage-label">Convert Fraction to Percentage</label>
                <div class="percentage-input-wrapper">
                    <div class="percentage-input-field">
                        <input type="number" id="numerator" placeholder="Numerator (top number)" step="any">
                    </div>
                    <div class="percentage-input-field">
                        <input type="number" id="denominator" placeholder="Denominator (bottom number)" step="any">
                    </div>
                </div>
            </div>
        </div>

        <div class="percentage-options">
            <div class="percentage-option">
                <input type="checkbox" id="showDecimals" class="percentage-checkbox" checked>
                <label for="showDecimals" class="percentage-option-label">Show decimal places</label>
            </div>
            <div class="percentage-option">
                <input type="checkbox" id="roundResults" class="percentage-checkbox">
                <label for="roundResults" class="percentage-option-label">Round to nearest whole</label>
            </div>
            <div class="percentage-option">
                <input type="checkbox" id="showSteps" class="percentage-checkbox" checked>
                <label for="showSteps" class="percentage-option-label">Show calculation steps</label>
            </div>
            <div class="percentage-option">
                <input type="checkbox" id="showFraction" class="percentage-checkbox">
                <label for="showFraction" class="percentage-option-label">Show as fraction</label>
            </div>
        </div>

        <div class="percentage-buttons">
            <button class="percentage-btn percentage-btn-primary" onclick="PercentageCalculator.calculate()">
                Calculate Percentage
            </button>
            <button class="percentage-btn percentage-btn-secondary" onclick="PercentageCalculator.clear()">
                Clear All
            </button>
            <button class="percentage-btn percentage-btn-success" onclick="PercentageCalculator.copy()">
                Copy Results
            </button>
        </div>

        <div class="percentage-result">
            <h3 class="percentage-result-title">Calculation Results:</h3>
            <div class="percentage-output" id="percentageOutput">
                Your percentage calculation will appear here...
            </div>
            <div class="percentage-results-grid" id="percentageResultsGrid" style="display: none;">
                <div class="percentage-result-item">
                    <div class="percentage-result-value" id="mainResult">0%</div>
                    <div class="percentage-result-label">Result</div>
                </div>
                <div class="percentage-result-item" id="decimalItem">
                    <div class="percentage-result-value" id="decimalResult">0</div>
                    <div class="percentage-result-label">Decimal</div>
                </div>
                <div class="percentage-result-item" id="fractionItem" style="display: none;">
                    <div class="percentage-result-value" id="fractionResult">0/0</div>
                    <div class="percentage-result-label">Fraction</div>
                </div>
            </div>
        </div>

        <div class="percentage-related-tools">
            <h3 class="percentage-related-tools-title">Related Tools</h3>
            <div class="percentage-related-tools-grid">
                <a href="/p/average-calculator.html" class="percentage-related-tool-item" rel="noopener">
                    <div class="percentage-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="percentage-related-tool-name">Average Calculator</div>
                </a>

                <a href="/p/margin-calculator.html" class="percentage-related-tool-item" rel="noopener">
                    <div class="percentage-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="percentage-related-tool-name">Margin Calculator</div>
                </a>

                <a href="/p/discount-calculator.html" class="percentage-related-tool-item" rel="noopener">
                    <div class="percentage-related-tool-icon">
                        <i class="fas fa-tag"></i>
                    </div>
                    <div class="percentage-related-tool-name">Discount Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Master Percentage Calculations with Our Advanced Calculator</h2>
            <p>Our comprehensive <strong>Percentage Calculator</strong> handles every type of percentage calculation you might need. Whether you're calculating test scores, determining price changes, finding discounts, or working with business metrics, this tool provides accurate results instantly. Understanding percentages is crucial in education, business, finance, and everyday life, and our calculator makes these computations effortless.</p>
            <p>From basic percentage calculations to complex percentage increases and decreases, this tool supports multiple calculation types with detailed step-by-step explanations. You can quickly convert between percentages, decimals, and fractions, making it perfect for students, professionals, and anyone who works with numerical data regularly.</p>
            
            <h3>How to Use the Percentage Calculator</h3>
            <ol>
                <li><strong>Choose Calculation Type:</strong> Select from Basic Percentage, Percentage Change, X% of Number, or Fraction to Percentage using the tabs.</li>
                <li><strong>Enter Your Numbers:</strong> Input the relevant values based on your calculation type. The calculator accepts decimal numbers for precise results.</li>
                <li><strong>Customize Display Options:</strong> Use checkboxes to show decimal places, round results, display calculation steps, or show fraction equivalents.</li>
                <li><strong>Get Instant Results:</strong> Click "Calculate Percentage" to see your results with detailed explanations and multiple format options.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Percentage Calculator</h3>
            
            <h4>How to use a calculator for percentage?</h4>
            <p>To use a percentage calculator effectively, first identify what type of calculation you need. For basic percentages, enter the part and whole numbers. For percentage changes, input the original and new values. For calculating a percentage of a number, enter the percentage and the number. The calculator will instantly provide accurate results with explanations.</p>
            
            <h4>How to quickly calculate percentages?</h4>
            <p>The fastest way to calculate percentages is using our online calculator. Just enter your numbers and select the calculation type. For mental math shortcuts, remember that 10% equals moving the decimal point one place left, 1% is moving it two places, and you can combine these for quick estimates like 15% = 10% + 5%.</p>
            
            <h4>How to calculate someone's percentage?</h4>
            <p>To calculate someone's percentage score or result, divide their achieved points by the total possible points, then multiply by 100. For example, if someone scored 42 out of 50 points, their percentage is (42 ÷ 50) × 100 = 84%. Our calculator handles this automatically when you select "Basic Percentage."</p>
            
            <h4>How to calculate percentage between two people?</h4>
            <p>To find the percentage difference between two people's scores or values, use the percentage change calculation. Subtract the first value from the second, divide by the original value, then multiply by 100. This shows the percentage increase or decrease between their results, useful for comparing performance or progress.</p>
            
            <h4>How to calculate the percentage of a group of people?</h4>
            <p>To determine what percentage a specific group represents within a larger population, divide the number of people in the specific group by the total population, then multiply by 100. For instance, if 15 out of 60 people are left-handed, left-handed people represent (15 ÷ 60) × 100 = 25% of the group.</p>
        </div>

        <div class="percentage-features">
            <h3 class="percentage-features-title">Key Features:</h3>
            <ul class="percentage-features-list">
                <li class="percentage-features-item" style="margin-bottom: 0.3em;">Multiple percentage calculation types</li>
                <li class="percentage-features-item" style="margin-bottom: 0.3em;">Step-by-step calculation explanations</li>
                <li class="percentage-features-item" style="margin-bottom: 0.3em;">Decimal and fraction conversions</li>
                <li class="percentage-features-item" style="margin-bottom: 0.3em;">Percentage increase/decrease calculations</li>
                <li class="percentage-features-item" style="margin-bottom: 0.3em;">Customizable display options</li>
                <li class="percentage-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="percentage-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="percentage-notification" id="percentageNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Percentage Calculator
        (function() {
            'use strict';

            const elements = {
                output: () => document.getElementById('percentageOutput'),
                notification: () => document.getElementById('percentageNotification'),
                resultsGrid: () => document.getElementById('percentageResultsGrid'),
                mainResult: () => document.getElementById('mainResult'),
                decimalResult: () => document.getElementById('decimalResult'),
                fractionResult: () => document.getElementById('fractionResult'),
                decimalItem: () => document.getElementById('decimalItem'),
                fractionItem: () => document.getElementById('fractionItem')
            };

            let currentTab = 'basic';

            window.PercentageCalculator = {
                switchTab(tabName) {
                    // Update active tab
                    document.querySelectorAll('.percentage-tab').forEach(tab => tab.classList.remove('active'));
                    document.querySelector(`[onclick="PercentageCalculator.switchTab('${tabName}')"]`).classList.add('active');
                    
                    // Show relevant calculation type
                    document.querySelectorAll('.percentage-calculation-type').forEach(type => type.classList.remove('active'));
                    document.getElementById(`${tabName}-calc`).classList.add('active');
                    
                    currentTab = tabName;
                    this.clear();
                },

                calculate() {
                    const output = elements.output();
                    let result;

                    try {
                        switch(currentTab) {
                            case 'basic':
                                result = this.calculateBasic();
                                break;
                            case 'increase':
                                result = this.calculateIncrease();
                                break;
                            case 'of-number':
                                result = this.calculateOfNumber();
                                break;
                            case 'fraction':
                                result = this.calculateFraction();
                                break;
                        }

                        if (result.error) {
                            output.textContent = result.error;
                            output.style.color = '#dc2626';
                            elements.resultsGrid().style.display = 'none';
                            return;
                        }

                        output.style.color = '';
                        this.displayResults(result);

                    } catch (error) {
                        output.textContent = 'Please enter valid numbers for calculation.';
                        output.style.color = '#dc2626';
                        elements.resultsGrid().style.display = 'none';
                    }
                },

                calculateBasic() {
                    const part = parseFloat(document.getElementById('basicPart').value);
                    const whole = parseFloat(document.getElementById('basicWhole').value);

                    if (isNaN(part) || isNaN(whole)) {
                        return { error: 'Please enter both numbers for basic percentage calculation.' };
                    }

                    if (whole === 0) {
                        return { error: 'Cannot divide by zero. Please enter a non-zero whole number.' };
                    }

                    const percentage = (part / whole) * 100;
                    const decimal = part / whole;

                    return {
                        percentage: percentage,
                        decimal: decimal,
                        description: `${part} is ${this.formatNumber(percentage)}% of ${whole}`,
                        steps: `Step 1: Divide ${part} by ${whole} = ${decimal.toFixed(4)}\nStep 2: Multiply by 100 = ${this.formatNumber(percentage)}%`
                    };
                },

                calculateIncrease() {
                    const original = parseFloat(document.getElementById('originalValue').value);
                    const newValue = parseFloat(document.getElementById('newValue').value);

                    if (isNaN(original) || isNaN(newValue)) {
                        return { error: 'Please enter both original and new values.' };
                    }

                    if (original === 0) {
                        return { error: 'Original value cannot be zero for percentage change calculation.' };
                    }

                    const change = newValue - original;
                    const percentage = (change / original) * 100;
                    const decimal = change / original;

                    const changeType = percentage >= 0 ? 'increase' : 'decrease';
                    const absPercentage = Math.abs(percentage);

                    return {
                        percentage: absPercentage,
                        decimal: Math.abs(decimal),
                        description: `${changeType.charAt(0).toUpperCase() + changeType.slice(1)} of ${this.formatNumber(absPercentage)}% from ${original} to ${newValue}`,
                        steps: `Step 1: Calculate change = ${newValue} - ${original} = ${change}\nStep 2: Divide by original = ${change} ÷ ${original} = ${decimal.toFixed(4)}\nStep 3: Convert to percentage = ${this.formatNumber(percentage)}%`,
                        isIncrease: percentage >= 0
                    };
                },

                calculateOfNumber() {
                    const percentage = parseFloat(document.getElementById('percentageAmount').value);
                    const number = parseFloat(document.getElementById('numberAmount').value);

                    if (isNaN(percentage) || isNaN(number)) {
                        return { error: 'Please enter both percentage and number values.' };
                    }

                    const result = (percentage / 100) * number;
                    const decimal = percentage / 100;

                    return {
                        percentage: result,
                        decimal: decimal,
                        description: `${percentage}% of ${number} is ${this.formatNumber(result)}`,
                        steps: `Step 1: Convert percentage to decimal = ${percentage} ÷ 100 = ${decimal}\nStep 2: Multiply by number = ${decimal} × ${number} = ${this.formatNumber(result)}`,
                        isResult: true
                    };
                },

                calculateFraction() {
                    const numerator = parseFloat(document.getElementById('numerator').value);
                    const denominator = parseFloat(document.getElementById('denominator').value);

                    if (isNaN(numerator) || isNaN(denominator)) {
                        return { error: 'Please enter both numerator and denominator.' };
                    }

                    if (denominator === 0) {
                        return { error: 'Denominator cannot be zero.' };
                    }

                    const decimal = numerator / denominator;
                    const percentage = decimal * 100;

                    return {
                        percentage: percentage,
                        decimal: decimal,
                        fraction: `${numerator}/${denominator}`,
                        description: `${numerator}/${denominator} = ${this.formatNumber(percentage)}%`,
                        steps: `Step 1: Divide numerator by denominator = ${numerator} ÷ ${denominator} = ${decimal.toFixed(4)}\nStep 2: Convert to percentage = ${decimal.toFixed(4)} × 100 = ${this.formatNumber(percentage)}%`
                    };
                },

                displayResults(result) {
                    const output = elements.output();
                    const resultsGrid = elements.resultsGrid();
                    
                    let resultText = result.description;
                    
                    const showSteps = document.getElementById('showSteps').checked;
                    if (showSteps && result.steps) {
                        resultText += '\n\nCalculation Steps:\n' + result.steps;
                    }
                    
                    output.textContent = resultText;

                    // Update main result
                    let mainValue;
                    if (result.isResult) {
                        mainValue = this.formatNumber(result.percentage);
                    } else {
                        mainValue = this.formatNumber(result.percentage) + '%';
                    }
                    
                    elements.mainResult().textContent = mainValue;

                    // Update decimal result
                    elements.decimalResult().textContent = result.decimal.toFixed(4);

                    // Show/hide fraction
                    const showFraction = document.getElementById('showFraction').checked;
                    const fractionItem = elements.fractionItem();
                    const decimalItem = elements.decimalItem();
                    
                    if (showFraction && result.fraction) {
                        elements.fractionResult().textContent = result.fraction;
                        fractionItem.style.display = 'block';
                    } else {
                        fractionItem.style.display = 'none';
                    }

                    // Show/hide decimal
                    const showDecimals = document.getElementById('showDecimals').checked;
                    decimalItem.style.display = showDecimals ? 'block' : 'none';

                    resultsGrid.style.display = 'grid';
                },

                formatNumber(num) {
                    const roundResults = document.getElementById('roundResults').checked;
                    if (roundResults) {
                        return Math.round(num).toString();
                    }
                    return parseFloat(num.toFixed(2)).toString();
                },

                clear() {
                    // Clear all inputs
                    document.querySelectorAll('input[type="number"]').forEach(input => input.value = '');
                    
                    elements.output().textContent = 'Your percentage calculation will appear here...';
                    elements.output().style.color = '';
                    elements.resultsGrid().style.display = 'none';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Your percentage calculation will appear here...' || text.includes('Please enter') || text.includes('Cannot')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize - NO AUTO-CALCULATION
            document.addEventListener('DOMContentLoaded', function() {
                // Theme compatibility only
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }
                // Calculator only works when "Calculate Percentage" button is pressed
            });
        })();
    </script>
</body>
</html>