<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ASCII to Binary Converter - Free Online Tool</title>
    <meta name="description" content="Convert ASCII codes to binary instantly with our free online tool. Supports various input separators and provides formatted binary output for easy reading and use.">
    <meta name="keywords" content="ascii to binary, ascii to binary converter, convert ascii to binary, ascii code to binary, character encoding, online tool">
    <link rel="canonical" href="https://www.webtoolskit.org/p/ascii-to-binary.html" />
    
    <!-- Page-specific Open Graph Meta Tags -->
    <meta property="og:url" content="https://www.webtoolskit.org/p/ascii-to-binary.html" />
    <meta property="og:title" content="Free ASCII to Binary Converter - Convert ASCII Codes Online" />
    <meta property="og:description" content="A fast and simple tool to convert numerical ASCII codes into their 8-bit binary representation. Perfect for developers, students, and tech enthusiasts." />
    <meta property="og:image" content="https://www.webtoolskit.org/images/binary-og.jpg" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free ASCII to Binary Converter - Convert ASCII Codes to Binary",
        "description": "Convert ASCII codes to binary instantly with our free online tool. Supports various input separators and provides formatted binary output for easy reading and use.",
        "url": "https://www.webtoolskit.org/p/ascii-to-binary.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-19",
        "dateModified": "2025-06-19",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "ASCII to Binary Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert ASCII to Binary" },
            { "@type": "CopyAction", "name": "Copy Binary Code" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert ASCII into binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert ASCII to binary, you take each ASCII decimal number and convert it into its 8-bit binary equivalent. For example, the ASCII code for 'A' is 65. The binary representation of 65 is 01000001. You repeat this for every ASCII code in your sequence."
          }
        },
        {
          "@type": "Question",
          "name": "What is 01101111 in ASCII?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The binary number 01101111 is equivalent to the decimal number 111. In the ASCII table, the value 111 corresponds to the lowercase letter 'o'."
          }
        },
        {
          "@type": "Question",
          "name": "How to find ASCII value in binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "First, you find the standard decimal ASCII value for a character (e.g., 'B' is 66). Then, you convert this decimal number to an 8-bit binary number. 66 in binary is 1000010, which becomes 01000010 when padded to 8 bits."
          }
        },
        {
          "@type": "Question",
          "name": "Is ASCII in binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Not directly. ASCII is a character encoding standard that assigns a unique decimal number to each character. However, computers store these numbers in binary format. So, while ASCII itself is a numerical mapping, its practical implementation in a computer is always in binary."
          }
        },
        {
          "@type": "Question",
          "name": "What is the ASCII code for space in binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The ASCII code for a space character is 32 in decimal. In 8-bit binary, this is represented as 00100000."
          }
        }
      ]
    }
    </script>

    <style>
        /* ASCII to Binary Widget - Simplified & Template Compatible */
        .ascii-to-binary-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .ascii-to-binary-widget-container * { box-sizing: border-box; }

        .ascii-to-binary-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .ascii-to-binary-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .ascii-to-binary-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .ascii-to-binary-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .ascii-to-binary-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .ascii-to-binary-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .ascii-to-binary-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .ascii-to-binary-select {
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-weight: 500;
        }

        .ascii-to-binary-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .ascii-to-binary-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .ascii-to-binary-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .ascii-to-binary-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .ascii-to-binary-btn:hover { transform: translateY(-2px); }
        .ascii-to-binary-btn-primary { background-color: var(--primary-color); color: white; }
        .ascii-to-binary-btn-primary:hover { background-color: var(--secondary-color); box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4); }
        .ascii-to-binary-btn-secondary { background-color: var(--background-color-alt); color: var(--text-color); border: 1px solid var(--border-color); }
        .ascii-to-binary-btn-secondary:hover { background-color: var(--border-color); }
        .ascii-to-binary-btn-success { background-color: #10b981; color: white; }
        .ascii-to-binary-btn-success:hover { background-color: #059669; }

        .ascii-to-binary-result { background-color: var(--background-color-alt); border-radius: var(--border-radius-lg); padding: var(--spacing-lg); border-left: 4px solid var(--primary-color); border: 1px solid var(--border-color); }
        .ascii-to-binary-result-title { margin: 0 0 var(--spacing-md) 0; color: var(--text-color); font-size: 1.25rem; font-weight: 700; }
        .ascii-to-binary-output { background-color: var(--card-bg); border: 2px solid var(--border-color); border-radius: var(--border-radius-md); padding: var(--spacing-md) var(--spacing-lg); font-family: 'SF Mono', Monaco, monospace; font-size: var(--font-size-base); word-break: break-all; min-height: 60px; color: var(--text-color); line-height: 1.5; }

        .ascii-to-binary-notification { position: fixed; top: 20px; right: 20px; background-color: #10b981; color: white; padding: var(--spacing-md) var(--spacing-lg); border-radius: var(--border-radius-md); font-weight: 600; z-index: 10000; transform: translateX(400px); transition: var(--transition-base); }
        .ascii-to-binary-notification.show { transform: translateX(0); }
        
        .seo-content { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); color: var(--text-color-light); line-height: 1.7; }
        .seo-content h2, .seo-content h3, .seo-content h4 { color: var(--text-color); margin-bottom: var(--spacing-md); }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code { background-color: var(--background-color-alt); padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 6px; font-family: 'SF Mono', Monaco, monospace; }

        .ascii-to-binary-features { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .ascii-to-binary-features-title { color: var(--text-color); margin-bottom: var(--spacing-md); font-size: 1.25rem; font-weight: 700; }
        .ascii-to-binary-features-list { list-style: none; padding: 0; margin: 0; columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; }
        .ascii-to-binary-features-item { padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg); color: var(--text-color-light); position: relative; margin-bottom: 0.3em; }
        .ascii-to-binary-features-item:before { content: ""; position: absolute; left: 0; top: calc(var(--spacing-sm) + 2px); width: 12px; height: 6px; border-left: 2px solid #10b981; border-bottom: 2px solid #10b981; transform: rotate(-45deg); }
        
        .ascii-to-binary-related-tool-icon { width: 72px; height: 72px; margin: 0 auto var(--spacing-sm); border-radius: 18px; display: flex; align-items: center; justify-content: center; font-size: 2.25rem; color: white; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15); border: none; }
        a[href*="binary-to-ascii"] .ascii-to-binary-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="ascii-to-text"] .ascii-to-binary-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="text-to-binary"] .ascii-to-binary-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }
        .ascii-to-binary-related-tool-item:hover .ascii-to-binary-related-tool-icon { transform: translateY(-5px) scale(1.05); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15); }
        a[href*="binary-to-ascii"]:hover .ascii-to-binary-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="ascii-to-text"]:hover .ascii-to-binary-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="text-to-binary"]:hover .ascii-to-binary-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .ascii-to-binary-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .ascii-to-binary-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .ascii-to-binary-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .ascii-to-binary-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .ascii-to-binary-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .ascii-to-binary-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .ascii-to-binary-related-tool-item:hover .ascii-to-binary-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .ascii-to-binary-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .ascii-to-binary-widget-title { font-size: 1.875rem; }
            .ascii-to-binary-buttons { flex-direction: column; }
            .ascii-to-binary-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .ascii-to-binary-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .ascii-to-binary-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .ascii-to-binary-related-tool-name { font-size: 0.875rem; }
        }
        @media (max-width: 600px) { 
            .ascii-to-binary-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } 
        }
        @media (max-width: 480px) {
            .ascii-to-binary-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .ascii-to-binary-related-tool-item { padding: var(--spacing-sm); }
            .ascii-to-binary-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .ascii-to-binary-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="ascii-to-binary-widget-container">
        <h1 class="ascii-to-binary-widget-title">ASCII to Binary Converter</h1>
        <p class="ascii-to-binary-widget-description">
            Convert decimal ASCII codes into their 8-bit binary representation. A fundamental tool for understanding data encoding at the lowest level.
        </p>
        
        <div class="ascii-to-binary-input-group">
            <label for="asciiToBinaryInput" class="ascii-to-binary-label">Enter ASCII codes:</label>
            <textarea 
                id="asciiToBinaryInput" 
                class="ascii-to-binary-textarea"
                placeholder="Paste your ASCII codes here (e.g., 65 66 67)..."
                rows="4"
            ></textarea>
        </div>

        <div class="ascii-to-binary-options">
            <div class="ascii-to-binary-option">
                <label for="asciiSeparator" class="ascii-to-binary-option-label" style="margin-right: 5px;">Input Separator:</label>
                <select id="asciiSeparator" class="ascii-to-binary-select">
                    <option value="space" selected>Space</option>
                    <option value="comma">Comma</option>
                    <option value="semicolon">Semicolon</option>
                    <option value="newline">New Line</option>
                </select>
            </div>
            <div class="ascii-to-binary-option">
                <input type="checkbox" id="binaryAddSpaces" class="ascii-to-binary-checkbox" checked>
                <label for="binaryAddSpaces" class="ascii-to-binary-option-label">Space between bytes</label>
            </div>
        </div>

        <div class="ascii-to-binary-buttons">
            <button class="ascii-to-binary-btn ascii-to-binary-btn-primary" onclick="AsciiToBinaryConverter.convert()">
                Convert to Binary
            </button>
            <button class="ascii-to-binary-btn ascii-to-binary-btn-secondary" onclick="AsciiToBinaryConverter.clear()">
                Clear All
            </button>
            <button class="ascii-to-binary-btn ascii-to-binary-btn-success" onclick="AsciiToBinaryConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="ascii-to-binary-result">
            <h3 class="ascii-to-binary-result-title">Binary Code:</h3>
            <div class="ascii-to-binary-output" id="asciiToBinaryOutput">
                Your binary code will appear here...
            </div>
        </div>
        
        <div class="ascii-to-binary-related-tools">
            <h3 class="ascii-to-binary-related-tools-title">Related Tools</h3>
            <div class="ascii-to-binary-related-tools-grid">
                <a href="/p/binary-to-ascii.html" class="ascii-to-binary-related-tool-item" rel="noopener">
                    <div class="ascii-to-binary-related-tool-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="ascii-to-binary-related-tool-name">Binary to ASCII</div>
                </a>
                <a href="/p/ascii-to-text.html" class="ascii-to-binary-related-tool-item" rel="noopener">
                    <div class="ascii-to-binary-related-tool-icon"><i class="fas fa-font"></i></div>
                    <div class="ascii-to-binary-related-tool-name">ASCII to Text</div>
                </a>
                <a href="/p/text-to-binary.html" class="ascii-to-binary-related-tool-item" rel="noopener">
                    <div class="ascii-to-binary-related-tool-icon"><i class="fas fa-file-alt"></i></div>
                    <div class="ascii-to-binary-related-tool-name">Text to Binary</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>The Bridge Between ASCII and Binary</h2>
            <p>Our <strong>ASCII to Binary Converter</strong> is a specialized tool that completes the final step in digital character encoding. While text is first mapped to numerical ASCII codes, computers ultimately store and process this information in binary format. This tool takes those decimal ASCII codes and translates them into the strings of 0s and 1s that form the foundation of all modern computing.</p>
            <p>This conversion is a crucial concept for anyone studying computer science, networking, or low-level programming. It reveals exactly how a character is represented by a byte (8 bits) in a computer's memory. For example, the ASCII code <code>65</code> (for the letter 'A') becomes the binary string <code>01000001</code>. Our converter makes this process transparent and instantaneous, helping you decode data, understand file structures, or complete academic exercises with precision and ease.</p>
            
            <h3>How to Use the ASCII to Binary Converter</h3>
            <ol>
                <li><strong>Enter ASCII Codes:</strong> Type or paste the numerical ASCII codes you wish to convert into the input box.</li>
                <li><strong>Select Input Separator:</strong> Choose the delimiter that separates your ASCII codes (e.g., space, comma).</li>
                <li><strong>Choose Output Format:</strong> Decide if you want spaces between the resulting binary bytes for readability.</li>
                <li><strong>Click Convert:</strong> Press the "Convert to Binary" button to see the result instantly.</li>
            </ol>
        
            <h3>Frequently Asked Questions About ASCII to Binary Conversion</h3>
            <h4>How to convert ASCII into binary?</h4>
            <p>To convert ASCII to binary, you take each ASCII decimal number and convert it into its 8-bit binary equivalent. For example, the ASCII code for 'A' is 65. The binary representation of 65 is 01000001. You repeat this for every ASCII code in your sequence.</p>
            
            <h4>What is 01101111 in ASCII?</h4>
            <p>The binary number 01101111 is equivalent to the decimal number 111. In the ASCII table, the value 111 corresponds to the lowercase letter 'o'.</p>
            
            <h4>How to find ASCII value in binary?</h4>
            <p>First, you find the standard decimal ASCII value for a character (e.g., 'B' is 66). Then, you convert this decimal number to an 8-bit binary number. 66 in binary is 1000010, which becomes 01000010 when padded to 8 bits.</p>
            
            <h4>Is ASCII in binary?</h4>
            <p>Not directly. ASCII is a character encoding standard that assigns a unique decimal number to each character. However, computers store these numbers in binary format. So, while ASCII itself is a numerical mapping, its practical implementation in a computer is always in binary.</p>
            
            <h4>What is the ASCII code for space in binary?</h4>
            <p>The ASCII code for a space character is 32 in decimal. In 8-bit binary, this is represented as 00100000.</p>
        </div>

        <div class="ascii-to-binary-features">
            <h3 class="ascii-to-binary-features-title">Key Features:</h3>
            <ul class="ascii-to-binary-features-list">
                <li class="ascii-to-binary-features-item">Instant ASCII-to-binary conversion</li>
                <li class="ascii-to-binary-features-item">Supports multiple input separators</li>
                <li class="ascii-to-binary-features-item">Formats output with optional spaces</li>
                <li class="ascii-to-binary-features-item">Handles all standard ASCII codes (0-255)</li>
                <li class="ascii-to-binary-features-item">Clear error handling for invalid input</li>
                <li class="ascii-to-binary-features-item">One-click copy for binary results</li>
                <li class="ascii-to-binary-features-item">Responsive on all devices</li>
                <li class="ascii-to-binary-features-item">Completely free and web-based</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="ascii-to-binary-notification" id="asciiToBinaryNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('asciiToBinaryInput'),
                output: () => document.getElementById('asciiToBinaryOutput'),
                notification: () => document.getElementById('asciiToBinaryNotification'),
                separator: () => document.getElementById('asciiSeparator'),
                addSpaces: () => document.getElementById('binaryAddSpaces')
            };

            window.AsciiToBinaryConverter = {
                convert() {
                    const input = elements.input();
                    const output = elements.output();
                    const ascii = input.value;

                    if (!ascii.trim()) {
                        output.textContent = 'Please enter ASCII codes to convert.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        separator: elements.separator().value,
                        addSpaces: elements.addSpaces().checked
                    };

                    const result = this.processAscii(ascii, options);
                    if (result.startsWith('Error:')) {
                        output.style.color = '#dc2626';
                    }
                    output.textContent = result;
                },

                processAscii(ascii, options) {
                    const separatorMap = {
                        'space': /\s+/,
                        'comma': /,/,
                        'semicolon': /;/,
                        'newline': /\r?\n/
                    };
                    const regex = separatorMap[options.separator] || /\s+/;
                    const codes = ascii.trim().split(regex);
                    
                    let binaryResult = [];
                    for (const code of codes) {
                        const trimmedCode = code.trim();
                        if (trimmedCode === '') continue;

                        const num = parseInt(trimmedCode, 10);
                        if (isNaN(num) || num < 0 || num > 255) {
                            return `Error: Invalid ASCII code ('${trimmedCode}'). Please use numbers from 0-255.`;
                        }
                        binaryResult.push(num.toString(2).padStart(8, '0'));
                    }
                    
                    return binaryResult.join(options.addSpaces ? ' ' : '');
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your binary code will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text.includes('will appear here') || text.includes('Please enter') || text.includes('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        AsciiToBinaryConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>