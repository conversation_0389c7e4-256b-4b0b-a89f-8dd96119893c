<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Minifier - Compress and Minify JS Code Online</title>
    <meta name="description" content="Reduce the file size of your JavaScript code with our free online JS minifier. Compress your scripts to improve website loading speed and performance.">
    <link rel="canonical" href="https://www.webtoolskit.org/p/javascript-minifier.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "JavaScript Minifier - Compress and Minify JS Code Online",
        "description": "Reduce the file size of your JavaScript code with our free online JS minifier. Compress your scripts to improve website loading speed and performance.",
        "url": "https://www.webtoolskit.org/p/javascript-minifier.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-29",
        "dateModified": "2025-06-29",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "JavaScript Minifier",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Minify JavaScript Code" },
            { "@type": "CopyAction", "name": "Copy Minified JavaScript" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is JS minification?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "JS minification is the process of removing all unnecessary characters from JavaScript source code without changing its functionality. This includes removing whitespace, comments, newlines, and shortening variable names. The result is a much smaller file that loads faster in a web browser."
          }
        },
        {
          "@type": "Question",
          "name": "How do I minify a JavaScript file?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You can easily minify a JavaScript file by using an online tool like this one. Simply paste your JS code into the input box, click the 'Minify JS' button, and copy the compressed output. For automated workflows, developers often use build tools like Webpack, Rollup, or Vite, which incorporate minifiers like Terser."
          }
        },
        {
          "@type": "Question",
          "name": "Why is it important to minify JavaScript?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Minifying JavaScript is crucial for web performance. It significantly reduces the file size of your scripts, which leads to faster download times, lower bandwidth consumption, and a quicker initial page load. This improves the user experience and can positively impact SEO rankings."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between minify, obfuscate, and uglify?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Minify: Removes unnecessary characters to reduce file size. The code's logic remains readable if formatted. Uglify: A term often used for a specific tool (UglifyJS) that both minifies and mangles code (shortens variable/function names). Obfuscate: Actively rewrites code to make it extremely difficult for humans to understand or reverse-engineer, often for security or intellectual property protection. Obfuscation usually results in a larger file size."
          }
        },
        {
          "@type": "Question",
          "name": "Does minifying JavaScript break the code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, when done correctly with a reliable tool, minifying JavaScript does not break the code. Modern minifiers parse the code into an Abstract Syntax Tree (AST), perform optimizations, and then regenerate the code. This ensures that the logic and functionality remain identical, even though the text representation is completely different."
          }
        }
      ]
    }
    </script>


    <style>
        /* JavaScript Minifier Widget - Simplified & Template Compatible */
        .javascript-minifier-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .javascript-minifier-widget-container * { box-sizing: border-box; }

        .javascript-minifier-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .javascript-minifier-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .javascript-minifier-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .javascript-minifier-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 150px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .javascript-minifier-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }
        
        .javascript-minifier-stats {
            font-size: 0.9rem;
            color: var(--text-color-light);
            text-align: center;
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-md);
            background-color: var(--background-color-alt);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
        }

        .javascript-minifier-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .javascript-minifier-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .javascript-minifier-btn:hover { transform: translateY(-2px); }

        .javascript-minifier-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .javascript-minifier-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .javascript-minifier-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .javascript-minifier-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .javascript-minifier-btn-success {
            background-color: #10b981;
            color: white;
        }

        .javascript-minifier-btn-success:hover {
            background-color: #059669;
        }

        .javascript-minifier-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .javascript-minifier-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .javascript-minifier-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            white-space: pre-wrap;
            min-height: 150px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .javascript-minifier-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .javascript-minifier-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .javascript-minifier-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .javascript-minifier-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .javascript-minifier-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .javascript-minifier-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .javascript-minifier-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .javascript-minifier-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="javascript-beautifier"] .javascript-minifier-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="javascript-obfuscator"] .javascript-minifier-related-tool-icon { background: linear-gradient(145deg, #84CC16, #65A30D); }
        a[href*="css-minifier"] .javascript-minifier-related-tool-icon { background: linear-gradient(145deg, #4F46E5, #4338CA); }

        .javascript-minifier-related-tool-item:hover .javascript-minifier-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="javascript-beautifier"]:hover .javascript-minifier-related-tool-icon { background: linear-gradient(145deg, #F87171, #EF4444); }
        a[href*="javascript-obfuscator"]:hover .javascript-minifier-related-tool-icon { background: linear-gradient(145deg, #9fdd3b, #84CC16); }
        a[href*="css-minifier"]:hover .javascript-minifier-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }
        
        .javascript-minifier-related-tool-item { box-shadow: none; border: none; }
        .javascript-minifier-related-tool-item:hover { box-shadow: none; border: none; }
        .javascript-minifier-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .javascript-minifier-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .javascript-minifier-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .javascript-minifier-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .javascript-minifier-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .javascript-minifier-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .javascript-minifier-related-tool-item:hover .javascript-minifier-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .javascript-minifier-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .javascript-minifier-widget-title { font-size: 1.875rem; }
            .javascript-minifier-buttons { flex-direction: column; }
            .javascript-minifier-btn { flex: none; }
            .javascript-minifier-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .javascript-minifier-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .javascript-minifier-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .javascript-minifier-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .javascript-minifier-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .javascript-minifier-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .javascript-minifier-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .javascript-minifier-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .javascript-minifier-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .javascript-minifier-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .javascript-minifier-output::selection { background-color: var(--primary-color); color: white; }
        @media (max-width: 600px) { .javascript-minifier-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="javascript-minifier-widget-container">
        <h1 class="javascript-minifier-widget-title">JavaScript Minifier</h1>
        <p class="javascript-minifier-widget-description">
            Optimize your website's performance by compressing your JavaScript code. Our powerful JS minifier reduces file size for faster loading and execution.
        </p>
        
        <div class="javascript-minifier-input-group">
            <label for="jsMinifierInput" class="javascript-minifier-label">Paste your JavaScript code here:</label>
            <textarea 
                id="jsMinifierInput" 
                class="javascript-minifier-textarea"
                placeholder="/* Paste your readable, uncompressed JavaScript code here */&#10;&#10;function calculateSum(array) {&#10;  // This is a comment that will be removed&#10;  let sum = 0;&#10;  for (let i = 0; i < array.length; i++) {&#10;    sum += array[i];&#10;  }&#10;  return sum;&#10;}"
                rows="8"
            ></textarea>
        </div>

        <div class="javascript-minifier-buttons">
            <button class="javascript-minifier-btn javascript-minifier-btn-primary" onclick="JsMinifier.minify()">
                Minify JS
            </button>
            <button class="javascript-minifier-btn javascript-minifier-btn-secondary" onclick="JsMinifier.clear()">
                Clear All
            </button>
            <button class="javascript-minifier-btn javascript-minifier-btn-success" onclick="JsMinifier.copy()">
                Copy Result
            </button>
        </div>
        
        <div id="jsMinifierStats" class="javascript-minifier-stats" style="display: none;"></div>

        <div class="javascript-minifier-result">
            <h3 class="javascript-minifier-result-title">Minified JavaScript:</h3>
            <div class="javascript-minifier-output" id="jsMinifierOutput">
                Your compressed JavaScript will appear here...
            </div>
        </div>

        <div class="javascript-minifier-related-tools">
            <h3 class="javascript-minifier-related-tools-title">Related Tools</h3>
            <div class="javascript-minifier-related-tools-grid">
                <a href="/p/javascript-beautifier.html" class="javascript-minifier-related-tool-item" rel="noopener">
                    <div class="javascript-minifier-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="javascript-minifier-related-tool-name">JavaScript Beautifier</div>
                </a>
                <a href="/p/javascript-obfuscator.html" class="javascript-minifier-related-tool-item" rel="noopener">
                    <div class="javascript-minifier-related-tool-icon">
                        <i class="fas fa-user-secret"></i>
                    </div>
                    <div class="javascript-minifier-related-tool-name">Javascript Obfuscator</div>
                </a>
                <a href="/p/css-minifier.html" class="javascript-minifier-related-tool-item" rel="noopener">
                    <div class="javascript-minifier-related-tool-icon">
                        <i class="fab fa-css3-alt"></i>
                    </div>
                    <div class="javascript-minifier-related-tool-name">CSS Minifier</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Optimize Your Site with Our JavaScript Minifier</h2>
            <p>In web development, every kilobyte counts. Large JavaScript files can slow down your website, leading to a poor user experience and lower search engine rankings. Our <strong>JavaScript Minifier</strong> is a powerful online tool that compresses your JS code by removing unnecessary characters like whitespace, comments, and newlines. This process, known as minification, significantly reduces the file size of your scripts, making your website faster and more efficient without altering the code's functionality.</p>
            
            <h3>How to Use the JavaScript Minifier</h3>
            <ol>
                <li><strong>Enter Your Code:</strong> Copy your complete JavaScript code and paste it into the input field above.</li>
                <li><strong>Click to Minify:</strong> Press the "Minify JS" button. Our tool will process your code using an advanced compression algorithm.</li>
                <li><strong>Copy the Result:</strong> The compressed, production-ready code will appear in the output box. You can then copy it with a single click and use it in your project.</li>
            </ol>
            
            <h3>Why Minify Your JavaScript?</h3>
            <p>Minification is a crucial step in preparing a website for production. The primary benefit is a dramatic improvement in page load speed. Browsers can download smaller files much faster, which is especially important for users on slower mobile networks. This leads to reduced bandwidth costs, a better experience for your visitors, and improved Core Web Vitals, which are an important factor for Google's SEO ranking.</p>
        
            <h3>Frequently Asked Questions About JavaScript Minifier</h3>
            
            <h4>What is JS minification?</h4>
            <p>JS minification is the process of removing all unnecessary characters from JavaScript source code without changing its functionality. This includes removing whitespace, comments, newlines, and shortening variable names. The result is a much smaller file that loads faster in a web browser.</p>
            
            <h4>How do I minify a JavaScript file?</h4>
            <p>You can easily minify a JavaScript file by using an online tool like this one. Simply paste your JS code into the input box, click the 'Minify JS' button, and copy the compressed output. For automated workflows, developers often use build tools like Webpack, Rollup, or Vite, which incorporate minifiers like Terser.</p>
            
            <h4>Why is it important to minify JavaScript?</h4>
            <p>Minifying JavaScript is crucial for web performance. It significantly reduces the file size of your scripts, which leads to faster download times, lower bandwidth consumption, and a quicker initial page load. This improves the user experience and can positively impact SEO rankings.</p>
            
            <h4>What is the difference between minify, obfuscate, and uglify?</h4>
            <p><strong>Minify:</strong> Removes unnecessary characters to reduce file size. The code's logic remains readable if formatted. <strong>Uglify:</strong> A term often used for a specific tool (UglifyJS) that both minifies and mangles code (shortens variable/function names). <strong>Obfuscate:</strong> Actively rewrites code to make it extremely difficult for humans to understand or reverse-engineer, often for security or intellectual property protection. Obfuscation usually results in a larger file size.</p>
            
            <h4>Does minifying JavaScript break the code?</h4>
            <p>No, when done correctly with a reliable tool, minifying JavaScript does not break the code. Modern minifiers parse the code into an Abstract Syntax Tree (AST), perform optimizations, and then regenerate the code. This ensures that the logic and functionality remain identical, even though the text representation is completely different.</p>
        </div>

        <div class="javascript-minifier-features">
            <h3 class="javascript-minifier-features-title">Key Features:</h3>
            <ul class="javascript-minifier-features-list">
                <li class="javascript-minifier-features-item">Drastic file size reduction</li>
                <li class="javascript-minifier-features-item">Faster website load times</li>
                <li class="javascript-minifier-features-item">Removes comments & whitespace</li>
                <li class="javascript-minifier-features-item">Safe code compression</li>
                <li class="javascript-minifier-features-item">Lowers bandwidth usage</li>
                <li class="javascript-minifier-features-item">One-click copy to clipboard</li>
                <li class="javascript-minifier-features-item">Improves SEO performance</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="javascript-minifier-notification" id="jsMinifierNotification">
        ✓ Copied to clipboard!
    </div>
    
    <!-- Terser Minifier Library (CDN) -->
    <script src="https://cdn.jsdelivr.net/npm/terser/dist/bundle.min.js"></script>

    <script>
        // JavaScript Minifier
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('jsMinifierInput'),
                output: () => document.getElementById('jsMinifierOutput'),
                notification: () => document.getElementById('jsMinifierNotification'),
                stats: () => document.getElementById('jsMinifierStats')
            };

            window.JsMinifier = {
                async minify() {
                    const input = elements.input();
                    const output = elements.output();
                    const stats = elements.stats();
                    const jsText = input.value;

                    if (!jsText.trim()) {
                        output.textContent = 'Please enter JavaScript code to minify.';
                        output.style.color = '#dc2626';
                        stats.style.display = 'none';
                        return;
                    }

                    output.style.color = '';
                    
                    try {
                        if (typeof Terser === 'undefined') {
                            throw new Error('Minifier library (Terser) not loaded.');
                        }
                        
                        const result = await Terser.minify(jsText, {
                            mangle: true, // Shorten variable names
                            compress: true // Apply various compression techniques
                        });

                        if (result.error) {
                            throw result.error;
                        }
                        
                        const originalSize = new Blob([jsText]).size;
                        const minifiedSize = new Blob([result.code]).size;
                        const reduction = (((originalSize - minifiedSize) / originalSize) * 100).toFixed(2);

                        output.textContent = result.code;
                        stats.innerHTML = `Original Size: ${originalSize} bytes | Minified Size: ${minifiedSize} bytes | <strong>Reduction: ${reduction}%</strong>`;
                        stats.style.display = 'block';

                    } catch (error) {
                        output.textContent = `Error: ${error.message}. Please check your JavaScript for syntax errors.`;
                        output.style.color = '#dc2626';
                        stats.style.display = 'none';
                        console.error("Minify Error:", error);
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your compressed JavaScript will appear here...';
                    elements.output().style.color = '';
                    elements.stats().style.display = 'none';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your compressed JavaScript will appear here...', 'Please enter JavaScript code to minify.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        JsMinifier.minify();
                    }
                });
            });
        })();
    </script>
</body>
</html>