<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Alt Text Checker - Analyze Image Accessibility & SEO</title>
    <meta name="description" content="Check and analyze image alt text for accessibility and SEO. Ensure your images are properly described for screen readers and search engines with our free alt text checker.">
    <meta name="keywords" content="alt text checker, image accessibility, alt attribute checker, screen reader compatibility, image SEO, web accessibility">
    <link rel="canonical" href="https://www.webtoolskit.org/p/alt-text-checker.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Alt Text Checker - Analyze Image Accessibility & SEO",
        "description": "Check and analyze image alt text for accessibility and SEO. Ensure your images are properly described for screen readers and search engines with our free alt text checker.",
        "url": "https://www.webtoolskit.org/p/alt-text-checker.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Alt Text Checker",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Alt text analysis",
                "Image accessibility checking",
                "Screen reader compatibility",
                "SEO optimization",
                "Accessibility compliance"
            ]
        },
        "potentialAction": [
            { "@type": "CheckAction", "name": "Check Alt Text" },
            { "@type": "AnalyzeAction", "name": "Analyze Image Accessibility" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is alt text and why is it important?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Alt text (alternative text) is a description of an image that appears in the HTML alt attribute. It's crucial for web accessibility as it allows screen readers to describe images to visually impaired users. Alt text also helps with SEO by providing context to search engines about image content."
          }
        },
        {
          "@type": "Question",
          "name": "How long should alt text be?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Alt text should typically be between 5-125 characters. Keep it concise but descriptive enough to convey the image's meaning and context. Screen readers may cut off longer descriptions, and search engines prefer shorter, more focused alt text for SEO purposes."
          }
        },
        {
          "@type": "Question",
          "name": "What makes good alt text?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Good alt text is descriptive, concise, and contextually relevant. It should describe what's in the image without starting with 'Image of' or 'Picture of'. Include important details that convey the image's purpose and meaning within the content context."
          }
        },
        {
          "@type": "Question",
          "name": "Should decorative images have alt text?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Decorative images that don't add meaningful content should have empty alt attributes (alt=\"\") to indicate they're decorative. This tells screen readers to skip them. However, if an image serves a purpose or conveys information, it should have descriptive alt text."
          }
        },
        {
          "@type": "Question",
          "name": "How does alt text affect SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Alt text helps search engines understand image content, which can improve image search rankings and overall SEO. Well-written alt text with relevant keywords can help images appear in Google Image search results and provide additional context for page content."
          }
        }
      ]
    }
    </script>

    <style>
        /* Alt Text Checker Widget - Simplified & Template Compatible */
        .alt-text-checker-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .alt-text-checker-widget-container * { box-sizing: border-box; }

        .alt-text-checker-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .alt-text-checker-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .alt-text-checker-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .alt-text-checker-field {
            display: flex;
            flex-direction: column;
        }

        .alt-text-checker-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .alt-text-checker-input,
        .alt-text-checker-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .alt-text-checker-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .alt-text-checker-input:focus,
        .alt-text-checker-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .alt-text-checker-char-count {
            font-size: 0.875rem;
            color: var(--text-color-light);
            margin-top: var(--spacing-xs);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .alt-text-checker-char-count.optimal {
            color: #10b981;
        }

        .alt-text-checker-char-count.warning {
            color: #f59e0b;
        }

        .alt-text-checker-char-count.error {
            color: #dc2626;
        }

        .alt-text-checker-status {
            font-weight: 600;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .alt-text-checker-status.optimal {
            background-color: #d1fae5;
            color: #065f46;
        }

        .alt-text-checker-status.warning {
            background-color: #fef3c7;
            color: #92400e;
        }

        .alt-text-checker-status.error {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .alt-text-checker-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .alt-text-checker-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .alt-text-checker-btn:hover { transform: translateY(-2px); }

        .alt-text-checker-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .alt-text-checker-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .alt-text-checker-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .alt-text-checker-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .alt-text-checker-results {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .alt-text-checker-results.show {
            display: block;
        }

        .alt-text-checker-results-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .alt-text-checker-score-display {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            border: 2px solid var(--border-color);
        }

        .alt-text-checker-score-value {
            font-size: 3rem;
            font-weight: 800;
            margin-right: var(--spacing-md);
        }

        .alt-text-checker-score-value.excellent {
            color: #10b981;
        }

        .alt-text-checker-score-value.good {
            color: #3b82f6;
        }

        .alt-text-checker-score-value.fair {
            color: #f59e0b;
        }

        .alt-text-checker-score-value.poor {
            color: #dc2626;
        }

        .alt-text-checker-score-label {
            font-size: 1.125rem;
            color: var(--text-color-light);
        }

        .alt-text-checker-checks {
            display: grid;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }

        .alt-text-checker-check-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .alt-text-checker-check-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .alt-text-checker-check-icon.pass {
            background-color: #10b981;
        }

        .alt-text-checker-check-icon.fail {
            background-color: #dc2626;
        }

        .alt-text-checker-check-icon.warning {
            background-color: #f59e0b;
        }

        .alt-text-checker-check-text {
            flex: 1;
            color: var(--text-color);
        }

        .alt-text-checker-recommendations {
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
        }

        .alt-text-checker-recommendations h4 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
        }

        .alt-text-checker-recommendation-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .alt-text-checker-recommendation-item {
            padding: var(--spacing-xs) 0;
            color: var(--text-color-light);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .alt-text-checker-recommendation-item:before {
            content: "•";
            color: var(--primary-color);
            margin-right: var(--spacing-xs);
            font-weight: bold;
        }

        .alt-text-checker-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .alt-text-checker-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .alt-text-checker-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .alt-text-checker-widget-title { font-size: 1.875rem; }
            .alt-text-checker-buttons { flex-direction: column; }
            .alt-text-checker-btn { flex: none; }
            .alt-text-checker-score-display { flex-direction: column; text-align: center; }
            .alt-text-checker-score-value { margin-right: 0; margin-bottom: var(--spacing-sm); }
            .alt-text-checker-char-count { flex-direction: column; align-items: flex-start; gap: var(--spacing-xs); }
        }

        [data-theme="dark"] .alt-text-checker-input:focus,
        [data-theme="dark"] .alt-text-checker-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .alt-text-checker-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }

        .alt-text-checker-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="meta-tag-generator"] .alt-text-checker-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="title-meta-description-checker"] .alt-text-checker-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="keyword-density-checker"] .alt-text-checker-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }

        .alt-text-checker-related-tool-item:hover .alt-text-checker-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="meta-tag-generator"]:hover .alt-text-checker-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="title-meta-description-checker"]:hover .alt-text-checker-related-tool-icon { background: linear-gradient(145deg, #38d9a9, #20c997); }
        a[href*="keyword-density-checker"]:hover .alt-text-checker-related-tool-icon { background: linear-gradient(145deg, #f7ac2e, #e28417); }

        .alt-text-checker-related-tool-item { box-shadow: none; border: none; }
        .alt-text-checker-related-tool-item:hover { box-shadow: none; border: none; }
        .alt-text-checker-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .alt-text-checker-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .alt-text-checker-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .alt-text-checker-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .alt-text-checker-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .alt-text-checker-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .alt-text-checker-related-tool-item:hover .alt-text-checker-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .alt-text-checker-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .alt-text-checker-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .alt-text-checker-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .alt-text-checker-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .alt-text-checker-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .alt-text-checker-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .alt-text-checker-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .alt-text-checker-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .alt-text-checker-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .alt-text-checker-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .alt-text-checker-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .alt-text-checker-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .alt-text-checker-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .alt-text-checker-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="alt-text-checker-widget-container">
        <h1 class="alt-text-checker-widget-title">Alt Text Checker</h1>
        <p class="alt-text-checker-widget-description">
            Analyze your image alt text for accessibility and SEO. Ensure your images are properly described for screen readers and search engines.
        </p>
        
        <form class="alt-text-checker-form">
            <div class="alt-text-checker-field">
                <label for="imageUrl" class="alt-text-checker-label">Image URL (Optional):</label>
                <input 
                    type="url" 
                    id="imageUrl" 
                    class="alt-text-checker-input"
                    placeholder="https://example.com/image.jpg"
                />
            </div>

            <div class="alt-text-checker-field">
                <label for="altText" class="alt-text-checker-label">Alt Text to Check:</label>
                <textarea 
                    id="altText" 
                    class="alt-text-checker-textarea"
                    placeholder="Enter the alt text you want to analyze for accessibility and SEO..."
                ></textarea>
                <div class="alt-text-checker-char-count" id="altTextCharCount">
                    <span>0/125 characters</span>
                    <span class="alt-text-checker-status optimal" id="altTextStatus">OPTIMAL</span>
                </div>
            </div>

            <div class="alt-text-checker-field">
                <label for="imageContext" class="alt-text-checker-label">Image Context (Optional):</label>
                <textarea 
                    id="imageContext" 
                    class="alt-text-checker-textarea"
                    placeholder="Describe the context where this image appears (e.g., blog post about SEO, product page, etc.)"
                ></textarea>
            </div>
        </form>

        <div class="alt-text-checker-buttons">
            <button class="alt-text-checker-btn alt-text-checker-btn-primary" onclick="AltTextChecker.analyze()">
                Check Alt Text
            </button>
            <button class="alt-text-checker-btn alt-text-checker-btn-secondary" onclick="AltTextChecker.clear()">
                Clear All
            </button>
        </div>

        <div class="alt-text-checker-results" id="analysisResults">
            <h3 class="alt-text-checker-results-title">Alt Text Analysis Results</h3>

            <div class="alt-text-checker-score-display">
                <div class="alt-text-checker-score-value excellent" id="accessibilityScore">0</div>
                <div class="alt-text-checker-score-label">
                    Accessibility Score<br>
                    <small id="scoreDescription">out of 100</small>
                </div>
            </div>

            <div class="alt-text-checker-checks" id="accessibilityChecks">
                <!-- Accessibility checks will be populated here -->
            </div>

            <div class="alt-text-checker-recommendations" id="recommendations">
                <h4>Recommendations for Improvement:</h4>
                <ul class="alt-text-checker-recommendation-list" id="recommendationList">
                    <li class="alt-text-checker-recommendation-item">Enter alt text to see personalized recommendations.</li>
                </ul>
            </div>
        </div>

        <div class="alt-text-checker-related-tools">
            <h3 class="alt-text-checker-related-tools-title">Related Tools</h3>
            <div class="alt-text-checker-related-tools-grid">
                <a href="/p/meta-tag-generator.html" class="alt-text-checker-related-tool-item" rel="noopener">
                    <div class="alt-text-checker-related-tool-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="alt-text-checker-related-tool-name">Meta Tag Generator</div>
                </a>

                <a href="/p/title-meta-description-checker.html" class="alt-text-checker-related-tool-item" rel="noopener">
                    <div class="alt-text-checker-related-tool-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="alt-text-checker-related-tool-name">Title Meta Description Checker</div>
                </a>

                <a href="/p/keyword-density-checker.html" class="alt-text-checker-related-tool-item" rel="noopener">
                    <div class="alt-text-checker-related-tool-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="alt-text-checker-related-tool-name">Keyword Density Checker</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Alt Text Analysis for Accessibility and SEO</h2>
            <p>Our <strong>Alt Text Checker</strong> analyzes your image alternative text for both accessibility compliance and SEO optimization. Proper alt text is essential for web accessibility, helping screen readers describe images to visually impaired users, while also providing valuable context to search engines for better image SEO.</p>
            <p>Whether you're ensuring ADA compliance, improving accessibility, or optimizing for search engines, our tool provides comprehensive analysis and actionable recommendations to make your images accessible to everyone and discoverable by search engines.</p>

            <h3>How to Use the Alt Text Checker</h3>
            <ol>
                <li><strong>Enter Image Details:</strong> Optionally add the image URL and provide context about where the image appears.</li>
                <li><strong>Add Alt Text:</strong> Paste or type the alt text you want to analyze in the textarea.</li>
                <li><strong>Check Analysis:</strong> Click "Check Alt Text" to get a comprehensive accessibility and SEO score.</li>
                <li><strong>Review Results:</strong> Check your score, individual accessibility checks, and follow the recommendations for improvement.</li>
            </ol>

            <h3>Frequently Asked Questions About Alt Text</h3>

            <h4>What is alt text and why is it important?</h4>
            <p>Alt text (alternative text) is a description of an image that appears in the HTML alt attribute. It's crucial for web accessibility as it allows screen readers to describe images to visually impaired users. Alt text also helps with SEO by providing context to search engines about image content.</p>

            <h4>How long should alt text be?</h4>
            <p>Alt text should typically be between 5-125 characters. Keep it concise but descriptive enough to convey the image's meaning and context. Screen readers may cut off longer descriptions, and search engines prefer shorter, more focused alt text for SEO purposes.</p>

            <h4>What makes good alt text?</h4>
            <p>Good alt text is descriptive, concise, and contextually relevant. It should describe what's in the image without starting with 'Image of' or 'Picture of'. Include important details that convey the image's purpose and meaning within the content context.</p>

            <h4>Should decorative images have alt text?</h4>
            <p>Decorative images that don't add meaningful content should have empty alt attributes (alt="") to indicate they're decorative. This tells screen readers to skip them. However, if an image serves a purpose or conveys information, it should have descriptive alt text.</p>

            <h4>How does alt text affect SEO?</h4>
            <p>Alt text helps search engines understand image content, which can improve image search rankings and overall SEO. Well-written alt text with relevant keywords can help images appear in Google Image search results and provide additional context for page content.</p>
        </div>

        <div class="alt-text-checker-features">
            <h3 class="alt-text-checker-features-title">Key Features:</h3>
            <ul class="alt-text-checker-features-list">
                <li class="alt-text-checker-features-item" style="margin-bottom: 0.3em;">Comprehensive Accessibility Analysis</li>
                <li class="alt-text-checker-features-item" style="margin-bottom: 0.3em;">SEO Optimization Scoring</li>
                <li class="alt-text-checker-features-item" style="margin-bottom: 0.3em;">Screen Reader Compatibility Check</li>
                <li class="alt-text-checker-features-item" style="margin-bottom: 0.3em;">Character Length Validation</li>
                <li class="alt-text-checker-features-item" style="margin-bottom: 0.3em;">Context-Aware Recommendations</li>
                <li class="alt-text-checker-features-item" style="margin-bottom: 0.3em;">Real-time Feedback</li>
                <li class="alt-text-checker-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="alt-text-checker-notification" id="altTextNotification">
        ✓ Analysis completed successfully!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                imageUrl: () => document.getElementById('imageUrl'),
                altText: () => document.getElementById('altText'),
                imageContext: () => document.getElementById('imageContext'),
                altTextCharCount: () => document.getElementById('altTextCharCount'),
                altTextStatus: () => document.getElementById('altTextStatus'),
                analysisResults: () => document.getElementById('analysisResults'),
                accessibilityScore: () => document.getElementById('accessibilityScore'),
                scoreDescription: () => document.getElementById('scoreDescription'),
                accessibilityChecks: () => document.getElementById('accessibilityChecks'),
                recommendationList: () => document.getElementById('recommendationList'),
                notification: () => document.getElementById('altTextNotification')
            };

            function updateCharCount(input, counter, status, maxLength) {
                const length = input.value.length;
                const span = counter.querySelector('span');
                span.textContent = `${length}/${maxLength} characters`;

                // Remove all status classes
                counter.classList.remove('optimal', 'warning', 'error');
                status.classList.remove('optimal', 'warning', 'error');

                // Determine status
                if (length === 0) {
                    counter.classList.add('error');
                    status.classList.add('error');
                    status.textContent = 'EMPTY';
                } else if (length >= 5 && length <= 125) {
                    counter.classList.add('optimal');
                    status.classList.add('optimal');
                    status.textContent = 'OPTIMAL';
                } else if (length < 5) {
                    counter.classList.add('warning');
                    status.classList.add('warning');
                    status.textContent = 'TOO SHORT';
                } else {
                    counter.classList.add('error');
                    status.classList.add('error');
                    status.textContent = 'TOO LONG';
                }
            }

            function analyzeAltText(altText, context = '') {
                const analysis = {
                    score: 0,
                    checks: [],
                    recommendations: []
                };

                const text = altText.trim();
                const length = text.length;
                const words = text.split(/\s+/).filter(word => word.length > 0);
                const wordCount = words.length;

                // Check if alt text exists
                if (length === 0) {
                    analysis.checks.push({ text: 'Alt text is missing', status: 'fail' });
                    analysis.recommendations.push('Add descriptive alt text to make the image accessible');
                    return analysis;
                } else {
                    analysis.checks.push({ text: 'Alt text is present', status: 'pass' });
                    analysis.score += 20;
                }

                // Check length
                if (length >= 5 && length <= 125) {
                    analysis.checks.push({ text: 'Alt text length is optimal (5-125 characters)', status: 'pass' });
                    analysis.score += 20;
                } else if (length < 5) {
                    analysis.checks.push({ text: 'Alt text is too short (less than 5 characters)', status: 'fail' });
                    analysis.recommendations.push('Make alt text more descriptive (at least 5 characters)');
                } else {
                    analysis.checks.push({ text: 'Alt text is too long (over 125 characters)', status: 'warning' });
                    analysis.score += 10;
                    analysis.recommendations.push('Shorten alt text to under 125 characters for better screen reader compatibility');
                }

                // Check for redundant phrases
                const redundantPhrases = ['image of', 'picture of', 'photo of', 'graphic of', 'illustration of'];
                const hasRedundantPhrase = redundantPhrases.some(phrase => text.toLowerCase().includes(phrase));
                if (!hasRedundantPhrase) {
                    analysis.checks.push({ text: 'No redundant phrases (image of, picture of)', status: 'pass' });
                    analysis.score += 15;
                } else {
                    analysis.checks.push({ text: 'Contains redundant phrases', status: 'warning' });
                    analysis.score += 5;
                    analysis.recommendations.push('Remove redundant phrases like "image of" or "picture of"');
                }

                // Check for descriptiveness
                if (wordCount >= 2) {
                    analysis.checks.push({ text: 'Alt text is descriptive (multiple words)', status: 'pass' });
                    analysis.score += 15;
                } else {
                    analysis.checks.push({ text: 'Alt text may not be descriptive enough', status: 'warning' });
                    analysis.score += 5;
                    analysis.recommendations.push('Make alt text more descriptive with multiple relevant words');
                }

                // Check for proper capitalization
                if (text.charAt(0) === text.charAt(0).toUpperCase()) {
                    analysis.checks.push({ text: 'Starts with capital letter', status: 'pass' });
                    analysis.score += 10;
                } else {
                    analysis.checks.push({ text: 'Should start with capital letter', status: 'warning' });
                    analysis.score += 5;
                    analysis.recommendations.push('Start alt text with a capital letter');
                }

                // Check for ending punctuation
                const lastChar = text.charAt(text.length - 1);
                if (['.', '!', '?'].includes(lastChar)) {
                    analysis.checks.push({ text: 'Ends with appropriate punctuation', status: 'pass' });
                    analysis.score += 10;
                } else {
                    analysis.checks.push({ text: 'Consider adding ending punctuation', status: 'warning' });
                    analysis.score += 5;
                    analysis.recommendations.push('End alt text with appropriate punctuation (period, exclamation, or question mark)');
                }

                // Check for context relevance (if context provided)
                if (context.trim()) {
                    analysis.checks.push({ text: 'Context provided for better analysis', status: 'pass' });
                    analysis.score += 10;
                } else {
                    analysis.checks.push({ text: 'No context provided', status: 'warning' });
                    analysis.score += 5;
                }

                return analysis;
            }

            function getScoreClass(score) {
                if (score >= 85) return 'excellent';
                if (score >= 70) return 'good';
                if (score >= 50) return 'fair';
                return 'poor';
            }

            function getScoreDescription(score) {
                if (score >= 85) return 'Excellent accessibility';
                if (score >= 70) return 'Good accessibility';
                if (score >= 50) return 'Fair accessibility';
                return 'Needs improvement';
            }

            window.AltTextChecker = {
                analyze() {
                    const altText = elements.altText().value.trim();
                    const context = elements.imageContext().value.trim();

                    if (!altText) {
                        this.showNotification('Please enter alt text to analyze.');
                        return;
                    }

                    const analysis = analyzeAltText(altText, context);

                    // Update score display
                    const scoreElement = elements.accessibilityScore();
                    scoreElement.textContent = analysis.score;
                    scoreElement.className = `alt-text-checker-score-value ${getScoreClass(analysis.score)}`;
                    elements.scoreDescription().innerHTML = `${getScoreDescription(analysis.score)}<br><small>out of 100</small>`;

                    // Update checks
                    let checksHTML = '';
                    analysis.checks.forEach(check => {
                        const icon = check.status === 'pass' ? '✓' : check.status === 'warning' ? '!' : '✗';
                        checksHTML += `
                            <div class="alt-text-checker-check-item">
                                <div class="alt-text-checker-check-icon ${check.status}">${icon}</div>
                                <div class="alt-text-checker-check-text">${check.text}</div>
                            </div>
                        `;
                    });
                    elements.accessibilityChecks().innerHTML = checksHTML;

                    // Update recommendations
                    const recommendationList = elements.recommendationList();
                    recommendationList.innerHTML = '';

                    if (analysis.recommendations.length === 0) {
                        const li = document.createElement('li');
                        li.className = 'alt-text-checker-recommendation-item';
                        li.textContent = 'Great! Your alt text follows accessibility best practices.';
                        recommendationList.appendChild(li);
                    } else {
                        analysis.recommendations.forEach(rec => {
                            const li = document.createElement('li');
                            li.className = 'alt-text-checker-recommendation-item';
                            li.textContent = rec;
                            recommendationList.appendChild(li);
                        });
                    }

                    // Show results
                    elements.analysisResults().classList.add('show');

                    this.showNotification('✓ Analysis completed successfully!');
                },

                clear() {
                    elements.imageUrl().value = '';
                    elements.altText().value = '';
                    elements.imageContext().value = '';
                    elements.analysisResults().classList.remove('show');

                    // Reset character count
                    updateCharCount(elements.altText(), elements.altTextCharCount(), elements.altTextStatus(), 125);

                    this.showNotification('✓ Form cleared successfully!');
                },

                showNotification(message) {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Character count updates for alt text
                elements.altText().addEventListener('input', function() {
                    updateCharCount(this, elements.altTextCharCount(), elements.altTextStatus(), 125);
                });

                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        AltTextChecker.analyze();
                    }
                });

                // Initialize with default values
                updateCharCount(elements.altText(), elements.altTextCharCount(), elements.altTextStatus(), 125);
            });
        })();
    </script>
</body>
</html>
