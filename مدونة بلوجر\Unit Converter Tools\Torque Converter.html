<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Torque Converter - Convert Nm, ft-lb, in-lb & More</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Torque Converter - Convert Nm, ft-lb, in-lb & More",
        "description": "Instantly convert between torque units like Newton-meters (Nm), foot-pounds (ft-lb), and inch-pounds (in-lb). Free online tool for mechanics, engineers, and technicians.",
        "url": "https://www.webtoolskit.org/p/torque-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-01",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Torque Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Torque Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the torque converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "In an automatic transmission, a torque converter is a fluid coupling that connects the engine to the transmission. Its primary job is to allow the engine to keep running even when the car is stopped, and to multiply the engine's torque at low speeds to help the car accelerate smoothly."
          }
        },
        {
          "@type": "Question",
          "name": "What happens when a torque converter goes bad?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "When a torque converter fails, you may notice several symptoms: shuddering or vibrating, especially at certain speeds; slipping, where the engine RPMs increase but the car doesn't accelerate accordingly; overheating of the transmission; and unusual noises like whirring or clunking. Contaminated transmission fluid is also a key indicator."
          }
        },
        {
          "@type": "Question",
          "name": "Can you drive a car with a bad torque converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "While you might be able to drive a short distance with a bad torque converter, it is strongly advised against. Continuing to drive can cause catastrophic damage to the entire transmission system as metal debris from the failing converter circulates through the transmission fluid. This can turn a single component repair into a full transmission replacement."
          }
        },
        {
          "@type": "Question",
          "name": "How do you diagnose a bad torque converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Diagnosing a bad torque converter involves checking for common symptoms like slipping, shuddering, and overheating. A mechanic will typically perform a stall speed test, which measures the maximum engine RPM achieved when the transmission is in gear but the brakes are fully applied. If the stall speed is too high or too low, it points to a converter issue. Scanning for diagnostic trouble codes and checking the transmission fluid for debris are also key diagnostic steps."
          }
        },
        {
          "@type": "Question",
          "name": "Can you replace a torque converter without removing the transmission?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, it is not possible to replace a torque converter without removing the transmission from the vehicle. The torque converter is located between the engine's flexplate and the transmission's input shaft, housed within the transmission bell housing. To access and replace it, the entire transmission assembly must be unbolted and separated from the engine."
          }
        }
      ]
    }
    </script>

    <style>
        /* Torque Converter Widget - Simplified & Template Compatible */
        .torque-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .torque-converter-widget-container * { box-sizing: border-box; }

        .torque-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .torque-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .torque-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .torque-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .torque-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .torque-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .torque-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .torque-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .torque-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .torque-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .torque-converter-btn:hover { transform: translateY(-2px); }

        .torque-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .torque-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .torque-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .torque-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .torque-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .torque-converter-btn-success:hover {
            background-color: #059669;
        }

        .torque-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .torque-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .torque-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .torque-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .torque-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .torque-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .torque-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .torque-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .torque-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .torque-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .torque-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="power-converter"] .torque-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="weight-converter"] .torque-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="length-converter"] .torque-converter-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }

        .torque-converter-related-tool-item:hover .torque-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="power-converter"]:hover .torque-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        a[href*="weight-converter"]:hover .torque-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        a[href*="length-converter"]:hover .torque-converter-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        
        .torque-converter-related-tool-item { box-shadow: none; border: none; }
        .torque-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .torque-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .torque-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .torque-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .torque-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .torque-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .torque-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .torque-converter-related-tool-item:hover .torque-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .torque-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .torque-converter-widget-title { font-size: 1.875rem; }
            .torque-converter-buttons { flex-direction: column; }
            .torque-converter-btn { flex: none; }
            .torque-converter-input-group { grid-template-columns: 1fr; }
            .torque-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .torque-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .torque-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .torque-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .torque-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .torque-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .torque-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .torque-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .torque-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .torque-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .torque-converter-output::selection { background-color: var(--primary-color); color: white; }
        .torque-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .torque-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="torque-converter-widget-container">
        <h1 class="torque-converter-widget-title">Torque Converter</h1>
        <p class="torque-converter-widget-description">
            This page features a unit converter for torque measurements (Nm, ft-lb) and provides helpful information on the automotive part with the same name.
        </p>
        
        <div class="torque-converter-input-group">
            <label for="torqueFromInput" class="torque-converter-label">From:</label>
            <input 
                type="number" 
                id="torqueFromInput" 
                class="torque-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="torqueFromUnit" class="torque-converter-select">
                <option value="nm" selected>Newton-meter (Nm)</option>
                <option value="ftlb">Foot-pound (ft-lb)</option>
                <option value="inlb">Inch-pound (in-lb)</option>
                <option value="kgfm">Kilogram-force meter (kgf-m)</option>
            </select>
        </div>

        <div class="torque-converter-input-group">
            <label for="torqueToInput" class="torque-converter-label">To:</label>
            <input 
                type="number" 
                id="torqueToInput" 
                class="torque-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="torqueToUnit" class="torque-converter-select">
                <option value="nm">Newton-meter (Nm)</option>
                <option value="ftlb" selected>Foot-pound (ft-lb)</option>
                <option value="inlb">Inch-pound (in-lb)</option>
                <option value="kgfm">Kilogram-force meter (kgf-m)</option>
            </select>
        </div>

        <div class="torque-converter-buttons">
            <button class="torque-converter-btn torque-converter-btn-primary" onclick="TorqueConverter.convert()">
                Convert Torque
            </button>
            <button class="torque-converter-btn torque-converter-btn-secondary" onclick="TorqueConverter.clear()">
                Clear All
            </button>
            <button class="torque-converter-btn torque-converter-btn-success" onclick="TorqueConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="torque-converter-result">
            <h3 class="torque-converter-result-title">Conversion Result:</h3>
            <div class="torque-converter-output" id="torqueConverterOutput">
                Your converted torque will appear here...
            </div>
        </div>

        <div class="torque-converter-related-tools">
            <h3 class="torque-converter-related-tools-title">Related Tools</h3>
            <div class="torque-converter-related-tools-grid">
                <a href="/p/power-converter.html" class="torque-converter-related-tool-item" rel="noopener">
                    <div class="torque-converter-related-tool-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="torque-converter-related-tool-name">Power Converter</div>
                </a>
                <a href="/p/weight-converter.html" class="torque-converter-related-tool-item" rel="noopener">
                    <div class="torque-converter-related-tool-icon">
                        <i class="fas fa-weight-hanging"></i>
                    </div>
                    <div class="torque-converter-related-tool-name">Weight Converter</div>
                </a>
                <a href="/p/length-converter.html" class="torque-converter-related-tool-item" rel="noopener">
                    <div class="torque-converter-related-tool-icon">
                        <i class="fas fa-ruler-horizontal"></i>
                    </div>
                    <div class="torque-converter-related-tool-name">Length Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Understanding Torque: Unit Conversion and Automotive Parts</h2>
            <p>Torque is a fundamental concept in mechanics and engineering, representing the rotational force applied to an object. It's what makes things turn, from a simple screw to the wheels of a car. Mechanics and engineers often need to convert between different units of torque, such as Newton-meters (Nm) and foot-pounds (ft-lb). Our free <strong>Torque Converter</strong> tool is designed for exactly that purpose, providing quick and accurate unit conversions.</p>
            <p>Confusingly, the term "torque converter" also refers to a critical component in vehicles with automatic transmissions. This device acts as a fluid coupling between the engine and transmission, allowing the car to stop without stalling the engine. Below, we provide answers to common questions about this automotive part, alongside our handy unit conversion tool.</p>

            <h3>How to Use the Torque Unit Converter</h3>
            <ol>
                <li><strong>Enter Your Value:</strong> Type the numeric value of the torque you want to convert.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., ft-lb) and your target unit (e.g., Nm) from the dropdown lists.</li>
                <li><strong>Convert:</strong> Click the "Convert Torque" button for an instant calculation.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to easily copy the value for your specifications or reports.</li>
            </ol>

            <h3>Frequently Asked Questions About Automotive Torque Converters</h3>
            
            <h4>What is the torque converter?</h4>
            <p>In an automatic transmission, a torque converter is a fluid coupling that connects the engine to the transmission. Its primary job is to allow the engine to keep running even when the car is stopped, and to multiply the engine's torque at low speeds to help the car accelerate smoothly.</p>

            <h4>What happens when a torque converter goes bad?</h4>
            <p>When a torque converter fails, you may notice several symptoms: shuddering or vibrating, especially at certain speeds; slipping, where the engine RPMs increase but the car doesn't accelerate accordingly; overheating of the transmission; and unusual noises like whirring or clunking. Contaminated transmission fluid is also a key indicator.</p>

            <h4>Can you drive a car with a bad torque converter?</h4>
            <p>While you might be able to drive a short distance with a bad torque converter, it is strongly advised against. Continuing to drive can cause catastrophic damage to the entire transmission system as metal debris from the failing converter circulates through the transmission fluid. This can turn a single component repair into a full transmission replacement.</p>

            <h4>How do you diagnose a bad torque converter?</h4>
            <p>Diagnosing a bad torque converter involves checking for common symptoms like slipping, shuddering, and overheating. A mechanic will typically perform a stall speed test, which measures the maximum engine RPM achieved when the transmission is in gear but the brakes are fully applied. If the stall speed is too high or too low, it points to a converter issue. Scanning for diagnostic trouble codes and checking the transmission fluid for debris are also key diagnostic steps.</p>

            <h4>Can you replace a torque converter without removing the transmission?</h4>
            <p>No, it is not possible to replace a torque converter without removing the transmission from the vehicle. The torque converter is located between the engine's flexplate and the transmission's input shaft, housed within the transmission bell housing. To access and replace it, the entire transmission assembly must be unbolted and separated from the engine.</p>
        </div>

        <div class="torque-converter-features">
            <h3 class="torque-converter-features-title">Key Features of the Unit Converter:</h3>
            <ul class="torque-converter-features-list">
                <li class="torque-converter-features-item" style="margin-bottom: 0.3em;">Converts Nm, ft-lb, in-lb & more</li>
                <li class="torque-converter-features-item" style="margin-bottom: 0.3em;">Ideal for mechanics & engineers</li>
                <li class="torque-converter-features-item" style="margin-bottom: 0.3em;">High-precision calculations</li>
                <li class="torque-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="torque-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side processing</li>
                <li class="torque-converter-features-item" style="margin-bottom: 0.3em;">Responsive for all devices</li>
                <li class="torque-converter-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="torque-converter-notification" id="torqueConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Torque Converter
        (function() {
            'use strict';

            // Conversion factors to Newton-meters (Nm)
            const conversionFactors = {
                'nm': 1,
                'ftlb': 1.35581795,
                'inlb': 0.11298483,
                'kgfm': 9.80665
            };

            const elements = {
                fromInput: () => document.getElementById('torqueFromInput'),
                toInput: () => document.getElementById('torqueToInput'),
                fromUnit: () => document.getElementById('torqueFromUnit'),
                toUnit: () => document.getElementById('torqueToUnit'),
                output: () => document.getElementById('torqueConverterOutput'),
                notification: () => document.getElementById('torqueConverterNotification')
            };

            window.TorqueConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to base unit (Nm) first, then to target unit
                    const valueInNm = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInNm / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-6 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(10)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'nm': 'Nm', 'ftlb': 'ft-lb', 'inlb': 'in-lb', 'kgfm': 'kgf-m' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted torque will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        TorqueConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>