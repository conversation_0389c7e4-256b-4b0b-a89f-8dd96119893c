<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Password Generator - Create Strong Secure Passwords Online</title>
    <meta name="description" content="Generate strong, secure passwords instantly with our free Password Generator. Create customizable passwords with letters, numbers, and symbols for maximum security.">
    <meta name="keywords" content="password generator, strong password, secure password, random password, password creator, password maker">
    <link rel="canonical" href="https://www.webtoolskit.org/p/password-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Password Generator - Create Strong Secure Passwords Online",
        "description": "Generate strong, secure passwords instantly with our free Password Generator. Create customizable passwords with letters, numbers, and symbols for maximum security.",
        "url": "https://www.webtoolskit.org/p/password-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Password Generator",
            "applicationCategory": "SecurityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Strong password generation",
                "Customizable length",
                "Character set options",
                "Password strength indicator",
                "Secure random generation"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Password" },
            { "@type": "CopyAction", "name": "Copy Generated Password" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I create a strong password?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use our Password Generator above to create strong passwords. Set the length to at least 12 characters, include uppercase and lowercase letters, numbers, and special characters. Avoid using personal information, common words, or predictable patterns."
          }
        },
        {
          "@type": "Question",
          "name": "What makes a password secure?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A secure password is long (12+ characters), uses a mix of character types (uppercase, lowercase, numbers, symbols), is unique for each account, and doesn't contain personal information or common words. Our generator creates passwords that meet all these criteria."
          }
        },
        {
          "@type": "Question",
          "name": "How long should my password be?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Security experts recommend passwords be at least 12-16 characters long. Longer passwords are exponentially harder to crack. Our generator allows you to create passwords up to 128 characters for maximum security."
          }
        },
        {
          "@type": "Question",
          "name": "Should I use special characters in passwords?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, special characters significantly increase password strength by expanding the character set. However, ensure the service you're using accepts the special characters. Our generator lets you customize which character types to include."
          }
        },
        {
          "@type": "Question",
          "name": "How often should I change my passwords?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Change passwords immediately if there's a security breach, every 90 days for high-security accounts, and annually for regular accounts. More importantly, use unique passwords for each account and enable two-factor authentication when available."
          }
        }
      ]
    }
    </script>

    <style>
        /* Password Generator Widget - Simplified & Template Compatible */
        .password-generator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .password-generator-widget-container * { box-sizing: border-box; }

        .password-generator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .password-generator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .password-generator-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .password-generator-field {
            display: flex;
            flex-direction: column;
        }

        .password-generator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .password-generator-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .password-generator-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .password-generator-range {
            width: 100%;
            margin: var(--spacing-sm) 0;
        }

        .password-generator-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin: var(--spacing-lg) 0;
        }

        .password-generator-checkbox {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            transition: var(--transition-base);
        }

        .password-generator-checkbox:hover {
            background-color: var(--background-color-alt);
        }

        .password-generator-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        .password-generator-checkbox label {
            font-weight: 500;
            color: var(--text-color);
            cursor: pointer;
            user-select: none;
        }

        .password-generator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .password-generator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .password-generator-btn:hover { transform: translateY(-2px); }

        .password-generator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .password-generator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .password-generator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .password-generator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .password-generator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .password-generator-btn-success:hover {
            background-color: #059669;
        }

        .password-generator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .password-generator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .password-generator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 1.125rem;
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
            display: flex;
            align-items: center;
        }

        .password-generator-strength {
            margin-top: var(--spacing-md);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            text-align: center;
        }

        .strength-weak { background-color: #fef2f2; color: #dc2626; border: 1px solid #fecaca; }
        .strength-medium { background-color: #fef3c7; color: #d97706; border: 1px solid #fde68a; }
        .strength-strong { background-color: #f0fdf4; color: #16a34a; border: 1px solid #bbf7d0; }

        .password-generator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .password-generator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        @media (max-width: 768px) {
            .password-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .password-generator-widget-title { font-size: 1.875rem; }
            .password-generator-buttons { flex-direction: column; }
            .password-generator-btn { flex: none; }
            .password-generator-options { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .password-generator-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .password-generator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .password-generator-output::selection { background-color: var(--primary-color); color: white; }

        .password-generator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="md5-generator"] .password-generator-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="base64-encode"] .password-generator-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="base64-decode"] .password-generator-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }

        .password-generator-related-tool-item:hover .password-generator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="md5-generator"]:hover .password-generator-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="base64-encode"]:hover .password-generator-related-tool-icon { background: linear-gradient(145deg, #fbbf24, #f59e0b); }
        a[href*="base64-decode"]:hover .password-generator-related-tool-icon { background: linear-gradient(145deg, #34d399, #10b981); }

        .password-generator-related-tool-item { box-shadow: none; border: none; }
        .password-generator-related-tool-item:hover { box-shadow: none; border: none; }
        .password-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .password-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .password-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .password-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .password-generator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .password-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .password-generator-related-tool-item:hover .password-generator-related-tool-name { color: var(--primary-color); }

        .password-generator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .password-generator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .password-generator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .password-generator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .password-generator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .password-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .password-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .password-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .password-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .password-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .password-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .password-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .password-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .password-generator-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="password-generator-widget-container">
        <h1 class="password-generator-widget-title">Password Generator</h1>
        <p class="password-generator-widget-description">
            Generate strong, secure passwords with customizable length and character sets. Create unique passwords for maximum security and protection.
        </p>

        <form class="password-generator-form">
            <div class="password-generator-field">
                <label for="passwordLength" class="password-generator-label">Password Length: <span id="lengthValue">16</span></label>
                <input
                    type="range"
                    id="passwordLength"
                    class="password-generator-range"
                    min="4"
                    max="128"
                    value="16"
                />
            </div>

            <div class="password-generator-options">
                <div class="password-generator-checkbox">
                    <input type="checkbox" id="includeUppercase" checked>
                    <label for="includeUppercase">Uppercase Letters (A-Z)</label>
                </div>
                <div class="password-generator-checkbox">
                    <input type="checkbox" id="includeLowercase" checked>
                    <label for="includeLowercase">Lowercase Letters (a-z)</label>
                </div>
                <div class="password-generator-checkbox">
                    <input type="checkbox" id="includeNumbers" checked>
                    <label for="includeNumbers">Numbers (0-9)</label>
                </div>
                <div class="password-generator-checkbox">
                    <input type="checkbox" id="includeSymbols" checked>
                    <label for="includeSymbols">Special Characters (!@#$%^&*)</label>
                </div>
            </div>
        </form>

        <div class="password-generator-buttons">
            <button class="password-generator-btn password-generator-btn-primary" onclick="PasswordGenerator.generate()">
                Generate Password
            </button>
            <button class="password-generator-btn password-generator-btn-secondary" onclick="PasswordGenerator.regenerate()">
                Generate New
            </button>
            <button class="password-generator-btn password-generator-btn-success" onclick="PasswordGenerator.copy()">
                Copy Password
            </button>
        </div>

        <div class="password-generator-result">
            <h3 class="password-generator-result-title">Generated Password:</h3>
            <div class="password-generator-output" id="passwordOutput">Click "Generate Password" to create a secure password...</div>
            <div class="password-generator-strength" id="passwordStrength" style="display: none;"></div>
        </div>

        <div class="password-generator-related-tools">
            <h3 class="password-generator-related-tools-title">Related Tools</h3>
            <div class="password-generator-related-tools-grid">
                <a href="/p/md5-generator.html" class="password-generator-related-tool-item" rel="noopener">
                    <div class="password-generator-related-tool-icon">
                        <i class="fas fa-fingerprint"></i>
                    </div>
                    <div class="password-generator-related-tool-name">MD5 Generator</div>
                </a>

                <a href="/p/base64-encode.html" class="password-generator-related-tool-item" rel="noopener">
                    <div class="password-generator-related-tool-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="password-generator-related-tool-name">Base64 Encode</div>
                </a>

                <a href="/p/base64-decode.html" class="password-generator-related-tool-item" rel="noopener">
                    <div class="password-generator-related-tool-icon">
                        <i class="fas fa-unlock-alt"></i>
                    </div>
                    <div class="password-generator-related-tool-name">Base64 Decode</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Password Generator for Maximum Security</h2>
            <p>Our <strong>Password Generator</strong> creates strong, secure passwords that protect your accounts from cyber threats. With customizable length and character options, you can generate passwords that meet any security requirement while ensuring maximum protection against brute force attacks and password cracking attempts.</p>
            <p>Perfect for individuals, businesses, and security professionals who need reliable password generation. Whether you're securing personal accounts, business systems, or client data, our tool provides cryptographically secure random passwords that follow industry best practices.</p>

            <h3>How to Use the Password Generator</h3>
            <ol>
                <li><strong>Set Password Length:</strong> Use the slider to choose your desired password length (4-128 characters).</li>
                <li><strong>Select Character Types:</strong> Check the boxes for uppercase, lowercase, numbers, and special characters.</li>
                <li><strong>Generate Password:</strong> Click "Generate Password" to create a secure password instantly.</li>
                <li><strong>Copy and Use:</strong> Click "Copy Password" to copy your new password to the clipboard.</li>
            </ol>

            <h3>Frequently Asked Questions About Password Security</h3>

            <h4>How do I create a strong password?</h4>
            <p>Use our Password Generator above to create strong passwords. Set the length to at least 12 characters, include uppercase and lowercase letters, numbers, and special characters. Avoid using personal information, common words, or predictable patterns.</p>

            <h4>What makes a password secure?</h4>
            <p>A secure password is long (12+ characters), uses a mix of character types (uppercase, lowercase, numbers, symbols), is unique for each account, and doesn't contain personal information or common words. Our generator creates passwords that meet all these criteria.</p>

            <h4>How long should my password be?</h4>
            <p>Security experts recommend passwords be at least 12-16 characters long. Longer passwords are exponentially harder to crack. Our generator allows you to create passwords up to 128 characters for maximum security.</p>

            <h4>Should I use special characters in passwords?</h4>
            <p>Yes, special characters significantly increase password strength by expanding the character set. However, ensure the service you're using accepts the special characters. Our generator lets you customize which character types to include.</p>

            <h4>How often should I change my passwords?</h4>
            <p>Change passwords immediately if there's a security breach, every 90 days for high-security accounts, and annually for regular accounts. More importantly, use unique passwords for each account and enable two-factor authentication when available.</p>
        </div>

        <div class="password-generator-features">
            <h3 class="password-generator-features-title">Key Features:</h3>
            <ul class="password-generator-features-list">
                <li class="password-generator-features-item" style="margin-bottom: 0.3em;">Cryptographically Secure Generation</li>
                <li class="password-generator-features-item" style="margin-bottom: 0.3em;">Customizable Length (4-128 chars)</li>
                <li class="password-generator-features-item" style="margin-bottom: 0.3em;">Multiple Character Set Options</li>
                <li class="password-generator-features-item" style="margin-bottom: 0.3em;">Password Strength Indicator</li>
                <li class="password-generator-features-item" style="margin-bottom: 0.3em;">One-Click Copy to Clipboard</li>
                <li class="password-generator-features-item" style="margin-bottom: 0.3em;">Mobile-Friendly Interface</li>
                <li class="password-generator-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="password-generator-notification" id="passwordNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                lengthSlider: () => document.getElementById('passwordLength'),
                lengthValue: () => document.getElementById('lengthValue'),
                uppercase: () => document.getElementById('includeUppercase'),
                lowercase: () => document.getElementById('includeLowercase'),
                numbers: () => document.getElementById('includeNumbers'),
                symbols: () => document.getElementById('includeSymbols'),
                output: () => document.getElementById('passwordOutput'),
                strength: () => document.getElementById('passwordStrength'),
                notification: () => document.getElementById('passwordNotification')
            };

            const characterSets = {
                uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
                lowercase: 'abcdefghijklmnopqrstuvwxyz',
                numbers: '0123456789',
                symbols: '!@#$%^&*()_+-=[]{}|;:,.<>?'
            };

            function getRandomChar(charset) {
                const randomArray = new Uint32Array(1);
                crypto.getRandomValues(randomArray);
                return charset[randomArray[0] % charset.length];
            }

            function generateSecurePassword(length, options) {
                let charset = '';
                let password = '';

                // Build character set based on options
                if (options.uppercase) charset += characterSets.uppercase;
                if (options.lowercase) charset += characterSets.lowercase;
                if (options.numbers) charset += characterSets.numbers;
                if (options.symbols) charset += characterSets.symbols;

                if (charset === '') {
                    throw new Error('At least one character type must be selected');
                }

                // Ensure at least one character from each selected type
                if (options.uppercase) password += getRandomChar(characterSets.uppercase);
                if (options.lowercase) password += getRandomChar(characterSets.lowercase);
                if (options.numbers) password += getRandomChar(characterSets.numbers);
                if (options.symbols) password += getRandomChar(characterSets.symbols);

                // Fill remaining length with random characters
                for (let i = password.length; i < length; i++) {
                    password += getRandomChar(charset);
                }

                // Shuffle the password to randomize character positions
                return password.split('').sort(() => Math.random() - 0.5).join('');
            }

            function calculatePasswordStrength(password) {
                let score = 0;
                let feedback = [];

                // Length scoring
                if (password.length >= 12) score += 25;
                else if (password.length >= 8) score += 15;
                else score += 5;

                // Character variety scoring
                if (/[a-z]/.test(password)) score += 15;
                if (/[A-Z]/.test(password)) score += 15;
                if (/[0-9]/.test(password)) score += 15;
                if (/[^A-Za-z0-9]/.test(password)) score += 20;

                // Bonus for length
                if (password.length >= 16) score += 10;

                // Determine strength level
                if (score >= 80) return { level: 'strong', text: 'Strong Password', class: 'strength-strong' };
                else if (score >= 50) return { level: 'medium', text: 'Medium Strength', class: 'strength-medium' };
                else return { level: 'weak', text: 'Weak Password', class: 'strength-weak' };
            }

            function updateStrengthIndicator(password) {
                const strength = calculatePasswordStrength(password);
                const strengthElement = elements.strength();

                strengthElement.textContent = strength.text;
                strengthElement.className = `password-generator-strength ${strength.class}`;
                strengthElement.style.display = 'block';
            }

            window.PasswordGenerator = {
                generate() {
                    const length = parseInt(elements.lengthSlider().value);
                    const options = {
                        uppercase: elements.uppercase().checked,
                        lowercase: elements.lowercase().checked,
                        numbers: elements.numbers().checked,
                        symbols: elements.symbols().checked
                    };

                    const output = elements.output();

                    try {
                        const password = generateSecurePassword(length, options);
                        output.textContent = password;
                        output.style.color = '';
                        updateStrengthIndicator(password);
                    } catch (error) {
                        output.textContent = 'Error: Please select at least one character type.';
                        output.style.color = '#dc2626';
                        elements.strength().style.display = 'none';
                    }
                },

                regenerate() {
                    this.generate();
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Click "Generate Password" to create a secure password...' || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Update length display when slider changes
                elements.lengthSlider().addEventListener('input', function() {
                    elements.lengthValue().textContent = this.value;
                });

                // Auto-generate password when options change
                const checkboxes = [elements.uppercase(), elements.lowercase(), elements.numbers(), elements.symbols()];
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        // Only auto-generate if there's already a password
                        if (elements.output().textContent !== 'Click "Generate Password" to create a secure password...') {
                            PasswordGenerator.generate();
                        }
                    });
                });

                // Generate initial password
                PasswordGenerator.generate();

                // Enter key shortcut
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        PasswordGenerator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
