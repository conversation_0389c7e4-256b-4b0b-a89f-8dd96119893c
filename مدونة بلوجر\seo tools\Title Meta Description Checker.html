<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Title Meta Description Checker - SERP Preview Tool</title>
    <meta name="description" content="Check your meta titles and descriptions with our free SERP preview tool. Optimize title tag length, meta description length, and see how your pages appear in Google search results.">
    <meta name="keywords" content="title meta description checker, SERP preview, meta title length, meta description length, title tag checker, SEO preview">
    <link rel="canonical" href="https://www.webtoolskit.org/p/title-meta-description-checker.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Title Meta Description Checker - SERP Preview Tool",
        "description": "Check your meta titles and descriptions with our free SERP preview tool. Optimize title tag length, meta description length, and see how your pages appear in Google search results.",
        "url": "https://www.webtoolskit.org/p/title-meta-description-checker.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Title Meta Description Checker",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "SERP preview simulation",
                "Title tag length checking",
                "Meta description length validation",
                "Real-time character counting",
                "SEO optimization recommendations"
            ]
        },
        "potentialAction": [
            { "@type": "CheckAction", "name": "Check Title and Description" },
            { "@type": "ViewAction", "name": "Preview SERP Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the ideal length for meta titles?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The ideal meta title length is 50-60 characters or approximately 580 pixels. This ensures your title displays fully in Google search results without being truncated. Titles longer than 60 characters may be cut off with an ellipsis (...) in search results."
          }
        },
        {
          "@type": "Question",
          "name": "What is the recommended meta description length?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The recommended meta description length is 150-160 characters. Google typically displays up to 160 characters in search results, though this can vary slightly. Descriptions longer than 160 characters will be truncated, potentially cutting off important information."
          }
        },
        {
          "@type": "Question",
          "name": "Why should I check my title and meta description length?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Checking your title and meta description length ensures they display properly in search results, improving click-through rates and user experience. Properly sized titles and descriptions help convey your message effectively and can significantly impact your SEO performance."
          }
        },
        {
          "@type": "Question",
          "name": "What happens if my meta title is too long?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "If your meta title exceeds 60 characters, Google will truncate it with an ellipsis (...) in search results. This can cut off important keywords or make your title appear incomplete, potentially reducing click-through rates and harming your SEO performance."
          }
        },
        {
          "@type": "Question",
          "name": "How does SERP preview help with SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "SERP preview shows exactly how your page will appear in Google search results, allowing you to optimize your title and description for maximum impact. You can test different variations, ensure proper length, and create compelling snippets that encourage users to click on your result."
          }
        }
      ]
    }
    </script>

    <style>
        /* Title Meta Description Checker Widget - Simplified & Template Compatible */
        .title-meta-checker-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .title-meta-checker-widget-container * { box-sizing: border-box; }

        .title-meta-checker-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title-meta-checker-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .title-meta-checker-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .title-meta-checker-field {
            display: flex;
            flex-direction: column;
        }

        .title-meta-checker-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .title-meta-checker-input,
        .title-meta-checker-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .title-meta-checker-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .title-meta-checker-input:focus,
        .title-meta-checker-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .title-meta-checker-char-count {
            font-size: 0.875rem;
            color: var(--text-color-light);
            margin-top: var(--spacing-xs);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .title-meta-checker-char-count.optimal {
            color: #10b981;
        }

        .title-meta-checker-char-count.warning {
            color: #f59e0b;
        }

        .title-meta-checker-char-count.error {
            color: #dc2626;
        }

        .title-meta-checker-status {
            font-weight: 600;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .title-meta-checker-status.optimal {
            background-color: #d1fae5;
            color: #065f46;
        }

        .title-meta-checker-status.warning {
            background-color: #fef3c7;
            color: #92400e;
        }

        .title-meta-checker-status.error {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .title-meta-checker-serp-preview {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .title-meta-checker-serp-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
            text-align: center;
        }

        .title-meta-checker-serp-result {
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            font-family: arial, sans-serif;
        }

        .title-meta-checker-serp-url {
            color: #1a0dab;
            font-size: 14px;
            line-height: 1.3;
            margin-bottom: 2px;
            word-break: break-all;
        }

        .title-meta-checker-serp-link {
            color: #1a0dab;
            font-size: 20px;
            line-height: 1.3;
            margin-bottom: 3px;
            text-decoration: none;
            font-weight: normal;
            display: block;
            word-wrap: break-word;
        }

        .title-meta-checker-serp-link:hover {
            text-decoration: underline;
        }

        .title-meta-checker-serp-description {
            color: #4d5156;
            font-size: 14px;
            line-height: 1.58;
            word-wrap: break-word;
        }

        .title-meta-checker-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .title-meta-checker-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .title-meta-checker-btn:hover { transform: translateY(-2px); }

        .title-meta-checker-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .title-meta-checker-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .title-meta-checker-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .title-meta-checker-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .title-meta-checker-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .title-meta-checker-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .title-meta-checker-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .title-meta-checker-widget-title { font-size: 1.875rem; }
            .title-meta-checker-buttons { flex-direction: column; }
            .title-meta-checker-btn { flex: none; }
            .title-meta-checker-char-count { flex-direction: column; align-items: flex-start; gap: var(--spacing-xs); }
        }

        [data-theme="dark"] .title-meta-checker-input:focus,
        [data-theme="dark"] .title-meta-checker-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .title-meta-checker-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        
        [data-theme="dark"] .title-meta-checker-serp-url { color: #8ab4f8; }
        [data-theme="dark"] .title-meta-checker-serp-link { color: #8ab4f8; }
        [data-theme="dark"] .title-meta-checker-serp-description { color: #bdc1c6; }

        .title-meta-checker-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="meta-tag-generator"] .title-meta-checker-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="keyword-density-checker"] .title-meta-checker-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="open-graph-tag-generator"] .title-meta-checker-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }

        .title-meta-checker-related-tool-item:hover .title-meta-checker-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="meta-tag-generator"]:hover .title-meta-checker-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="keyword-density-checker"]:hover .title-meta-checker-related-tool-icon { background: linear-gradient(145deg, #f7ac2e, #e28417); }
        a[href*="open-graph-tag-generator"]:hover .title-meta-checker-related-tool-icon { background: linear-gradient(145deg, #f56565, #e53e3e); }

        .title-meta-checker-related-tool-item { box-shadow: none; border: none; }
        .title-meta-checker-related-tool-item:hover { box-shadow: none; border: none; }
        .title-meta-checker-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .title-meta-checker-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .title-meta-checker-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .title-meta-checker-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .title-meta-checker-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .title-meta-checker-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .title-meta-checker-related-tool-item:hover .title-meta-checker-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .title-meta-checker-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .title-meta-checker-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .title-meta-checker-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .title-meta-checker-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .title-meta-checker-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .title-meta-checker-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .title-meta-checker-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .title-meta-checker-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .title-meta-checker-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .title-meta-checker-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .title-meta-checker-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .title-meta-checker-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .title-meta-checker-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .title-meta-checker-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="title-meta-checker-widget-container">
        <h1 class="title-meta-checker-widget-title">Title Meta Description Checker</h1>
        <p class="title-meta-checker-widget-description">
            Check your meta titles and descriptions with our SERP preview tool. Optimize length, see how your pages appear in Google search results, and improve your click-through rates.
        </p>
        
        <form class="title-meta-checker-form">
            <div class="title-meta-checker-field">
                <label for="pageUrl" class="title-meta-checker-label">Page URL:</label>
                <input 
                    type="url" 
                    id="pageUrl" 
                    class="title-meta-checker-input"
                    placeholder="https://example.com/page"
                />
            </div>

            <div class="title-meta-checker-field">
                <label for="metaTitle" class="title-meta-checker-label">Meta Title:</label>
                <input 
                    type="text" 
                    id="metaTitle" 
                    class="title-meta-checker-input"
                    placeholder="Enter your page title (50-60 characters recommended)"
                    maxlength="100"
                />
                <div class="title-meta-checker-char-count" id="titleCharCount">
                    <span>0/60 characters</span>
                    <span class="title-meta-checker-status optimal" id="titleStatus">OPTIMAL</span>
                </div>
            </div>

            <div class="title-meta-checker-field">
                <label for="metaDescription" class="title-meta-checker-label">Meta Description:</label>
                <textarea 
                    id="metaDescription" 
                    class="title-meta-checker-textarea"
                    placeholder="Enter your meta description (150-160 characters recommended)"
                    maxlength="200"
                ></textarea>
                <div class="title-meta-checker-char-count" id="descCharCount">
                    <span>0/160 characters</span>
                    <span class="title-meta-checker-status optimal" id="descStatus">OPTIMAL</span>
                </div>
            </div>
        </form>

        <div class="title-meta-checker-serp-preview">
            <h3 class="title-meta-checker-serp-title">SERP Preview</h3>
            <div class="title-meta-checker-serp-result">
                <div class="title-meta-checker-serp-url" id="serpUrl">https://example.com/page</div>
                <a href="#" class="title-meta-checker-serp-link" id="serpTitle">Your page title will appear here</a>
                <div class="title-meta-checker-serp-description" id="serpDescription">Your meta description will appear here. This is how your page will look in Google search results.</div>
            </div>
        </div>

        <div class="title-meta-checker-buttons">
            <button class="title-meta-checker-btn title-meta-checker-btn-primary" onclick="TitleMetaChecker.updatePreview()">
                Update Preview
            </button>
            <button class="title-meta-checker-btn title-meta-checker-btn-secondary" onclick="TitleMetaChecker.clear()">
                Clear All
            </button>
        </div>

        <div class="title-meta-checker-related-tools">
            <h3 class="title-meta-checker-related-tools-title">Related Tools</h3>
            <div class="title-meta-checker-related-tools-grid">
                <a href="/p/meta-tag-generator.html" class="title-meta-checker-related-tool-item" rel="noopener">
                    <div class="title-meta-checker-related-tool-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="title-meta-checker-related-tool-name">Meta Tag Generator</div>
                </a>

                <a href="/p/keyword-density-checker.html" class="title-meta-checker-related-tool-item" rel="noopener">
                    <div class="title-meta-checker-related-tool-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="title-meta-checker-related-tool-name">Keyword Density Checker</div>
                </a>

                <a href="/p/open-graph-tag-generator.html" class="title-meta-checker-related-tool-item" rel="noopener">
                    <div class="title-meta-checker-related-tool-icon">
                        <i class="fab fa-facebook"></i>
                    </div>
                    <div class="title-meta-checker-related-tool-name">Open Graph Tag Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Title and Meta Description Checker</h2>
            <p>Our <strong>Title Meta Description Checker</strong> helps you optimize your page titles and meta descriptions for better search engine performance. With our SERP preview tool, you can see exactly how your pages will appear in Google search results and ensure optimal length for maximum click-through rates.</p>
            <p>Whether you're an SEO professional, content creator, or website owner, our tool makes it easy to create compelling titles and descriptions that attract clicks and improve your search rankings. Get real-time feedback on character counts and see instant previews of your search result snippets.</p>

            <h3>How to Use the Title Meta Description Checker</h3>
            <ol>
                <li><strong>Enter Page Details:</strong> Add your page URL, meta title, and meta description in the form above.</li>
                <li><strong>Check Length:</strong> Monitor the character counts and status indicators to ensure optimal length.</li>
                <li><strong>Preview Results:</strong> Click "Update Preview" to see how your page will appear in Google search results.</li>
                <li><strong>Optimize and Refine:</strong> Adjust your title and description based on the preview and length recommendations.</li>
            </ol>

            <h3>Frequently Asked Questions About Title and Meta Description Optimization</h3>

            <h4>What is the ideal length for meta titles?</h4>
            <p>The ideal meta title length is 50-60 characters or approximately 580 pixels. This ensures your title displays fully in Google search results without being truncated. Titles longer than 60 characters may be cut off with an ellipsis (...) in search results.</p>

            <h4>What is the recommended meta description length?</h4>
            <p>The recommended meta description length is 150-160 characters. Google typically displays up to 160 characters in search results, though this can vary slightly. Descriptions longer than 160 characters will be truncated, potentially cutting off important information.</p>

            <h4>Why should I check my title and meta description length?</h4>
            <p>Checking your title and meta description length ensures they display properly in search results, improving click-through rates and user experience. Properly sized titles and descriptions help convey your message effectively and can significantly impact your SEO performance.</p>

            <h4>What happens if my meta title is too long?</h4>
            <p>If your meta title exceeds 60 characters, Google will truncate it with an ellipsis (...) in search results. This can cut off important keywords or make your title appear incomplete, potentially reducing click-through rates and harming your SEO performance.</p>

            <h4>How does SERP preview help with SEO?</h4>
            <p>SERP preview shows exactly how your page will appear in Google search results, allowing you to optimize your title and description for maximum impact. You can test different variations, ensure proper length, and create compelling snippets that encourage users to click on your result.</p>
        </div>

        <div class="title-meta-checker-features">
            <h3 class="title-meta-checker-features-title">Key Features:</h3>
            <ul class="title-meta-checker-features-list">
                <li class="title-meta-checker-features-item" style="margin-bottom: 0.3em;">Real-time SERP Preview</li>
                <li class="title-meta-checker-features-item" style="margin-bottom: 0.3em;">Character Count Validation</li>
                <li class="title-meta-checker-features-item" style="margin-bottom: 0.3em;">Length Optimization Alerts</li>
                <li class="title-meta-checker-features-item" style="margin-bottom: 0.3em;">Google-style Result Display</li>
                <li class="title-meta-checker-features-item" style="margin-bottom: 0.3em;">Mobile-Friendly Interface</li>
                <li class="title-meta-checker-features-item" style="margin-bottom: 0.3em;">Instant Feedback</li>
                <li class="title-meta-checker-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="title-meta-checker-notification" id="titleMetaNotification">
        ✓ Preview updated successfully!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                pageUrl: () => document.getElementById('pageUrl'),
                metaTitle: () => document.getElementById('metaTitle'),
                metaDescription: () => document.getElementById('metaDescription'),
                titleCharCount: () => document.getElementById('titleCharCount'),
                descCharCount: () => document.getElementById('descCharCount'),
                titleStatus: () => document.getElementById('titleStatus'),
                descStatus: () => document.getElementById('descStatus'),
                serpUrl: () => document.getElementById('serpUrl'),
                serpTitle: () => document.getElementById('serpTitle'),
                serpDescription: () => document.getElementById('serpDescription'),
                notification: () => document.getElementById('titleMetaNotification')
            };

            function updateCharCount(input, counter, status, maxLength, optimalMin, optimalMax) {
                const length = input.value.length;
                const span = counter.querySelector('span');
                span.textContent = `${length}/${maxLength} characters`;

                // Remove all status classes
                counter.classList.remove('optimal', 'warning', 'error');
                status.classList.remove('optimal', 'warning', 'error');

                // Determine status
                if (length === 0) {
                    counter.classList.add('optimal');
                    status.classList.add('optimal');
                    status.textContent = 'EMPTY';
                } else if (length >= optimalMin && length <= optimalMax) {
                    counter.classList.add('optimal');
                    status.classList.add('optimal');
                    status.textContent = 'OPTIMAL';
                } else if (length < optimalMin || (length > optimalMax && length <= maxLength)) {
                    counter.classList.add('warning');
                    status.classList.add('warning');
                    status.textContent = length < optimalMin ? 'TOO SHORT' : 'LONG';
                } else {
                    counter.classList.add('error');
                    status.classList.add('error');
                    status.textContent = 'TOO LONG';
                }
            }

            function truncateText(text, maxLength) {
                if (text.length <= maxLength) return text;
                return text.substring(0, maxLength) + '...';
            }

            window.TitleMetaChecker = {
                updatePreview() {
                    const url = elements.pageUrl().value.trim() || 'https://example.com/page';
                    const title = elements.metaTitle().value.trim() || 'Your page title will appear here';
                    const description = elements.metaDescription().value.trim() || 'Your meta description will appear here. This is how your page will look in Google search results.';

                    // Update SERP preview
                    elements.serpUrl().textContent = url;
                    elements.serpTitle().textContent = truncateText(title, 60);
                    elements.serpDescription().textContent = truncateText(description, 160);

                    this.showNotification('✓ Preview updated successfully!');
                },

                clear() {
                    elements.pageUrl().value = '';
                    elements.metaTitle().value = '';
                    elements.metaDescription().value = '';

                    // Reset character counts
                    updateCharCount(elements.metaTitle(), elements.titleCharCount(), elements.titleStatus(), 100, 50, 60);
                    updateCharCount(elements.metaDescription(), elements.descCharCount(), elements.descStatus(), 200, 150, 160);

                    // Reset SERP preview
                    elements.serpUrl().textContent = 'https://example.com/page';
                    elements.serpTitle().textContent = 'Your page title will appear here';
                    elements.serpDescription().textContent = 'Your meta description will appear here. This is how your page will look in Google search results.';

                    this.showNotification('✓ Form cleared successfully!');
                },

                showNotification(message) {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Character count updates for title
                elements.metaTitle().addEventListener('input', function() {
                    updateCharCount(this, elements.titleCharCount(), elements.titleStatus(), 100, 50, 60);
                    // Auto-update preview on input
                    TitleMetaChecker.updatePreview();
                });

                // Character count updates for description
                elements.metaDescription().addEventListener('input', function() {
                    updateCharCount(this, elements.descCharCount(), elements.descStatus(), 200, 150, 160);
                    // Auto-update preview on input
                    TitleMetaChecker.updatePreview();
                });

                // Auto-update preview when URL changes
                elements.pageUrl().addEventListener('input', function() {
                    TitleMetaChecker.updatePreview();
                });

                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        TitleMetaChecker.updatePreview();
                    }
                });

                // Initialize with default values
                updateCharCount(elements.metaTitle(), elements.titleCharCount(), elements.titleStatus(), 100, 50, 60);
                updateCharCount(elements.metaDescription(), elements.descCharCount(), elements.descStatus(), 200, 150, 160);
            });
        })();
    </script>
</body>
</html>
