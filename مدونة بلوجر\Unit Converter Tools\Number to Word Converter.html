<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Number to Word Converter - Convert Numbers to Text Instantly</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Number to Word Converter - Convert Numbers to Text Instantly",
        "description": "Easily convert numbers into their written English word representation. Ideal for writing checks, filling out forms, and formal documents. Supports large numbers and decimals.",
        "url": "https://www.webtoolskit.org/p/number-to-word-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-14",
        "dateModified": "2025-06-20",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Number to Word Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Number to Words" },
            { "@type": "CopyAction", "name": "Copy Text Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert numeric value to words in Excel?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Excel does not have a built-in function to convert numbers to words. However, you can achieve this by using a custom VBA (Visual Basic for Applications) function. You would need to open the VBA editor, insert a new module, and paste in a pre-written function (often called 'SpellNumber'). Once saved, you can use '=SpellNumber(A1)' in any cell, just like a regular formula, to convert the number in cell A1 to words."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert number to text in Excel without scientific notation?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert a number to text in Excel and avoid it turning into scientific notation, you have two main options. First, you can format the cell as 'Text' before entering the number. Second, you can use the TEXT function, for example: =TEXT(A1, \"0\"). This converts the numeric value in cell A1 into a text string, preserving all its digits."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert a number into text format?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Converting a number into text format means representing the numeric digits as written words (e.g., 123 becomes 'one hundred twenty-three'). This is commonly required for legal documents, financial instruments like checks, and formal reports. You can use an online tool like this Number to Word Converter, or use programming libraries designed for this purpose."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert an integer to words?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The process involves breaking the integer into groups of three digits from right to left (e.g., 1,234,567 becomes 1 | 234 | 567). Each group is converted to words (one, two hundred thirty-four, five hundred sixty-seven), and then the appropriate scale name (million, thousand) is added. Finally, these parts are joined together to form the full text: 'one million, two hundred thirty-four thousand, five hundred sixty-seven'."
          }
        },
        {
          "@type": "Question",
          "name": "How to make a number into a word?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Making a number into a word involves a logical process of reading the number in chunks. You identify the place values—ones, tens, hundreds, thousands, millions, and so on. You then translate each digit or group of digits into its corresponding word and combine them according to grammatical rules. For example, 542 becomes 'five' (for the hundreds place), 'forty' (for the tens place), and 'two' (for the ones place), which are combined to 'five hundred forty-two'."
          }
        }
      ]
    }
    </script>

    <style>
        /* Number to Word Converter Widget - Simplified & Template Compatible */
        .number-to-word-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .number-to-word-converter-widget-container * { box-sizing: border-box; }

        .number-to-word-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .number-to-word-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .number-to-word-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .number-to-word-converter-input-group {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .number-to-word-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .number-to-word-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .number-to-word-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .number-to-word-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .number-to-word-converter-btn:hover { transform: translateY(-2px); }

        .number-to-word-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .number-to-word-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .number-to-word-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .number-to-word-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .number-to-word-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .number-to-word-converter-btn-success:hover {
            background-color: #059669;
        }

        .number-to-word-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .number-to-word-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .number-to-word-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 90px;
            color: var(--text-color);
            line-height: 1.6;
            text-transform: capitalize;
        }

        .number-to-word-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .number-to-word-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .number-to-word-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .number-to-word-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .number-to-word-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .number-to-word-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .number-to-word-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .number-to-word-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="word-to-number-converter"] .number-to-word-converter-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="number-to-roman-numerals"] .number-to-word-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="roman-numerals-to-number"] .number-to-word-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }

        .number-to-word-converter-related-tool-item:hover .number-to-word-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="word-to-number-converter"]:hover .number-to-word-converter-related-tool-icon { background: linear-gradient(145deg, #2dd4bf, #14b8a6); }
        a[href*="number-to-roman-numerals"]:hover .number-to-word-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        a[href*="roman-numerals-to-number"]:hover .number-to-word-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        
        .number-to-word-converter-related-tool-item { box-shadow: none; border: none; }
        .number-to-word-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .number-to-word-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .number-to-word-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .number-to-word-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .number-to-word-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .number-to-word-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .number-to-word-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .number-to-word-converter-related-tool-item:hover .number-to-word-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .number-to-word-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .number-to-word-converter-widget-title { font-size: 1.875rem; }
            .number-to-word-converter-buttons { flex-direction: column; }
            .number-to-word-converter-btn { flex: none; }
            .number-to-word-converter-input-group { grid-template-columns: 1fr; }
            .number-to-word-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .number-to-word-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .number-to-word-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .number-to-word-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .number-to-word-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .number-to-word-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .number-to-word-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .number-to-word-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .number-to-word-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .number-to-word-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .number-to-word-converter-output::selection { background-color: var(--primary-color); color: white; }
        .number-to-word-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .number-to-word-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="number-to-word-converter-widget-container">
        <h1 class="number-to-word-converter-widget-title">Number to Word Converter</h1>
        <p class="number-to-word-converter-widget-description">
            Instantly convert any number into its full, written-out English text equivalent. Perfect for writing checks and formal documents.
        </p>
        
        <div class="number-to-word-converter-input-group">
            <label for="numberInput" class="number-to-word-converter-label">Enter Number:</label>
            <input 
                type="text" 
                id="numberInput" 
                class="number-to-word-converter-input"
                placeholder="e.g., 1234.56"
            />
        </div>

        <div class="number-to-word-converter-buttons">
            <button class="number-to-word-converter-btn number-to-word-converter-btn-primary" onclick="NumberToWordConverter.convert()">
                Convert to Words
            </button>
            <button class="number-to-word-converter-btn number-to-word-converter-btn-secondary" onclick="NumberToWordConverter.clear()">
                Clear All
            </button>
            <button class="number-to-word-converter-btn number-to-word-converter-btn-success" onclick="NumberToWordConverter.copy()">
                Copy Words
            </button>
        </div>

        <div class="number-to-word-converter-result">
            <h3 class="number-to-word-converter-result-title">Result in Words:</h3>
            <div class="number-to-word-converter-output" id="numberToWordOutput">
                Your result will appear here...
            </div>
        </div>

        <div class="number-to-word-converter-related-tools">
            <h3 class="number-to-word-converter-related-tools-title">Related Tools</h3>
            <div class="number-to-word-converter-related-tools-grid">
                <a href="/p/word-to-number-converter.html" class="number-to-word-converter-related-tool-item" rel="noopener">
                    <div class="number-to-word-converter-related-tool-icon">
                        <i class="fas fa-keyboard"></i>
                    </div>
                    <div class="number-to-word-converter-related-tool-name">Word to Number Converter</div>
                </a>
                <a href="/p/number-to-roman-numerals.html" class="number-to-word-converter-related-tool-item" rel="noopener">
                    <div class="number-to-word-converter-related-tool-icon">
                        <i class="fas fa-columns"></i>
                    </div>
                    <div class="number-to-word-converter-related-tool-name">Number to Roman Numerals</div>
                </a>
                <a href="/p/roman-numerals-to-number.html" class="number-to-word-converter-related-tool-item" rel="noopener">
                    <div class="number-to-word-converter-related-tool-icon">
                        <i class="fas fa-list-ol"></i>
                    </div>
                    <div class="number-to-word-converter-related-tool-name">Roman to Number</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Accurately Convert Numbers to Words Online</h2>
            <p>Have you ever needed to write out a large number for a check, legal document, or formal report and weren't sure about the correct spelling? Our free <strong>Number to Word Converter</strong> is the perfect solution. This tool accurately translates any numeric figure into its proper English word equivalent, handling everything from simple integers to large numbers with decimals. It's designed to provide clarity and prevent common errors, ensuring your documents are professional and precise.</p>
            <p>This tool is invaluable for financial transactions where writing out the amount is required, for educational purposes to help teach number names, and for any situation that demands formality over numerals. Simply type in your number and let our converter do the work for you, instantly and without error.</p>

            <h3>How to Use the Number to Word Converter</h3>
            <ol>
                <li><strong>Enter Your Number:</strong> Type any number, with or without decimals, into the input field.</li>
                <li><strong>Convert:</strong> Click the "Convert to Words" button to generate the text representation instantly.</li>
                <li><strong>Copy the Result:</strong> Use the "Copy Words" button to copy the full text to your clipboard for use in any application.</li>
            </ol>

            <h3>Frequently Asked Questions About Converting Numbers</h3>

            <h4>How to convert numeric value to words in Excel?</h4>
            <p>Excel does not have a built-in function to convert numbers to words. However, you can achieve this by using a custom VBA (Visual Basic for Applications) function. You would need to open the VBA editor, insert a new module, and paste in a pre-written function (often called 'SpellNumber'). Once saved, you can use '=SpellNumber(A1)' in any cell, just like a regular formula, to convert the number in cell A1 to words.</p>

            <h4>How to convert number to text in Excel without scientific notation?</h4>
            <p>To convert a number to text in Excel and avoid it turning into scientific notation, you have two main options. First, you can format the cell as 'Text' before entering the number. Second, you can use the TEXT function, for example: =TEXT(A1, "0"). This converts the numeric value in cell A1 into a text string, preserving all its digits.</p>

            <h4>How to convert a number into text format?</h4>
            <p>Converting a number into text format means representing the numeric digits as written words (e.g., 123 becomes 'one hundred twenty-three'). This is commonly required for legal documents, financial instruments like checks, and formal reports. You can use an online tool like this Number to Word Converter, or use programming libraries designed for this purpose.</p>

            <h4>How to convert an integer to words?</h4>
            <p>The process involves breaking the integer into groups of three digits from right to left (e.g., 1,234,567 becomes 1 | 234 | 567). Each group is converted to words (one, two hundred thirty-four, five hundred sixty-seven), and then the appropriate scale name (million, thousand) is added. Finally, these parts are joined together to form the full text: 'one million, two hundred thirty-four thousand, five hundred sixty-seven'.</p>

            <h4>How to make a number into a word?</h4>
            <p>Making a number into a word involves a logical process of reading the number in chunks. You identify the place values—ones, tens, hundreds, thousands, millions, and so on. You then translate each digit or group of digits into its corresponding word and combine them according to grammatical rules. For example, 542 becomes 'five' (for the hundreds place), 'forty' (for the tens place), and 'two' (for the ones place), which are combined to 'five hundred forty-two'.</p>
        </div>

        <div class="number-to-word-converter-features">
            <h3 class="number-to-word-converter-features-title">Key Features:</h3>
            <ul class="number-to-word-converter-features-list">
                <li class="number-to-word-converter-features-item" style="margin-bottom: 0.3em;">Handles large numbers</li>
                <li class="number-to-word-converter-features-item" style="margin-bottom: 0.3em;">Supports decimal values</li>
                <li class="number-to-word-converter-features-item" style="margin-bottom: 0.3em;">Ideal for writing checks</li>
                <li class="number-to-word-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="number-to-word-converter-features-item" style="margin-bottom: 0.3em;">Accurate English spelling</li>
                <li class="number-to-word-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side conversion</li>
                <li class="number-to-word-converter-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="number-to-word-converter-notification" id="numberToWordConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Number to Word Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('numberInput'),
                output: () => document.getElementById('numberToWordOutput'),
                notification: () => document.getElementById('numberToWordConverterNotification')
            };
            
            const ones = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
            const teens = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
            const tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
            const scales = ['', 'thousand', 'million', 'billion', 'trillion', 'quadrillion'];


            window.NumberToWordConverter = {
                convert() {
                    const outputEl = elements.output();
                    let numStr = elements.input().value.replace(/,/g, '');

                    if (numStr === '' || isNaN(numStr) || numStr.trim() === '') {
                        outputEl.textContent = 'Please enter a valid number.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }
                    
                    if (BigInt(numStr.split('.')[0]) > 9007199254740991) {
                         outputEl.textContent = 'Number is too large to be safely converted.';
                         outputEl.style.color = '#dc2626';
                         return;
                    }

                    outputEl.style.color = '';
                    
                    if (numStr === '0') {
                        outputEl.textContent = 'Zero';
                        return;
                    }

                    let [integerPart, decimalPart] = numStr.split('.');
                    let words = this.convertInteger(integerPart);
                    
                    if (decimalPart) {
                        decimalPart = decimalPart.slice(0, 2).padEnd(2, '0');
                        words += ` and ${decimalPart}/100`;
                    }
                    
                    outputEl.textContent = words.trim();
                },
                
                convertInteger(numStr) {
                    if (numStr === '0') return 'zero';
                    let words = '';
                    
                    if (numStr.startsWith('-')) {
                        words += 'negative ';
                        numStr = numStr.substring(1);
                    }
                    
                    let chunkCount = Math.ceil(numStr.length / 3);
                    let chunks = [];
                    
                    for (let i = numStr.length; i > 0; i -= 3) {
                        chunks.push(numStr.substring(Math.max(0, i - 3), i));
                    }
                    
                    if (chunks.length > scales.length) {
                        return 'Number is too large.';
                    }

                    let result = chunks.map((chunk, i) => {
                        let chunkWords = this.processChunk(chunk);
                        if (chunkWords && scales[i]) {
                            return chunkWords + ' ' + scales[i];
                        }
                        return chunkWords;
                    }).reverse().join(' ').replace(/\s+/g, ' ').trim();
                    
                    return result;
                },
                
                processChunk(chunk) {
                    let num = parseInt(chunk, 10);
                    if (num === 0) return '';
                    
                    let words = [];
                    
                    if (num >= 100) {
                        words.push(ones[Math.floor(num / 100)], 'hundred');
                        num %= 100;
                    }
                    
                    if (num >= 20) {
                        words.push(tens[Math.floor(num / 10)]);
                        num %= 10;
                    } else if (num >= 10) {
                        words.push(teens[num - 10]);
                        num = 0;
                    }
                    
                    if (num > 0) {
                        if(words.length > 0 && !words[words.length-1].endsWith('ty')) {
                            words.push(ones[num]);
                        } else if (words.length > 0 && words[words.length-1].endsWith('ty')) {
                           words[words.length-1] = words[words.length-1] + '-' + ones[num];
                        } else {
                           words.push(ones[num]);
                        }
                    }
                    
                    return words.join(' ');
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your result will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (!text || text === 'Your result will appear here...') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const inputEl = elements.input();
                inputEl.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        NumberToWordConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>