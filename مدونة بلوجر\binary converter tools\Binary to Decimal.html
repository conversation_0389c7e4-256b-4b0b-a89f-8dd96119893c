<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary to Decimal Converter Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Binary to Decimal Converter - Convert Binary Numbers Online",
        "description": "Convert binary numbers to decimal instantly. Free online tool with real-time conversion, multiple formats, and one-click copying.",
        "url": "https://www.webtoolskit.org/p/binary-to-decimal.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Binary to Decimal Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Binary to Decimal" },
            { "@type": "CopyAction", "name": "Copy Decimal Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert binary to decimal easily?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The easiest way to convert binary to decimal is using our online converter. Simply enter your binary number and click convert. For manual conversion, multiply each binary digit by 2 raised to its position power (starting from 0 on the right), then sum all results."
          }
        },
        {
          "@type": "Question",
          "name": "What is the decimal for 11111111 binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The binary number 11111111 equals 255 in decimal. This is calculated as: 1×2⁷ + 1×2⁶ + 1×2⁵ + 1×2⁴ + 1×2³ + 1×2² + 1×2¹ + 1×2⁰ = 128+64+32+16+8+4+2+1 = 255."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert binary to decimal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert binary to decimal, multiply each binary digit by 2 raised to its position power (starting from position 0 on the right), then add all the results together. For example: 1011 = 1×2³ + 0×2² + 1×2¹ + 1×2⁰ = 8+0+2+1 = 11."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert decimal point to binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert decimal numbers with decimal points to binary, convert the integer part normally, then for the fractional part, multiply by 2 repeatedly, taking the integer part of each result as the next binary digit. For example: 5.25 = 101.01 in binary."
          }
        },
        {
          "@type": "Question",
          "name": "What does 10011 mean in binary code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The binary code 10011 means 19 in decimal. This is calculated as: 1×2⁴ + 0×2³ + 0×2² + 1×2¹ + 1×2⁰ = 16+0+0+2+1 = 19."
          }
        }
      ]
    }
    </script>


    <style>
        /* Binary to Decimal Widget - Simplified & Template Compatible */
        .binary-to-decimal-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .binary-to-decimal-widget-container * { box-sizing: border-box; }

        .binary-to-decimal-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .binary-to-decimal-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .binary-to-decimal-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .binary-to-decimal-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .binary-to-decimal-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .binary-to-decimal-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .binary-to-decimal-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .binary-to-decimal-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .binary-to-decimal-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .binary-to-decimal-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .binary-to-decimal-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .binary-to-decimal-btn:hover { transform: translateY(-2px); }

        .binary-to-decimal-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .binary-to-decimal-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .binary-to-decimal-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .binary-to-decimal-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .binary-to-decimal-btn-success {
            background-color: #10b981;
            color: white;
        }

        .binary-to-decimal-btn-success:hover {
            background-color: #059669;
        }

        .binary-to-decimal-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .binary-to-decimal-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .binary-to-decimal-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .binary-to-decimal-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .binary-to-decimal-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .binary-to-decimal-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .binary-to-decimal-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .binary-to-decimal-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .binary-to-decimal-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .binary-to-decimal-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .binary-to-decimal-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="decimal-to-binary"] .binary-to-decimal-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="binary-to-text"] .binary-to-decimal-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="binary-to-hex"] .binary-to-decimal-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .binary-to-decimal-related-tool-item:hover .binary-to-decimal-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="decimal-to-binary"]:hover .binary-to-decimal-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="binary-to-text"]:hover .binary-to-decimal-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="binary-to-hex"]:hover .binary-to-decimal-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .binary-to-decimal-related-tool-item { box-shadow: none; border: none; }
        .binary-to-decimal-related-tool-item:hover { box-shadow: none; border: none; }
        .binary-to-decimal-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .binary-to-decimal-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .binary-to-decimal-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .binary-to-decimal-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .binary-to-decimal-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .binary-to-decimal-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .binary-to-decimal-related-tool-item:hover .binary-to-decimal-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .binary-to-decimal-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .binary-to-decimal-widget-title { font-size: 1.875rem; }
            .binary-to-decimal-buttons { flex-direction: column; }
            .binary-to-decimal-btn { flex: none; }
            .binary-to-decimal-options { grid-template-columns: 1fr; }
            .binary-to-decimal-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .binary-to-decimal-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .binary-to-decimal-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .binary-to-decimal-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .binary-to-decimal-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .binary-to-decimal-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .binary-to-decimal-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .binary-to-decimal-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .binary-to-decimal-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .binary-to-decimal-checkbox:focus, .binary-to-decimal-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .binary-to-decimal-output::selection { background-color: var(--primary-color); color: white; }
        .binary-to-decimal-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .binary-to-decimal-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="binary-to-decimal-widget-container">
        <h1 class="binary-to-decimal-widget-title">Binary to Decimal Converter</h1>
        <p class="binary-to-decimal-widget-description">
            Convert binary numbers to decimal format instantly. Perfect for programming, mathematics, and computer science applications.
        </p>
        
        <div class="binary-to-decimal-input-group">
            <label for="binaryToDecimalInput" class="binary-to-decimal-label">Enter binary number:</label>
            <textarea 
                id="binaryToDecimalInput" 
                class="binary-to-decimal-textarea"
                placeholder="Enter binary number here (e.g., 1011, 11111111, 10011)..."
                rows="4"
            ></textarea>
        </div>

        <div class="binary-to-decimal-options">
            <div class="binary-to-decimal-option">
                <input type="checkbox" id="binaryShowSteps" class="binary-to-decimal-checkbox">
                <label for="binaryShowSteps" class="binary-to-decimal-option-label">Show conversion steps</label>
            </div>
            <div class="binary-to-decimal-option">
                <input type="checkbox" id="binaryMultipleNumbers" class="binary-to-decimal-checkbox">
                <label for="binaryMultipleNumbers" class="binary-to-decimal-option-label">Convert multiple numbers</label>
            </div>
            <div class="binary-to-decimal-option">
                <input type="checkbox" id="binaryValidateInput" class="binary-to-decimal-checkbox" checked>
                <label for="binaryValidateInput" class="binary-to-decimal-option-label">Validate binary input</label>
            </div>
            <div class="binary-to-decimal-option">
                <input type="checkbox" id="binaryIgnoreSpaces" class="binary-to-decimal-checkbox" checked>
                <label for="binaryIgnoreSpaces" class="binary-to-decimal-option-label">Ignore spaces</label>
            </div>
        </div>

        <div class="binary-to-decimal-buttons">
            <button class="binary-to-decimal-btn binary-to-decimal-btn-primary" onclick="BinaryToDecimalConverter.convert()">
                Convert to Decimal
            </button>
            <button class="binary-to-decimal-btn binary-to-decimal-btn-secondary" onclick="BinaryToDecimalConverter.clear()">
                Clear All
            </button>
            <button class="binary-to-decimal-btn binary-to-decimal-btn-success" onclick="BinaryToDecimalConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="binary-to-decimal-result">
            <h3 class="binary-to-decimal-result-title">Decimal Result:</h3>
            <div class="binary-to-decimal-output" id="binaryToDecimalOutput">
                Your decimal result will appear here...
            </div>
        </div>

        <div class="binary-to-decimal-related-tools">
            <h3 class="binary-to-decimal-related-tools-title">Related Tools</h3>
            <div class="binary-to-decimal-related-tools-grid">
                <a href="/p/decimal-to-binary.html" class="binary-to-decimal-related-tool-item" rel="noopener">
                    <div class="binary-to-decimal-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="binary-to-decimal-related-tool-name">Decimal to Binary</div>
                </a>

                <a href="/p/binary-to-text.html" class="binary-to-decimal-related-tool-item" rel="noopener">
                    <div class="binary-to-decimal-related-tool-icon">
                        <i class="fas fa-font"></i>
                    </div>
                    <div class="binary-to-decimal-related-tool-name">Binary to Text</div>
                </a>

                <a href="/p/binary-to-hex.html" class="binary-to-decimal-related-tool-item" rel="noopener">
                    <div class="binary-to-decimal-related-tool-icon">
                        <i class="fas fa-hashtag"></i>
                    </div>
                    <div class="binary-to-decimal-related-tool-name">Binary to HEX</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert Binary to Decimal Numbers with Our Free Online Tool</h2>
            <p>Binary to decimal conversion is a fundamental skill in computer science and mathematics. Our <strong>Binary to Decimal</strong> converter simplifies this process, allowing you to convert binary numbers to their decimal equivalents instantly. Whether you're a student learning number systems, a programmer working with binary data, or someone who needs quick conversions, this tool provides accurate results with optional step-by-step explanations.</p>
            <p>The binary number system uses only two digits (0 and 1) and is the foundation of all digital computing. Each position in a binary number represents a power of 2, making conversion to decimal straightforward once you understand the pattern. Our converter handles single numbers or multiple conversions simultaneously, making it perfect for homework, programming projects, or professional development work.</p>
            
            <h3>How to Use the Binary to Decimal Converter</h3>
            <ol>
                <li><strong>Enter Binary Number:</strong> Type your binary number in the input field. You can enter single numbers or multiple numbers separated by spaces or new lines.</li>
                <li><strong>Configure Options:</strong> Choose whether to show conversion steps, handle multiple numbers, validate input, or ignore spaces between digits.</li>
                <li><strong>Convert and Copy:</strong> Click "Convert to Decimal" to get your result. The tool shows the decimal equivalent and optionally displays the calculation steps.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Binary to Decimal</h3>
            
            <h4>How to convert binary to decimal easily?</h4>
            <p>The easiest way to convert binary to decimal is using our online converter. Simply enter your binary number and click convert. For manual conversion, multiply each binary digit by 2 raised to its position power (starting from 0 on the right), then sum all results.</p>
            
            <h4>What is the decimal for 11111111 binary?</h4>
            <p>The binary number 11111111 equals 255 in decimal. This is calculated as: 1×2⁷ + 1×2⁶ + 1×2⁵ + 1×2⁴ + 1×2³ + 1×2² + 1×2¹ + 1×2⁰ = 128+64+32+16+8+4+2+1 = 255.</p>
            
            <h4>How do you convert binary to decimal?</h4>
            <p>To convert binary to decimal, multiply each binary digit by 2 raised to its position power (starting from position 0 on the right), then add all the results together. For example: 1011 = 1×2³ + 0×2² + 1×2¹ + 1×2⁰ = 8+0+2+1 = 11.</p>
            
            <h4>How to convert decimal point to binary?</h4>
            <p>To convert decimal numbers with decimal points to binary, convert the integer part normally, then for the fractional part, multiply by 2 repeatedly, taking the integer part of each result as the next binary digit. For example: 5.25 = 101.01 in binary.</p>
            
            <h4>What does 10011 mean in binary code?</h4>
            <p>The binary code 10011 means 19 in decimal. This is calculated as: 1×2⁴ + 0×2³ + 0×2² + 1×2¹ + 1×2⁰ = 16+0+0+2+1 = 19.</p>
        </div>


        <div class="binary-to-decimal-features">
            <h3 class="binary-to-decimal-features-title">Key Features:</h3>
            <ul class="binary-to-decimal-features-list">
                <li class="binary-to-decimal-features-item" style="margin-bottom: 0.3em;">Instant binary-to-decimal conversion</li>
                <li class="binary-to-decimal-features-item" style="margin-bottom: 0.3em;">Step-by-step calculation display</li>
                <li class="binary-to-decimal-features-item" style="margin-bottom: 0.3em;">Multiple number processing</li>
                <li class="binary-to-decimal-features-item" style="margin-bottom: 0.3em;">Input validation and error checking</li>
                <li class="binary-to-decimal-features-item" style="margin-bottom: 0.3em;">Flexible formatting options</li>
                <li class="binary-to-decimal-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="binary-to-decimal-features-item">Real-time processing</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="binary-to-decimal-notification" id="binaryToDecimalNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Binary to Decimal Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('binaryToDecimalInput'),
                output: () => document.getElementById('binaryToDecimalOutput'),
                notification: () => document.getElementById('binaryToDecimalNotification')
            };

            window.BinaryToDecimalConverter = {
                convert() {
                    const input = elements.input();
                    const output = elements.output();
                    const binaryInput = input.value;

                    if (!binaryInput.trim()) {
                        output.textContent = 'Please enter a binary number to convert.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        showSteps: document.getElementById('binaryShowSteps').checked,
                        multipleNumbers: document.getElementById('binaryMultipleNumbers').checked,
                        validateInput: document.getElementById('binaryValidateInput').checked,
                        ignoreSpaces: document.getElementById('binaryIgnoreSpaces').checked
                    };

                    try {
                        const result = this.processBinary(binaryInput, options);
                        output.textContent = result;
                    } catch (error) {
                        output.textContent = `Error: ${error.message}`;
                        output.style.color = '#dc2626';
                    }
                },

                processBinary(binaryInput, options) {
                    let cleanInput = binaryInput;
                    if (options.ignoreSpaces) {
                        cleanInput = binaryInput.replace(/\s/g, '');
                    }

                    // Handle multiple numbers
                    let binaryNumbers = [];
                    if (options.multipleNumbers) {
                        binaryNumbers = binaryInput.split(/[\s,\n]+/).filter(num => num.trim().length > 0);
                    } else {
                        binaryNumbers = [cleanInput];
                    }

                    let results = [];
                    let details = [];

                    for (let binaryStr of binaryNumbers) {
                        let cleanBinary = options.ignoreSpaces ? binaryStr.replace(/\s/g, '') : binaryStr.trim();
                        
                        // Validate binary input
                        if (options.validateInput && !/^[01]+$/.test(cleanBinary)) {
                            throw new Error(`Invalid binary number: ${cleanBinary}. Only 0 and 1 are allowed.`);
                        }

                        // Convert to decimal
                        const decimal = parseInt(cleanBinary, 2);
                        if (isNaN(decimal)) {
                            throw new Error(`Unable to convert: ${cleanBinary}`);
                        }

                        results.push(decimal);

                        // Generate step-by-step calculation
                        if (options.showSteps) {
                            let steps = [];
                            let calculation = [];
                            let sum = 0;

                            for (let i = 0; i < cleanBinary.length; i++) {
                                const digit = cleanBinary[cleanBinary.length - 1 - i];
                                const power = i;
                                const value = parseInt(digit) * Math.pow(2, power);
                                
                                if (digit === '1') {
                                    steps.push(`${digit}×2^${power} = ${value}`);
                                    calculation.push(value);
                                } else {
                                    steps.push(`${digit}×2^${power} = 0`);
                                }
                                sum += value;
                            }

                            details.push(`Binary: ${cleanBinary}`);
                            details.push(`Steps: ${steps.join(' + ')}`);
                            details.push(`Calculation: ${calculation.join(' + ')} = ${decimal}`);
                            details.push('---');
                        }
                    }

                    let output = '';
                    if (options.multipleNumbers) {
                        output = `Results: ${results.join(', ')}`;
                    } else {
                        output = `${results[0]}`;
                    }

                    if (options.showSteps && details.length > 0) {
                        output += `\n\nDetailed Steps:\n${details.join('\n')}`;
                    }

                    return output;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your decimal result will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your decimal result will appear here...', 'Please enter a binary number to convert.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                const checkboxes = document.querySelectorAll('.binary-to-decimal-checkbox');

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        BinaryToDecimalConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>