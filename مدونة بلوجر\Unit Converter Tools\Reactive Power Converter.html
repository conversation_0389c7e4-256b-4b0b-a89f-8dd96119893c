<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reactive Power Converter - Convert VAR, kVAR, MVAR</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Reactive Power Converter - Convert VAR, kVAR, MVAR",
        "description": "Instantly convert between reactive power units like Volt-Ampere Reactive (VAR), kilovar (kVAR), and megavar (MVAR). Free online tool for electrical engineers and technicians.",
        "url": "https://www.webtoolskit.org/p/reactive-power-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-11",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Reactive Power Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Reactive Power Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the purpose of reactive power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The main purpose of reactive power is to create and sustain the magnetic and electric fields necessary for AC equipment with inductive or capacitive components to operate. For example, motors, transformers, and fluorescent lighting ballasts require reactive power to generate their magnetic fields, which is essential for them to function and do work."
          }
        },
        {
          "@type": "Question",
          "name": "How do you control reactive power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Reactive power is controlled using power factor correction techniques. This typically involves installing capacitor banks to counteract the inductive reactance from motors and other loads. For more dynamic control, devices like synchronous condensers or modern power electronics such as Static VAR Compensators (SVCs) and STATCOMs are used to inject or absorb reactive power as needed to stabilize voltage and improve system efficiency."
          }
        },
        {
          "@type": "Question",
          "name": "Can an inverter generate reactive power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, modern inverters, especially smart inverters used in solar and wind power systems, are capable of generating or absorbing reactive power. This capability allows them to help regulate grid voltage, improve power quality, and provide ancillary services to the utility grid, making renewable energy sources more grid-friendly."
          }
        },
        {
          "@type": "Question",
          "name": "What happens if reactive power is high?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "High reactive power demand leads to a low power factor, which means the electrical system is operating inefficiently. It causes a larger total current to flow through the wires for the same amount of useful work (real power), leading to increased energy losses in equipment and transmission lines, voltage drops, and reduced system capacity. Utilities often charge penalties for industrial customers with low power factors."
          }
        },
        {
          "@type": "Question",
          "name": "What are the disadvantages of reactive power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The main disadvantages of excessive reactive power are: 1. Increased Energy Costs: Higher current flow leads to greater resistive losses (I²R losses), wasting energy. 2. Reduced Equipment Capacity: Wires, transformers, and generators must be oversized to handle the extra current, limiting the amount of real work they can do. 3. Voltage Instability: Poor reactive power management can lead to voltage drops or sags on the power grid. 4. Utility Penalties: Many power companies impose extra charges on customers with poor power factors."
          }
        }
      ]
    }
    </script>

    <style>
        /* Reactive Power Converter Widget - Simplified & Template Compatible */
        .reactive-power-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .reactive-power-converter-widget-container * { box-sizing: border-box; }

        .reactive-power-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .reactive-power-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .reactive-power-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .reactive-power-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .reactive-power-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .reactive-power-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .reactive-power-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .reactive-power-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .reactive-power-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .reactive-power-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .reactive-power-converter-btn:hover { transform: translateY(-2px); }

        .reactive-power-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .reactive-power-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .reactive-power-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .reactive-power-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .reactive-power-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .reactive-power-converter-btn-success:hover {
            background-color: #059669;
        }

        .reactive-power-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .reactive-power-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .reactive-power-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .reactive-power-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .reactive-power-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .reactive-power-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .reactive-power-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .reactive-power-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .reactive-power-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .reactive-power-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .reactive-power-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="power-converter"] .reactive-power-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="apparent-power-converter"] .reactive-power-converter-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="voltage-converter"] .reactive-power-converter-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }

        .reactive-power-converter-related-tool-item:hover .reactive-power-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="power-converter"]:hover .reactive-power-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        a[href*="apparent-power-converter"]:hover .reactive-power-converter-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="voltage-converter"]:hover .reactive-power-converter-related-tool-icon { background: linear-gradient(145deg, #fbbd24, #f59e0b); }
        
        .reactive-power-converter-related-tool-item { box-shadow: none; border: none; }
        .reactive-power-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .reactive-power-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .reactive-power-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .reactive-power-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .reactive-power-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .reactive-power-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .reactive-power-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .reactive-power-converter-related-tool-item:hover .reactive-power-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .reactive-power-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .reactive-power-converter-widget-title { font-size: 1.875rem; }
            .reactive-power-converter-buttons { flex-direction: column; }
            .reactive-power-converter-btn { flex: none; }
            .reactive-power-converter-input-group { grid-template-columns: 1fr; }
            .reactive-power-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .reactive-power-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .reactive-power-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .reactive-power-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .reactive-power-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .reactive-power-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .reactive-power-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .reactive-power-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .reactive-power-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .reactive-power-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .reactive-power-converter-output::selection { background-color: var(--primary-color); color: white; }
        .reactive-power-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .reactive-power-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="reactive-power-converter-widget-container">
        <h1 class="reactive-power-converter-widget-title">Reactive Power Converter</h1>
        <p class="reactive-power-converter-widget-description">
            A simple tool for electrical engineers to convert reactive power units, including VAR, kilovar (kVAR), and megavar (MVAR).
        </p>
        
        <div class="reactive-power-converter-input-group">
            <label for="reactivePowerFromInput" class="reactive-power-converter-label">From:</label>
            <input 
                type="number" 
                id="reactivePowerFromInput" 
                class="reactive-power-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="reactivePowerFromUnit" class="reactive-power-converter-select">
                <option value="var" selected>VAR</option>
                <option value="kvar">kVAR</option>
                <option value="mvar">MVAR</option>
            </select>
        </div>

        <div class="reactive-power-converter-input-group">
            <label for="reactivePowerToInput" class="reactive-power-converter-label">To:</label>
            <input 
                type="number" 
                id="reactivePowerToInput" 
                class="reactive-power-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="reactivePowerToUnit" class="reactive-power-converter-select">
                <option value="var">VAR</option>
                <option value="kvar" selected>kVAR</option>
                <option value="mvar">MVAR</option>
            </select>
        </div>

        <div class="reactive-power-converter-buttons">
            <button class="reactive-power-converter-btn reactive-power-converter-btn-primary" onclick="ReactivePowerConverter.convert()">
                Convert Power
            </button>
            <button class="reactive-power-converter-btn reactive-power-converter-btn-secondary" onclick="ReactivePowerConverter.clear()">
                Clear All
            </button>
            <button class="reactive-power-converter-btn reactive-power-converter-btn-success" onclick="ReactivePowerConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="reactive-power-converter-result">
            <h3 class="reactive-power-converter-result-title">Conversion Result:</h3>
            <div class="reactive-power-converter-output" id="reactivePowerConverterOutput">
                Your converted reactive power will appear here...
            </div>
        </div>

        <div class="reactive-power-converter-related-tools">
            <h3 class="reactive-power-converter-related-tools-title">Related Tools</h3>
            <div class="reactive-power-converter-related-tools-grid">
                <a href="/p/power-converter.html" class="reactive-power-converter-related-tool-item" rel="noopener">
                    <div class="reactive-power-converter-related-tool-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="reactive-power-converter-related-tool-name">Power Converter</div>
                </a>
                <a href="/p/apparent-power-converter.html" class="reactive-power-converter-related-tool-item" rel="noopener">
                    <div class="reactive-power-converter-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="reactive-power-converter-related-tool-name">Apparent Power Converter</div>
                </a>
                <a href="/p/voltage-converter.html" class="reactive-power-converter-related-tool-item" rel="noopener">
                    <div class="reactive-power-converter-related-tool-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <div class="reactive-power-converter-related-tool-name">Voltage Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Understanding and Converting Reactive Power</h2>
            <p>In AC power systems, not all power does useful work. Reactive power, measured in Volt-Amperes Reactive (VAR), is the "non-working" power required to sustain magnetic fields in inductive equipment like motors and transformers. While essential, managing it is crucial for system efficiency. Our free <strong>Reactive Power Converter</strong> is a specialized tool for electrical engineers, technicians, and students to quickly convert between the standard units: VAR, kilovar (kVAR), and megavar (MVAR).</p>
            <p>Whether you are designing a power factor correction system, analyzing a utility bill, or studying power flow, this converter simplifies your calculations. It provides instant, accurate results, helping you work more efficiently and avoid manual conversion errors in critical applications.</p>

            <h3>How to Use the Reactive Power Converter</h3>
            <ol>
                <li><strong>Enter Value:</strong> Type the numeric value of the reactive power you want to convert.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., VAR) and the unit you are converting to (e.g., kVAR) from the dropdowns.</li>
                <li><strong>Convert:</strong> Click the "Convert Power" button to perform the calculation.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to easily transfer the converted value to your clipboard.</li>
            </ol>

            <h3>Frequently Asked Questions About Reactive Power</h3>
            
            <h4>What is the purpose of reactive power?</h4>
            <p>The main purpose of reactive power is to create and sustain the magnetic and electric fields necessary for AC equipment with inductive or capacitive components to operate. For example, motors, transformers, and fluorescent lighting ballasts require reactive power to generate their magnetic fields, which is essential for them to function and do work.</p>

            <h4>How do you control reactive power?</h4>
            <p>Reactive power is controlled using power factor correction techniques. This typically involves installing capacitor banks to counteract the inductive reactance from motors and other loads. For more dynamic control, devices like synchronous condensers or modern power electronics such as Static VAR Compensators (SVCs) and STATCOMs are used to inject or absorb reactive power as needed to stabilize voltage and improve system efficiency.</p>

            <h4>Can an inverter generate reactive power?</h4>
            <p>Yes, modern inverters, especially smart inverters used in solar and wind power systems, are capable of generating or absorbing reactive power. This capability allows them to help regulate grid voltage, improve power quality, and provide ancillary services to the utility grid, making renewable energy sources more grid-friendly.</p>

            <h4>What happens if reactive power is high?</h4>
            <p>High reactive power demand leads to a low power factor, which means the electrical system is operating inefficiently. It causes a larger total current to flow through the wires for the same amount of useful work (real power), leading to increased energy losses in equipment and transmission lines, voltage drops, and reduced system capacity. Utilities often charge penalties for industrial customers with low power factors.</p>

            <h4>What are the disadvantages of reactive power?</h4>
            <p>The main disadvantages of excessive reactive power are: 1. Increased Energy Costs: Higher current flow leads to greater resistive losses (I²R losses), wasting energy. 2. Reduced Equipment Capacity: Wires, transformers, and generators must be oversized to handle the extra current, limiting the amount of real work they can do. 3. Voltage Instability: Poor reactive power management can lead to voltage drops or sags on the power grid. 4. Utility Penalties: Many power companies impose extra charges on customers with poor power factors.</p>
        </div>

        <div class="reactive-power-converter-features">
            <h3 class="reactive-power-converter-features-title">Key Features:</h3>
            <ul class="reactive-power-converter-features-list">
                <li class="reactive-power-converter-features-item" style="margin-bottom: 0.3em;">Converts VAR, kVAR, MVAR</li>
                <li class="reactive-power-converter-features-item" style="margin-bottom: 0.3em;">High-precision calculations</li>
                <li class="reactive-power-converter-features-item" style="margin-bottom: 0.3em;">Essential for power engineering</li>
                <li class="reactive-power-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="reactive-power-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side processing</li>
                <li class="reactive-power-converter-features-item" style="margin-bottom: 0.3em;">Fully responsive design</li>
                <li class="reactive-power-converter-features-item">100% free and private to use</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="reactive-power-converter-notification" id="reactivePowerConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Reactive Power Converter
        (function() {
            'use strict';

            // Conversion factors to VAR
            const conversionFactors = {
                'var': 1,
                'kvar': 1000,
                'mvar': 1000000
            };

            const elements = {
                fromInput: () => document.getElementById('reactivePowerFromInput'),
                toInput: () => document.getElementById('reactivePowerToInput'),
                fromUnit: () => document.getElementById('reactivePowerFromUnit'),
                toUnit: () => document.getElementById('reactivePowerToUnit'),
                output: () => document.getElementById('reactivePowerConverterOutput'),
                notification: () => document.getElementById('reactivePowerConverterNotification')
            };

            window.ReactivePowerConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to VAR first, then to target unit
                    const valueInVAR = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInVAR / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-9 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(12)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'var': 'VAR', 'kvar': 'kVAR', 'mvar': 'MVAR' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted reactive power will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        ReactivePowerConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>