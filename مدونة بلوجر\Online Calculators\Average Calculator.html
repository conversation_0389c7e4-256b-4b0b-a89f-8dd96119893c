<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Average Calculator Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Average Calculator - Calculate Mean, Median, Mode & Range",
        "description": "Calculate mean, median, mode, and range for any set of numbers instantly. Free online average calculator with step-by-step results and one-click copying.",
        "url": "https://www.webtoolskit.org/p/average-calculator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Average Calculator",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CalculateAction", "name": "Calculate Average" },
            { "@type": "CopyAction", "name": "Copy Calculation Results" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to calculate overall average score?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate an overall average score, add up all the individual scores and divide by the total number of scores. For example, if you have test scores of 85, 90, and 78, add them (85+90+78=253) and divide by 3 (253÷3=84.33). This gives you the mean average score."
          }
        },
        {
          "@type": "Question",
          "name": "How do you find out your overall average?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Finding your overall average depends on what you're averaging. For grades, add all your scores and divide by the number of assignments. For weighted averages, multiply each score by its weight, add the results, then divide by the total weight. Our calculator handles both simple and weighted averages automatically."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate an overall mean?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The overall mean is calculated by summing all values in your dataset and dividing by the count of values. This is the most common type of average. Simply enter your numbers into the calculator, and it will compute the mean along with other statistical measures like median and mode."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate average cost?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate average cost, add up all the costs and divide by the number of items or time periods. For example, if you spent $50, $30, and $70 over three months, the average monthly cost is ($50+$30+$70)÷3 = $50. This method works for any cost analysis."
          }
        },
        {
          "@type": "Question",
          "name": "How to know your average using a calculator?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using our online average calculator is simple: enter your numbers separated by commas or spaces, then click 'Calculate Average'. The calculator will instantly show your mean, median, mode, and range. You can also copy the results for use in reports or assignments."
          }
        }
      ]
    }
    </script>


    <style>
        /* Average Calculator Widget - Simplified & Template Compatible */
        .average-calculator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .average-calculator-widget-container * { box-sizing: border-box; }

        .average-calculator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .average-calculator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .average-calculator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .average-calculator-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .average-calculator-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .average-calculator-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .average-calculator-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .average-calculator-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .average-calculator-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .average-calculator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .average-calculator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .average-calculator-btn:hover { transform: translateY(-2px); }

        .average-calculator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .average-calculator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .average-calculator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .average-calculator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .average-calculator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .average-calculator-btn-success:hover {
            background-color: #059669;
        }

        .average-calculator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .average-calculator-result-title {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .average-calculator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 120px;
            color: var(--text-color);
            line-height: 1.8;
            white-space: pre-wrap;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            margin: 0;
        }

        .average-calculator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .average-calculator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .average-calculator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .average-calculator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .average-calculator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .average-calculator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .average-calculator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .average-calculator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="percentage-calculator"] .average-calculator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="confidence-interval-calculator"] .average-calculator-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="margin-calculator"] .average-calculator-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }

        .average-calculator-related-tool-item:hover .average-calculator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="percentage-calculator"]:hover .average-calculator-related-tool-icon { background: linear-gradient(145deg, #9d6ff7, #8e4aee); }
        a[href*="confidence-interval-calculator"]:hover .average-calculator-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #047857); }
        a[href*="margin-calculator"]:hover .average-calculator-related-tool-icon { background: linear-gradient(145deg, #f6a509, #c67006); }
        
        .average-calculator-related-tool-item { box-shadow: none; border: none; }
        .average-calculator-related-tool-item:hover { box-shadow: none; border: none; }
        .average-calculator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .average-calculator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .average-calculator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .average-calculator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .average-calculator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .average-calculator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .average-calculator-related-tool-item:hover .average-calculator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .average-calculator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .average-calculator-widget-title { font-size: 1.875rem; }
            .average-calculator-buttons { flex-direction: column; }
            .average-calculator-btn { flex: none; }
            .average-calculator-options { grid-template-columns: 1fr; }
            .average-calculator-output { 
                padding: var(--spacing-xs) var(--spacing-sm); 
                font-size: 0.875rem; 
                min-height: 100px; 
                line-height: 1.6; 
            }
            .average-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .average-calculator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .average-calculator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .average-calculator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .average-calculator-output { 
                padding: var(--spacing-xs) var(--spacing-xs); 
                font-size: 0.8rem; 
                min-height: 80px; 
                line-height: 1.5; 
            }
            .average-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .average-calculator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .average-calculator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .average-calculator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .average-calculator-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .average-calculator-checkbox:focus, .average-calculator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .average-calculator-output::selection { background-color: var(--primary-color); color: white; }
        .average-calculator-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .average-calculator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="average-calculator-widget-container">
        <h1 class="average-calculator-widget-title">Average Calculator</h1>
        <p class="average-calculator-widget-description">
            Calculate mean, median, mode, and range for any set of numbers instantly. Perfect for analyzing data sets, calculating grades, and statistical analysis.
        </p>
        
        <div class="average-calculator-input-group">
            <label for="averageCalculatorInput" class="average-calculator-label">Enter your numbers:</label>
            <textarea 
                id="averageCalculatorInput" 
                class="average-calculator-textarea"
                placeholder="Enter numbers separated by commas, spaces, or new lines (e.g., 85, 90, 78, 92, 88)"
                rows="4"
            ></textarea>
        </div>

        <div class="average-calculator-options">
            <div class="average-calculator-option">
                <input type="checkbox" id="showMean" class="average-calculator-checkbox" checked>
                <label for="showMean" class="average-calculator-option-label">Show Mean (Average)</label>
            </div>
            <div class="average-calculator-option">
                <input type="checkbox" id="showMedian" class="average-calculator-checkbox" checked>
                <label for="showMedian" class="average-calculator-option-label">Show Median</label>
            </div>
            <div class="average-calculator-option">
                <input type="checkbox" id="showMode" class="average-calculator-checkbox" checked>
                <label for="showMode" class="average-calculator-option-label">Show Mode</label>
            </div>
            <div class="average-calculator-option">
                <input type="checkbox" id="showRange" class="average-calculator-checkbox" checked>
                <label for="showRange" class="average-calculator-option-label">Show Range</label>
            </div>
        </div>

        <div class="average-calculator-buttons">
            <button class="average-calculator-btn average-calculator-btn-primary" onclick="AverageCalculator.calculate()">
                Calculate Average
            </button>
            <button class="average-calculator-btn average-calculator-btn-secondary" onclick="AverageCalculator.clear()">
                Clear All
            </button>
            <button class="average-calculator-btn average-calculator-btn-success" onclick="AverageCalculator.copy()">
                Copy Result
            </button>
        </div>

        <div class="average-calculator-result">
            <h3 class="average-calculator-result-title">Calculation Results:</h3>
            <div class="average-calculator-output" id="averageCalculatorOutput">
                Your calculation results will appear here...
            </div>
        </div>

        <div class="average-calculator-related-tools">
            <h3 class="average-calculator-related-tools-title">Related Tools</h3>
            <div class="average-calculator-related-tools-grid">
                <a href="/p/percentage-calculator.html" class="average-calculator-related-tool-item" rel="noopener">
                    <div class="average-calculator-related-tool-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="average-calculator-related-tool-name">Percentage Calculator</div>
                </a>

                <a href="/p/confidence-interval-calculator.html" class="average-calculator-related-tool-item" rel="noopener">
                    <div class="average-calculator-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="average-calculator-related-tool-name">Confidence Interval Calculator</div>
                </a>

                <a href="/p/margin-calculator.html" class="average-calculator-related-tool-item" rel="noopener">
                    <div class="average-calculator-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="average-calculator-related-tool-name">Margin Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Calculate Averages with Precision and Ease</h2>
            <p>An <strong>Average Calculator</strong> is an essential tool for anyone working with numerical data. Whether you're a student calculating grade averages, a business analyst reviewing performance metrics, or a researcher analyzing survey data, understanding different types of averages is crucial for making informed decisions. Our comprehensive calculator computes mean, median, mode, and range instantly, giving you a complete statistical overview of your dataset.</p>
            <p>The mean (arithmetic average) is the most common measure of central tendency, calculated by summing all values and dividing by the count. However, median and mode provide additional insights that can be more representative in certain situations, especially when dealing with skewed data or outliers.</p>
            
            <h3>How to Use the Average Calculator</h3>
            <ol>
                <li><strong>Enter Your Numbers:</strong> Type or paste your numbers into the input field. You can separate them with commas, spaces, or new lines.</li>
                <li><strong>Choose Your Options:</strong> Select which statistical measures you want to calculate using the checkboxes. All options are enabled by default.</li>
                <li><strong>Calculate and Review:</strong> Click "Calculate Average" to get instant results showing mean, median, mode, and range with detailed explanations.</li>
                <li><strong>Copy Results:</strong> Use the "Copy Result" button to easily paste your calculations into reports, assignments, or presentations.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Average Calculations</h3>
            
            <h4>How to calculate overall average score?</h4>
            <p>To calculate an overall average score, add up all the individual scores and divide by the total number of scores. For example, if you have test scores of 85, 90, and 78, add them (85+90+78=253) and divide by 3 (253÷3=84.33). This gives you the mean average score.</p>
            
            <h4>How do you find out your overall average?</h4>
            <p>Finding your overall average depends on what you're averaging. For grades, add all your scores and divide by the number of assignments. For weighted averages, multiply each score by its weight, add the results, then divide by the total weight. Our calculator handles both simple and weighted averages automatically.</p>
            
            <h4>How to calculate an overall mean?</h4>
            <p>The overall mean is calculated by summing all values in your dataset and dividing by the count of values. This is the most common type of average. Simply enter your numbers into the calculator, and it will compute the mean along with other statistical measures like median and mode.</p>
            
            <h4>How to calculate average cost?</h4>
            <p>To calculate average cost, add up all the costs and divide by the number of items or time periods. For example, if you spent $50, $30, and $70 over three months, the average monthly cost is ($50+$30+$70)÷3 = $50. This method works for any cost analysis.</p>
            
            <h4>How to know your average using a calculator?</h4>
            <p>Using our online average calculator is simple: enter your numbers separated by commas or spaces, then click 'Calculate Average'. The calculator will instantly show your mean, median, mode, and range. You can also copy the results for use in reports or assignments.</p>
        </div>


        <div class="average-calculator-features">
            <h3 class="average-calculator-features-title">Key Features:</h3>
            <ul class="average-calculator-features-list">
                <li class="average-calculator-features-item" style="margin-bottom: 0.3em;">Calculate mean, median, mode, and range</li>
                <li class="average-calculator-features-item" style="margin-bottom: 0.3em;">Handles multiple input formats</li>
                <li class="average-calculator-features-item" style="margin-bottom: 0.3em;">Detailed step-by-step results</li>
                <li class="average-calculator-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="average-calculator-features-item" style="margin-bottom: 0.3em;">Customizable output options</li>
                <li class="average-calculator-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="average-calculator-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="average-calculator-notification" id="averageCalculatorNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Average Calculator
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('averageCalculatorInput'),
                output: () => document.getElementById('averageCalculatorOutput'),
                notification: () => document.getElementById('averageCalculatorNotification')
            };

            window.AverageCalculator = {
                calculate() {
                    const input = elements.input();
                    const output = elements.output();
                    const text = input.value.trim();

                    if (!text) {
                        output.textContent = 'Please enter some numbers to calculate.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    // Parse numbers from input
                    const numbers = this.parseNumbers(text);
                    
                    if (numbers.length === 0) {
                        output.textContent = 'Please enter valid numbers.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        showMean: document.getElementById('showMean').checked,
                        showMedian: document.getElementById('showMedian').checked,
                        showMode: document.getElementById('showMode').checked,
                        showRange: document.getElementById('showRange').checked
                    };

                    const results = this.calculateStatistics(numbers, options);
                    output.textContent = results;
                },

                parseNumbers(text) {
                    // Split by commas, spaces, newlines, or combination
                    const matches = text.match(/[-+]?(\d*\.?\d+)/g);
                    if (!matches) return [];
                    
                    return matches.map(num => parseFloat(num)).filter(num => !isNaN(num));
                },

                calculateStatistics(numbers, options) {
                    const sortedNumbers = [...numbers].sort((a, b) => a - b);
                    const count = numbers.length;
                    let results = [];

                    // Add input summary
                    results.push(`Numbers entered: ${numbers.join(', ')}`);
                    results.push(`Total count: ${count}`);
                    results.push('');

                    // Calculate mean
                    if (options.showMean) {
                        const sum = numbers.reduce((acc, num) => acc + num, 0);
                        const mean = sum / count;
                        results.push(`Mean (Average): ${mean.toFixed(4)}`);
                        results.push(`Calculation: (${numbers.join(' + ')}) ÷ ${count} = ${mean.toFixed(4)}`);
                        results.push('');
                    }

                    // Calculate median
                    if (options.showMedian) {
                        let median;
                        if (count % 2 === 0) {
                            const mid1 = sortedNumbers[count / 2 - 1];
                            const mid2 = sortedNumbers[count / 2];
                            median = (mid1 + mid2) / 2;
                            results.push(`Median: ${median.toFixed(4)}`);
                            results.push(`Calculation: (${mid1} + ${mid2}) ÷ 2 = ${median.toFixed(4)}`);
                        } else {
                            median = sortedNumbers[Math.floor(count / 2)];
                            results.push(`Median: ${median.toFixed(4)}`);
                            results.push(`Middle value in sorted list: ${median}`);
                        }
                        results.push('');
                    }

                    // Calculate mode
                    if (options.showMode) {
                        const frequency = {};
                        numbers.forEach(num => {
                            frequency[num] = (frequency[num] || 0) + 1;
                        });
                        
                        const maxFrequency = Math.max(...Object.values(frequency));
                        const modes = Object.keys(frequency).filter(num => frequency[num] === maxFrequency);
                        
                        if (maxFrequency === 1) {
                            results.push('Mode: No mode (all values appear once)');
                        } else {
                            results.push(`Mode: ${modes.join(', ')}`);
                            results.push(`Frequency: ${maxFrequency} times each`);
                        }
                        results.push('');
                    }

                    // Calculate range
                    if (options.showRange) {
                        const min = sortedNumbers[0];
                        const max = sortedNumbers[sortedNumbers.length - 1];
                        const range = max - min;
                        results.push(`Range: ${range.toFixed(4)}`);
                        results.push(`Calculation: ${max} - ${min} = ${range.toFixed(4)}`);
                        results.push(`Minimum: ${min}, Maximum: ${max}`);
                    }

                    return results.join('\n');
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your calculation results will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your calculation results will appear here...', 'Please enter some numbers to calculate.', 'Please enter valid numbers.'].includes(text)) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                const checkboxes = document.querySelectorAll('.average-calculator-checkbox');

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        AverageCalculator.calculate();
                    }
                });
            });
        })();
    </script>
</body>
</html>