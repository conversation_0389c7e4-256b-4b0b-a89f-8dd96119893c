<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UTM Builder - Free Campaign URL Generator</title>
    <meta name="description" content="Easily create UTM tracking links for your marketing campaigns. Our free online UTM builder helps you generate tagged URLs for Google Analytics and other platforms.">
    <link rel="canonical" href="https://www.webtoolskit.org/p/utm-builder.html">
    <!-- Font Awesome CDN for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "UTM Builder - Free Campaign URL Generator",
        "description": "Easily create UTM tracking links for your marketing campaigns. Our free online UTM builder helps you generate tagged URLs for Google Analytics and other platforms.",
        "url": "https://www.webtoolskit.org/p/utm-builder.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-07-06",
        "dateModified": "2025-07-06",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "UTM Builder",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Generate UTM Link" },
            { "@type": "CopyAction", "name": "Copy UTM Link" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a UTM builder?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A UTM builder is a tool that helps you add UTM (Urchin Tracking Module) parameters to a URL. These parameters are tags that you add to a URL so you can track the effectiveness of your online marketing campaigns in analytics platforms like Google Analytics. When a user clicks the tagged link, the parameters are sent back to your analytics account."
          }
        },
        {
          "@type": "Question",
          "name": "What are the 5 main UTM parameters?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The five standard UTM parameters are: utm_source (the referrer, e.g., google, newsletter), utm_medium (the marketing medium, e.g., cpc, email), utm_campaign (the specific campaign name, e.g., summer_sale), utm_term (used for paid keywords), and utm_content (to differentiate ads or links that point to the same URL)."
          }
        },
        {
          "@type": "Question",
          "name": "How do I create a UTM link for a campaign?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using a UTM builder is the easiest way. First, enter your base website URL. Then, fill in the values for your Campaign Source, Medium, and Name. You can also add optional parameters like Term and Content. The tool will automatically generate a complete URL with all the parameters correctly formatted for you to copy and use."
          }
        },
        {
          "@type": "Question",
          "name": "How do I track UTM codes in Google Analytics?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Google Analytics automatically detects and tracks UTM parameters from incoming URLs. You can find the data in your Google Analytics account under Acquisition > Traffic acquisition, and then by looking at the 'Session source / medium' and 'Session campaign' dimensions. No extra setup is needed in GA to start tracking them."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between utm_source and utm_medium?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "utm_source identifies where the traffic is coming from (the referrer). For example, 'google', 'facebook', or 'newsletter'. utm_medium identifies the type of link that was used, or the marketing channel. For example, 'cpc' (cost-per-click), 'social', or 'email'. Together, they tell a clear story: for instance, traffic came from Facebook (source) via a paid ad (medium)."
          }
        }
      ]
    }
    </script>


    <style>
        /* UTM Builder Widget - Simplified & Template Compatible */
        .utm-builder-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .utm-builder-widget-container * { box-sizing: border-box; }

        .utm-builder-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .utm-builder-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .utm-builder-form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        @media (min-width: 768px) {
            .utm-builder-form-grid {
                grid-template-columns: 1fr 1fr;
            }
            .utm-builder-input-group.full-width {
                grid-column: 1 / -1;
            }
        }

        .utm-builder-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }
        
        .utm-builder-label .required {
            color: #dc2626;
            margin-left: 4px;
        }

        .utm-builder-input {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .utm-builder-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }
        
        .utm-builder-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .utm-builder-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .utm-builder-btn:hover { transform: translateY(-2px); }

        .utm-builder-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .utm-builder-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .utm-builder-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .utm-builder-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .utm-builder-btn-success {
            background-color: #10b981;
            color: white;
        }

        .utm-builder-btn-success:hover {
            background-color: #059669;
        }

        .utm-builder-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .utm-builder-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .utm-builder-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            white-space: pre-wrap;
            min-height: 100px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .utm-builder-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .utm-builder-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .utm-builder-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .utm-builder-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .utm-builder-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .utm-builder-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .utm-builder-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .utm-builder-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="url-parser"] .utm-builder-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="url-encoder"] .utm-builder-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="qr-code-generator"] .utm-builder-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .utm-builder-related-tool-item:hover .utm-builder-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="url-parser"]:hover .utm-builder-related-tool-icon { background: linear-gradient(145deg, #f472b6, #EC4899); }
        a[href*="url-encoder"]:hover .utm-builder-related-tool-icon { background: linear-gradient(145deg, #16a34a, #10B981); }
        a[href*="qr-code-generator"]:hover .utm-builder-related-tool-icon { background: linear-gradient(145deg, #9333ea, #8B5CF6); }
        
        .utm-builder-related-tool-item { box-shadow: none; border: none; }
        .utm-builder-related-tool-item:hover { box-shadow: none; border: none; }
        .utm-builder-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .utm-builder-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .utm-builder-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .utm-builder-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .utm-builder-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .utm-builder-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .utm-builder-related-tool-item:hover .utm-builder-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .utm-builder-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .utm-builder-widget-title { font-size: 1.875rem; }
            .utm-builder-buttons { flex-direction: column; }
            .utm-builder-btn { flex: none; }
            .utm-builder-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .utm-builder-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .utm-builder-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .utm-builder-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .utm-builder-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .utm-builder-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .utm-builder-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .utm-builder-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .utm-builder-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .utm-builder-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .utm-builder-output::selection { background-color: var(--primary-color); color: white; }
        @media (max-width: 600px) { .utm-builder-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="utm-builder-widget-container">
        <h1 class="utm-builder-widget-title">UTM Builder</h1>
        <p class="utm-builder-widget-description">
            Create perfectly structured campaign URLs with UTM parameters for accurate tracking in Google Analytics and other marketing platforms.
        </p>
        
        <div class="utm-builder-form-grid">
            <div class="utm-builder-input-group full-width">
                <label for="utmUrl" class="utm-builder-label">Website URL <span class="required">*</span></label>
                <input type="text" id="utmUrl" class="utm-builder-input" placeholder="https://www.example.com">
            </div>
            <div class="utm-builder-input-group">
                <label for="utmSource" class="utm-builder-label">Campaign Source <span class="required">*</span></label>
                <input type="text" id="utmSource" class="utm-builder-input" placeholder="e.g., google, newsletter">
            </div>
            <div class="utm-builder-input-group">
                <label for="utmMedium" class="utm-builder-label">Campaign Medium <span class="required">*</span></label>
                <input type="text" id="utmMedium" class="utm-builder-input" placeholder="e.g., cpc, email">
            </div>
            <div class="utm-builder-input-group">
                <label for="utmCampaign" class="utm-builder-label">Campaign Name <span class="required">*</span></label>
                <input type="text" id="utmCampaign" class="utm-builder-input" placeholder="e.g., summer_sale">
            </div>
            <div class="utm-builder-input-group">
                <label for="utmTerm" class="utm-builder-label">Campaign Term</label>
                <input type="text" id="utmTerm" class="utm-builder-input" placeholder="(Optional) e.g., running+shoes">
            </div>
            <div class="utm-builder-input-group">
                <label for="utmContent" class="utm-builder-label">Campaign Content</label>
                <input type="text" id="utmContent" class="utm-builder-input" placeholder="(Optional) e.g., logo_link">
            </div>
        </div>
        
        <div class="utm-builder-buttons">
            <button class="utm-builder-btn utm-builder-btn-primary" onclick="UtmBuilder.generate()">
                Generate URL
            </button>
            <button class="utm-builder-btn utm-builder-btn-secondary" onclick="UtmBuilder.clear()">
                Clear All
            </button>
            <button class="utm-builder-btn utm-builder-btn-success" onclick="UtmBuilder.copy()">
                Copy URL
            </button>
        </div>

        <div class="utm-builder-result">
            <h3 class="utm-builder-result-title">Generated Campaign URL:</h3>
            <div class="utm-builder-output" id="utmOutput">Your generated URL will appear here...</div>
        </div>

        <div class="utm-builder-related-tools">
            <h3 class="utm-builder-related-tools-title">Related Tools</h3>
            <div class="utm-builder-related-tools-grid">
                <a href="/p/url-parser.html" class="utm-builder-related-tool-item" rel="noopener">
                    <div class="utm-builder-related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="utm-builder-related-tool-name">URL Parser</div>
                </a>
                <a href="/p/url-encoder.html" class="utm-builder-related-tool-item" rel="noopener">
                    <div class="utm-builder-related-tool-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="utm-builder-related-tool-name">URL Encoder</div>
                </a>
                <a href="/p/qr-code-generator.html" class="utm-builder-related-tool-item" rel="noopener">
                    <div class="utm-builder-related-tool-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="utm-builder-related-tool-name">QR Code Generator</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Track Your Campaigns Accurately with Our UTM Builder</h2>
            <p>Understanding which marketing efforts are driving traffic and conversions is essential for success. That's where UTM parameters come in. Our free online <strong>UTM Builder</strong> helps you easily add these tracking tags to your URLs, ensuring every click is properly attributed in your analytics platform. By creating consistent, well-structured campaign links, you can measure the ROI of your ads, social media posts, and email newsletters with precision, allowing you to make data-driven decisions and optimize your strategy.</p>
            
            <h3>How to Use the UTM Builder</h3>
            <ol>
                <li><strong>Enter Your Base URL:</strong> Start by pasting the URL of the landing page you want to send traffic to.</li>
                <li><strong>Fill in Campaign Parameters:</strong> Complete the required fields: Source, Medium, and Campaign Name. These tell you where the traffic came from, the channel, and the specific campaign.</li>
                <li><strong>Add Optional Parameters:</strong> For more detailed tracking, you can add Term (for paid keywords) and Content (to A/B test ads or links).</li>
                <li><strong>Generate and Copy Your URL:</strong> Click the "Generate URL" button. The tool will create the final tagged URL, which you can then copy and use in your marketing materials.</li>
            </ol>
            
            <h3>The Importance of Consistent UTM Tagging</h3>
            <p>Consistency is key to effective campaign tracking. Using a standardized naming convention for your sources, mediums, and campaigns prevents your analytics data from becoming fragmented and confusing. A UTM builder encourages this consistency, ensuring that data from 'google', 'Google', and 'google.com' are all tracked as one source. This clean data provides clear insights into which channels deliver the best results.</p>
        
            <h3>Frequently Asked Questions About UTM Builders</h3>
            
            <h4>What is a UTM builder?</h4>
            <p>A UTM builder is a tool that helps you add UTM (Urchin Tracking Module) parameters to a URL. These parameters are tags that you add to a URL so you can track the effectiveness of your online marketing campaigns in analytics platforms like Google Analytics. When a user clicks the tagged link, the parameters are sent back to your analytics account.</p>
            
            <h4>What are the 5 main UTM parameters?</h4>
            <p>The five standard UTM parameters are: utm_source (the referrer, e.g., google, newsletter), utm_medium (the marketing medium, e.g., cpc, email), utm_campaign (the specific campaign name, e.g., summer_sale), utm_term (used for paid keywords), and utm_content (to differentiate ads or links that point to the same URL).</p>
            
            <h4>How do I create a UTM link for a campaign?</h4>
            <p>Using a UTM builder is the easiest way. First, enter your base website URL. Then, fill in the values for your Campaign Source, Medium, and Name. You can also add optional parameters like Term and Content. The tool will automatically generate a complete URL with all the parameters correctly formatted for you to copy and use.</p>
            
            <h4>How do I track UTM codes in Google Analytics?</h4>
            <p>Google Analytics automatically detects and tracks UTM parameters from incoming URLs. You can find the data in your Google Analytics account under Acquisition > Traffic acquisition, and then by looking at the 'Session source / medium' and 'Session campaign' dimensions. No extra setup is needed in GA to start tracking them.</p>
            
            <h4>What is the difference between utm_source and utm_medium?</h4>
            <p>utm_source identifies where the traffic is coming from (the referrer). For example, 'google', 'facebook', or 'newsletter'. utm_medium identifies the type of link that was used, or the marketing channel. For example, 'cpc' (cost-per-click), 'social', or 'email'. Together, they tell a clear story: for instance, traffic came from Facebook (source) via a paid ad (medium).</p>
        </div>

        <div class="utm-builder-features">
            <h3 class="utm-builder-features-title">Key Features:</h3>
            <ul class="utm-builder-features-list">
                <li class="utm-builder-features-item">Real-time URL generation</li>
                <li class="utm-builder-features-item">Supports all 5 UTM parameters</li>
                <li class="utm-builder-features-item">Validates base URL format</li>
                <li class="utm-builder-features-item">Encourages consistent tagging</li>
                <li class="utm-builder-features-item">Easy one-click copy</li>
                <li class="utm-builder-features-item">Improves campaign tracking</li>
                <li class="utm-builder-features-item">Perfect for Google Analytics</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="utm-builder-notification" id="utmNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // UTM Builder
        (function() {
            'use strict';

            const elements = {
                output: () => document.getElementById('utmOutput'),
                notification: () => document.getElementById('utmNotification'),
                url: () => document.getElementById('utmUrl'),
                source: () => document.getElementById('utmSource'),
                medium: () => document.getElementById('utmMedium'),
                campaign: () => document.getElementById('utmCampaign'),
                term: () => document.getElementById('utmTerm'),
                content: () => document.getElementById('utmContent'),
                allInputs: () => document.querySelectorAll('.utm-builder-input')
            };

            window.UtmBuilder = {
                generate() {
                    const baseUrl = elements.url().value.trim();
                    const params = {
                        utm_source: elements.source().value.trim(),
                        utm_medium: elements.medium().value.trim(),
                        utm_campaign: elements.campaign().value.trim(),
                        utm_term: elements.term().value.trim(),
                        utm_content: elements.content().value.trim(),
                    };

                    elements.output().style.color = '';

                    if (!baseUrl || !params.utm_source || !params.utm_medium || !params.utm_campaign) {
                        elements.output().textContent = 'Please fill in all required fields (*) to generate the URL.';
                        elements.output().style.color = '#dc2626';
                        return;
                    }

                    try {
                        new URL(baseUrl);
                    } catch (e) {
                        elements.output().textContent = 'Invalid base URL. Please check the format (e.g., https://example.com).';
                        elements.output().style.color = '#dc2626';
                        return;
                    }

                    const queryParams = [];
                    for (const key in params) {
                        if (params[key]) {
                            queryParams.push(`${key}=${encodeURIComponent(params[key])}`);
                        }
                    }

                    const separator = baseUrl.includes('?') ? '&' : '?';
                    const finalUrl = baseUrl + separator + queryParams.join('&');
                    elements.output().textContent = finalUrl;
                },

                clear() {
                    elements.allInputs().forEach(input => input.value = '');
                    elements.output().textContent = 'Your generated URL will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text.includes('...') || text.startsWith('Invalid') || text.startsWith('Please fill')) {
                       this.showNotification('Nothing to copy. Please generate a valid URL first.', true);
                       return;
                    }
                    
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification('✓ Copied to clipboard!')).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification('✓ Copied to clipboard!');
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message, isError = false) {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.style.backgroundColor = isError ? '#dc2626' : '#10b981';
                    notification.classList.add('show');
                    setTimeout(() => {
                       notification.classList.remove('show');
                    }, 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }
            });
        })();
    </script>
</body>
</html>