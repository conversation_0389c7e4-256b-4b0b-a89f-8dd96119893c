<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UUID Generator - Generate Unique IDs Online (v1 & v4)</title>
    <meta name="description" content="Generate one or multiple Version 1 (timestamp-based) or Version 4 (random) UUIDs instantly. Our free online tool is perfect for developers and database admins.">
    <link rel="canonical" href="https://www.webtoolskit.org/p/uuid-generator.html">
    <!-- Font Awesome CDN for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "UUID Generator - Generate Unique IDs Online (v1 & v4)",
        "description": "Generate one or multiple Version 1 (timestamp-based) or Version 4 (random) UUIDs instantly. Our free online tool is perfect for developers and database admins.",
        "url": "https://www.webtoolskit.org/p/uuid-generator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-07-04",
        "dateModified": "2025-07-04",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "UUID Generator",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Generate UUID" },
            { "@type": "CopyAction", "name": "Copy UUIDs" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a UUID used for?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A UUID (Universally Unique Identifier) is used to generate unique IDs for things like database records (as primary keys), transaction IDs, user accounts, and any other item that needs a unique reference without relying on a central number generator. This is especially useful in distributed systems."
          }
        },
        {
          "@type": "Question",
          "name": "Are UUIDs guaranteed to be unique?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "While not mathematically guaranteed, the probability of a collision (two identical UUIDs) is astronomically low, making them unique for all practical purposes. For Version 4 UUIDs, there are 2^122 possible combinations, so the chance of a duplicate is negligible."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between UUID v1 and v4?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "UUID v1 is generated based on the current timestamp and a device-specific MAC address (or a random node), making them time-sortable but potentially revealing information about when and where it was created. UUID v4 is generated from purely random numbers and is the most common version used today as it does not expose any information about the creator."
          }
        },
        {
          "@type": "Question",
          "name": "How do you generate a UUID?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You can generate a UUID using an online tool like this one by simply clicking a button. In programming, most languages have built-in libraries to generate them. For instance, in browser JavaScript, you can use `crypto.randomUUID()` to generate a Version 4 UUID."
          }
        },
        {
          "@type": "Question",
          "name": "Should you use UUIDs for session IDs?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, Version 4 UUIDs are an excellent choice for session IDs. They are long, random, and computationally difficult to guess, which provides a strong foundation for secure session management. Version 1 UUIDs are not recommended for security-sensitive contexts as they can be predictable."
          }
        }
      ]
    }
    </script>


    <style>
        /* UUID Generator Widget - Simplified & Template Compatible */
        .uuid-generator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .uuid-generator-widget-container * { box-sizing: border-box; }

        .uuid-generator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .uuid-generator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .uuid-generator-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .uuid-generator-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .uuid-generator-radio-group, .uuid-generator-number-group {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
        }

        .uuid-generator-radio {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .uuid-generator-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }
        
        .uuid-generator-number-input {
            width: 80px;
            padding: var(--spacing-sm);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-weight: 500;
        }

        .uuid-generator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .uuid-generator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .uuid-generator-btn:hover { transform: translateY(-2px); }

        .uuid-generator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .uuid-generator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .uuid-generator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .uuid-generator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .uuid-generator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .uuid-generator-btn-success:hover {
            background-color: #059669;
        }

        .uuid-generator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .uuid-generator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .uuid-generator-output {
            width: 100%;
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            min-height: 150px;
            color: var(--text-color);
            line-height: 1.5;
            resize: vertical;
        }

        .uuid-generator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .uuid-generator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .uuid-generator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .uuid-generator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .uuid-generator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .uuid-generator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .uuid-generator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .uuid-generator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="url-parser"] .uuid-generator-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="javascript-obfuscator"] .uuid-generator-related-tool-icon { background: linear-gradient(145deg, #84CC16, #65A30D); }
        a[href*="html-minifier"] .uuid-generator-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }


        .uuid-generator-related-tool-item:hover .uuid-generator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="url-parser"]:hover .uuid-generator-related-tool-icon { background: linear-gradient(145deg, #f472b6, #EC4899); }
        a[href*="javascript-obfuscator"]:hover .uuid-generator-related-tool-icon { background: linear-gradient(145deg, #9fdd3b, #84CC16); }
        a[href*="html-minifier"]:hover .uuid-generator-related-tool-icon { background: linear-gradient(145deg, #7c82f2, #6366F1); }
        
        .uuid-generator-related-tool-item { box-shadow: none; border: none; }
        .uuid-generator-related-tool-item:hover { box-shadow: none; border: none; }
        .uuid-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .uuid-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .uuid-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .uuid-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .uuid-generator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .uuid-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .uuid-generator-related-tool-item:hover .uuid-generator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .uuid-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .uuid-generator-widget-title { font-size: 1.875rem; }
            .uuid-generator-buttons { flex-direction: column; }
            .uuid-generator-btn { flex: none; }
            .uuid-generator-options { grid-template-columns: 1fr; }
            .uuid-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .uuid-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .uuid-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .uuid-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .uuid-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .uuid-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .uuid-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .uuid-generator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .uuid-generator-number-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .uuid-generator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .uuid-generator-output::selection { background-color: var(--primary-color); color: white; }
        @media (max-width: 600px) { .uuid-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="uuid-generator-widget-container">
        <h1 class="uuid-generator-widget-title">UUID Generator</h1>
        <p class="uuid-generator-widget-description">
            Generate standards-compliant Version 1 (timestamp-based) and Version 4 (random) Universally Unique Identifiers right in your browser.
        </p>
        
        <div class="uuid-generator-options">
            <div class="uuid-generator-radio-group">
                <label class="uuid-generator-option-label">Version:</label>
                <div class="uuid-generator-option">
                    <input type="radio" id="uuid-v1" name="uuid-version" value="v1" class="uuid-generator-radio">
                    <label for="uuid-v1" class="uuid-generator-option-label">v1</label>
                </div>
                <div class="uuid-generator-option">
                    <input type="radio" id="uuid-v4" name="uuid-version" value="v4" class="uuid-generator-radio" checked>
                    <label for="uuid-v4" class="uuid-generator-option-label">v4</label>
                </div>
            </div>
            <div class="uuid-generator-number-group">
                <label for="uuid-amount" class="uuid-generator-option-label">Amount:</label>
                <input type="number" id="uuid-amount" class="uuid-generator-number-input" value="1" min="1" max="1000">
            </div>
        </div>

        <div class="uuid-generator-buttons">
            <button class="uuid-generator-btn uuid-generator-btn-primary" onclick="UuidGenerator.generate()">
                Generate UUID(s)
            </button>
            <button class="uuid-generator-btn uuid-generator-btn-success" onclick="UuidGenerator.copy()">
                Copy
            </button>
            <button class="uuid-generator-btn uuid-generator-btn-secondary" onclick="UuidGenerator.clear()">
                Clear
            </button>
        </div>

        <div class="uuid-generator-result">
            <h3 class="uuid-generator-result-title">Generated UUIDs:</h3>
            <textarea class="uuid-generator-output" id="uuidOutput" readonly rows="5" placeholder="Your generated UUIDs will appear here..."></textarea>
        </div>

        <div class="uuid-generator-related-tools">
            <h3 class="uuid-generator-related-tools-title">Related Tools</h3>
            <div class="uuid-generator-related-tools-grid">
                <a href="/p/url-parser.html" class="uuid-generator-related-tool-item" rel="noopener">
                    <div class="uuid-generator-related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="uuid-generator-related-tool-name">URL Parser</div>
                </a>
                <a href="/p/javascript-obfuscator.html" class="uuid-generator-related-tool-item" rel="noopener">
                    <div class="uuid-generator-related-tool-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="uuid-generator-related-tool-name">Javascript Obfuscator</div>
                </a>
                <a href="/p/html-minifier.html" class="uuid-generator-related-tool-item" rel="noopener">
                    <div class="uuid-generator-related-tool-icon">
                        <i class="fas fa-compress"></i>
                    </div>
                    <div class="uuid-generator-related-tool-name">HTML Minifier</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Generate Unique IDs with Our UUID Generator</h2>
            <p>A Universally Unique Identifier (UUID) is a 128-bit number used to uniquely identify information in computer systems. Our <strong>UUID Generator</strong> provides a simple and powerful interface to generate both Version 1 (timestamp-based) and Version 4 (random) UUIDs. Whether you're a developer needing primary keys for a database, a system architect designing distributed systems, or just need a unique ID, this tool delivers standards-compliant UUIDs instantly.</p>
            
            <h3>How to Use the UUID Generator</h3>
            <ol>
                <li><strong>Select a Version:</strong> Choose between UUID v1 (based on the current time and a unique node) or UUID v4 (based on random numbers). V4 is recommended for most use cases.</li>
                <li><strong>Set the Amount:</strong> Enter the number of UUIDs you want to generate in a single batch.</li>
                <li><strong>Click "Generate UUID(s)":</strong> The tool will create the requested number of unique IDs.</li>
                <li><strong>Copy the Results:</strong> The generated UUIDs will appear in the text box, ready to be copied and used in your application.</li>
            </ol>
            
            <h3>Practical Use Cases for UUIDs</h3>
            <p>UUIDs are indispensable in modern software development. They are commonly used as primary keys in database tables, ensuring each record has a unique ID that can be generated anywhere without conflicts. They're also used for transaction IDs, session identifiers for web applications, and in any scenario where creating a unique identifier without a centralized authority is necessary.</p>
        
            <h3>Frequently Asked Questions About UUIDs</h3>
            
            <h4>What is a UUID used for?</h4>
            <p>A UUID (Universally Unique Identifier) is used to generate unique IDs for things like database records (as primary keys), transaction IDs, user accounts, and any other item that needs a unique reference without relying on a central number generator. This is especially useful in distributed systems.</p>
            
            <h4>Are UUIDs guaranteed to be unique?</h4>
            <p>While not mathematically guaranteed, the probability of a collision (two identical UUIDs) is astronomically low, making them unique for all practical purposes. For Version 4 UUIDs, there are 2^122 possible combinations, so the chance of a duplicate is negligible.</p>
            
            <h4>What is the difference between UUID v1 and v4?</h4>
            <p>UUID v1 is generated based on the current timestamp and a device-specific MAC address (or a random node), making them time-sortable but potentially revealing information about when and where it was created. UUID v4 is generated from purely random numbers and is the most common version used today as it does not expose any information about the creator.</p>
            
            <h4>How do you generate a UUID?</h4>
            <p>You can generate a UUID using an online tool like this one by simply clicking a button. In programming, most languages have built-in libraries to generate them. For instance, in browser JavaScript, you can use `crypto.randomUUID()` to generate a Version 4 UUID.</p>
            
            <h4>Should you use UUIDs for session IDs?</h4>
            <p>Yes, Version 4 UUIDs are an excellent choice for session IDs. They are long, random, and computationally difficult to guess, which provides a strong foundation for secure session management. Version 1 UUIDs are not recommended for security-sensitive contexts as they can be predictable.</p>
        </div>

        <div class="uuid-generator-features">
            <h3 class="uuid-generator-features-title">Key Features:</h3>
            <ul class="uuid-generator-features-list">
                <li class="uuid-generator-features-item">Generate Version 1 UUIDs</li>
                <li class="uuid-generator-features-item">Generate Version 4 UUIDs</li>
                <li class="uuid-generator-features-item">Bulk UUID generation</li>
                <li class="uuid-generator-features-item">Standards-compliant output</li>
                <li class="uuid-generator-features-item">Secure client-side generation</li>
                <li class="uuid-generator-features-item">One-click copy to clipboard</li>
                <li class="uuid-generator-features-item">Fast and easy to use</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="uuid-generator-notification" id="uuidNotification">
        ✓ Copied to clipboard!
    </div>
    
    <!-- UUID Library (CDN) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/uuid/9.0.0/uuid.min.js"></script>

    <script>
        // UUID Generator
        (function() {
            'use strict';

            const elements = {
                output: () => document.getElementById('uuidOutput'),
                amountInput: () => document.getElementById('uuid-amount'),
                notification: () => document.getElementById('uuidNotification')
            };

            window.UuidGenerator = {
                generate() {
                    if (typeof uuid === 'undefined') {
                        this.showNotification('UUID library not loaded. Please try again.', true);
                        return;
                    }
                    
                    const output = elements.output();
                    const amount = parseInt(elements.amountInput().value, 10);
                    const version = document.querySelector('input[name="uuid-version"]:checked').value;
                    
                    if (isNaN(amount) || amount < 1 || amount > 1000) {
                        this.showNotification('Please enter a number between 1 and 1000.', true);
                        return;
                    }

                    let results = [];
                    for (let i = 0; i < amount; i++) {
                        if (version === 'v1') {
                            results.push(uuid.v1());
                        } else {
                            results.push(uuid.v4());
                        }
                    }
                    
                    output.value = results.join('\n');
                },

                clear() {
                    elements.output().value = '';
                    elements.amountInput().value = '1';
                },

                copy() {
                    const text = elements.output().value;
                    if (!text.trim()) {
                       this.showNotification('Nothing to copy. Please generate UUIDs first.', true);
                       return;
                    }
                    
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification('✓ Copied to clipboard!')).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification('✓ Copied to clipboard!');
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message, isError = false) {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.style.backgroundColor = isError ? '#dc2626' : '#10b981';
                    notification.classList.add('show');
                    setTimeout(() => {
                       notification.classList.remove('show');
                    }, 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }
                
                // Set initial UUID on load
                UuidGenerator.generate();
            });
        })();
    </script>
</body>
</html>