<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Converter - Live Exchange Rate Calculator</title>
    <meta name="description" content="Instantly convert between major world currencies like the US Dollar, Euro, British Pound, and Japanese Yen. A free tool for travelers, shoppers, and businesses.">
    <meta name="keywords" content="currency converter, exchange rate calculator, convert USD to EUR, foreign exchange, money converter, USD to GBP">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Currency Converter - USD, EUR, GBP, JPY & More",
        "description": "Convert between major world currencies with our simple exchange rate calculator. Free online tool providing quick conversions for finance, travel, and online shopping.",
        "url": "https://www.webtoolskit.org/p/currency-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-25",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Currency Converter",
            "applicationCategory": "FinanceApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Currencies" },
            { "@type": "CopyAction", "name": "Copy Converted Amount" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How much is $1 US dollar in British pounds?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The exact value changes daily, but as of mid-2024, $1 US dollar is typically worth around £0.78 to £0.80 British pounds. Exchange rates fluctuate constantly due to economic factors, so it's always best to check a live currency converter for the current rate."
          }
        },
        {
          "@type": "Question",
          "name": "What is $100 in pounds today?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Based on a rate of approximately £0.79 per dollar, $100 would be around £79. However, this is an estimate. The precise amount you would get depends on the live exchange rate at the exact moment of conversion, which can be checked with our tool."
          }
        },
        {
          "@type": "Question",
          "name": "Is the US dollar stronger than the British pound?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, the British pound (GBP) is historically 'stronger' than the US dollar (USD). This means that one British pound can be exchanged for more than one US dollar. For example, if £1 equals $1.25, the pound has a higher value per unit. 'Strength' in this context refers to the exchange value, not the overall economic power."
          }
        },
        {
          "@type": "Question",
          "name": "Where is the US dollar worth the most?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The US dollar's value is highest in countries with a low cost of living and a favorable exchange rate. This is often measured by 'purchasing power.' Historically, the dollar goes furthest in countries across Southeast Asia (like Vietnam or Indonesia), South America (like Argentina or Colombia), and Eastern Europe, where you can buy more goods and services for one dollar."
          }
        },
        {
          "@type": "Question",
          "name": "What is the strongest currency in the world?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The 'strongest' currency, meaning the one with the highest exchange value against the US dollar, is typically the Kuwaiti Dinar (KWD). One Kuwaiti Dinar is often worth over $3. Other high-value currencies include the Bahraini Dinar (BHD) and the Omani Rial (OMR). This high value is often tied to the country's economic stability and wealth from natural resources like oil."
          }
        }
      ]
    }
    </script>

    <style>
        /* Currency Converter Widget - Simplified & Template Compatible */
        .currency-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .currency-converter-widget-container * { box-sizing: border-box; }

        .currency-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .currency-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .currency-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .currency-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .currency-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .currency-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .currency-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .currency-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .currency-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .currency-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .currency-converter-btn:hover { transform: translateY(-2px); }

        .currency-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .currency-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .currency-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .currency-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .currency-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .currency-converter-btn-success:hover {
            background-color: #059669;
        }

        .currency-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .currency-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .currency-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .currency-converter-disclaimer {
            font-size: 0.875rem;
            color: var(--text-color-light);
            text-align: center;
            margin-top: calc(-1 * var(--spacing-md));
            margin-bottom: var(--spacing-xl);
        }

        .currency-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .currency-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .currency-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .currency-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .currency-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .currency-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .currency-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .currency-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="number-to-word-converter"] .currency-converter-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="weight-converter"] .currency-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="length-converter"] .currency-converter-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }

        .currency-converter-related-tool-item:hover .currency-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="number-to-word-converter"]:hover .currency-converter-related-tool-icon { background: linear-gradient(145deg, #2dd4bf, #14b8a6); }
        a[href*="weight-converter"]:hover .currency-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        a[href*="length-converter"]:hover .currency-converter-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        
        .currency-converter-related-tool-item { box-shadow: none; border: none; }
        .currency-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .currency-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .currency-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .currency-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .currency-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .currency-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .currency-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .currency-converter-related-tool-item:hover .currency-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .currency-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .currency-converter-widget-title { font-size: 1.875rem; }
            .currency-converter-buttons { flex-direction: column; }
            .currency-converter-btn { flex: none; }
            .currency-converter-input-group { grid-template-columns: 1fr; }
            .currency-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .currency-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .currency-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .currency-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .currency-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .currency-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .currency-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .currency-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .currency-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .currency-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .currency-converter-output::selection { background-color: var(--primary-color); color: white; }
        .currency-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .currency-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="currency-converter-widget-container">
        <h1 class="currency-converter-widget-title">Currency Converter</h1>
        <p class="currency-converter-widget-description">
            Get fast and easy exchange rate estimates between major world currencies. Ideal for travelers, online shoppers, and financial planning.
        </p>
        
        <div class="currency-converter-input-group">
            <label for="currencyFromInput" class="currency-converter-label">From:</label>
            <input 
                type="number" 
                id="currencyFromInput" 
                class="currency-converter-input"
                placeholder="Enter amount to convert..."
                step="any"
            />
            <select id="currencyFromUnit" class="currency-converter-select">
                <option value="USD" selected>USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
                <option value="JPY">JPY - Japanese Yen</option>
                <option value="CAD">CAD - Canadian Dollar</option>
                <option value="AUD">AUD - Australian Dollar</option>
                <option value="CHF">CHF - Swiss Franc</option>
                <option value="CNY">CNY - Chinese Yuan</option>
            </select>
        </div>

        <div class="currency-converter-input-group">
            <label for="currencyToInput" class="currency-converter-label">To:</label>
            <input 
                type="number" 
                id="currencyToInput" 
                class="currency-converter-input"
                placeholder="Converted amount will appear here..."
                readonly
            />
            <select id="currencyToUnit" class="currency-converter-select">
                <option value="USD">USD - US Dollar</option>
                <option value="EUR" selected>EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
                <option value="JPY">JPY - Japanese Yen</option>
                <option value="CAD">CAD - Canadian Dollar</option>
                <option value="AUD">AUD - Australian Dollar</option>
                <option value="CHF">CHF - Swiss Franc</option>
                <option value="CNY">CNY - Chinese Yuan</option>
            </select>
        </div>

        <div class="currency-converter-buttons">
            <button class="currency-converter-btn currency-converter-btn-primary" onclick="CurrencyConverter.convert()">
                Convert Currency
            </button>
            <button class="currency-converter-btn currency-converter-btn-secondary" onclick="CurrencyConverter.clear()">
                Clear All
            </button>
            <button class="currency-converter-btn currency-converter-btn-success" onclick="CurrencyConverter.copy()">
                Copy Result
            </button>
        </div>

        <p class="currency-converter-disclaimer">
            Note: Exchange rates are for illustrative purposes and are not updated in real-time.
        </p>

        <div class="currency-converter-result">
            <h3 class="currency-converter-result-title">Conversion Result:</h3>
            <div class="currency-converter-output" id="currencyConverterOutput">
                Your converted amount will appear here...
            </div>
        </div>

        <div class="currency-converter-related-tools">
            <h3 class="currency-converter-related-tools-title">Related Tools</h3>
            <div class="currency-converter-related-tools-grid">
                <a href="/p/number-to-word-converter.html" class="currency-converter-related-tool-item" rel="noopener">
                    <div class="currency-converter-related-tool-icon">
                        <i class="fas fa-spell-check"></i>
                    </div>
                    <div class="currency-converter-related-tool-name">Number to Word Converter</div>
                </a>

                <a href="/p/weight-converter.html" class="currency-converter-related-tool-item" rel="noopener">
                    <div class="currency-converter-related-tool-icon">
                        <i class="fas fa-weight"></i>
                    </div>
                    <div class="currency-converter-related-tool-name">Weight Converter</div>
                </a>

                <a href="/p/length-converter.html" class="currency-converter-related-tool-item" rel="noopener">
                    <div class="currency-converter-related-tool-icon">
                        <i class="fas fa-ruler"></i>
                    </div>
                    <div class="currency-converter-related-tool-name">Length Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Easy Foreign Exchange Rate Calculations</h2>
            <p>In today's global economy, understanding the value of your money across borders is essential. Whether you are planning a trip, shopping on an international website, or conducting business overseas, our free <strong>Currency Converter</strong> provides a quick and reliable way to estimate foreign exchange rates. This tool allows you to seamlessly convert between the world's major currencies, including the US Dollar (USD), Euro (EUR), British Pound (GBP), and Japanese Yen (JPY), taking the complexity out of financial planning.</p>
            <p>Foreign exchange rates are dynamic and change by the second. While this tool uses recent, representative rates for calculation, it is intended to give you a strong estimate for your planning needs. It's the perfect first stop before making international transactions.</p>

            <h3>How to Use the Currency Converter</h3>
            <ol>
                <li><strong>Enter Amount:</strong> Type the amount of money you wish to convert into the "From" field.</li>
                <li><strong>Select Currencies:</strong> Choose your starting currency and the currency you want to convert to from the dropdown menus.</li>
                <li><strong>Click to Convert:</strong> Press the "Convert Currency" button to see the estimated conversion instantly.</li>
                <li><strong>Copy or Clear:</strong> Use the "Copy Result" button to save the amount or "Clear All" for a new calculation.</li>
            </ol>

            <h3>Frequently Asked Questions About Currency Conversion</h3>

            <h4>How much is $1 US dollar in British pounds?</h4>
            <p>The exact value changes daily, but as of mid-2024, $1 US dollar is typically worth around £0.78 to £0.80 British pounds. Exchange rates fluctuate constantly due to economic factors, so it's always best to check a live currency converter for the current rate.</p>

            <h4>What is $100 in pounds today?</h4>
            <p>Based on a rate of approximately £0.79 per dollar, $100 would be around £79. However, this is an estimate. The precise amount you would get depends on the live exchange rate at the exact moment of conversion, which can be checked with our tool.</p>

            <h4>Is the US dollar stronger than the British pound?</h4>
            <p>No, the British pound (GBP) is historically 'stronger' than the US dollar (USD). This means that one British pound can be exchanged for more than one US dollar. For example, if £1 equals $1.25, the pound has a higher value per unit. 'Strength' in this context refers to the exchange value, not the overall economic power.</p>

            <h4>Where is the US dollar worth the most?</h4>
            <p>The US dollar's value is highest in countries with a low cost of living and a favorable exchange rate. This is often measured by 'purchasing power.' Historically, the dollar goes furthest in countries across Southeast Asia (like Vietnam or Indonesia), South America (like Argentina or Colombia), and Eastern Europe, where you can buy more goods and services for one dollar.</p>

            <h4>What is the strongest currency in the world?</h4>
            <p>The 'strongest' currency, meaning the one with the highest exchange value against the US dollar, is typically the Kuwaiti Dinar (KWD). One Kuwaiti Dinar is often worth over $3. Other high-value currencies include the Bahraini Dinar (BHD) and the Omani Rial (OMR). This high value is often tied to the country's economic stability and wealth from natural resources like oil.</p>
        </div>

        <div class="currency-converter-features">
            <h3 class="currency-converter-features-title">Key Features:</h3>
            <ul class="currency-converter-features-list">
                <li class="currency-converter-features-item" style="margin-bottom: 0.3em;">Major world currencies supported</li>
                <li class="currency-converter-features-item" style="margin-bottom: 0.3em;">Great for travel & online shopping</li>
                <li class="currency-converter-features-item" style="margin-bottom: 0.3em;">Simple, intuitive interface</li>
                <li class="currency-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="currency-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side calculations</li>
                <li class="currency-converter-features-item" style="margin-bottom: 0.3em;">Responsive on all devices</li>
                <li class="currency-converter-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="currency-converter-notification" id="currencyConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Currency Converter
        (function() {
            'use strict';

            // Illustrative exchange rates: how many units of the currency equal 1 USD.
            const conversionRates = {
                'USD': 1,
                'EUR': 0.92,
                'GBP': 0.79,
                'JPY': 157,
                'CAD': 1.37,
                'AUD': 1.50,
                'CHF': 0.90,
                'CNY': 7.25
            };
            
            const currencySymbols = {
                'USD': '$', 'EUR': '€', 'GBP': '£', 'JPY': '¥', 
                'CAD': '$', 'AUD': '$', 'CHF': 'CHF', 'CNY': '¥'
            };

            const elements = {
                fromInput: () => document.getElementById('currencyFromInput'),
                toInput: () => document.getElementById('currencyToInput'),
                fromUnit: () => document.getElementById('currencyFromUnit'),
                toUnit: () => document.getElementById('currencyToUnit'),
                output: () => document.getElementById('currencyConverterOutput'),
                notification: () => document.getElementById('currencyConverterNotification')
            };

            window.CurrencyConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit().value;
                    const toUnit = elements.toUnit().value;
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid amount to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Step 1: Convert the input amount to the base currency (USD)
                    const valueInUSD = value / conversionRates[fromUnit];
                    
                    // Step 2: Convert the USD amount to the target currency
                    const convertedValue = valueInUSD * conversionRates[toUnit];

                    const formattedResult = this.formatResult(convertedValue, toUnit);
                    toInput.value = formattedResult;

                    const fromSymbol = currencySymbols[fromUnit] || fromUnit;
                    const toSymbol = currencySymbols[toUnit] || toUnit;
                    
                    output.textContent = `${fromSymbol}${this.formatResult(value, fromUnit)} ${fromUnit} = ${toSymbol}${formattedResult} ${toUnit}`;
                },

                formatResult(value, unit) {
                    // JPY usually doesn't have decimals
                    const decimalPlaces = (unit === 'JPY') ? 0 : 2;
                    return parseFloat(value.toFixed(decimalPlaces)).toLocaleString(undefined, {
                        minimumFractionDigits: decimalPlaces,
                        maximumFractionDigits: decimalPlaces
                    }).replace(/,/g, ''); // Remove commas for input field compatibility
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted amount will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        CurrencyConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>