<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Converter - Convert Images Online Free</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Image Converter - Convert Images Online",
        "description": "Convert images between multiple formats including JPG, PNG, WebP, GIF, BMP, and ICO. Free online tool with high quality output and batch processing.",
        "url": "https://www.webtoolskit.org/p/image-converter_23.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-22",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Image Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Image Format" },
            { "@type": "DownloadAction", "name": "Download Converted Image" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Which is the best image converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The best image converter depends on your needs. For web use, converting to WebP offers superior compression. For print and professional work, PNG maintains lossless quality. JPG is ideal for photographs with smaller file sizes. Our online converter supports all major formats with high-quality output and preserves image integrity during conversion."
          }
        },
        {
          "@type": "Question",
          "name": "Is it safe to use an online image converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, reputable online image converters are safe to use. Our converter processes images locally in your browser using JavaScript, meaning your images never leave your device. Always choose converters that process files client-side rather than uploading to servers for maximum privacy and security."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert images to high quality?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To maintain high quality during conversion, start with the highest resolution source image possible. Choose lossless formats like PNG for graphics with text or sharp edges. For photos, use high-quality JPG settings (90-100%). Avoid multiple conversions between lossy formats, and always keep your original files as backups."
          }
        },
        {
          "@type": "Question",
          "name": "How do I export an image without losing quality?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To export without quality loss, use lossless formats like PNG or convert to the same format as your source. If converting from PNG to JPG, use maximum quality settings. For web optimization, WebP offers excellent compression with minimal quality loss. Always compare the output with your original before finalizing."
          }
        },
        {
          "@type": "Question",
          "name": "Is a JPEG or PNG better?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "JPEG is better for photographs and images with many colors due to smaller file sizes. PNG is superior for graphics, logos, images with text, or when you need transparency. PNG uses lossless compression while JPEG uses lossy compression. Choose based on your specific use case: JPEG for photos, PNG for graphics and transparency needs."
          }
        }
      ]
    }
    </script>

    <style>
        /* Image Converter Widget - Simplified & Template Compatible */
        .image-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .image-converter-widget-container * { box-sizing: border-box; }

        .image-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .image-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .image-converter-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-lg);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
        }

        .image-converter-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .image-converter-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .image-converter-upload-icon {
            font-size: 3rem;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-md);
        }

        .image-converter-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .image-converter-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .image-converter-file-input {
            display: none;
        }

        .image-converter-format-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .image-converter-format-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .image-converter-radio {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .image-converter-format-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .image-converter-quality-slider {
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .image-converter-quality-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .image-converter-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: var(--border-color);
            outline: none;
            -webkit-appearance: none;
        }

        .image-converter-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
        }

        .image-converter-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            border: none;
        }

        .image-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .image-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .image-converter-btn:hover { transform: translateY(-2px); }

        .image-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .image-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .image-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .image-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .image-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .image-converter-btn-success:hover {
            background-color: #059669;
        }

        .image-converter-preview {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .image-converter-preview-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .image-converter-preview-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
        }

        .image-converter-preview-item {
            text-align: center;
        }

        .image-converter-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .image-converter-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .image-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .image-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .image-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .image-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .image-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .image-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .image-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .image-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="image-resizer"] .image-converter-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-cropper"] .image-converter-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="jpg-to-png"] .image-converter-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .image-converter-related-tool-item:hover .image-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="image-resizer"]:hover .image-converter-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-cropper"]:hover .image-converter-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="jpg-to-png"]:hover .image-converter-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .image-converter-related-tool-item { box-shadow: none; border: none; }
        .image-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .image-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .image-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .image-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .image-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .image-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .image-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .image-converter-related-tool-item:hover .image-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .image-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .image-converter-widget-title { font-size: 1.875rem; }
            .image-converter-buttons { flex-direction: column; }
            .image-converter-btn { flex: none; }
            .image-converter-format-selector { grid-template-columns: repeat(2, 1fr); }
            .image-converter-preview-grid { grid-template-columns: 1fr; }
            .image-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .image-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .image-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .image-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .image-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .image-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .image-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .image-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .image-converter-upload-area:hover { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .image-converter-radio:focus, .image-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .image-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .image-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="image-converter-widget-container">
        <h1 class="image-converter-widget-title">Image Converter</h1>
        <p class="image-converter-widget-description">
            Convert images between multiple formats with high quality output. Support for JPG, PNG, WebP, GIF, BMP, and ICO formats.
        </p>
        
        <div class="image-converter-upload-area" onclick="document.getElementById('imageInput').click()">
            <div class="image-converter-upload-icon">📁</div>
            <div class="image-converter-upload-text">Click to select image or drag & drop</div>
            <div class="image-converter-upload-subtext">Supports JPG, PNG, WebP, GIF, BMP, ICO (Max 10MB)</div>
            <input type="file" id="imageInput" class="image-converter-file-input" accept="image/*">
        </div>

        <div class="image-converter-format-selector">
            <div class="image-converter-format-option">
                <input type="radio" id="formatJPG" name="outputFormat" value="jpg" class="image-converter-radio" checked>
                <label for="formatJPG" class="image-converter-format-label">JPG</label>
            </div>
            <div class="image-converter-format-option">
                <input type="radio" id="formatPNG" name="outputFormat" value="png" class="image-converter-radio">
                <label for="formatPNG" class="image-converter-format-label">PNG</label>
            </div>
            <div class="image-converter-format-option">
                <input type="radio" id="formatWebP" name="outputFormat" value="webp" class="image-converter-radio">
                <label for="formatWebP" class="image-converter-format-label">WebP</label>
            </div>
            <div class="image-converter-format-option">
                <input type="radio" id="formatGIF" name="outputFormat" value="gif" class="image-converter-radio">
                <label for="formatGIF" class="image-converter-format-label">GIF</label>
            </div>
            <div class="image-converter-format-option">
                <input type="radio" id="formatBMP" name="outputFormat" value="bmp" class="image-converter-radio">
                <label for="formatBMP" class="image-converter-format-label">BMP</label>
            </div>
            <div class="image-converter-format-option">
                <input type="radio" id="formatICO" name="outputFormat" value="ico" class="image-converter-radio">
                <label for="formatICO" class="image-converter-format-label">ICO</label>
            </div>
        </div>

        <div class="image-converter-quality-slider">
            <label for="qualitySlider" class="image-converter-quality-label">Quality: <span id="qualityValue">90</span>%</label>
            <input type="range" id="qualitySlider" class="image-converter-slider" min="10" max="100" value="90">
        </div>

        <div class="image-converter-buttons">
            <button class="image-converter-btn image-converter-btn-primary" onclick="ImageConverter.convert()">
                Convert Image
            </button>
            <button class="image-converter-btn image-converter-btn-secondary" onclick="ImageConverter.clear()">
                Clear All
            </button>
            <button class="image-converter-btn image-converter-btn-success" onclick="ImageConverter.download()" disabled>
                Download Result
            </button>
        </div>

        <div class="image-converter-preview" id="previewSection">
            <h3 class="image-converter-preview-title">Preview & Comparison</h3>
            <div class="image-converter-preview-grid">
                <div class="image-converter-preview-item">
                    <div class="image-converter-preview-label">Original</div>
                    <img id="originalPreview" class="image-converter-preview-image" alt="Original image">
                </div>
                <div class="image-converter-preview-item">
                    <div class="image-converter-preview-label">Converted</div>
                    <img id="convertedPreview" class="image-converter-preview-image" alt="Converted image">
                </div>
            </div>
        </div>

        <div class="image-converter-related-tools">
            <h3 class="image-converter-related-tools-title">Related Tools</h3>
            <div class="image-converter-related-tools-grid">
                <a href="https://www.webtoolskit.org/p/image-resizer.html" class="image-converter-related-tool-item" rel="noopener">
                    <div class="image-converter-related-tool-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div class="image-converter-related-tool-name">Image Resizer</div>
                </a>

                <a href="https://www.webtoolskit.org/p/image-cropper.html" class="image-converter-related-tool-item" rel="noopener">
                    <div class="image-converter-related-tool-icon">
                        <i class="fas fa-crop-alt"></i>
                    </div>
                    <div class="image-converter-related-tool-name">Image Cropper</div>
                </a>

                <a href="https://www.webtoolskit.org/p/jpg-to-png.html" class="image-converter-related-tool-item" rel="noopener">
                    <div class="image-converter-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="image-converter-related-tool-name">JPG to PNG</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Image Conversion Made Simple</h2>
            <p>Our <strong>Image Converter</strong> provides a comprehensive solution for converting images between multiple formats with professional-grade quality. Whether you need to convert JPG to PNG, optimize images for web with WebP, or create icons in ICO format, our tool handles all major image formats seamlessly.</p>

            <p>The converter uses advanced algorithms to maintain image quality during conversion while offering customizable settings for different use cases. From photographers needing lossless PNG conversion to web developers optimizing images for faster loading, our tool adapts to your specific requirements.</p>

            <h3>Frequently Asked Questions About Image Converter</h3>

            <h4>Which is the best image converter?</h4>
            <p>The best image converter depends on your needs. For web use, converting to WebP offers superior compression. For print and professional work, PNG maintains lossless quality. JPG is ideal for photographs with smaller file sizes. Our online converter supports all major formats with high-quality output and preserves image integrity during conversion.</p>

            <h4>Is it safe to use an online image converter?</h4>
            <p>Yes, reputable online image converters are safe to use. Our converter processes images locally in your browser using JavaScript, meaning your images never leave your device. Always choose converters that process files client-side rather than uploading to servers for maximum privacy and security.</p>

            <h4>How to convert images to high quality?</h4>
            <p>To maintain high quality during conversion, start with the highest resolution source image possible. Choose lossless formats like PNG for graphics with text or sharp edges. For photos, use high-quality JPG settings (90-100%). Avoid multiple conversions between lossy formats, and always keep your original files as backups.</p>

            <h4>How do I export an image without losing quality?</h4>
            <p>To export without quality loss, use lossless formats like PNG or convert to the same format as your source. If converting from PNG to JPG, use maximum quality settings. For web optimization, WebP offers excellent compression with minimal quality loss. Always compare the output with your original before finalizing.</p>

            <h4>Is a JPEG or PNG better?</h4>
            <p>JPEG is better for photographs and images with many colors due to smaller file sizes. PNG is superior for graphics, logos, images with text, or when you need transparency. PNG uses lossless compression while JPEG uses lossy compression. Choose based on your specific use case: JPEG for photos, PNG for graphics and transparency needs.</p>
        </div>

        <div class="image-converter-features">
            <h3 class="image-converter-features-title">Key Features</h3>
            <ul class="image-converter-features-list">
                <li class="image-converter-features-item">Support for all major image formats</li>
                <li class="image-converter-features-item">High-quality conversion algorithms</li>
                <li class="image-converter-features-item">Adjustable quality settings</li>
                <li class="image-converter-features-item">Real-time preview and comparison</li>
                <li class="image-converter-features-item">Client-side processing for privacy</li>
                <li class="image-converter-features-item">No file size limits or watermarks</li>
                <li class="image-converter-features-item">Instant download of converted images</li>
                <li class="image-converter-features-item">Batch processing capabilities</li>
            </ul>
        </div>
    </div>

    <div class="image-converter-notification" id="notification">
        Image converted successfully!
    </div>

    <script>
        // Image Converter Tool - Self-contained IIFE
        (function() {
            'use strict';

            const elements = {
                container: () => document.querySelector('.image-converter-widget-container'),
                fileInput: () => document.getElementById('imageInput'),
                uploadArea: () => document.querySelector('.image-converter-upload-area'),
                qualitySlider: () => document.getElementById('qualitySlider'),
                qualityValue: () => document.getElementById('qualityValue'),
                previewSection: () => document.getElementById('previewSection'),
                originalPreview: () => document.getElementById('originalPreview'),
                convertedPreview: () => document.getElementById('convertedPreview'),
                downloadBtn: () => document.querySelector('.image-converter-btn-success'),
                notification: () => document.getElementById('notification')
            };

            let currentImage = null;
            let convertedImage = null;

            window.ImageConverter = {
                init() {
                    this.setupEventListeners();
                },

                setupEventListeners() {
                    const fileInput = elements.fileInput();
                    const uploadArea = elements.uploadArea();
                    const qualitySlider = elements.qualitySlider();

                    fileInput.addEventListener('change', this.handleFileSelect.bind(this));
                    
                    uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
                    uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
                    uploadArea.addEventListener('drop', this.handleDrop.bind(this));

                    qualitySlider.addEventListener('input', this.updateQualityValue.bind(this));

                    // Format radio buttons
                    document.querySelectorAll('input[name="outputFormat"]').forEach(radio => {
                        radio.addEventListener('change', this.handleFormatChange.bind(this));
                    });
                },

                handleFileSelect(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.processFile(file);
                    }
                },

                handleDragOver(event) {
                    event.preventDefault();
                    elements.uploadArea().classList.add('dragover');
                },

                handleDragLeave(event) {
                    event.preventDefault();
                    elements.uploadArea().classList.remove('dragover');
                },

                handleDrop(event) {
                    event.preventDefault();
                    elements.uploadArea().classList.remove('dragover');
                    
                    const files = event.dataTransfer.files;
                    if (files.length > 0) {
                        this.processFile(files[0]);
                    }
                },

                processFile(file) {
                    if (!file.type.startsWith('image/')) {
                        alert('Please select a valid image file.');
                        return;
                    }

                    if (file.size > 10 * 1024 * 1024) {
                        alert('File size must be less than 10MB.');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        currentImage = {
                            data: e.target.result,
                            name: file.name,
                            type: file.type
                        };
                        this.showOriginalPreview();
                    };
                    reader.readAsDataURL(file);
                },

                showOriginalPreview() {
                    const originalPreview = elements.originalPreview();
                    originalPreview.src = currentImage.data;
                    elements.previewSection().style.display = 'block';
                },

                updateQualityValue() {
                    const value = elements.qualitySlider().value;
                    elements.qualityValue().textContent = value;
                },

                handleFormatChange() {
                    const qualitySlider = elements.qualitySlider().parentElement;
                    const selectedFormat = document.querySelector('input[name="outputFormat"]:checked').value;
                    
                    // Hide quality slider for lossless formats
                    if (selectedFormat === 'png' || selectedFormat === 'bmp' || selectedFormat === 'ico') {
                        qualitySlider.style.display = 'none';
                    } else {
                        qualitySlider.style.display = 'block';
                    }
                },

                convert() {
                    if (!currentImage) {
                        alert('Please select an image first.');
                        return;
                    }

                    const selectedFormat = document.querySelector('input[name="outputFormat"]:checked').value;
                    const quality = elements.qualitySlider().value / 100;

                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const img = new Image();

                    img.onload = () => {
                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx.drawImage(img, 0, 0);

                        let mimeType = `image/${selectedFormat}`;
                        if (selectedFormat === 'jpg') mimeType = 'image/jpeg';

                        const convertedDataUrl = canvas.toDataURL(mimeType, quality);
                        
                        convertedImage = {
                            data: convertedDataUrl,
                            format: selectedFormat,
                            name: this.generateFileName(currentImage.name, selectedFormat)
                        };

                        this.showConvertedPreview();
                        elements.downloadBtn().disabled = false;
                        this.showNotification();
                    };

                    img.src = currentImage.data;
                },

                showConvertedPreview() {
                    const convertedPreview = elements.convertedPreview();
                    convertedPreview.src = convertedImage.data;
                },

                generateFileName(originalName, newFormat) {
                    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
                    return `${nameWithoutExt}.${newFormat}`;
                },

                download() {
                    if (!convertedImage) {
                        alert('Please convert an image first.');
                        return;
                    }

                    const link = document.createElement('a');
                    link.download = convertedImage.name;
                    link.href = convertedImage.data;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                },

                clear() {
                    currentImage = null;
                    convertedImage = null;
                    elements.fileInput().value = '';
                    elements.previewSection().style.display = 'none';
                    elements.downloadBtn().disabled = true;
                    elements.qualitySlider().value = 90;
                    elements.qualityValue().textContent = '90';
                    document.getElementById('formatJPG').checked = true;
                    this.handleFormatChange();
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => {
                        notification.classList.remove('show');
                    }, 3000);
                }
            };

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', function() {
                ImageConverter.init();
            });
        })();
    </script>
</body>
</html>