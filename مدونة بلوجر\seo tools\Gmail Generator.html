<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Gmail Generator - Create Gmail Variations & Aliases</title>
    <meta name="description" content="Generate Gmail variations using dot trick and plus trick. Create multiple Gmail aliases from your existing email address for better organization and privacy.">
    <meta name="keywords" content="Gmail generator, Gmail dot trick, Gmail plus trick, email aliases, Gmail variations, email generator, Gmail alias generator">
    <link rel="canonical" href="https://www.webtoolskit.org/p/gmail-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Gmail Generator - Create Gmail Variations & Aliases",
        "description": "Generate Gmail variations using dot trick and plus trick. Create multiple Gmail aliases from your existing email address for better organization and privacy.",
        "url": "https://www.webtoolskit.org/p/gmail-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Gmail Generator",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Gmail dot trick variations",
                "Gmail plus trick aliases",
                "Email organization",
                "Privacy protection",
                "Bulk email generation"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Gmail Variations" },
            { "@type": "CopyAction", "name": "Copy Generated Emails" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the Gmail dot trick?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The Gmail dot trick allows you to add dots anywhere in your Gmail username before the @ symbol. Gmail ignores dots in email addresses, so <EMAIL>, <EMAIL>, and <EMAIL> all deliver to the same inbox while appearing as different email addresses to websites."
          }
        },
        {
          "@type": "Question",
          "name": "What is the Gmail plus trick?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The Gmail plus trick lets you add a plus sign (+) followed by any text before the @ symbol. For example, if your <NAME_EMAIL>, you <NAME_EMAIL> or <EMAIL>. All emails will arrive in your main inbox but with different 'To' addresses for easy filtering."
          }
        },
        {
          "@type": "Question",
          "name": "Are Gmail variations safe to use?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, Gmail variations are completely safe and officially supported by Google. They're useful for organizing emails, tracking sign-ups, and maintaining privacy. However, be aware that some websites may not accept email addresses with dots or plus signs, though this is becoming less common."
          }
        },
        {
          "@type": "Question",
          "name": "Can I use Gmail tricks for multiple accounts?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Gmail variations don't create separate accounts - they're aliases that all deliver to your existing Gmail inbox. If you need truly separate Gmail accounts, you'll need to create them individually through Google's account creation process. Gmail variations are for organization within a single account."
          }
        },
        {
          "@type": "Question",
          "name": "How can Gmail variations help with email organization?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Gmail variations help you organize emails by creating filters based on the 'To' address. You can automatically label, sort, or forward emails sent to specific variations. For example, use <EMAIL> for online purchases and create a filter to label all those emails as 'Shopping'."
          }
        }
      ]
    }
    </script>

    <style>
        /* Gmail Generator Widget - Simplified & Template Compatible */
        .gmail-generator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .gmail-generator-widget-container * { box-sizing: border-box; }

        .gmail-generator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .gmail-generator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .gmail-generator-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .gmail-generator-field {
            display: flex;
            flex-direction: column;
        }

        .gmail-generator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .gmail-generator-input,
        .gmail-generator-select {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .gmail-generator-input:focus,
        .gmail-generator-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .gmail-generator-help-text {
            font-size: 0.875rem;
            color: var(--text-color-light);
            margin-top: var(--spacing-xs);
            line-height: 1.4;
        }

        .gmail-generator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .gmail-generator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .gmail-generator-btn:hover { transform: translateY(-2px); }

        .gmail-generator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .gmail-generator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .gmail-generator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .gmail-generator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .gmail-generator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .gmail-generator-btn-success:hover {
            background-color: #059669;
        }

        .gmail-generator-results {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .gmail-generator-results.show {
            display: block;
        }

        .gmail-generator-results-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .gmail-generator-count {
            font-size: 0.875rem;
            color: var(--text-color-light);
            font-weight: normal;
        }

        .gmail-generator-email-list {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            max-height: 300px;
            overflow-y: auto;
        }

        .gmail-generator-email-item {
            padding: var(--spacing-sm);
            margin-bottom: var(--spacing-xs);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--border-color);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 0.9rem;
            color: var(--text-color);
            cursor: pointer;
            transition: var(--transition-base);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .gmail-generator-email-item:last-child {
            margin-bottom: 0;
        }

        .gmail-generator-email-item:hover {
            background-color: var(--border-color);
            transform: translateX(2px);
        }

        .gmail-generator-email-text {
            flex: 1;
        }

        .gmail-generator-copy-icon {
            color: var(--text-color-light);
            font-size: 0.8rem;
            opacity: 0;
            transition: var(--transition-base);
        }

        .gmail-generator-email-item:hover .gmail-generator-copy-icon {
            opacity: 1;
        }

        .gmail-generator-tabs {
            display: flex;
            margin-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--border-color);
        }

        .gmail-generator-tab {
            padding: var(--spacing-sm) var(--spacing-md);
            background: none;
            border: none;
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-color-light);
            cursor: pointer;
            transition: var(--transition-base);
            border-bottom: 2px solid transparent;
        }

        .gmail-generator-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .gmail-generator-tab:hover {
            color: var(--text-color);
        }

        .gmail-generator-tab-content {
            display: none;
        }

        .gmail-generator-tab-content.active {
            display: block;
        }

        .gmail-generator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .gmail-generator-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .gmail-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .gmail-generator-widget-title { font-size: 1.875rem; }
            .gmail-generator-buttons { flex-direction: column; }
            .gmail-generator-btn { flex: none; }
            .gmail-generator-results-title { flex-direction: column; align-items: flex-start; gap: var(--spacing-xs); }
            .gmail-generator-tabs { flex-wrap: wrap; }
            .gmail-generator-tab { flex: 1; min-width: 120px; }
        }

        [data-theme="dark"] .gmail-generator-input:focus,
        [data-theme="dark"] .gmail-generator-select:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .gmail-generator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }

        .gmail-generator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="password-generator"] .gmail-generator-related-tool-icon { background: linear-gradient(145deg, #DC2626, #B91C1C); }
        a[href*="qr-code-generator"] .gmail-generator-related-tool-icon { background: linear-gradient(145deg, #7C3AED, #6D28D9); }
        a[href*="url-shortener"] .gmail-generator-related-tool-icon { background: linear-gradient(145deg, #059669, #047857); }

        .gmail-generator-related-tool-item:hover .gmail-generator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="password-generator"]:hover .gmail-generator-related-tool-icon { background: linear-gradient(145deg, #ef4444, #dc2626); }
        a[href*="qr-code-generator"]:hover .gmail-generator-related-tool-icon { background: linear-gradient(145deg, #8b5cf6, #7c3aed); }
        a[href*="url-shortener"]:hover .gmail-generator-related-tool-icon { background: linear-gradient(145deg, #10b981, #059669); }

        .gmail-generator-related-tool-item { box-shadow: none; border: none; }
        .gmail-generator-related-tool-item:hover { box-shadow: none; border: none; }
        .gmail-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .gmail-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .gmail-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .gmail-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .gmail-generator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .gmail-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .gmail-generator-related-tool-item:hover .gmail-generator-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .gmail-generator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .gmail-generator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .gmail-generator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .gmail-generator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .gmail-generator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .gmail-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .gmail-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .gmail-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .gmail-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .gmail-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .gmail-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .gmail-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .gmail-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .gmail-generator-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="gmail-generator-widget-container">
        <h1 class="gmail-generator-widget-title">Gmail Generator</h1>
        <p class="gmail-generator-widget-description">
            Generate Gmail variations using dot trick and plus trick. Create multiple Gmail aliases from your existing email for better organization and privacy.
        </p>
        
        <form class="gmail-generator-form">
            <div class="gmail-generator-field">
                <label for="gmailAddress" class="gmail-generator-label">Your Gmail Address:</label>
                <input 
                    type="email" 
                    id="gmailAddress" 
                    class="gmail-generator-input"
                    placeholder="<EMAIL>"
                />
                <div class="gmail-generator-help-text">
                    Enter your existing Gmail address (without @gmail.com if you prefer).
                </div>
            </div>

            <div class="gmail-generator-field">
                <label for="generationType" class="gmail-generator-label">Generation Type:</label>
                <select id="generationType" class="gmail-generator-select">
                    <option value="both">Both Dot & Plus Tricks</option>
                    <option value="dot">Dot Trick Only</option>
                    <option value="plus">Plus Trick Only</option>
                </select>
                <div class="gmail-generator-help-text">
                    Choose which Gmail tricks to use for generating variations.
                </div>
            </div>

            <div class="gmail-generator-field">
                <label for="customTags" class="gmail-generator-label">Custom Tags (Optional):</label>
                <input 
                    type="text" 
                    id="customTags" 
                    class="gmail-generator-input"
                    placeholder="shopping, newsletter, work, social"
                />
                <div class="gmail-generator-help-text">
                    Enter custom tags separated by commas for plus trick variations (e.g., shopping, newsletter, work).
                </div>
            </div>
        </form>

        <div class="gmail-generator-buttons">
            <button class="gmail-generator-btn gmail-generator-btn-primary" onclick="GmailGenerator.generate()">
                Generate Variations
            </button>
            <button class="gmail-generator-btn gmail-generator-btn-secondary" onclick="GmailGenerator.clear()">
                Clear All
            </button>
            <button class="gmail-generator-btn gmail-generator-btn-success" onclick="GmailGenerator.copyAll()">
                Copy All
            </button>
        </div>

        <div class="gmail-generator-results" id="generationResults">
            <div class="gmail-generator-results-title">
                Generated Gmail Variations
                <span class="gmail-generator-count" id="emailCount">0 variations</span>
            </div>

            <div class="gmail-generator-tabs">
                <button class="gmail-generator-tab active" onclick="GmailGenerator.switchTab('all')">All Variations</button>
                <button class="gmail-generator-tab" onclick="GmailGenerator.switchTab('dot')">Dot Trick</button>
                <button class="gmail-generator-tab" onclick="GmailGenerator.switchTab('plus')">Plus Trick</button>
            </div>

            <div class="gmail-generator-tab-content active" id="tab-all">
                <div class="gmail-generator-email-list" id="allEmailList">
                    <!-- All email variations will be populated here -->
                </div>
            </div>

            <div class="gmail-generator-tab-content" id="tab-dot">
                <div class="gmail-generator-email-list" id="dotEmailList">
                    <!-- Dot trick variations will be populated here -->
                </div>
            </div>

            <div class="gmail-generator-tab-content" id="tab-plus">
                <div class="gmail-generator-email-list" id="plusEmailList">
                    <!-- Plus trick variations will be populated here -->
                </div>
            </div>
        </div>

        <div class="gmail-generator-related-tools">
            <h3 class="gmail-generator-related-tools-title">Related Tools</h3>
            <div class="gmail-generator-related-tools-grid">
                <a href="/p/password-generator.html" class="gmail-generator-related-tool-item" rel="noopener">
                    <div class="gmail-generator-related-tool-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="gmail-generator-related-tool-name">Password Generator</div>
                </a>

                <a href="/p/qr-code-generator.html" class="gmail-generator-related-tool-item" rel="noopener">
                    <div class="gmail-generator-related-tool-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="gmail-generator-related-tool-name">QR Code Generator</div>
                </a>

                <a href="/p/url-shortener.html" class="gmail-generator-related-tool-item" rel="noopener">
                    <div class="gmail-generator-related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="gmail-generator-related-tool-name">URL Shortener</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Gmail Variation Generator</h2>
            <p>Our <strong>Gmail Generator</strong> helps you create multiple email variations from your existing Gmail address using Google's built-in dot trick and plus trick features. These variations allow you to organize emails, track sign-ups, and maintain privacy while using a single Gmail account.</p>
            <p>Whether you're managing online subscriptions, organizing business communications, or protecting your privacy, our tool generates unlimited Gmail aliases that all deliver to your main inbox while appearing as different email addresses to websites and services.</p>

            <h3>How to Use the Gmail Generator</h3>
            <ol>
                <li><strong>Enter Gmail Address:</strong> Add your existing Gmail address (with or without @gmail.com).</li>
                <li><strong>Choose Generation Type:</strong> Select dot trick, plus trick, or both for maximum variations.</li>
                <li><strong>Add Custom Tags:</strong> Optionally enter custom tags for plus trick variations (shopping, work, etc.).</li>
                <li><strong>Generate & Copy:</strong> Click "Generate Variations" and copy the emails you need for different purposes.</li>
            </ol>

            <h3>Frequently Asked Questions About Gmail Variations</h3>

            <h4>What is the Gmail dot trick?</h4>
            <p>The Gmail dot trick allows you to add dots anywhere in your Gmail username before the @ symbol. Gmail ignores dots in email addresses, so <EMAIL>, <EMAIL>, and <EMAIL> all deliver to the same inbox while appearing as different email addresses to websites.</p>

            <h4>What is the Gmail plus trick?</h4>
            <p>The Gmail plus trick lets you add a plus sign (+) followed by any text before the @ symbol. For example, if your <NAME_EMAIL>, you <NAME_EMAIL> or <EMAIL>. All emails will arrive in your main inbox but with different 'To' addresses for easy filtering.</p>

            <h4>Are Gmail variations safe to use?</h4>
            <p>Yes, Gmail variations are completely safe and officially supported by Google. They're useful for organizing emails, tracking sign-ups, and maintaining privacy. However, be aware that some websites may not accept email addresses with dots or plus signs, though this is becoming less common.</p>

            <h4>Can I use Gmail tricks for multiple accounts?</h4>
            <p>Gmail variations don't create separate accounts - they're aliases that all deliver to your existing Gmail inbox. If you need truly separate Gmail accounts, you'll need to create them individually through Google's account creation process. Gmail variations are for organization within a single account.</p>

            <h4>How can Gmail variations help with email organization?</h4>
            <p>Gmail variations help you organize emails by creating filters based on the 'To' address. You can automatically label, sort, or forward emails sent to specific variations. For example, use <EMAIL> for online purchases and create a filter to label all those emails as 'Shopping'.</p>
        </div>

        <div class="gmail-generator-features">
            <h3 class="gmail-generator-features-title">Key Features:</h3>
            <ul class="gmail-generator-features-list">
                <li class="gmail-generator-features-item" style="margin-bottom: 0.3em;">Gmail Dot Trick Variations</li>
                <li class="gmail-generator-features-item" style="margin-bottom: 0.3em;">Gmail Plus Trick Aliases</li>
                <li class="gmail-generator-features-item" style="margin-bottom: 0.3em;">Custom Tag Support</li>
                <li class="gmail-generator-features-item" style="margin-bottom: 0.3em;">Organized Tab Display</li>
                <li class="gmail-generator-features-item" style="margin-bottom: 0.3em;">One-Click Copy Function</li>
                <li class="gmail-generator-features-item" style="margin-bottom: 0.3em;">Bulk Copy All Emails</li>
                <li class="gmail-generator-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="gmail-generator-notification" id="gmailNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                gmailAddress: () => document.getElementById('gmailAddress'),
                generationType: () => document.getElementById('generationType'),
                customTags: () => document.getElementById('customTags'),
                generationResults: () => document.getElementById('generationResults'),
                emailCount: () => document.getElementById('emailCount'),
                allEmailList: () => document.getElementById('allEmailList'),
                dotEmailList: () => document.getElementById('dotEmailList'),
                plusEmailList: () => document.getElementById('plusEmailList'),
                notification: () => document.getElementById('gmailNotification')
            };

            let generatedEmails = {
                all: [],
                dot: [],
                plus: []
            };

            function normalizeGmailAddress(email) {
                // Remove @gmail.com if present and clean up
                let cleanEmail = email.toLowerCase().trim();
                if (cleanEmail.includes('@')) {
                    cleanEmail = cleanEmail.split('@')[0];
                }
                return cleanEmail;
            }

            function generateDotVariations(username) {
                const variations = new Set();
                const len = username.length;

                // Generate all possible dot combinations
                for (let i = 0; i < Math.pow(2, len - 1); i++) {
                    let variation = '';
                    for (let j = 0; j < len; j++) {
                        variation += username[j];
                        if (j < len - 1 && (i & (1 << j))) {
                            variation += '.';
                        }
                    }
                    variations.add(variation + '@gmail.com');
                }

                return Array.from(variations);
            }

            function generatePlusVariations(username, customTags) {
                const defaultTags = [
                    'shopping', 'newsletter', 'work', 'social', 'spam', 'test',
                    'signup', 'promo', 'deals', 'news', 'updates', 'alerts'
                ];

                let tags = [];
                if (customTags && customTags.trim()) {
                    tags = customTags.split(',').map(tag => tag.trim()).filter(tag => tag);
                }

                if (tags.length === 0) {
                    tags = defaultTags;
                }

                return tags.map(tag => `${username}+${tag}@gmail.com`);
            }

            function createEmailItem(email, type) {
                const item = document.createElement('div');
                item.className = 'gmail-generator-email-item';
                item.onclick = () => copyToClipboard(email);

                item.innerHTML = `
                    <span class="gmail-generator-email-text">${email}</span>
                    <span class="gmail-generator-copy-icon">📋</span>
                `;

                return item;
            }

            function copyToClipboard(text) {
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(text).then(() => {
                        GmailGenerator.showNotification(`✓ Copied: ${text}`);
                    }).catch(() => {
                        fallbackCopy(text);
                    });
                } else {
                    fallbackCopy(text);
                }
            }

            function fallbackCopy(text) {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    GmailGenerator.showNotification(`✓ Copied: ${text}`);
                } catch (err) {
                    console.error('Failed to copy text: ', err);
                    GmailGenerator.showNotification('Copy failed. Please select and copy manually.');
                }
                document.body.removeChild(textArea);
            }

            window.GmailGenerator = {
                generate() {
                    const gmailAddress = elements.gmailAddress().value.trim();
                    const generationType = elements.generationType().value;
                    const customTags = elements.customTags().value.trim();

                    if (!gmailAddress) {
                        this.showNotification('Please enter a Gmail address.');
                        return;
                    }

                    const username = normalizeGmailAddress(gmailAddress);

                    // Reset generated emails
                    generatedEmails = { all: [], dot: [], plus: [] };

                    // Generate dot variations
                    if (generationType === 'dot' || generationType === 'both') {
                        const dotVariations = generateDotVariations(username);
                        generatedEmails.dot = dotVariations;
                        generatedEmails.all.push(...dotVariations);
                    }

                    // Generate plus variations
                    if (generationType === 'plus' || generationType === 'both') {
                        const plusVariations = generatePlusVariations(username, customTags);
                        generatedEmails.plus = plusVariations;
                        generatedEmails.all.push(...plusVariations);
                    }

                    // Remove duplicates from all
                    generatedEmails.all = [...new Set(generatedEmails.all)];

                    this.displayResults();
                    elements.generationResults().classList.add('show');
                    this.showNotification(`✓ Generated ${generatedEmails.all.length} Gmail variations!`);
                },

                displayResults() {
                    // Update count
                    elements.emailCount().textContent = `${generatedEmails.all.length} variations`;

                    // Clear existing lists
                    elements.allEmailList().innerHTML = '';
                    elements.dotEmailList().innerHTML = '';
                    elements.plusEmailList().innerHTML = '';

                    // Populate all emails
                    generatedEmails.all.forEach(email => {
                        elements.allEmailList().appendChild(createEmailItem(email, 'all'));
                    });

                    // Populate dot emails
                    generatedEmails.dot.forEach(email => {
                        elements.dotEmailList().appendChild(createEmailItem(email, 'dot'));
                    });

                    // Populate plus emails
                    generatedEmails.plus.forEach(email => {
                        elements.plusEmailList().appendChild(createEmailItem(email, 'plus'));
                    });

                    // Show message if no emails generated
                    if (generatedEmails.all.length === 0) {
                        elements.allEmailList().innerHTML = '<div style="color: var(--text-color-light); font-style: italic; text-align: center; padding: var(--spacing-lg);">No variations generated. Please check your input.</div>';
                    }
                },

                switchTab(tabName) {
                    // Remove active class from all tabs and content
                    document.querySelectorAll('.gmail-generator-tab').forEach(tab => tab.classList.remove('active'));
                    document.querySelectorAll('.gmail-generator-tab-content').forEach(content => content.classList.remove('active'));

                    // Add active class to selected tab and content
                    event.target.classList.add('active');
                    document.getElementById(`tab-${tabName}`).classList.add('active');
                },

                copyAll() {
                    if (generatedEmails.all.length === 0) {
                        this.showNotification('Please generate variations first.');
                        return;
                    }

                    const allEmails = generatedEmails.all.join('\n');
                    copyToClipboard(allEmails);
                },

                clear() {
                    elements.gmailAddress().value = '';
                    elements.generationType().selectedIndex = 0;
                    elements.customTags().value = '';
                    elements.generationResults().classList.remove('show');

                    generatedEmails = { all: [], dot: [], plus: [] };

                    this.showNotification('✓ Form cleared successfully!');
                },

                showNotification(message) {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 3000);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Auto-add @gmail.com if not present
                elements.gmailAddress().addEventListener('blur', function() {
                    let value = this.value.trim();
                    if (value && !value.includes('@')) {
                        this.value = value + '@gmail.com';
                    }
                });

                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        GmailGenerator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
