<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Meta Tag Generator - Create SEO-Optimized Meta Tags</title>
    <meta name="description" content="Generate SEO-optimized meta tags instantly with our free Meta Tag Generator. Create title tags, meta descriptions, Open Graph tags, and more for better search rankings.">
    <meta name="keywords" content="meta tag generator, meta tags, seo meta tags, title tag generator, meta description generator, open graph tags">
    <link rel="canonical" href="https://www.webtoolskit.org/p/meta-tag-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Meta Tag Generator - Create SEO-Optimized Meta Tags",
        "description": "Generate SEO-optimized meta tags instantly with our free Meta Tag Generator. Create title tags, meta descriptions, Open Graph tags, and more for better search rankings.",
        "url": "https://www.webtoolskit.org/p/meta-tag-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Meta Tag Generator",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Meta title generation",
                "Meta description creation",
                "Open Graph tags",
                "Twitter Card tags",
                "SEO optimization"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Meta Tags" },
            { "@type": "CopyAction", "name": "Copy Generated Tags" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What are meta tags?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Meta tags are HTML elements that provide information about a webpage to search engines and browsers. They include the title tag, meta description, and other metadata that help search engines understand your content and display it properly in search results."
          }
        },
        {
          "@type": "Question",
          "name": "How do I create meta tags for my website?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using our Meta Tag Generator is the easiest way. Simply enter your page title, description, keywords, and URL into the form above, then click 'Generate Meta Tags'. The tool will create properly formatted HTML meta tags that you can copy and paste into your website's head section."
          }
        },
        {
          "@type": "Question",
          "name": "Why are meta tags important for SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Meta tags are crucial for SEO because they help search engines understand your content and determine how to display it in search results. A well-crafted title tag and meta description can improve your click-through rates, while proper meta tags help search engines index your pages correctly."
          }
        },
        {
          "@type": "Question",
          "name": "What is the ideal length for meta descriptions?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The ideal meta description length is between 150-160 characters. This ensures your description displays fully in search results without being truncated. Our generator automatically checks the length and provides recommendations to optimize your meta descriptions."
          }
        },
        {
          "@type": "Question",
          "name": "Do meta keywords still matter for SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Meta keywords are no longer used by Google for ranking purposes and have been deprecated since 2009. However, some other search engines may still consider them. Focus on creating quality title tags and meta descriptions instead, as these have a much greater impact on SEO."
          }
        }
      ]
    }
    </script>

    <style>
        /* Meta Tag Generator Widget - Simplified & Template Compatible */
        .meta-tag-generator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .meta-tag-generator-widget-container * { box-sizing: border-box; }

        .meta-tag-generator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .meta-tag-generator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .meta-tag-generator-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .meta-tag-generator-field {
            display: flex;
            flex-direction: column;
        }

        .meta-tag-generator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .meta-tag-generator-input,
        .meta-tag-generator-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .meta-tag-generator-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .meta-tag-generator-input:focus,
        .meta-tag-generator-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .meta-tag-generator-char-count {
            font-size: 0.875rem;
            color: var(--text-color-light);
            margin-top: var(--spacing-xs);
        }

        .meta-tag-generator-char-count.warning {
            color: #f59e0b;
        }

        .meta-tag-generator-char-count.error {
            color: #dc2626;
        }

        .meta-tag-generator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .meta-tag-generator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .meta-tag-generator-btn:hover { transform: translateY(-2px); }

        .meta-tag-generator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .meta-tag-generator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .meta-tag-generator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .meta-tag-generator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .meta-tag-generator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .meta-tag-generator-btn-success:hover {
            background-color: #059669;
        }

        .meta-tag-generator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .meta-tag-generator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .meta-tag-generator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .meta-tag-generator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .meta-tag-generator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        @media (max-width: 768px) {
            .meta-tag-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .meta-tag-generator-widget-title { font-size: 1.875rem; }
            .meta-tag-generator-buttons { flex-direction: column; }
            .meta-tag-generator-btn { flex: none; }
        }

        [data-theme="dark"] .meta-tag-generator-input:focus,
        [data-theme="dark"] .meta-tag-generator-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .meta-tag-generator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .meta-tag-generator-output::selection { background-color: var(--primary-color); color: white; }

        .meta-tag-generator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="open-graph-tag-generator"] .meta-tag-generator-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="title-meta-description-checker"] .meta-tag-generator-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="robots-txt-generator"] .meta-tag-generator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .meta-tag-generator-related-tool-item:hover .meta-tag-generator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="open-graph-tag-generator"]:hover .meta-tag-generator-related-tool-icon { background: linear-gradient(145deg, #f56565, #e53e3e); }
        a[href*="title-meta-description-checker"]:hover .meta-tag-generator-related-tool-icon { background: linear-gradient(145deg, #38d9a9, #20c997); }
        a[href*="robots-txt-generator"]:hover .meta-tag-generator-related-tool-icon { background: linear-gradient(145deg, #9373f7, #8a4ff0); }

        .meta-tag-generator-related-tool-item { box-shadow: none; border: none; }
        .meta-tag-generator-related-tool-item:hover { box-shadow: none; border: none; }
        .meta-tag-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .meta-tag-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .meta-tag-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .meta-tag-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .meta-tag-generator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .meta-tag-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .meta-tag-generator-related-tool-item:hover .meta-tag-generator-related-tool-name { color: var(--primary-color); }

        .meta-tag-generator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .meta-tag-generator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .meta-tag-generator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .meta-tag-generator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .meta-tag-generator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .meta-tag-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .meta-tag-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .meta-tag-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .meta-tag-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .meta-tag-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .meta-tag-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .meta-tag-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .meta-tag-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .meta-tag-generator-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="meta-tag-generator-widget-container">
        <h1 class="meta-tag-generator-widget-title">Meta Tag Generator</h1>
        <p class="meta-tag-generator-widget-description">
            Create SEO-optimized meta tags for your website. Generate title tags, meta descriptions, Open Graph tags, and more to improve your search engine rankings.
        </p>
        
        <form class="meta-tag-generator-form">
            <div class="meta-tag-generator-field">
                <label for="metaTagTitle" class="meta-tag-generator-label">Page Title:</label>
                <input 
                    type="text" 
                    id="metaTagTitle" 
                    class="meta-tag-generator-input"
                    placeholder="Enter your page title (50-60 characters recommended)"
                    maxlength="100"
                />
                <div class="meta-tag-generator-char-count" id="titleCharCount">0/60 characters</div>
            </div>

            <div class="meta-tag-generator-field">
                <label for="metaTagDescription" class="meta-tag-generator-label">Meta Description:</label>
                <textarea 
                    id="metaTagDescription" 
                    class="meta-tag-generator-textarea"
                    placeholder="Enter a compelling description of your page (150-160 characters recommended)"
                    maxlength="200"
                ></textarea>
                <div class="meta-tag-generator-char-count" id="descCharCount">0/160 characters</div>
            </div>

            <div class="meta-tag-generator-field">
                <label for="metaTagKeywords" class="meta-tag-generator-label">Keywords (Optional):</label>
                <input 
                    type="text" 
                    id="metaTagKeywords" 
                    class="meta-tag-generator-input"
                    placeholder="Enter keywords separated by commas"
                />
            </div>

            <div class="meta-tag-generator-field">
                <label for="metaTagUrl" class="meta-tag-generator-label">Page URL:</label>
                <input 
                    type="url" 
                    id="metaTagUrl" 
                    class="meta-tag-generator-input"
                    placeholder="https://example.com/page"
                />
            </div>

            <div class="meta-tag-generator-field">
                <label for="metaTagImage" class="meta-tag-generator-label">Image URL (for social sharing):</label>
                <input 
                    type="url" 
                    id="metaTagImage" 
                    class="meta-tag-generator-input"
                    placeholder="https://example.com/image.jpg"
                />
            </div>
        </form>

        <div class="meta-tag-generator-buttons">
            <button class="meta-tag-generator-btn meta-tag-generator-btn-primary" onclick="MetaTagGenerator.generate()">
                Generate Meta Tags
            </button>
            <button class="meta-tag-generator-btn meta-tag-generator-btn-secondary" onclick="MetaTagGenerator.clear()">
                Clear All
            </button>
            <button class="meta-tag-generator-btn meta-tag-generator-btn-success" onclick="MetaTagGenerator.copy()">
                Copy Tags
            </button>
        </div>

        <div class="meta-tag-generator-result">
            <h3 class="meta-tag-generator-result-title">Generated Meta Tags:</h3>
            <div class="meta-tag-generator-output" id="metaTagOutput">Your generated meta tags will appear here...</div>
        </div>

        <div class="meta-tag-generator-related-tools">
            <h3 class="meta-tag-generator-related-tools-title">Related Tools</h3>
            <div class="meta-tag-generator-related-tools-grid">
                <a href="/p/open-graph-tag-generator.html" class="meta-tag-generator-related-tool-item" rel="noopener">
                    <div class="meta-tag-generator-related-tool-icon">
                        <i class="fab fa-facebook"></i>
                    </div>
                    <div class="meta-tag-generator-related-tool-name">Open Graph Tag Generator</div>
                </a>

                <a href="/p/title-meta-description-checker.html" class="meta-tag-generator-related-tool-item" rel="noopener">
                    <div class="meta-tag-generator-related-tool-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="meta-tag-generator-related-tool-name">Title Meta Description Checker</div>
                </a>

                <a href="/p/robots-txt-generator.html" class="meta-tag-generator-related-tool-item" rel="noopener">
                    <div class="meta-tag-generator-related-tool-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="meta-tag-generator-related-tool-name">Robots.txt Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Meta Tag Generator for Better SEO</h2>
            <p>Our <strong>Meta Tag Generator</strong> helps you create perfectly optimized meta tags that boost your website's search engine visibility. Meta tags are essential HTML elements that tell search engines what your pages are about, directly impacting how your content appears in search results and social media shares.</p>
            <p>Whether you're a website owner, digital marketer, or SEO professional, our tool makes it easy to generate properly formatted meta tags that follow current best practices. Simply fill in your page details, and we'll create all the necessary tags you need to improve your search rankings.</p>

            <h3>How to Use the Meta Tag Generator</h3>
            <ol>
                <li><strong>Enter Page Details:</strong> Fill in your page title, description, keywords, URL, and image URL in the form above.</li>
                <li><strong>Generate Tags:</strong> Click the "Generate Meta Tags" button to create your optimized meta tags.</li>
                <li><strong>Copy and Implement:</strong> Copy the generated HTML code and paste it into the &lt;head&gt; section of your webpage.</li>
            </ol>

            <h3>Frequently Asked Questions About Meta Tags</h3>

            <h4>What are meta tags?</h4>
            <p>Meta tags are HTML elements that provide information about a webpage to search engines and browsers. They include the title tag, meta description, and other metadata that help search engines understand your content and display it properly in search results.</p>

            <h4>How do I create meta tags for my website?</h4>
            <p>Using our Meta Tag Generator is the easiest way. Simply enter your page title, description, keywords, and URL into the form above, then click 'Generate Meta Tags'. The tool will create properly formatted HTML meta tags that you can copy and paste into your website's head section.</p>

            <h4>Why are meta tags important for SEO?</h4>
            <p>Meta tags are crucial for SEO because they help search engines understand your content and determine how to display it in search results. A well-crafted title tag and meta description can improve your click-through rates, while proper meta tags help search engines index your pages correctly.</p>

            <h4>What is the ideal length for meta descriptions?</h4>
            <p>The ideal meta description length is between 150-160 characters. This ensures your description displays fully in search results without being truncated. Our generator automatically checks the length and provides recommendations to optimize your meta descriptions.</p>

            <h4>Do meta keywords still matter for SEO?</h4>
            <p>Meta keywords are no longer used by Google for ranking purposes and have been deprecated since 2009. However, some other search engines may still consider them. Focus on creating quality title tags and meta descriptions instead, as these have a much greater impact on SEO.</p>
        </div>

        <div class="meta-tag-generator-features">
            <h3 class="meta-tag-generator-features-title">Key Features:</h3>
            <ul class="meta-tag-generator-features-list">
                <li class="meta-tag-generator-features-item" style="margin-bottom: 0.3em;">SEO-Optimized Meta Tags</li>
                <li class="meta-tag-generator-features-item" style="margin-bottom: 0.3em;">Character Count Validation</li>
                <li class="meta-tag-generator-features-item" style="margin-bottom: 0.3em;">Open Graph Tags Included</li>
                <li class="meta-tag-generator-features-item" style="margin-bottom: 0.3em;">Twitter Card Support</li>
                <li class="meta-tag-generator-features-item" style="margin-bottom: 0.3em;">One-Click Copy to Clipboard</li>
                <li class="meta-tag-generator-features-item" style="margin-bottom: 0.3em;">Mobile-Friendly Interface</li>
                <li class="meta-tag-generator-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="meta-tag-generator-notification" id="metaTagNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                title: () => document.getElementById('metaTagTitle'),
                description: () => document.getElementById('metaTagDescription'),
                keywords: () => document.getElementById('metaTagKeywords'),
                url: () => document.getElementById('metaTagUrl'),
                image: () => document.getElementById('metaTagImage'),
                output: () => document.getElementById('metaTagOutput'),
                notification: () => document.getElementById('metaTagNotification'),
                titleCharCount: () => document.getElementById('titleCharCount'),
                descCharCount: () => document.getElementById('descCharCount')
            };

            function updateCharCount(input, counter, maxLength) {
                const length = input.value.length;
                const remaining = maxLength - length;
                counter.textContent = `${length}/${maxLength} characters`;

                counter.classList.remove('warning', 'error');
                if (length > maxLength * 0.9) {
                    counter.classList.add('warning');
                }
                if (length > maxLength) {
                    counter.classList.add('error');
                }
            }

            window.MetaTagGenerator = {
                generate() {
                    const title = elements.title().value.trim();
                    const description = elements.description().value.trim();
                    const keywords = elements.keywords().value.trim();
                    const url = elements.url().value.trim();
                    const image = elements.image().value.trim();
                    const output = elements.output();

                    if (!title && !description) {
                        output.textContent = 'Please enter at least a title or description to generate meta tags.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';

                    let metaTags = '';

                    // Basic meta tags
                    if (title) {
                        metaTags += `<title>${this.escapeHtml(title)}</title>\n`;
                        metaTags += `<meta name="title" content="${this.escapeHtml(title)}">\n`;
                    }

                    if (description) {
                        metaTags += `<meta name="description" content="${this.escapeHtml(description)}">\n`;
                    }

                    if (keywords) {
                        metaTags += `<meta name="keywords" content="${this.escapeHtml(keywords)}">\n`;
                    }

                    // Open Graph tags
                    if (title) {
                        metaTags += `<meta property="og:title" content="${this.escapeHtml(title)}">\n`;
                    }

                    if (description) {
                        metaTags += `<meta property="og:description" content="${this.escapeHtml(description)}">\n`;
                    }

                    if (url) {
                        metaTags += `<meta property="og:url" content="${this.escapeHtml(url)}">\n`;
                        metaTags += `<link rel="canonical" href="${this.escapeHtml(url)}">\n`;
                    }

                    if (image) {
                        metaTags += `<meta property="og:image" content="${this.escapeHtml(image)}">\n`;
                    }

                    metaTags += `<meta property="og:type" content="website">\n`;

                    // Twitter Card tags
                    metaTags += `<meta name="twitter:card" content="summary_large_image">\n`;

                    if (title) {
                        metaTags += `<meta name="twitter:title" content="${this.escapeHtml(title)}">\n`;
                    }

                    if (description) {
                        metaTags += `<meta name="twitter:description" content="${this.escapeHtml(description)}">\n`;
                    }

                    if (image) {
                        metaTags += `<meta name="twitter:image" content="${this.escapeHtml(image)}">\n`;
                    }

                    // Additional meta tags
                    metaTags += `<meta name="viewport" content="width=device-width, initial-scale=1.0">\n`;
                    metaTags += `<meta charset="UTF-8">`;

                    output.textContent = metaTags;
                },

                escapeHtml(text) {
                    const div = document.createElement('div');
                    div.textContent = text;
                    return div.innerHTML;
                },

                clear() {
                    elements.title().value = '';
                    elements.description().value = '';
                    elements.keywords().value = '';
                    elements.url().value = '';
                    elements.image().value = '';
                    elements.output().textContent = 'Your generated meta tags will appear here...';
                    elements.output().style.color = '';
                    elements.titleCharCount().textContent = '0/60 characters';
                    elements.descCharCount().textContent = '0/160 characters';
                    elements.titleCharCount().classList.remove('warning', 'error');
                    elements.descCharCount().classList.remove('warning', 'error');
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Your generated meta tags will appear here...' || text.startsWith('Please enter') || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Character count updates
                elements.title().addEventListener('input', function() {
                    updateCharCount(this, elements.titleCharCount(), 60);
                });

                elements.description().addEventListener('input', function() {
                    updateCharCount(this, elements.descCharCount(), 160);
                });

                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        MetaTagGenerator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
