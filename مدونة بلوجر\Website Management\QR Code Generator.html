<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free QR Code Generator with Logo - Create Custom QR Codes</title>

    <!-- NEW, ROBUST QR CODE LIBRARY -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode/build/qrcode.min.js"></script>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareApplication"],
        "name": "Free QR Code Generator with Logo - Create Custom QR Codes Online",
        "description": "Generate high-quality QR codes with your own logo. Free, fast, and easy to use online tool with options for custom colors, sizes, and one-click download.",
        "url": "https://www.webtoolskit.org/p/qr-code-generator.html",
        "applicationCategory": "UtilityApplication",
        "operatingSystem": "Any",
        "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
        "featureList": ["QR Code Generation", "Custom Logo Overlay", "Color Customization", "PNG Download"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-29",
        "dateModified": "2025-06-29",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate a QR Code with a Logo" },
            { "@type": "ControlAction", "name": "Download QR Code" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I create a QR code for a website link?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To create a QR code for a website link, simply paste the full URL (e.g., https://www.example.com) into the text input field of our generator, click 'Generate QR Code,' and your code will appear instantly, ready for download."
          }
        },
        {
          "@type": "Question",
          "name": "Do QR codes expire?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Standard (static) QR codes, like the ones generated by this tool, do not expire. They encode the data directly and will work forever as long as the destination link or data is valid. Dynamic QR codes, offered by subscription services, can expire as they point to a redirect service that can be deactivated."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between a static and a dynamic QR code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A static QR code contains fixed data that cannot be changed once created. A dynamic QR code points to a short URL that can be redirected to different destination URLs, allowing you to update the content without changing the code itself. Our tool creates static QR codes for free."
          }
        },
        {
          "@type": "Question",
          "name": "Can I put a logo in a QR code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, and our tool makes it easy! Simply upload your logo using the 'Upload Logo' button. The tool automatically places it in the center. This is possible due to the error correction feature built into QR codes. Using a higher error correction level (like 'H') allows more of the code to be obscured by a logo while remaining scannable."
          }
        },
        {
          "@type": "Question",
          "name": "How do I test if my QR code is working?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To test your QR code, simply open the camera app on your smartphone (most modern phones have a built-in QR scanner) and point it at the code. A notification should pop up with the link or text. Always test your code, especially after adding a logo, before printing it."
          }
        }
      ]
    }
    </script>

    <style>
        /* QR Code Generator Widget - Inherited & Adapted from Template */
        .qr-code-generator-widget-container { max-width: 800px; margin: var(--spacing-xl) auto; padding: var(--spacing-xl); background-color: var(--card-bg); border-radius: var(--border-radius-lg); box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); font-family: var(--font-family); border: 1px solid var(--border-color); }
        .qr-code-generator-widget-container * { box-sizing: border-box; }
        .qr-code-generator-widget-title { color: var(--text-color); text-align: center; margin-bottom: var(--spacing-sm); font-size: 2.25rem; font-weight: 800; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
        .qr-code-generator-widget-description { text-align: center; color: var(--text-color-light); margin-bottom: var(--spacing-xl); font-size: 1.125rem; line-height: 1.6; }
        .qr-code-generator-label { display: block; margin-bottom: var(--spacing-sm); font-weight: 600; color: var(--text-color); }
        .qr-code-generator-input, .qr-code-generator-select { width: 100%; padding: var(--spacing-md) var(--spacing-lg); border: 2px solid var(--border-color); border-radius: var(--border-radius-lg); font-size: var(--font-size-base); transition: var(--transition-base); background-color: var(--background-color-alt); font-family: var(--font-family); color: var(--text-color); margin-bottom: var(--spacing-lg); }
        .qr-code-generator-input:focus, .qr-code-generator-select:focus { outline: none; border-color: var(--primary-color); box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1); background-color: var(--card-bg); }
        .qr-code-generator-options { display: grid; grid-template-columns: repeat(2, 1fr); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl); }
        .qr-code-generator-color-input { width: 40px; height: 40px; border: none; padding: 0; border-radius: 8px; cursor: pointer; background-color: transparent; }
        .qr-code-generator-option { display: flex; flex-direction: column; gap: var(--spacing-sm); }
        .qr-code-logo-option { display: flex; align-items: center; gap: 10px; }
        .qr-code-logo-upload-label { padding: 8px 12px; background-color: var(--background-color-alt); border: 1px solid var(--border-color); border-radius: var(--border-radius-md); cursor: pointer; font-weight: 500; transition: var(--transition-base); }
        .qr-code-logo-upload-label:hover { background-color: var(--border-color); }
        .qr-code-generator-clear-logo-btn { font-size: 0.8rem; padding: 4px 8px; border-radius: 4px; border: 1px solid var(--border-color); background-color: #f87171; color: white; cursor: pointer; display: none; }
        #qrLogoInput { display: none; }
        .qr-code-generator-buttons { display: flex; gap: var(--spacing-md); margin-bottom: var(--spacing-xl); flex-wrap: wrap; }
        .qr-code-generator-btn { padding: var(--spacing-sm) var(--spacing-lg); border: none; border-radius: var(--border-radius-md); font-size: var(--font-size-base); font-weight: 600; cursor: pointer; transition: var(--transition-base); flex: 1; min-width: 140px; }
        .qr-code-generator-btn:hover { transform: translateY(-2px); }
        .qr-code-generator-btn:disabled { background-color: #9ca3af; cursor: not-allowed; transform: none; box-shadow: none; }
        .qr-code-generator-btn-primary { background-color: var(--primary-color); color: white; }
        .qr-code-generator-btn-primary:hover:not(:disabled) { background-color: var(--secondary-color); box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4); }
        .qr-code-generator-btn-secondary { background-color: var(--background-color-alt); color: var(--text-color); border: 1px solid var(--border-color); }
        .qr-code-generator-btn-secondary:hover:not(:disabled) { background-color: var(--border-color); }
        .qr-code-generator-btn-success { background-color: #10b981; color: white; }
        .qr-code-generator-btn-success:hover:not(:disabled) { background-color: #059669; }
        .qr-code-generator-result { display: flex; flex-direction: column; align-items: center; justify-content: center; background-color: var(--background-color-alt); border-radius: var(--border-radius-lg); padding: var(--spacing-lg); border: 1px solid var(--border-color); min-height: 280px; transition: var(--transition-base); }
        .qr-code-generator-output { background-color: white; border-radius: var(--border-radius-md); padding: 10px; line-height: 0; box-shadow: 0 4px 15px rgba(0,0,0,0.1); max-width: 100%; }
        .qr-code-generator-output-placeholder { color: var(--text-color-light); }
        .qr-code-generator-output img { max-width: 100%; height: auto !important; object-fit: contain; }
        .qr-code-generator-notification { position: fixed; top: 20px; right: 20px; background-color: #10b981; color: white; padding: var(--spacing-md) var(--spacing-lg); border-radius: var(--border-radius-md); font-weight: 600; z-index: 10000; transform: translateX(400px); transition: var(--transition-base); }
        .qr-code-generator-notification.show { transform: translateX(0); }
        .seo-content { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); color: var(--text-color-light); line-height: 1.7; }
        .seo-content h2, .seo-content h3 { color: var(--text-color); margin-bottom: var(--spacing-md); }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code { background-color: var(--background-color-alt); padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 6px; font-family: 'SF Mono', Monaco, monospace; }
        
        .qr-code-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .qr-code-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .qr-code-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .qr-code-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .qr-code-generator-related-tool-item:hover .qr-code-generator-related-tool-name { color: var(--primary-color); }
        .qr-code-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .qr-code-generator-related-tool-icon { width: 72px; height: 72px; margin: 0 auto var(--spacing-sm); border-radius: 18px; display: flex; align-items: center; justify-content: center; font-size: 2.25rem; color: white; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15); border: none; }
        a[href*="url-encode"] .qr-code-generator-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="utm-builder"] .qr-code-generator-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }
        a[href*="qr-code-decoder"] .qr-code-generator-related-tool-icon { background: linear-gradient(145deg, #A855F7, #9333EA); }
        
        .qr-code-generator-features { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .qr-code-generator-features-title { color: var(--text-color); margin-bottom: var(--spacing-md); font-size: 1.25rem; font-weight: 700; }
        .qr-code-generator-features-list { list-style: none; padding: 0; margin: 0; columns: 2; -webkit-columns: 2; -moz-columns: 2; }
        .qr-code-generator-features-item { padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg); color: var(--text-color-light); position: relative; margin-bottom: 0.3em; break-inside: avoid; -webkit-column-break-inside: avoid; }
        .qr-code-generator-features-item:before { content: ""; position: absolute; left: 0; top: calc(var(--spacing-sm) + 2px); width: 12px; height: 6px; border-left: 2px solid #10b981; border-bottom: 2px solid #10b981; transform: rotate(-45deg); }
        @media (max-width: 600px) { .qr-code-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 768px) {
            .qr-code-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .qr-code-generator-widget-title { font-size: 1.875rem; }
            .qr-code-generator-buttons { flex-direction: column; }
            .qr-code-generator-btn { flex: none; }
            .qr-code-generator-options { grid-template-columns: 1fr; }
            .qr-code-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .qr-code-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .qr-code-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .qr-code-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .qr-code-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .qr-code-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .qr-code-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .qr-code-generator-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="qr-code-generator-widget-container">
        <h1 class="qr-code-generator-widget-title">Free QR Code Generator with Logo</h1>
        <p class="qr-code-generator-widget-description">
            Create high-quality, branded QR codes in seconds. Add your logo, customize colors, and download instantly for free.
        </p>
        
        <div class="qr-code-generator-input-group">
            <label for="qrCodeInput" class="qr-code-generator-label">Enter URL or Text:</label>
            <input 
                id="qrCodeInput" 
                class="qr-code-generator-input"
                placeholder="e.g., https://www.webtoolskit.org or 'Hello World'"
            />
        </div>

        <div class="qr-code-generator-options">
            <div class="qr-code-generator-option">
                <label for="qrCodeSize" class="qr-code-generator-label">Size (px):</label>
                <select id="qrCodeSize" class="qr-code-generator-select">
                    <option value="256" selected>256 x 256</option>
                    <option value="512">512 x 512</option>
                    <option value="1024">1024 x 1024</option>
                </select>
            </div>
            <div class="qr-code-generator-option">
                <label class="qr-code-generator-label">Logo:</label>
                <div class="qr-code-logo-option">
                    <label for="qrLogoInput" class="qr-code-logo-upload-label">Upload Logo</label>
                    <input type="file" id="qrLogoInput" accept="image/png, image/jpeg, image/svg+xml">
                    <button id="clearLogoBtn" class="qr-code-generator-clear-logo-btn">Clear</button>
                </div>
            </div>
            <div class="qr-code-generator-option">
                <label for="qrCodeColor" class="qr-code-generator-label">Color:</label>
                <input type="color" id="qrCodeColor" class="qr-code-generator-color-input" value="#000000">
            </div>
            <div class="qr-code-generator-option">
                <label for="qrCodeErrorCorrection" class="qr-code-generator-label">Error Correction:</label>
                <select id="qrCodeErrorCorrection" class="qr-code-generator-select">
                    <option value="L">Low (L)</option>
                    <option value="M" selected>Medium (M)</option>
                    <option value="Q">Quartile (Q)</option>
                    <option value="H">High (H)</option>
                </select>
            </div>
        </div>

        <div class="qr-code-generator-buttons">
            <button class="qr-code-generator-btn qr-code-generator-btn-primary" onclick="QRCodeGenerator.generate()">
                Generate QR Code
            </button>
            <button class="qr-code-generator-btn qr-code-generator-btn-secondary" onclick="QRCodeGenerator.clear()">
                Clear All
            </button>
            <button id="downloadBtn" class="qr-code-generator-btn qr-code-generator-btn-success" onclick="QRCodeGenerator.download()" disabled>
                Download PNG
            </button>
        </div>

        <div class="qr-code-generator-result">
            <div id="qrCodeOutput" class="qr-code-generator-output">
                <p class="qr-code-generator-output-placeholder">Your QR code will appear here</p>
            </div>
        </div>

        <div class="qr-code-generator-related-tools">
            <h3 class="qr-code-generator-related-tools-title">Related Tools</h3>
            <div class="qr-code-generator-related-tools-grid">
                <a href="/p/utm-builder.html" class="qr-code-generator-related-tool-item" rel="noopener">
                    <div class="qr-code-generator-related-tool-icon" style="background: linear-gradient(145deg, #10B981, #059669);"><i class="fas fa-chart-line"></i></div>
                    <div class="qr-code-generator-related-tool-name">UTM Builder</div>
                </a>
                <a href="/p/url-encode.html" class="qr-code-generator-related-tool-item" rel="noopener">
                    <div class="qr-code-generator-related-tool-icon" style="background: linear-gradient(145deg, #6f42c1, #563d7c);"><i class="fas fa-shield-alt"></i></div>
                    <div class="qr-code-generator-related-tool-name">URL Encode</div>
                </a>
                <a href="/p/qr-code-decoder.html" class="qr-code-generator-related-tool-item" rel="noopener">
                    <div class="qr-code-generator-related-tool-icon" style="background: linear-gradient(145deg, #A855F7, #9333EA);"><i class="fas fa-search"></i></div>
                    <div class="qr-code-generator-related-tool-name">QR Code Decoder</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Your Instant, Free QR Code Solution</h2>
            <p>Unlock the power of quick-response technology with our <strong>Free QR Code Generator</strong>. In a world that moves fast, QR codes provide a bridge between the physical and digital, allowing users to access websites, view information, or connect to Wi-Fi with a simple scan. Our tool is designed for speed and simplicity, enabling you to create a custom, high-resolution QR code in just a few clicks.</p>
            <p>Whether you're a small business owner creating a link for a menu, a marketer tracking a campaign, or just sharing a personal project, this tool gives you the control you need. You're not limited to a standard black-and-white code; customize the color and size to match your brand or aesthetic. The generated codes are static, meaning they'll never expire and will always point to the data you provide.</p>
            
            <h3>How to Use the QR Code Generator</h3>
            <ol>
                <li><strong>Enter Your Data:</strong> Paste a full URL (like <code>https://www.example.com</code>) or type any text into the input field.</li>
                <li><strong>Add a Logo (Optional):</strong> Click "Upload Logo" to select an image file from your device.</li>
                <li><strong>Customize:</strong> Select your desired size, choose a color, and set the error correction level. "High (H)" is recommended when using a logo.</li>
                <li><strong>Generate and Download:</strong> Click "Generate QR Code". Your code with your logo will appear instantly. Click "Download PNG" to save it.</li>
            </ol>
        
            <h2 id="faq-heading">Frequently Asked Questions About QR Code Generator</h2>
            
            <h3>How do I create a QR code for a website link?</h3>
            <p>To create a QR code for a website link, simply paste the full URL (e.g., https://www.example.com) into the text input field of our generator, click 'Generate QR Code,' and your code will appear instantly, ready for download.</p>
            
            <h3>Do QR codes expire?</h3>
            <p>Standard (static) QR codes, like the ones generated by this tool, do not expire. They encode the data directly and will work forever as long as the destination link or data is valid. Dynamic QR codes, offered by subscription services, can expire as they point to a redirect service that can be deactivated.</p>
            
            <h3>What is the difference between a static and a dynamic QR code?</h3>
            <p>A static QR code contains fixed data that cannot be changed once created. A dynamic QR code points to a short URL that can be redirected to different destination URLs, allowing you to update the content without changing the code itself. Our tool creates static QR codes for free.</p>
            
            <h3>Can I put a logo in a QR code?</h3>
            <p>Yes, and our tool makes it easy! Simply upload your logo using the 'Upload Logo' button. The tool automatically places it in the center. This is possible due to the error correction feature built into QR codes. Using a higher error correction level (like 'H') allows more of the code to be obscured by a logo while remaining scannable.</p>
            
            <h3>How do I test if my QR code is working?</h3>
            <p>To test your QR code, simply open the camera app on your smartphone (most modern phones have a built-in QR scanner) and point it at the code. A notification should pop up with the link or text. Always test your code, especially after adding a logo, before printing it.</p>
        </div>

        <div class="qr-code-generator-features">
            <h3 class="qr-code-generator-features-title">Key Features:</h3>
            <ul class="qr-code-generator-features-list">
                <li class="qr-code-generator-features-item">Instant QR code generation</li>
                <li class="qr-code-generator-features-item">Custom logo overlay</li>
                <li class="qr-code-generator-features-item">Supports URLs and plain text</li>
                <li class="qr-code-generator-features-item">Customizable colors</li>
                <li class="qr-code-generator-features-item">Adjustable error correction</li>
                <li class="qr-code-generator-features-item">Variable size options</li>
                <li class="qr-code-generator-features-item">High-quality PNG download</li>
                <li class="qr-code-generator-features-item">100% free and client-side</li>
            </ul>
        </div>
    </div>

    <div class="qr-code-generator-notification" id="qrCodeNotification">
        ✓ Download started!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                input: document.getElementById('qrCodeInput'),
                output: document.getElementById('qrCodeOutput'),
                downloadBtn: document.getElementById('downloadBtn'),
                notification: document.getElementById('qrCodeNotification'),
                sizeSelect: document.getElementById('qrCodeSize'),
                colorSelect: document.getElementById('qrCodeColor'),
                errorCorrectionSelect: document.getElementById('qrCodeErrorCorrection'),
                logoInput: document.getElementById('qrLogoInput'),
                clearLogoBtn: document.getElementById('clearLogoBtn')
            };

            let uploadedLogo = null;
            let finalImageSrc = null;

            window.QRCodeGenerator = {
                async generate() {
                    const text = elements.input.value.trim();
                    if (!text) {
                        elements.output.innerHTML = '<p class="qr-code-generator-output-placeholder" style="color: #dc2626;">Please enter a URL or text.</p>';
                        elements.downloadBtn.disabled = true;
                        finalImageSrc = null;
                        return;
                    }

                    elements.output.innerHTML = '<p class="qr-code-generator-output-placeholder">Generating...</p>';
                    
                    const size = parseInt(elements.sizeSelect.value, 10);
                    const color = elements.colorSelect.value;
                    const errorCorrectionLevel = elements.errorCorrectionSelect.value;
                    
                    const qrCodeOptions = {
                        errorCorrectionLevel: errorCorrectionLevel,
                        type: 'image/png',
                        width: size,
                        margin: 1,
                        color: {
                            dark: color,
                            light: '#FFFFFFFF' // Fully transparent background
                        }
                    };

                    try {
                        const qrCodeDataURL = await QRCode.toDataURL(text, qrCodeOptions);
                        
                        if (uploadedLogo) {
                            finalImageSrc = await this.addLogoToQRCode(qrCodeDataURL, uploadedLogo, size);
                        } else {
                            finalImageSrc = qrCodeDataURL;
                        }

                        elements.output.innerHTML = `<img src="${finalImageSrc}" alt="Generated QR Code">`;
                        elements.downloadBtn.disabled = false;

                    } catch (error) {
                        console.error("QR Code generation failed:", error);
                        elements.output.innerHTML = '<p class="qr-code-generator-output-placeholder" style="color: #dc2626;">Generation failed. Text may be too long.</p>';
                        elements.downloadBtn.disabled = true;
                        finalImageSrc = null;
                    }
                },
                
                addLogoToQRCode(qrSrc, logoSrc, size) {
                    return new Promise((resolve) => {
                        const canvas = document.createElement('canvas');
                        canvas.width = size;
                        canvas.height = size;
                        const ctx = canvas.getContext('2d');

                        const qrImage = new Image();
                        qrImage.onload = () => {
                            // Draw the QR code first
                            ctx.drawImage(qrImage, 0, 0, size, size);

                            // Then draw the logo on top
                            const logoImage = new Image();
                            logoImage.onload = () => {
                                const logoSize = size * 0.25;
                                const logoX = (size - logoSize) / 2;
                                const logoY = (size - logoSize) / 2;
                                
                                // Draw a white background for the logo
                                const bgPadding = size * 0.02;
                                ctx.fillStyle = '#ffffff';
                                ctx.fillRect(logoX - bgPadding, logoY - bgPadding, logoSize + (bgPadding * 2), logoSize + (bgPadding * 2));
                                
                                // Draw the logo
                                ctx.drawImage(logoImage, logoX, logoY, logoSize, logoSize);
                                
                                resolve(canvas.toDataURL('image/png'));
                            };
                            logoImage.src = logoSrc;
                        };
                        qrImage.src = qrSrc;
                    });
                },

                download() {
                    if (!finalImageSrc) return;
                    let downloadLink = document.createElement("a");
                    downloadLink.href = finalImageSrc;
                    downloadLink.download = "qrcode.png";
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);
                    this.showNotification();
                },

                clear() {
                    elements.input.value = '';
                    elements.output.innerHTML = '<p class="qr-code-generator-output-placeholder">Your QR code will appear here</p>';
                    elements.downloadBtn.disabled = true;
                    this.clearLogo();
                    finalImageSrc = null;
                },
                
                clearLogo() {
                    uploadedLogo = null;
                    elements.logoInput.value = '';
                    elements.clearLogoBtn.style.display = 'none';
                },

                showNotification() {
                    const notification = elements.notification;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            elements.logoInput.addEventListener('change', (e) => {
                if (e.target.files && e.target.files[0]) {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                        uploadedLogo = event.target.result;
                        elements.clearLogoBtn.style.display = 'inline-block';
                    };
                    reader.readAsDataURL(e.target.files[0]);
                }
            });
            
            elements.clearLogoBtn.addEventListener('click', () => {
                QRCodeGenerator.clearLogo();
            });

            elements.input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    QRCodeGenerator.generate();
                }
            });
        })();
    </script>
</body>
</html>