<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Enlarger Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Image Enlarger - Upscale Images Without Quality Loss",
        "description": "Enlarge and upscale images up to 4x their original size while maintaining quality. Free online image enlarger with instant processing and high-quality results.",
        "url": "https://www.webtoolskit.org/p/image-enlarger_30.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Image Enlarger",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Enlarge Image" },
            { "@type": "DownloadAction", "name": "Download Enlarged Image" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I upscale my image without losing quality?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To upscale an image without losing quality, use an AI-powered image enlarger that employs advanced algorithms like bicubic interpolation or machine learning techniques. Our free image enlarger maintains image quality by intelligently analyzing pixels and creating smooth transitions when scaling up, preserving details and reducing pixelation."
          }
        },
        {
          "@type": "Question",
          "name": "What is the best image enlarger or upscaling tool?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The best image enlarger depends on your needs, but our free online tool offers excellent results for most use cases. It uses advanced upscaling algorithms to enlarge images up to 4x their original size while maintaining quality. For professional work, AI-based tools generally provide superior results compared to simple interpolation methods."
          }
        },
        {
          "@type": "Question",
          "name": "Can you upscale an image for free online?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can upscale images for free using our online image enlarger. Simply upload your image, choose your desired enlargement factor (2x, 3x, or 4x), and download the enlarged result. No registration required, and all processing happens in your browser for privacy and speed."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert a normal image into high resolution?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert a normal image into high resolution, use an image enlarger that increases both the pixel dimensions and applies smart algorithms to fill in the additional detail. Our tool scales your image while using interpolation techniques to maintain sharpness and detail, effectively converting low-resolution images to higher resolutions."
          }
        },
        {
          "@type": "Question",
          "name": "How do I make a JPEG image bigger?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To make a JPEG image bigger, upload it to our image enlarger, select your desired scale factor (2x to 4x larger), and click 'Enlarge Image'. The tool will process your JPEG and provide a downloadable enlarged version. The process preserves image quality and supports various output formats including JPEG, PNG, and WebP."
          }
        }
      ]
    }
    </script>

    <style>
        /* Image Enlarger Widget - Simplified & Template Compatible */
        .image-enlarger-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .image-enlarger-widget-container * { box-sizing: border-box; }

        .image-enlarger-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .image-enlarger-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .image-enlarger-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .image-enlarger-upload-area {
            width: 100%;
            padding: var(--spacing-xl);
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            text-align: center;
            cursor: pointer;
            transition: var(--transition-base);
            margin-bottom: var(--spacing-lg);
            position: relative;
        }

        .image-enlarger-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .image-enlarger-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
            transform: scale(1.02);
        }

        .image-enlarger-upload-text {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .image-enlarger-upload-subtext {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .image-enlarger-file-input {
            display: none;
        }

        .image-enlarger-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .image-enlarger-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .image-enlarger-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .image-enlarger-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
        }

        .image-enlarger-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .image-enlarger-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .image-enlarger-btn:hover { transform: translateY(-2px); }

        .image-enlarger-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .image-enlarger-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .image-enlarger-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .image-enlarger-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .image-enlarger-btn-success {
            background-color: #10b981;
            color: white;
        }

        .image-enlarger-btn-success:hover {
            background-color: #059669;
        }

        .image-enlarger-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-lg);
        }

        .image-enlarger-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .image-enlarger-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }

        .image-enlarger-preview-section {
            text-align: center;
        }

        .image-enlarger-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
            display: block;
        }

        .image-enlarger-preview-image {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .image-enlarger-preview-info {
            margin-top: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .image-enlarger-processing {
            display: none;
            text-align: center;
            padding: var(--spacing-lg);
            color: var(--primary-color);
            font-weight: 600;
        }

        .image-enlarger-processing::before {
            content: "⏳ ";
            font-size: 1.2em;
        }

        .image-enlarger-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="image-resizer"] .image-enlarger-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-cropper"] .image-enlarger-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="image-converter"] .image-enlarger-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .image-enlarger-related-tool-item:hover .image-enlarger-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="image-resizer"]:hover .image-enlarger-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-cropper"]:hover .image-enlarger-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="image-converter"]:hover .image-enlarger-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .image-enlarger-related-tool-item { box-shadow: none; border: none; }
        .image-enlarger-related-tool-item:hover { box-shadow: none; border: none; }
        .image-enlarger-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .image-enlarger-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .image-enlarger-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .image-enlarger-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .image-enlarger-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .image-enlarger-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .image-enlarger-related-tool-item:hover .image-enlarger-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .image-enlarger-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .image-enlarger-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .image-enlarger-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .image-enlarger-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .image-enlarger-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        .image-enlarger-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .image-enlarger-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .image-enlarger-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .image-enlarger-widget-title { font-size: 1.875rem; }
            .image-enlarger-buttons { flex-direction: column; }
            .image-enlarger-btn { flex: none; }
            .image-enlarger-options { grid-template-columns: 1fr; }
            .image-enlarger-preview { grid-template-columns: 1fr; }
            .image-enlarger-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .image-enlarger-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .image-enlarger-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .image-enlarger-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .image-enlarger-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .image-enlarger-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .image-enlarger-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .image-enlarger-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .image-enlarger-upload-area:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .image-enlarger-checkbox:focus, .image-enlarger-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .image-enlarger-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .image-enlarger-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="image-enlarger-widget-container">
        <h1 class="image-enlarger-widget-title">Image Enlarger</h1>
        <p class="image-enlarger-widget-description">
            Enlarge and upscale your images up to 4x their original size while maintaining quality. Perfect for enhancing photos, graphics, and artwork.
        </p>
        
        <div class="image-enlarger-input-group">
            <label for="imageEnlargerInput" class="image-enlarger-label">Select your image:</label>
            <div class="image-enlarger-upload-area" id="imageEnlargerUploadArea">
                <div class="image-enlarger-upload-text">📤 Click to select or drag & drop your image</div>
                <div class="image-enlarger-upload-subtext">Supports JPG, PNG, WebP, GIF (max 10MB)</div>
                <input type="file" id="imageEnlargerInput" class="image-enlarger-file-input" accept="image/*">
            </div>
        </div>

        <div class="image-enlarger-options">
            <div class="image-enlarger-option">
                <input type="checkbox" id="enlarger2x" class="image-enlarger-checkbox" checked>
                <label for="enlarger2x" class="image-enlarger-option-label">2x Size (200%)</label>
            </div>
            <div class="image-enlarger-option">
                <input type="checkbox" id="enlarger3x" class="image-enlarger-checkbox">
                <label for="enlarger3x" class="image-enlarger-option-label">3x Size (300%)</label>
            </div>
            <div class="image-enlarger-option">
                <input type="checkbox" id="enlarger4x" class="image-enlarger-checkbox">
                <label for="enlarger4x" class="image-enlarger-option-label">4x Size (400%)</label>
            </div>
        </div>

        <div class="image-enlarger-buttons">
            <button class="image-enlarger-btn image-enlarger-btn-primary" onclick="ImageEnlarger.enlarge()">
                Enlarge Image
            </button>
            <button class="image-enlarger-btn image-enlarger-btn-secondary" onclick="ImageEnlarger.clear()">
                Clear All
            </button>
            <button class="image-enlarger-btn image-enlarger-btn-success" onclick="ImageEnlarger.download()">
                Download Result
            </button>
        </div>

        <div class="image-enlarger-processing" id="imageEnlargerProcessing">
            Processing your image...
        </div>

        <div class="image-enlarger-result" id="imageEnlargerResult" style="display: none;">
            <h3 class="image-enlarger-result-title">Image Preview:</h3>
            <div class="image-enlarger-preview">
                <div class="image-enlarger-preview-section">
                    <span class="image-enlarger-preview-label">Original</span>
                    <img id="imageEnlargerOriginal" class="image-enlarger-preview-image" alt="Original image">
                    <div class="image-enlarger-preview-info" id="imageEnlargerOriginalInfo"></div>
                </div>
                <div class="image-enlarger-preview-section">
                    <span class="image-enlarger-preview-label">Enlarged</span>
                    <img id="imageEnlargerEnlarged" class="image-enlarger-preview-image" alt="Enlarged image">
                    <div class="image-enlarger-preview-info" id="imageEnlargerEnlargedInfo"></div>
                </div>
            </div>
        </div>

        <div class="image-enlarger-related-tools">
            <h3 class="image-enlarger-related-tools-title">Related Tools</h3>
            <div class="image-enlarger-related-tools-grid">
                <a href="/p/image-resizer.html" class="image-enlarger-related-tool-item" rel="noopener">
                    <div class="image-enlarger-related-tool-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div class="image-enlarger-related-tool-name">Image Resizer</div>
                </a>

                <a href="/p/image-cropper.html" class="image-enlarger-related-tool-item" rel="noopener">
                    <div class="image-enlarger-related-tool-icon">
                        <i class="fas fa-crop"></i>
                    </div>
                    <div class="image-enlarger-related-tool-name">Image Cropper</div>
                </a>

                <a href="/p/image-converter.html" class="image-enlarger-related-tool-item" rel="noopener">
                    <div class="image-enlarger-related-tool-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="image-enlarger-related-tool-name">Image Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Image Enlarger for High-Quality Upscaling</h2>
            <p>Need to make your images bigger without losing quality? Our free <strong>Image Enlarger</strong> tool uses advanced upscaling algorithms to increase image size up to 4x while preserving sharpness and detail. Whether you're working with photos, graphics, or artwork, this tool ensures your enlarged images maintain their visual integrity and professional appearance.</p>
            <p>Unlike simple resizing that often results in pixelated or blurry images, our enlarger employs intelligent interpolation techniques that analyze surrounding pixels to create smooth, high-quality enlargements. This makes it perfect for print preparation, web graphics, and any situation where you need larger images without quality compromise.</p>
            
            <h3>How to Enlarge Your Images</h3>
            <ol>
                <li><strong>Upload Your Image:</strong> Click the upload area or drag and drop your image file (JPG, PNG, WebP, GIF supported).</li>
                <li><strong>Choose Scale Factor:</strong> Select how much larger you want your image to be - 2x, 3x, or 4x the original size.</li>
                <li><strong>Process and Download:</strong> Click "Enlarge Image" to process, then download your high-quality enlarged result.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Image Enlarging</h3>
            
            <h4>How do I upscale my image without losing quality?</h4>
            <p>To upscale an image without losing quality, use an AI-powered image enlarger that employs advanced algorithms like bicubic interpolation or machine learning techniques. Our free image enlarger maintains image quality by intelligently analyzing pixels and creating smooth transitions when scaling up, preserving details and reducing pixelation.</p>
            
            <h4>What is the best image enlarger or upscaling tool?</h4>
            <p>The best image enlarger depends on your needs, but our free online tool offers excellent results for most use cases. It uses advanced upscaling algorithms to enlarge images up to 4x their original size while maintaining quality. For professional work, AI-based tools generally provide superior results compared to simple interpolation methods.</p>
            
            <h4>Can you upscale an image for free online?</h4>
            <p>Yes, you can upscale images for free using our online image enlarger. Simply upload your image, choose your desired enlargement factor (2x, 3x, or 4x), and download the enlarged result. No registration required, and all processing happens in your browser for privacy and speed.</p>
            
            <h4>How to convert a normal image into high resolution?</h4>
            <p>To convert a normal image into high resolution, use an image enlarger that increases both the pixel dimensions and applies smart algorithms to fill in the additional detail. Our tool scales your image while using interpolation techniques to maintain sharpness and detail, effectively converting low-resolution images to higher resolutions.</p>
            
            <h4>How do I make a JPEG image bigger?</h4>
            <p>To make a JPEG image bigger, upload it to our image enlarger, select your desired scale factor (2x to 4x larger), and click 'Enlarge Image'. The tool will process your JPEG and provide a downloadable enlarged version. The process preserves image quality and supports various output formats including JPEG, PNG, and WebP.</p>
        </div>

        <div class="image-enlarger-features">
            <h3 class="image-enlarger-features-title">Key Features:</h3>
            <ul class="image-enlarger-features-list">
                <li class="image-enlarger-features-item" style="margin-bottom: 0.3em;">Advanced upscaling algorithms</li>
                <li class="image-enlarger-features-item" style="margin-bottom: 0.3em;">Up to 4x size increase</li>
                <li class="image-enlarger-features-item" style="margin-bottom: 0.3em;">Quality preservation technology</li>
                <li class="image-enlarger-features-item" style="margin-bottom: 0.3em;">Multiple format support</li>
                <li class="image-enlarger-features-item" style="margin-bottom: 0.3em;">Instant preview comparison</li>
                <li class="image-enlarger-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="image-enlarger-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="image-enlarger-notification" id="imageEnlargerNotification">
        ✓ Image processed successfully!
    </div>

    <script>
        // Simplified Image Enlarger
        (function() {
            'use strict';

            let originalImage = null;
            let enlargedCanvas = null;

            const elements = {
                uploadArea: () => document.getElementById('imageEnlargerUploadArea'),
                input: () => document.getElementById('imageEnlargerInput'),
                result: () => document.getElementById('imageEnlargerResult'),
                processing: () => document.getElementById('imageEnlargerProcessing'),
                originalImg: () => document.getElementById('imageEnlargerOriginal'),
                enlargedImg: () => document.getElementById('imageEnlargerEnlarged'),
                originalInfo: () => document.getElementById('imageEnlargerOriginalInfo'),
                enlargedInfo: () => document.getElementById('imageEnlargerEnlargedInfo'),
                notification: () => document.getElementById('imageEnlargerNotification')
            };

            window.ImageEnlarger = {
                enlarge() {
                    if (!originalImage) {
                        alert('Please select an image first.');
                        return;
                    }

                    const scaleFactor = this.getScaleFactor();
                    if (!scaleFactor) {
                        alert('Please select at least one enlargement option.');
                        return;
                    }

                    elements.processing().style.display = 'block';
                    elements.result().style.display = 'none';

                    setTimeout(() => {
                        try {
                            enlargedCanvas = this.createEnlargedImage(originalImage, scaleFactor);
                            this.displayResult(scaleFactor);
                            this.showNotification();
                        } catch (error) {
                            console.error('Error enlarging image:', error);
                            alert('Error processing image. Please try again.');
                        }
                        elements.processing().style.display = 'none';
                    }, 100);
                },

                getScaleFactor() {
                    if (document.getElementById('enlarger4x').checked) return 4;
                    if (document.getElementById('enlarger3x').checked) return 3;
                    if (document.getElementById('enlarger2x').checked) return 2;
                    return 0;
                },

                createEnlargedImage(img, scaleFactor) {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    const newWidth = img.width * scaleFactor;
                    const newHeight = img.height * scaleFactor;

                    canvas.width = newWidth;
                    canvas.height = newHeight;

                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    if (scaleFactor > 2) {
                        const tempCanvas = document.createElement('canvas');
                        const tempCtx = tempCanvas.getContext('2d');
                        
                        tempCanvas.width = img.width * 2;
                        tempCanvas.height = img.height * 2;
                        tempCtx.imageSmoothingEnabled = true;
                        tempCtx.imageSmoothingQuality = 'high';
                        tempCtx.drawImage(img, 0, 0, tempCanvas.width, tempCanvas.height);
                        
                        ctx.drawImage(tempCanvas, 0, 0, newWidth, newHeight);
                    } else {
                        ctx.drawImage(img, 0, 0, newWidth, newHeight);
                    }

                    return canvas;
                },

                displayResult(scaleFactor) {
                    const originalImg = elements.originalImg();
                    const enlargedImg = elements.enlargedImg();
                    const originalInfo = elements.originalInfo();
                    const enlargedInfo = elements.enlargedInfo();
                    const result = elements.result();

                    originalImg.src = originalImage.src;
                    enlargedImg.src = enlargedCanvas.toDataURL('image/png');
                    
                    originalInfo.textContent = `${originalImage.width} × ${originalImage.height} px`;
                    enlargedInfo.textContent = `${enlargedCanvas.width} × ${enlargedCanvas.height} px (${scaleFactor}x larger)`;
                    
                    result.style.display = 'block';
                },

                download() {
                    if (!enlargedCanvas) {
                        alert('Please enlarge an image first.');
                        return;
                    }

                    const link = document.createElement('a');
                    link.download = `enlarged-image-${Date.now()}.png`;
                    link.href = enlargedCanvas.toDataURL('image/png');
                    link.click();
                },

                clear() {
                    originalImage = null;
                    enlargedCanvas = null;
                    elements.input().value = '';
                    elements.result().style.display = 'none';
                    elements.processing().style.display = 'none';
                    
                    document.getElementById('enlarger2x').checked = true;
                    document.getElementById('enlarger3x').checked = false;
                    document.getElementById('enlarger4x').checked = false;
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                const uploadArea = elements.uploadArea();
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                uploadArea.addEventListener('click', () => input.click());
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });
                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });
                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        processFile(files[0]);
                    }
                });

                input.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        processFile(e.target.files[0]);
                    }
                });

                const checkboxes = document.querySelectorAll('.image-enlarger-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        if (this.checked) {
                            checkboxes.forEach(cb => {
                                if (cb !== this) cb.checked = false;
                            });
                        }
                    });
                });

                function processFile(file) {
                    if (!file.type.startsWith('image/')) {
                        alert('Please select a valid image file.');
                        return;
                    }
                    if (file.size > 10 * 1024 * 1024) {
                        alert('File size must be less than 10MB.');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = new Image();
                        img.onload = function() {
                            originalImage = img;
                            originalImage.src = e.target.result;
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });
        })();
    </script>
</body>
</html>