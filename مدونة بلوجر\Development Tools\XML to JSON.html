<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML to JSON Converter - Convert XML Data to JSON Online</title>
    <meta name="description" content="Easily and accurately convert your XML data into a clean, readable JSON format. Free online XML to JSON converter perfect for web developers and API integration.">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "XML to JSON Converter - Convert XML Data to JSON Online",
        "description": "Easily and accurately convert your XML data into a clean, readable JSON format. Free online XML to JSON converter perfect for web developers and API integration.",
        "url": "https://www.webtoolskit.org/p/xml-to-json.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-21",
        "dateModified": "2025-06-21",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "SoftwareApplication",
            "name": "XML to JSON Converter",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert XML to JSON" },
            { "@type": "CopyAction", "name": "Copy Converted JSON" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the difference between XML and JSON?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "XML (eXtensible Markup Language) is a markup language that uses tags to define elements, similar to HTML. It is verbose and strictly structured. JSON (JavaScript Object Notation) is a lightweight data-interchange format that uses key-value pairs. JSON is generally less verbose, easier for humans to read, and faster for machines to parse, making it the preferred format for modern web APIs."
          }
        },
        {
          "@type": "Question",
          "name": "How do I convert XML to JSON?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert XML to JSON, you can use an online converter like this one. Simply paste your XML code into the input field, click the 'Convert to JSON' button, and the tool will automatically parse the XML and generate the equivalent JSON structure in the output field."
          }
        },
        {
          "@type": "Question",
          "name": "Why convert XML to JSON?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Developers often convert XML to JSON to integrate with modern web services and JavaScript frameworks that work natively with JSON. JSON is easier to parse in JavaScript, requires less bandwidth due to its compact nature, and is generally considered more readable and developer-friendly."
          }
        },
        {
          "@type": "Question",
          "name": "Does XML to JSON conversion lose data?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A well-designed converter should not lose data, but it will represent it differently. XML concepts like attributes, comments, and processing instructions don't have a direct equivalent in JSON. A good converter will handle attributes by mapping them to special keys (e.g., prefixing them with '@') and will typically ignore comments, as they are not part of the JSON standard."
          }
        },
        {
          "@type": "Question",
          "name": "Can you convert XML with attributes to JSON?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes. A robust XML to JSON converter handles attributes correctly. This tool follows a common convention by converting XML attributes into JSON key-value pairs, prefixed with an '@' symbol, to distinguish them from regular child elements. This ensures all data from the XML structure is preserved in the resulting JSON."
          }
        }
      ]
    }
    </script>


    <style>
        /* XML to JSON Converter Widget - Simplified & Template Compatible */
        .xml-to-json-widget-container {
            max-width: 900px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .xml-to-json-widget-container * { box-sizing: border-box; }

        .xml-to-json-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .xml-to-json-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .xml-to-json-io-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            align-items: start;
        }
        
        .xml-to-json-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .xml-to-json-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: 0.9rem;
            transition: var(--transition-base);
            resize: vertical;
            min-height: 250px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
        }

        .xml-to-json-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .xml-to-json-controls {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            margin: var(--spacing-xl) 0;
        }

        .xml-to-json-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
        }
        .xml-to-json-btn:hover { transform: translateY(-2px); }

        .xml-to-json-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        .xml-to-json-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .xml-to-json-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }
        .xml-to-json-btn-secondary:hover { background-color: var(--border-color); }
        
        [data-theme="dark"] .xml-to-json-btn-secondary {
            background-color: #374151;
            color: #e5e7eb;
            border-color: #4b5563;
        }
        [data-theme="dark"] .xml-to-json-btn-secondary:hover {
            background-color: #4b5563;
            border-color: #6b7280;
        }

        .xml-to-json-status {
            padding: var(--spacing-md);
            text-align: center;
            border-radius: var(--border-radius-md);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 0.9rem;
            font-weight: 600;
            background-color: var(--background-color-alt);
            border: 1px solid var(--border-color);
        }
        .xml-to-json-status.success { color: #10b981; }
        .xml-to-json-status.error { color: #ef4444; }


        .xml-to-json-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }
        .xml-to-json-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 { color: var(--text-color); margin-bottom: var(--spacing-md); }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .xml-to-json-features { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .xml-to-json-features-title { color: var(--text-color); margin-bottom: var(--spacing-md); font-size: 1.25rem; font-weight: 700; }
        .xml-to-json-features-list { list-style: none; padding: 0; margin: 0; columns: 2; -webkit-columns: 2; -moz-columns: 2; }
        .xml-to-json-features-item { padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg); color: var(--text-color-light); position: relative; }
        .xml-to-json-features-item:before { content: ""; position: absolute; left: 0; top: calc(var(--spacing-sm) + 4px); width: 12px; height: 6px; border-left: 2px solid #10b981; border-bottom: 2px solid #10b981; transform: rotate(-45deg); }
        
        .xml-to-json-related-tool-icon { width: 72px; height: 72px; margin: 0 auto var(--spacing-sm); border-radius: 18px; display: flex; align-items: center; justify-content: center; font-size: 2.25rem; color: white; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15); border: none; }
        a[href*="json-to-xml"] .xml-to-json-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="json-formatter"] .xml-to-json-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="json-validator"] .xml-to-json-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        .xml-to-json-related-tool-item:hover .xml-to-json-related-tool-icon { transform: translateY(-5px) scale(1.05); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15); }
        
        .xml-to-json-related-tool-item { box-shadow: none; border: none; }
        .xml-to-json-related-tool-item:hover { box-shadow: none; border: none; }
        .xml-to-json-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .xml-to-json-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .xml-to-json-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .xml-to-json-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .xml-to-json-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .xml-to-json-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .xml-to-json-related-tool-item:hover .xml-to-json-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .xml-to-json-io-grid { grid-template-columns: 1fr; }
            .xml-to-json-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .xml-to-json-widget-title { font-size: 1.875rem; }
            .xml-to-json-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .xml-to-json-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .xml-to-json-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .xml-to-json-related-tool-name { font-size: 0.875rem; }
        }
        @media (max-width: 600px) { .xml-to-json-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
        @media (max-width: 480px) {
            .xml-to-json-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .xml-to-json-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .xml-to-json-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .xml-to-json-related-tool-name { font-size: 0.75rem; }
        }
        [data-theme="dark"] .xml-to-json-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .xml-to-json-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
    </style>
</head>
<body>
    <div class="xml-to-json-widget-container">
        <h1 class="xml-to-json-widget-title">XML to JSON Converter</h1>
        <p class="xml-to-json-widget-description">
            Bridge the gap between legacy and modern data formats. Paste your XML below to instantly convert it into a clean, structured JSON object.
        </p>
        
        <div class="xml-to-json-io-grid">
            <div class="xml-to-json-input-group">
                <label for="xmlToJsonInput" class="xml-to-json-label">XML Input</label>
                <textarea 
                    id="xmlToJsonInput" 
                    class="xml-to-json-textarea"
                    placeholder='<root><person id="1"><name>John</name></person></root>'
                    rows="10"
                ></textarea>
            </div>
            <div class="xml-to-json-output-group">
                <label for="xmlToJsonOutput" class="xml-to-json-label">JSON Output</label>
                <textarea 
                    id="xmlToJsonOutput" 
                    class="xml-to-json-textarea"
                    placeholder="Your converted JSON will appear here..."
                    rows="10"
                    readonly
                ></textarea>
            </div>
        </div>

        <div class="xml-to-json-controls">
            <button class="xml-to-json-btn xml-to-json-btn-primary" onclick="XmlToJson.convert()">Convert to JSON</button>
            <div id="xmlToJsonStatus" class="xml-to-json-status">Ready to convert...</div>
            <div style="display: flex; gap: var(--spacing-md);">
                <button class="xml-to-json-btn xml-to-json-btn-secondary" onclick="XmlToJson.copy()" style="flex:1;">Copy JSON</button>
                <button class="xml-to-json-btn xml-to-json-btn-secondary" onclick="XmlToJson.clear()" style="flex:1;">Clear All</button>
            </div>
        </div>

        <div class="xml-to-json-related-tools">
            <h3 class="xml-to-json-related-tools-title">Related Tools</h3>
            <div class="xml-to-json-related-tools-grid">
                <a href="/p/json-to-xml.html" class="xml-to-json-related-tool-item" rel="noopener">
                    <div class="xml-to-json-related-tool-icon">
                        <i class="fas fa-exchange-alt fa-flip-horizontal"></i>
                    </div>
                    <div class="xml-to-json-related-tool-name">JSON to XML</div>
                </a>
                <a href="/p/json-formatter.html" class="xml-to-json-related-tool-item" rel="noopener">
                    <div class="xml-to-json-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="xml-to-json-related-tool-name">JSON Formatter</div>
                </a>
                <a href="/p/json-validator.html" class="xml-to-json-related-tool-item" rel="noopener">
                    <div class="xml-to-json-related-tool-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="xml-to-json-related-tool-name">JSON Validator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Modernize Your Data with Our XML to JSON Converter</h2>
            <p>While XML has been a cornerstone of data exchange for decades, modern web development has largely embraced JSON for its simplicity and ease of use with JavaScript. Our <strong>XML to JSON Converter</strong> is an essential utility for any developer needing to bridge this gap. This tool intelligently parses your XML data, including its attributes and nested structure, and transforms it into a clean, well-formatted JSON object.</p>
            <p>Whether you're migrating a legacy system, consuming an old API, or just need to switch formats for a project, our converter provides a fast, reliable, and accurate solution. It also validates your XML first, ensuring a smooth conversion process.</p>
            
            <h3>How to Convert XML to JSON</h3>
            <ol>
                <li><strong>Paste Your XML:</strong> Copy your XML code and paste it into the "XML Input" field on the left.</li>
                <li><strong>Click Convert:</strong> Press the "Convert to JSON" button.</li>
                <li><strong>Get Your JSON:</strong> The equivalent JSON structure will be generated in the "JSON Output" field on the right, ready for you to copy and use.</li>
            </ol>
        
            <h3>Frequently Asked Questions About XML to JSON Conversion</h3>
            
            <h4>What is the difference between XML and JSON?</h4>
            <p>XML (eXtensible Markup Language) is a markup language that uses tags to define elements, similar to HTML. It is verbose and strictly structured. JSON (JavaScript Object Notation) is a lightweight data-interchange format that uses key-value pairs. JSON is generally less verbose, easier for humans to read, and faster for machines to parse, making it the preferred format for modern web APIs.</p>
            
            <h4>How do I convert XML to JSON?</h4>
            <p>To convert XML to JSON, you can use an online converter like this one. Simply paste your XML code into the input field, click the 'Convert to JSON' button, and the tool will automatically parse the XML and generate the equivalent JSON structure in the output field.</p>
            
            <h4>Why convert XML to JSON?</h4>
            <p>Developers often convert XML to JSON to integrate with modern web services and JavaScript frameworks that work natively with JSON. JSON is easier to parse in JavaScript, requires less bandwidth due to its compact nature, and is generally considered more readable and developer-friendly.</p>
            
            <h4>Does XML to JSON conversion lose data?</h4>
            <p>A well-designed converter should not lose data, but it will represent it differently. XML concepts like attributes, comments, and processing instructions don't have a direct equivalent in JSON. A good converter will handle attributes by mapping them to special keys (e.g., prefixing them with '@') and will typically ignore comments, as they are not part of the JSON standard.</p>
            
            <h4>Can you convert XML with attributes to JSON?</h4>
            <p>Yes. A robust XML to JSON converter handles attributes correctly. This tool follows a common convention by converting XML attributes into JSON key-value pairs, prefixed with an '@' symbol, to distinguish them from regular child elements. This ensures all data from the XML structure is preserved in the resulting JSON.</p>
        </div>

        <div class="xml-to-json-features">
            <h3 class="xml-to-json-features-title">Key Features:</h3>
            <ul class="xml-to-json-features-list">
                <li class="xml-to-json-features-item">Accurate XML Parsing</li>
                <li class="xml-to-json-features-item">Handles Attributes & Nesting</li>
                <li class="xml-to-json-features-item">Built-in XML Validation</li>
                <li class="xml-to-json-features-item">Creates Arrays for Siblings</li>
                <li class="xml-to-json-features-item">Side-by-Side Comparison</li>
                <li class="xml-to-json-features-item">Fast & Secure Conversion</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="xml-to-json-notification" id="xmlToJsonNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // XML to JSON Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('xmlToJsonInput'),
                output: () => document.getElementById('xmlToJsonOutput'),
                status: () => document.getElementById('xmlToJsonStatus'),
                notification: () => document.getElementById('xmlToJsonNotification')
            };

            const setStatus = (message, type) => {
                const statusEl = elements.status();
                statusEl.textContent = message;
                statusEl.className = 'xml-to-json-status'; // Reset classes
                if (type) {
                    statusEl.classList.add(type);
                }
            };

            const xmlToJsonParser = (xml) => {
                let obj = {};

                if (xml.nodeType == 1) { // element
                    if (xml.attributes.length > 0) {
                        for (let j = 0; j < xml.attributes.length; j++) {
                            const attribute = xml.attributes.item(j);
                            obj[`@${attribute.nodeName}`] = attribute.nodeValue;
                        }
                    }
                } else if (xml.nodeType == 3) { // text
                    obj = xml.nodeValue;
                }

                if (xml.hasChildNodes()) {
                    for(let i = 0; i < xml.childNodes.length; i++) {
                        const item = xml.childNodes.item(i);
                        const nodeName = item.nodeName;

                        if (nodeName === '#text') {
                             if (item.nodeValue.trim()) obj['#text'] = item.nodeValue.trim();
                             continue;
                        }
                        
                        if (typeof(obj[nodeName]) == "undefined") {
                            obj[nodeName] = xmlToJsonParser(item);
                        } else {
                            if (typeof(obj[nodeName].push) == "undefined") {
                                const old = obj[nodeName];
                                obj[nodeName] = [];
                                obj[nodeName].push(old);
                            }
                            obj[nodeName].push(xmlToJsonParser(item));
                        }
                    }
                }
                return obj;
            };


            window.XmlToJson = {
                convert() {
                    const input = elements.input();
                    const output = elements.output();
                    const xmlString = input.value.trim();

                    if (!xmlString) {
                        setStatus('Input is empty.', '');
                        output.value = '';
                        return;
                    }

                    try {
                        const parser = new DOMParser();
                        const xmlDoc = parser.parseFromString(xmlString, "application/xml");
                        
                        const parserError = xmlDoc.querySelector("parsererror");
                        if(parserError){
                           throw new Error("Invalid XML: " + parserError.textContent.split("\n")[1]);
                        }

                        const resultObj = xmlToJsonParser(xmlDoc.documentElement);
                        output.value = JSON.stringify(resultObj, null, 2);
                        setStatus('Success! Converted XML to JSON.', 'success');

                    } catch (error) {
                        output.value = '';
                        setStatus(`Error: ${error.message}`, 'error');
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().value = '';
                    setStatus('Ready to convert...', '');
                },

                copy() {
                    const text = elements.output().value;
                    if (!text) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

        })();
    </script>
</body>
</html>