<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Base64 to Image Converter - Free Online Base64 Decoder</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Base64 to Image Converter - Decode Base64 Strings to Images",
        "description": "Convert Base64 encoded strings back to images instantly. Perfect for decoding data URIs and Base64 image data to downloadable image files.",
        "url": "https://www.webtoolskit.org/p/base64-to-image.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-21",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Base64 to Image Converter",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Base64 to Image" },
            { "@type": "DownloadAction", "name": "Download Converted Image" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert Base64 to image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Paste your Base64 string into our converter tool, and it will instantly decode it back to an image. You can then preview the image and download it in your preferred format (PNG, JPG, etc.)."
          }
        },
        {
          "@type": "Question",
          "name": "How to decode a Base64 image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Base64 decoding converts the text-based Base64 string back to binary image data. Our tool automatically detects the image format from the Base64 data and creates a downloadable image file from the decoded data."
          }
        },
        {
          "@type": "Question",
          "name": "How to check if a Base64 string is an image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A Base64 image string typically starts with 'data:image/' followed by the format (png, jpg, gif, etc.) and ';base64,'. Our tool automatically validates the format and shows an error if the Base64 string is not a valid image."
          }
        },
        {
          "@type": "Question",
          "name": "What is the Base64 image format?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Base64 image format is a way to represent binary image data as ASCII text using 64 characters. It's commonly used in data URIs with the format 'data:image/[type];base64,[encoded-data]' where [type] is png, jpg, gif, etc."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert Base64 image to PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Paste your Base64 string into our converter, preview the decoded image, and click 'Download as PNG'. The tool will automatically convert the image to PNG format regardless of the original Base64 image type."
          }
        }
      ]
    }
    </script>

    <style>
        /* Base64 to Image Converter Widget - Matching design with Image to Base64 */
        .base64-image-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .base64-image-widget-container * { box-sizing: border-box; }

        .base64-image-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .base64-image-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .base64-image-input-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
        }

        .base64-image-input-area.has-content {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .base64-image-input-label {
            display: block;
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }

        .base64-image-textarea {
            width: 100%;
            min-height: 120px;
            max-height: 300px;
            resize: vertical;
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            color: var(--text-color);
            outline: none;
            transition: var(--transition-base);
        }

        .base64-image-textarea:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .base64-image-textarea::placeholder {
            color: var(--text-color-light);
            font-style: italic;
        }

        .base64-image-format-info {
            margin-top: var(--spacing-sm);
            padding: var(--spacing-sm);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            font-size: 0.875rem;
            color: var(--text-color-light);
            line-height: 1.5;
        }

        .base64-image-format-info strong {
            color: var(--text-color);
        }

        .base64-image-validation {
            margin-top: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            font-size: 0.875rem;
            font-weight: 600;
            display: none;
        }

        .base64-image-validation.valid {
            background-color: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
            display: block;
        }

        .base64-image-validation.invalid {
            background-color: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
            display: block;
        }

        [data-theme="dark"] .base64-image-validation.valid {
            background-color: rgba(34, 197, 94, 0.1);
            color: #4ade80;
            border-color: rgba(34, 197, 94, 0.3);
        }

        [data-theme="dark"] .base64-image-validation.invalid {
            background-color: rgba(239, 68, 68, 0.1);
            color: #f87171;
            border-color: rgba(239, 68, 68, 0.3);
        }

        .base64-image-preview {
            display: none;
            text-align: center;
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .base64-image-preview.show { display: block; }

        .base64-image-preview-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.125rem;
            font-weight: 600;
        }

        .base64-image-preview-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            padding: var(--spacing-sm);
            margin: 0 auto var(--spacing-md);
            display: block;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .base64-image-preview-info {
            color: var(--text-color-light);
            font-size: 0.9rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .base64-image-info-item {
            background: var(--card-bg);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .base64-image-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .base64-image-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .base64-image-btn:hover { transform: translateY(-2px); }

        .base64-image-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .base64-image-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .base64-image-btn-primary:hover:not(:disabled) {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .base64-image-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .base64-image-btn-secondary:hover:not(:disabled) {
            background-color: var(--border-color);
        }

        .base64-image-btn-success {
            background-color: #10b981;
            color: white;
        }

        .base64-image-btn-success:hover:not(:disabled) {
            background-color: #059669;
        }

        .base64-image-download-options {
            display: none;
            margin-bottom: var(--spacing-xl);
        }

        .base64-image-download-options.show { display: block; }

        .base64-image-download-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.125rem;
            font-weight: 600;
        }

        .base64-image-download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--spacing-md);
        }

        .base64-image-download-btn {
            padding: var(--spacing-md);
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            color: var(--text-color);
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            transition: var(--transition-base);
            cursor: pointer;
            display: block;
        }

        .base64-image-download-btn:hover {
            border-color: var(--primary-color);
            background-color: var(--background-color-alt);
            transform: translateY(-2px);
            text-decoration: none;
            color: var(--primary-color);
        }

        .base64-image-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .base64-image-notification.show { transform: translateX(0); }
        
        .base64-image-notification.error {
            background-color: #ef4444;
        }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .base64-image-related-tool-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            margin: 0 auto var(--spacing-sm);
            transition: var(--transition-base);
            background: linear-gradient(145deg, #667eea, #764ba2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1), inset 0 1px 1px rgba(255, 255, 255, 0.1);
        }
        
        a[href*="image-to-base64"] .base64-image-related-tool-icon { background: linear-gradient(145deg, #ff6b6b, #ee5a52); }
        a[href*="ico-converter"] .base64-image-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="image-converter"] .base64-image-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        a[href*="image-to-base64"]:hover .base64-image-related-tool-icon { background: linear-gradient(145deg, #ff7979, #fd6c6c); }
        a[href*="ico-converter"]:hover .base64-image-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="image-converter"]:hover .base64-image-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .base64-image-related-tool-item:hover .base64-image-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .base64-image-related-tool-item { box-shadow: none; border: none; }
        .base64-image-related-tool-item:hover { box-shadow: none; border: none; }
        .base64-image-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .base64-image-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .base64-image-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .base64-image-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .base64-image-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .base64-image-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .base64-image-related-tool-item:hover .base64-image-related-tool-name { color: var(--primary-color); }

        .base64-image-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .base64-image-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .base64-image-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .base64-image-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .base64-image-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .base64-image-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .base64-image-widget-title { font-size: 1.875rem; }
            .base64-image-buttons { flex-direction: column; }
            .base64-image-btn { flex: none; }
            .base64-image-download-grid { grid-template-columns: repeat(2, 1fr); }
            .base64-image-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .base64-image-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .base64-image-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .base64-image-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { 
            .base64-image-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } 
            .base64-image-download-grid { grid-template-columns: 1fr; }
        }

        @media (max-width: 480px) {
            .base64-image-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .base64-image-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .base64-image-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .base64-image-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .base64-image-input-area.has-content { background-color: rgba(96, 165, 250, 0.1); }
        .base64-image-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .base64-image-textarea::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="base64-image-widget-container">
        <h1 class="base64-image-widget-title">Base64 to Image Converter</h1>
        <p class="base64-image-widget-description">
            Convert Base64 encoded strings back to images instantly. Decode data URIs and Base64 image data to downloadable image files.
        </p>
        
        <div class="base64-image-input-area" id="inputArea">
            <label for="base64Input" class="base64-image-input-label">Paste your Base64 string here:</label>
            <textarea 
                class="base64-image-textarea" 
                id="base64Input" 
                placeholder="Paste your Base64 string here... 
Examples:
• data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==
• iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
            ></textarea>
            
            <div class="base64-image-format-info">
                <strong>Supported formats:</strong> Full data URI (data:image/type;base64,data) or just Base64 string.<br>
                <strong>Image types:</strong> PNG, JPG, JPEG, GIF, WebP, BMP, SVG
            </div>
            
            <div class="base64-image-validation" id="validation"></div>
        </div>

        <div class="base64-image-preview" id="preview">
            <div class="base64-image-preview-title">Decoded Image Preview:</div>
            <img class="base64-image-preview-image" id="previewImage" alt="Decoded image preview">
            <div class="base64-image-preview-info" id="previewInfo"></div>
        </div>

        <div class="base64-image-buttons">
            <button class="base64-image-btn base64-image-btn-primary" id="decodeBtn" onclick="Base64ImageConverter.decode()" disabled>
                Decode & Preview
            </button>
            <button class="base64-image-btn base64-image-btn-secondary" onclick="Base64ImageConverter.clear()">
                Clear All
            </button>
            <button class="base64-image-btn base64-image-btn-success" onclick="Base64ImageConverter.validateOnly()" disabled id="validateBtn">
                Validate Base64
            </button>
        </div>

        <div class="base64-image-download-options" id="downloadOptions">
            <h3 class="base64-image-download-title">Download as:</h3>
            <div class="base64-image-download-grid">
                <button class="base64-image-download-btn" onclick="Base64ImageConverter.download('png')">
                    Download PNG
                </button>
                <button class="base64-image-download-btn" onclick="Base64ImageConverter.download('jpg')">
                    Download JPG
                </button>
                <button class="base64-image-download-btn" onclick="Base64ImageConverter.download('original')">
                    Download Original
                </button>
                <button class="base64-image-download-btn" onclick="Base64ImageConverter.copyDataUri()">
                    Copy Data URI
                </button>
            </div>
        </div>

        <div class="base64-image-related-tools">
            <h3 class="base64-image-related-tools-title">Related Tools</h3>
            <div class="base64-image-related-tools-grid">
                <a href="/p/image-to-base64.html" class="base64-image-related-tool-item" rel="noopener">
                    <div class="base64-image-related-tool-icon">
                        <i class="fas fa-file-code"></i>
                    </div>
                    <div class="base64-image-related-tool-name">Image to Base64</div>
                </a>

                <a href="/p/ico-converter.html" class="base64-image-related-tool-item" rel="noopener">
                    <div class="base64-image-related-tool-icon">
                        <i class="fas fa-icons"></i>
                    </div>
                    <div class="base64-image-related-tool-name">ICO Converter</div>
                </a>

                <a href="/p/image-converter.html" class="base64-image-related-tool-item" rel="noopener">
                    <div class="base64-image-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="base64-image-related-tool-name">Image Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>How to Convert Base64 to Image</h2>
            <p>Converting Base64 encoded strings back to images is a crucial skill for web developers and anyone working with encoded image data. Our Base64 to Image converter makes this process simple and efficient.</p>
            
            <h3>Simple 3-Step Process:</h3>
            <ol>
                <li><strong>Paste Base64 String:</strong> Copy your Base64 encoded image data into the text area. It can be a full data URI or just the Base64 string.</li>
                <li><strong>Decode & Preview:</strong> Click "Decode & Preview" to convert the Base64 data back to an image and see the preview.</li>
                <li><strong>Download Image:</strong> Choose your preferred format (PNG, JPG, or original) and download the converted image file.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Base64 to Image</h3>
            
            <h4>How to convert Base64 to image?</h4>
            <p>Paste your Base64 string into our converter tool, and it will instantly decode it back to an image. You can then preview the image and download it in your preferred format (PNG, JPG, etc.).</p>
            
            <h4>How to decode a Base64 image?</h4>
            <p>Base64 decoding converts the text-based Base64 string back to binary image data. Our tool automatically detects the image format from the Base64 data and creates a downloadable image file from the decoded data.</p>
            
            <h4>How to check if a Base64 string is an image?</h4>
            <p>A Base64 image string typically starts with 'data:image/' followed by the format (png, jpg, gif, etc.) and ';base64,'. Our tool automatically validates the format and shows an error if the Base64 string is not a valid image. Use the "Validate Base64" button to check without decoding.</p>
            
            <h4>What is the Base64 image format?</h4>
            <p>Base64 image format is a way to represent binary image data as ASCII text using 64 characters (A-Z, a-z, 0-9, +, /). It's commonly used in data URIs with the format <code>data:image/[type];base64,[encoded-data]</code> where [type] is png, jpg, gif, etc. This allows images to be embedded directly in HTML, CSS, or transmitted as text.</p>
            
            <h4>How to convert Base64 image to PNG?</h4>
            <p>Paste your Base64 string into our converter, preview the decoded image, and click 'Download PNG'. The tool will automatically convert the image to PNG format regardless of the original Base64 image type. PNG is ideal for images with transparency or when you need lossless compression.</p>
            
            <h3>Common Use Cases for Base64 to Image Conversion</h3>
            <ul>
                <li><strong>Email Templates:</strong> Convert embedded Base64 images from email HTML to separate image files</li>
                <li><strong>API Responses:</strong> Decode Base64 image data received from APIs</li>
                <li><strong>Data Recovery:</strong> Extract images from Base64 encoded data in databases or logs</li>
                <li><strong>Web Scraping:</strong> Convert Base64 images found in web pages to downloadable files</li>
                <li><strong>Development Testing:</strong> Quickly view and save Base64 encoded test images</li>
                <li><strong>Documentation:</strong> Extract images from Base64 strings in technical documentation</li>
            </ul>

            <h3>Supported Image Formats</h3>
            <p>Our Base64 to Image converter supports all major image formats including:</p>
            <ul>
                <li><strong>PNG:</strong> Best for images with transparency and graphics</li>
                <li><strong>JPEG/JPG:</strong> Ideal for photographs and images with many colors</li>
                <li><strong>GIF:</strong> Perfect for simple animations and images with few colors</li>
                <li><strong>WebP:</strong> Modern format with excellent compression</li>
                <li><strong>BMP:</strong> Uncompressed bitmap format</li>
                <li><strong>SVG:</strong> Vector graphics format</li>
            </ul>

            <h3>Base64 Format Examples</h3>
            <p>Our tool accepts Base64 data in multiple formats:</p>
            <ul>
                <li><strong>Full Data URI:</strong> <code>data:image/png;base64,iVBORw0KGgo...</code></li>
                <li><strong>Base64 Only:</strong> <code>iVBORw0KGgoAAAANSUhEUgAAAAEA...</code></li>
                <li><strong>With Line Breaks:</strong> Multi-line Base64 strings are automatically cleaned</li>
            </ul>
        </div>

        <div class="base64-image-features">
            <h3 class="base64-image-features-title">Key Features:</h3>
            <ul class="base64-image-features-list">
                <li class="base64-image-features-item" style="margin-bottom: 0.3em;">Instant Base64 decoding</li>
                <li class="base64-image-features-item" style="margin-bottom: 0.3em;">Multiple output formats</li>
                <li class="base64-image-features-item" style="margin-bottom: 0.3em;">Real-time validation</li>
                <li class="base64-image-features-item" style="margin-bottom: 0.3em;">Image preview before download</li>
                <li class="base64-image-features-item" style="margin-bottom: 0.3em;">Format auto-detection</li>
                <li class="base64-image-features-item" style="margin-bottom: 0.3em;">Works with data URIs</li>
                <li class="base64-image-features-item" style="margin-bottom: 0.3em;">No server upload required</li>
                <li class="base64-image-features-item">Privacy-focused processing</li>
            </ul>
        </div>
    </div>

    <!-- Notification -->
    <div class="base64-image-notification" id="notification">
        Success!
    </div>

    <script>
        // Base64 to Image Converter - Self-contained IIFE
        (function() {
            'use strict';

            let currentImageData = null;
            let currentImageType = null;

            const elements = {
                inputArea: () => document.getElementById('inputArea'),
                base64Input: () => document.getElementById('base64Input'),
                validation: () => document.getElementById('validation'),
                preview: () => document.getElementById('preview'),
                previewImage: () => document.getElementById('previewImage'),
                previewInfo: () => document.getElementById('previewInfo'),
                decodeBtn: () => document.getElementById('decodeBtn'),
                validateBtn: () => document.getElementById('validateBtn'),
                downloadOptions: () => document.getElementById('downloadOptions'),
                notification: () => document.getElementById('notification')
            };

            window.Base64ImageConverter = {
                validateBase64(base64String) {
                    // Clean the input
                    let cleanBase64 = base64String.trim().replace(/\s+/g, '');
                    
                    // Check if it's a data URI
                    const dataUriRegex = /^data:image\/([a-zA-Z0-9+/]+);base64,(.+)$/;
                    const dataUriMatch = cleanBase64.match(dataUriRegex);
                    
                    let imageType = null;
                    let base64Data = null;
                    
                    if (dataUriMatch) {
                        imageType = dataUriMatch[1].toLowerCase();
                        base64Data = dataUriMatch[2];
                    } else {
                        // Try to detect format from Base64 header
                        if (cleanBase64.startsWith('/9j/')) imageType = 'jpg';
                        else if (cleanBase64.startsWith('iVBORw0KGgo')) imageType = 'png';
                        else if (cleanBase64.startsWith('R0lGODlh')) imageType = 'gif';
                        else if (cleanBase64.startsWith('UklGR')) imageType = 'webp';
                        else if (cleanBase64.startsWith('Qk')) imageType = 'bmp';
                        else if (cleanBase64.startsWith('PHN2Zy')) imageType = 'svg';
                        
                        base64Data = cleanBase64;
                    }
                    
                    // Validate Base64 format
                    const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
                    
                    if (!base64Data || !base64Regex.test(base64Data)) {
                        return { valid: false, error: 'Invalid Base64 format' };
                    }
                    
                    if (base64Data.length % 4 !== 0) {
                        return { valid: false, error: 'Invalid Base64 length' };
                    }
                    
                    if (!imageType) {
                        return { valid: false, error: 'Could not detect image format' };
                    }
                    
                    return { 
                        valid: true, 
                        imageType: imageType,
                        base64Data: base64Data,
                        dataUri: dataUriMatch ? cleanBase64 : `data:image/${imageType};base64,${base64Data}`
                    };
                },

                decode() {
                    const input = elements.base64Input().value.trim();
                    
                    if (!input) {
                        this.showNotification('Please paste a Base64 string first', 'error');
                        return;
                    }
                    
                    const validation = this.validateBase64(input);
                    
                    if (!validation.valid) {
                        this.showValidation(false, validation.error);
                        this.showNotification(`Validation failed: ${validation.error}`, 'error');
                        return;
                    }
                    
                    this.showValidation(true, `Valid ${validation.imageType.toUpperCase()} image detected`);
                    
                    // Create image element to decode
                    const img = new Image();
                    img.onload = () => {
                        currentImageData = validation.dataUri;
                        currentImageType = validation.imageType;
                        
                        elements.previewImage().src = validation.dataUri;
                        
                        // Calculate approximate file size
                        const sizeBytes = Math.round((validation.base64Data.length * 3) / 4);
                        const sizeKB = (sizeBytes / 1024).toFixed(1);
                        
                        elements.previewInfo().innerHTML = `
                            <div class="base64-image-info-item"><strong>Format:</strong> ${validation.imageType.toUpperCase()}</div>
                            <div class="base64-image-info-item"><strong>Size:</strong> ${img.naturalWidth} × ${img.naturalHeight}px</div>
                            <div class="base64-image-info-item"><strong>File Size:</strong> ~${sizeKB} KB</div>
                            <div class="base64-image-info-item"><strong>Base64 Length:</strong> ${validation.base64Data.length} chars</div>
                        `;
                        
                        elements.preview().classList.add('show');
                        elements.downloadOptions().classList.add('show');
                        
                        this.showNotification('Image decoded successfully!');
                    };
                    
                    img.onerror = () => {
                        this.showValidation(false, 'Failed to decode image data');
                        this.showNotification('Failed to decode image data', 'error');
                    };
                    
                    img.src = validation.dataUri;
                },

                validateOnly() {
                    const input = elements.base64Input().value.trim();
                    
                    if (!input) {
                        this.showNotification('Please paste a Base64 string first', 'error');
                        return;
                    }
                    
                    const validation = this.validateBase64(input);
                    
                    if (validation.valid) {
                        this.showValidation(true, `Valid ${validation.imageType.toUpperCase()} image detected`);
                        this.showNotification('Base64 validation passed!');
                    } else {
                        this.showValidation(false, `Error: ${validation.error}`);
                        this.showNotification(`Validation failed: ${validation.error}`, 'error');
                    }
                },

                download(format) {
                    if (!currentImageData) {
                        this.showNotification('Please decode an image first', 'error');
                        return;
                    }
                    
                    if (format === 'original') {
                        this.downloadAsOriginal();
                    } else {
                        this.downloadAsFormat(format);
                    }
                },

                downloadAsOriginal() {
                    const link = document.createElement('a');
                    link.href = currentImageData;
                    link.download = `decoded-image.${currentImageType}`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    this.showNotification(`Downloaded as ${currentImageType.toUpperCase()}`);
                },

                downloadAsFormat(format) {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const img = new Image();
                    
                    img.onload = () => {
                        canvas.width = img.naturalWidth;
                        canvas.height = img.naturalHeight;
                        
                        // Set white background for JPG
                        if (format === 'jpg') {
                            ctx.fillStyle = 'white';
                            ctx.fillRect(0, 0, canvas.width, canvas.height);
                        }
                        
                        ctx.drawImage(img, 0, 0);
                        
                        canvas.toBlob((blob) => {
                            const url = URL.createObjectURL(blob);
                            const link = document.createElement('a');
                            link.href = url;
                            link.download = `decoded-image.${format}`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(url);
                            
                            this.showNotification(`Downloaded as ${format.toUpperCase()}`);
                        }, `image/${format}`, format === 'jpg' ? 0.9 : undefined);
                    };
                    
                    img.src = currentImageData;
                },

                copyDataUri() {
                    if (!currentImageData) {
                        this.showNotification('Please decode an image first', 'error');
                        return;
                    }
                    
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(currentImageData).then(() => {
                            this.showNotification('Data URI copied to clipboard!');
                        }).catch(() => {
                            this.fallbackCopy(currentImageData);
                        });
                    } else {
                        this.fallbackCopy(currentImageData);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification('Data URI copied to clipboard!');
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                        this.showNotification('Could not copy to clipboard', 'error');
                    }
                    document.body.removeChild(textArea);
                },

                clear() {
                    elements.base64Input().value = '';
                    elements.validation().classList.remove('valid', 'invalid');
                    elements.preview().classList.remove('show');
                    elements.downloadOptions().classList.remove('show');
                    elements.inputArea().classList.remove('has-content');
                    elements.decodeBtn().disabled = true;
                    elements.validateBtn().disabled = true;
                    currentImageData = null;
                    currentImageType = null;
                },

                showValidation(isValid, message) {
                    const validation = elements.validation();
                    validation.textContent = message;
                    validation.classList.remove('valid', 'invalid');
                    validation.classList.add(isValid ? 'valid' : 'invalid');
                },

                showNotification(message, type = 'success') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.remove('show', 'error');
                    if (type === 'error') notification.classList.add('error');
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 3000);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const base64Input = elements.base64Input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Input validation on type
                base64Input.addEventListener('input', function() {
                    const hasContent = this.value.trim().length > 0;
                    elements.inputArea().classList.toggle('has-content', hasContent);
                    elements.decodeBtn().disabled = !hasContent;
                    elements.validateBtn().disabled = !hasContent;
                    
                    if (!hasContent) {
                        elements.validation().classList.remove('valid', 'invalid');
                    }
                });

                // Auto-resize textarea
                base64Input.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 300) + 'px';
                });
            });
        })();
    </script>
</body>
</html>