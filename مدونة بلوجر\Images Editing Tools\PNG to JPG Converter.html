<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PNG to JPG Converter - Free Online Image Format Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free PNG to JPG Converter - Convert Images Online",
        "description": "Convert PNG images to JPG format instantly. Free online tool with quality control, file size optimization, and batch conversion capabilities.",
        "url": "https://www.webtoolskit.org/p/png-to-jpg.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "PNG to JPG Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert PNG to JPG" },
            { "@type": "DownloadAction", "name": "Download Converted JPG" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert PNG to JPG locally?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use our free online converter that works locally in your browser. Upload your PNG file, adjust quality settings if needed, and download the converted JPG. No files are uploaded to servers - everything processes locally for complete privacy."
          }
        },
        {
          "@type": "Question",
          "name": "Do you lose quality converting PNG to JPG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, converting PNG to JPG involves some quality loss due to JPG's lossy compression. However, you can control the quality level to balance file size and image quality. For photographs, the difference is often minimal at high quality settings."
          }
        },
        {
          "@type": "Question",
          "name": "Should I convert PNG to JPG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Convert PNG to JPG when you need smaller file sizes for web use, email sharing, or storage optimization. JPG is ideal for photographs and complex images where transparency isn't needed. Keep PNG for graphics with transparency or sharp edges."
          }
        },
        {
          "@type": "Question",
          "name": "Why convert PNG to JPG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Convert PNG to JPG to reduce file size significantly, improve web loading speeds, save storage space, and ensure compatibility with older systems. JPG files are typically 50-80% smaller than PNG for photographic content."
          }
        },
        {
          "@type": "Question",
          "name": "Can you convert PNG to JPEG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, PNG to JPEG conversion is the same as PNG to JPG - JPEG and JPG are identical formats with different file extensions. Our converter outputs JPG files that work everywhere JPEG files are accepted."
          }
        }
      ]
    }
    </script>

    <style>
        /* PNG to JPG Widget - Simplified & Template Compatible */
        .png-jpg-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .png-jpg-widget-container * { box-sizing: border-box; }

        .png-jpg-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .png-jpg-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .png-jpg-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            margin-bottom: var(--spacing-xl);
            cursor: pointer;
        }

        .png-jpg-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .png-jpg-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .png-jpg-upload-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .png-jpg-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .png-jpg-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .png-jpg-file-input {
            display: none;
        }

        .png-jpg-quality-control {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            border: 1px solid var(--border-color);
        }

        .png-jpg-quality-title {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }

        .png-jpg-quality-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: var(--border-color);
            outline: none;
            margin-bottom: var(--spacing-sm);
        }

        .png-jpg-quality-value {
            color: var(--text-color-light);
            font-size: 0.875rem;
            text-align: center;
        }

        .png-jpg-preview {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            border: 1px solid var(--border-color);
        }

        .png-jpg-preview-title {
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            text-align: center;
        }

        .png-jpg-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .png-jpg-preview-item {
            text-align: center;
            flex: 1;
            min-width: 200px;
        }

        .png-jpg-preview-label {
            color: var(--text-color-light);
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .png-jpg-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .png-jpg-file-info {
            margin-top: var(--spacing-sm);
            font-size: 0.75rem;
            color: var(--text-color-light);
        }

        .png-jpg-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .png-jpg-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .png-jpg-btn:hover { transform: translateY(-2px); }

        .png-jpg-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .png-jpg-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .png-jpg-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .png-jpg-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .png-jpg-btn-success {
            background-color: #10b981;
            color: white;
        }

        .png-jpg-btn-success:hover {
            background-color: #059669;
        }

        .png-jpg-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .png-jpg-btn:disabled:hover {
            transform: none;
        }

        .png-jpg-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .png-jpg-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        .png-jpg-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .png-jpg-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .png-jpg-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .png-jpg-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .png-jpg-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .png-jpg-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="jpg-to-png"] .png-jpg-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .png-jpg-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="image-resizer"] .png-jpg-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .png-jpg-related-tool-item:hover .png-jpg-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="jpg-to-png"]:hover .png-jpg-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .png-jpg-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="image-resizer"]:hover .png-jpg-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .png-jpg-related-tool-item { box-shadow: none; border: none; }
        .png-jpg-related-tool-item:hover { box-shadow: none; border: none; }
        .png-jpg-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .png-jpg-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .png-jpg-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .png-jpg-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .png-jpg-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .png-jpg-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .png-jpg-related-tool-item:hover .png-jpg-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .png-jpg-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .png-jpg-widget-title { font-size: 1.875rem; }
            .png-jpg-buttons { flex-direction: column; }
            .png-jpg-btn { flex: none; }
            .png-jpg-preview-content { flex-direction: column; }
            .png-jpg-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .png-jpg-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .png-jpg-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .png-jpg-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .png-jpg-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .png-jpg-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .png-jpg-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .png-jpg-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .png-jpg-upload-area:hover { background-color: var(--card-bg); }
        .png-jpg-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .png-jpg-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .png-jpg-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="png-jpg-widget-container">
        <h1 class="png-jpg-widget-title">PNG to JPG Converter</h1>
        <p class="png-jpg-widget-description">
            Convert PNG images to JPG format instantly with quality control and file size optimization. Fast, free, and secure online conversion.
        </p>
        
        <div class="png-jpg-upload-area" id="uploadArea">
            <div class="png-jpg-upload-icon">📁</div>
            <div class="png-jpg-upload-text">Click to upload or drag & drop your PNG file</div>
            <div class="png-jpg-upload-subtext">Supports PNG files up to 10MB</div>
            <input type="file" id="fileInput" class="png-jpg-file-input" accept=".png" />
        </div>

        <div class="png-jpg-quality-control" id="qualityControl">
            <div class="png-jpg-quality-title">Quality Settings</div>
            <input type="range" id="qualitySlider" class="png-jpg-quality-slider" min="10" max="100" value="85" />
            <div class="png-jpg-quality-value">Quality: <span id="qualityValue">85</span>%</div>
        </div>

        <div class="png-jpg-preview" id="previewSection">
            <h3 class="png-jpg-preview-title">Image Preview</h3>
            <div class="png-jpg-preview-content">
                <div class="png-jpg-preview-item">
                    <div class="png-jpg-preview-label">Original (PNG)</div>
                    <img id="originalImage" class="png-jpg-preview-image" alt="Original PNG" />
                    <div class="png-jpg-file-info" id="originalInfo"></div>
                </div>
                <div class="png-jpg-preview-item">
                    <div class="png-jpg-preview-label">Converted (JPG)</div>
                    <img id="convertedImage" class="png-jpg-preview-image" alt="Converted JPG" />
                    <div class="png-jpg-file-info" id="convertedInfo"></div>
                </div>
            </div>
        </div>

        <div class="png-jpg-buttons">
            <button id="convertBtn" class="png-jpg-btn png-jpg-btn-primary" disabled>
                Convert to JPG
            </button>
            <button id="downloadBtn" class="png-jpg-btn png-jpg-btn-success" disabled>
                Download JPG
            </button>
            <button id="resetBtn" class="png-jpg-btn png-jpg-btn-secondary">
                Reset
            </button>
        </div>

        <div class="png-jpg-related-tools">
            <h3 class="png-jpg-related-tools-title">Related Tools</h3>
            <div class="png-jpg-related-tools-grid">
                <a href="/p/jpg-to-png-converter.html" class="png-jpg-related-tool-item" rel="noopener">
                    <div class="png-jpg-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="png-jpg-related-tool-name">JPG to PNG</div>
                </a>

                <a href="/p/image-converter.html" class="png-jpg-related-tool-item" rel="noopener">
                    <div class="png-jpg-related-tool-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="png-jpg-related-tool-name">Image Converter</div>
                </a>

                <a href="https://www.webtoolskit.org/p/image-resizer.html" class="png-jpg-related-tool-item" rel="noopener">
                    <div class="png-jpg-related-tool-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div class="png-jpg-related-tool-name">Image Resizer</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Free PNG to JPG Converter Online</h2>
            <p>Transform your PNG images to JPG format effortlessly with our free online converter. JPG format offers excellent compression for photographs and complex images, significantly reducing file sizes while maintaining good visual quality.</p>
            
            <p>Converting PNG to JPG is ideal when you need smaller file sizes for web use, email sharing, or storage optimization. Our tool processes conversions locally in your browser with adjustable quality settings, ensuring complete privacy and control over your images.</p>

            <h3>Why Convert PNG to JPG?</h3>
            <p>JPG format excels at compressing photographic content with minimal visible quality loss. Unlike PNG's lossless compression, JPG uses lossy compression that can reduce file sizes by 50-80% compared to PNG, making it perfect for web optimization and storage efficiency.</p>

            <p>Choose JPG conversion when file size matters more than transparency support. JPG is universally supported and ideal for photographs, complex images, and situations where you need to balance quality with file size constraints.</p>

            <h3>Frequently Asked Questions About PNG to JPG Conversion</h3>
            
            <h4>How to convert PNG to JPG locally?</h4>
            <p>Use our free online converter that works locally in your browser. Upload your PNG file, adjust quality settings if needed, and download the converted JPG. No files are uploaded to servers - everything processes locally for complete privacy and security.</p>
            
            <h4>Do you lose quality converting PNG to JPG?</h4>
            <p>Yes, converting PNG to JPG involves some quality loss due to JPG's lossy compression. However, you can control the quality level to balance file size and image quality. For photographs, the difference is often minimal at high quality settings (80-90%).</p>
            
            <h4>Should I convert PNG to JPG?</h4>
            <p>Convert PNG to JPG when you need smaller file sizes for web use, email sharing, or storage optimization. JPG is ideal for photographs and complex images where transparency isn't needed. Keep PNG for graphics with transparency or sharp edges.</p>
            
            <h4>Why convert PNG to JPG?</h4>
            <p>Convert PNG to JPG to reduce file size significantly, improve web loading speeds, save storage space, and ensure compatibility with older systems. JPG files are typically 50-80% smaller than PNG for photographic content, making them perfect for web optimization.</p>
            
            <h4>Can you convert PNG to JPEG?</h4>
            <p>Yes, PNG to JPEG conversion is the same as PNG to JPG - JPEG and JPG are identical formats with different file extensions. Our converter outputs JPG files that work everywhere JPEG files are accepted, providing universal compatibility.</p>
        </div>

        <div class="png-jpg-features">
            <h3 class="png-jpg-features-title">Key Features</h3>
            <ul class="png-jpg-features-list">
                <li class="png-jpg-features-item">Instant PNG to JPG conversion</li>
                <li class="png-jpg-features-item">Adjustable quality control</li>
                <li class="png-jpg-features-item">File size optimization</li>
                <li class="png-jpg-features-item">Local browser processing</li>
                <li class="png-jpg-features-item">No file upload required</li>
                <li class="png-jpg-features-item">Drag and drop interface</li>
                <li class="png-jpg-features-item">Real-time preview</li>
                <li class="png-jpg-features-item">Mobile-friendly design</li>
            </ul>
        </div>
    </div>

    <div id="notification" class="png-jpg-notification"></div>

    <script>
        (function() {
            'use strict';
            
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const qualityControl = document.getElementById('qualityControl');
            const qualitySlider = document.getElementById('qualitySlider');
            const qualityValue = document.getElementById('qualityValue');
            const previewSection = document.getElementById('previewSection');
            const originalImage = document.getElementById('originalImage');
            const convertedImage = document.getElementById('convertedImage');
            const originalInfo = document.getElementById('originalInfo');
            const convertedInfo = document.getElementById('convertedInfo');
            const convertBtn = document.getElementById('convertBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            const resetBtn = document.getElementById('resetBtn');
            const notification = document.getElementById('notification');
            
            let currentFile = null;
            let convertedBlob = null;
            
            // Upload area events
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            
            // File input change
            fileInput.addEventListener('change', handleFileSelect);
            
            // Quality slider
            qualitySlider.addEventListener('input', updateQualityValue);
            qualitySlider.addEventListener('change', reconvertIfNeeded);
            
            // Button events
            convertBtn.addEventListener('click', convertImage);
            downloadBtn.addEventListener('click', downloadImage);
            resetBtn.addEventListener('click', resetTool);
            
            function handleDragOver(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            }
            
            function handleDragLeave(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            }
            
            function handleDrop(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    processFile(files[0]);
                }
            }
            
            function handleFileSelect(e) {
                const file = e.target.files[0];
                if (file) {
                    processFile(file);
                }
            }
            
            function updateQualityValue() {
                qualityValue.textContent = qualitySlider.value;
            }
            
            function reconvertIfNeeded() {
                if (currentFile && convertedBlob) {
                    convertImage();
                }
            }
            
            function processFile(file) {
                if (!file.type.match(/^image\/png$/i)) {
                    showNotification('Please select a valid PNG file.', 'error');
                    return;
                }
                
                if (file.size > 10 * 1024 * 1024) {
                    showNotification('File size must be less than 10MB.', 'error');
                    return;
                }
                
                currentFile = file;
                displayPreview(file);
                qualityControl.style.display = 'block';
                convertBtn.disabled = false;
            }
            
            function displayPreview(file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    originalImage.src = e.target.result;
                    originalInfo.textContent = `Size: ${formatFileSize(file.size)}`;
                    previewSection.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
            
            function convertImage() {
                if (!currentFile) return;
                
                convertBtn.textContent = 'Converting...';
                convertBtn.disabled = true;
                
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    // Fill with white background (JPG doesn't support transparency)
                    ctx.fillStyle = '#FFFFFF';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // Draw image on canvas
                    ctx.drawImage(img, 0, 0);
                    
                    // Convert to JPG blob with quality setting
                    const quality = qualitySlider.value / 100;
                    canvas.toBlob(function(blob) {
                        convertedBlob = blob;
                        
                        // Display converted image
                        const url = URL.createObjectURL(blob);
                        convertedImage.src = url;
                        convertedInfo.textContent = `Size: ${formatFileSize(blob.size)} (${Math.round((1 - blob.size / currentFile.size) * 100)}% smaller)`;
                        
                        // Enable download button
                        downloadBtn.disabled = false;
                        convertBtn.textContent = 'Convert to JPG';
                        convertBtn.disabled = false;
                        
                        showNotification('Image converted successfully!', 'success');
                    }, 'image/jpeg', quality);
                };
                
                img.src = originalImage.src;
            }
            
            function downloadImage() {
                if (!convertedBlob) return;
                
                const url = URL.createObjectURL(convertedBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = getFileName(currentFile.name) + '.jpg';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                showNotification('JPG file downloaded!', 'success');
            }
            
            function resetTool() {
                currentFile = null;
                convertedBlob = null;
                fileInput.value = '';
                qualityControl.style.display = 'none';
                previewSection.style.display = 'none';
                convertBtn.disabled = true;
                downloadBtn.disabled = true;
                convertBtn.textContent = 'Convert to JPG';
                qualitySlider.value = 85;
                qualityValue.textContent = '85';
                uploadArea.classList.remove('dragover');
            }
            
            function getFileName(fullName) {
                return fullName.substring(0, fullName.lastIndexOf('.')) || fullName;
            }
            
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            function showNotification(message, type = 'success') {
                notification.textContent = message;
                notification.className = 'png-jpg-notification show';
                
                if (type === 'error') {
                    notification.style.backgroundColor = '#dc3545';
                } else {
                    notification.style.backgroundColor = '#10b981';
                }
                
                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
        })();
    </script>
</body>
</html>