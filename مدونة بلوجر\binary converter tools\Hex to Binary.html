<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Hex to Binary Converter - Convert Hexadecimal to Binary</title>
    <meta name="description" content="Instantly convert hexadecimal values to binary code with our free online tool. Supports formatting options like spacing and prefixes, perfect for developers and students.">
    <meta name="keywords" content="hex to binary, hex to binary converter, hexadecimal to binary, convert hex to binary, hex converter, online tool, free tool">
    <link rel="canonical" href="https://www.webtoolskit.org/p/hex-to-binary.html" />
    
    <!-- Page-specific Open Graph Meta Tags -->
    <meta property="og:url" content="https://www.webtoolskit.org/p/hex-to-binary.html" />
    <meta property="og:title" content="Free Hex to Binary Converter - Convert Hexadecimal Online" />
    <meta property="og:description" content="A simple and powerful tool to convert hexadecimal numbers to binary code in real-time. Includes formatting options and one-click copy." />
    <meta property="og:image" content="https://www.webtoolskit.org/images/binary-og.jpg" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Hex to Binary Converter - Convert Hexadecimal to Binary Code",
        "description": "Instantly convert hexadecimal values to binary code with our free online tool. Supports formatting options like spacing and prefixes, perfect for developers and students.",
        "url": "https://www.webtoolskit.org/p/hex-to-binary.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-19",
        "dateModified": "2025-06-19",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Hex to Binary Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Hex to Binary" },
            { "@type": "CopyAction", "name": "Copy Binary Code" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I convert hex to binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert hex to binary, you translate each hexadecimal digit into its 4-bit binary equivalent (nibble). For example, the hex digit 'A' is '1010' in binary, and '7' is '0111'. You then combine these binary groups in the same order. Our tool automates this process for instant, error-free conversions."
          }
        },
        {
          "@type": "Question",
          "name": "What is FF in binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The hexadecimal value 'FF' is '11111111' in binary. This is because each 'F' digit converts to '1111'. Combined, they form an 8-bit number, which is the highest possible value for a single byte (255 in decimal)."
          }
        },
        {
          "@type": "Question",
          "name": "What is 0x1 in binary code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The '0x' prefix simply indicates that the following number is in hexadecimal format. So, '0x1' is the hex value '1'. Its 4-bit binary equivalent is '0001'. In the context of a full byte, it would be represented as '00000001'."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert hex FF216 to binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "While 'FF216' is not a standard hexadecimal number because its digits must be between 0-9 and A-F, we can demonstrate the process with a valid hex number like 'FA2'. To convert it, you handle each digit separately: F = 1111, A = 1010, and 2 = 0010. Combining these gives you '111110100010' in binary."
          }
        },
        {
          "@type": "Question",
          "name": "What is the binary conversion of 11111111?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The value '11111111' is already a binary number. If you are looking to convert it *to* hexadecimal, you group the bits into sets of four from right to left: '1111' and '1111'. Each '1111' group is equivalent to the hex digit 'F'. Therefore, the binary number '11111111' is 'FF' in hexadecimal."
          }
        }
      ]
    }
    </script>


    <style>
        /* Hex to Binary Widget - Simplified & Template Compatible */
        .hex-to-binary-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .hex-to-binary-widget-container * { box-sizing: border-box; }

        .hex-to-binary-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hex-to-binary-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .hex-to-binary-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .hex-to-binary-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .hex-to-binary-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .hex-to-binary-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .hex-to-binary-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .hex-to-binary-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .hex-to-binary-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .hex-to-binary-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .hex-to-binary-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .hex-to-binary-btn:hover { transform: translateY(-2px); }

        .hex-to-binary-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .hex-to-binary-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .hex-to-binary-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .hex-to-binary-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .hex-to-binary-btn-success {
            background-color: #10b981;
            color: white;
        }

        .hex-to-binary-btn-success:hover {
            background-color: #059669;
        }

        .hex-to-binary-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .hex-to-binary-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .hex-to-binary-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .hex-to-binary-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .hex-to-binary-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .hex-to-binary-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .hex-to-binary-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .hex-to-binary-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .hex-to-binary-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .hex-to-binary-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .hex-to-binary-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="binary-to-hex"] .hex-to-binary-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="hex-to-decimal"] .hex-to-binary-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="text-to-hex"] .hex-to-binary-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .hex-to-binary-related-tool-item:hover .hex-to-binary-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="binary-to-hex"]:hover .hex-to-binary-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="hex-to-decimal"]:hover .hex-to-binary-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="text-to-hex"]:hover .hex-to-binary-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .hex-to-binary-related-tool-item { box-shadow: none; border: none; }
        .hex-to-binary-related-tool-item:hover { box-shadow: none; border: none; }
        .hex-to-binary-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .hex-to-binary-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .hex-to-binary-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .hex-to-binary-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .hex-to-binary-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .hex-to-binary-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .hex-to-binary-related-tool-item:hover .hex-to-binary-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .hex-to-binary-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .hex-to-binary-widget-title { font-size: 1.875rem; }
            .hex-to-binary-buttons { flex-direction: column; }
            .hex-to-binary-btn { flex: none; }
            .hex-to-binary-options { grid-template-columns: 1fr; }
            .hex-to-binary-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .hex-to-binary-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .hex-to-binary-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .hex-to-binary-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .hex-to-binary-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .hex-to-binary-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .hex-to-binary-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .hex-to-binary-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .hex-to-binary-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .hex-to-binary-checkbox:focus, .hex-to-binary-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .hex-to-binary-output::selection { background-color: var(--primary-color); color: white; }
        .hex-to-binary-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .hex-to-binary-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="hex-to-binary-widget-container">
        <h1 class="hex-to-binary-widget-title">Hex to Binary Converter</h1>
        <p class="hex-to-binary-widget-description">
            Effortlessly convert hexadecimal numbers to their binary equivalents. Our tool provides a clean, fast, and accurate way to translate hex data into binary format.
        </p>
        
        <div class="hex-to-binary-input-group">
            <label for="hexToBinaryInput" class="hex-to-binary-label">Enter Hexadecimal Value:</label>
            <textarea 
                id="hexToBinaryInput" 
                class="hex-to-binary-textarea"
                placeholder="Type your hex code here (e.g., 1A3F)..."
                rows="4"
            ></textarea>
        </div>

        <div class="hex-to-binary-options">
            <div class="hex-to-binary-option">
                <input type="checkbox" id="hexAddSpaces" class="hex-to-binary-checkbox" checked>
                <label for="hexAddSpaces" class="hex-to-binary-option-label">Add spaces between bytes</label>
            </div>
            <div class="hex-to-binary-option">
                <input type="checkbox" id="hexAddPrefix" class="hex-to-binary-checkbox">
                <label for="hexAddPrefix" class="hex-to-binary-option-label">Add "0b" prefix</label>
            </div>
        </div>

        <div class="hex-to-binary-buttons">
            <button class="hex-to-binary-btn hex-to-binary-btn-primary" onclick="HexToBinaryConverter.convert()">
                Convert to Binary
            </button>
            <button class="hex-to-binary-btn hex-to-binary-btn-secondary" onclick="HexToBinaryConverter.clear()">
                Clear All
            </button>
            <button class="hex-to-binary-btn hex-to-binary-btn-success" onclick="HexToBinaryConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="hex-to-binary-result">
            <h3 class="hex-to-binary-result-title">Binary Code:</h3>
            <div class="hex-to-binary-output" id="hexToBinaryOutput">
                Your binary code will appear here...
            </div>
        </div>

        <div class="hex-to-binary-related-tools">
            <h3 class="hex-to-binary-related-tools-title">Related Tools</h3>
            <div class="hex-to-binary-related-tools-grid">
                <a href="/p/binary-to-hex.html" class="hex-to-binary-related-tool-item" rel="noopener">
                    <div class="hex-to-binary-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="hex-to-binary-related-tool-name">Binary to Hex</div>
                </a>

                <a href="/p/hex-to-decimal.html" class="hex-to-binary-related-tool-item" rel="noopener">
                    <div class="hex-to-binary-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="hex-to-binary-related-tool-name">Hex to Decimal</div>
                </a>

                <a href="/p/text-to-hex.html" class="hex-to-binary-related-tool-item" rel="noopener">
                    <div class="hex-to-binary-related-tool-icon">
                        <i class="fas fa-font"></i>
                    </div>
                    <div class="hex-to-binary-related-tool-name">Text to Hex</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Fast and Accurate Hex to Binary Conversion</h2>
            <p>Our <strong>Hex to Binary Converter</strong> is an essential utility for anyone working with low-level data. Hexadecimal (base-16) is a compact, human-friendly way to represent binary (base-2) values. This tool seamlessly bridges the gap between the two, providing instant and accurate conversions for developers, computer science students, and hardware engineers.</p>
            <p>Whether you're debugging memory dumps, analyzing network packets, or programming microcontrollers, converting hex to binary is a frequent necessity. Our converter simplifies this task by automating the process, handling all valid hex characters (0-9 and A-F) and offering formatting options to improve readability.</p>
            
            <h3>How to Use the Hex to Binary Converter</h3>
            <ol>
                <li><strong>Enter Hex Value:</strong> Type or paste your hexadecimal string (e.g., <code>1A3F</code> or <code>ff 0c 88</code>) into the input field. The tool automatically ignores spaces and is case-insensitive.</li>
                <li><strong>Choose Options:</strong> Select your preferred formatting. You can add spaces between each byte (8 bits) or include a '0b' prefix to clearly identify the output as a binary number.</li>
                <li><strong>Convert and Copy:</strong> Click the "Convert to Binary" button to generate the binary code instantly. Use the "Copy Result" button for easy pasting into your work.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Hex to Binary</h3>
            
            <h4>How do I convert hex to binary?</h4>
            <p>To convert hex to binary, you translate each hexadecimal digit into its 4-bit binary equivalent (nibble). For example, the hex digit 'A' is '1010' in binary, and '7' is '0111'. You then combine these binary groups in the same order. Our tool automates this process for instant, error-free conversions.</p>
            
            <h4>What is FF in binary?</h4>
            <p>The hexadecimal value 'FF' is '11111111' in binary. This is because each 'F' digit converts to '1111'. Combined, they form an 8-bit number, which is the highest possible value for a single byte (255 in decimal).</p>
            
            <h4>What is 0x1 in binary code?</h4>
            <p>The '0x' prefix simply indicates that the following number is in hexadecimal format. So, '0x1' is the hex value '1'. Its 4-bit binary equivalent is '0001'. In the context of a full byte, it would be represented as '00000001'.</p>

            <h4>How do you convert hex FF216 to binary?</h4>
            <p>While 'FF216' is not a standard hexadecimal number because its digits must be between 0-9 and A-F, we can demonstrate the process with a valid hex number like 'FA2'. To convert it, you handle each digit separately: F = 1111, A = 1010, and 2 = 0010. Combining these gives you '111110100010' in binary.</p>

            <h4>What is the binary conversion of 11111111?</h4>
            <p>The value '11111111' is already a binary number. If you are looking to convert it <em>to</em> hexadecimal, you group the bits into sets of four from right to left: '1111' and '1111'. Each '1111' group is equivalent to the hex digit 'F'. Therefore, the binary number '11111111' is 'FF' in hexadecimal.</p>
        </div>


        <div class="hex-to-binary-features">
            <h3 class="hex-to-binary-features-title">Key Features:</h3>
            <ul class="hex-to-binary-features-list">
                <li class="hex-to-binary-features-item" style="margin-bottom: 0.3em;">Instant and accurate conversion</li>
                <li class="hex-to-binary-features-item" style="margin-bottom: 0.3em;">Supports uppercase and lowercase</li>
                <li class="hex-to-binary-features-item" style="margin-bottom: 0.3em;">Customizable output formatting</li>
                <li class="hex-to-binary-features-item" style="margin-bottom: 0.3em;">Handles spaces and line breaks</li>
                <li class="hex-to-binary-features-item" style="margin-bottom: 0.3em;">User-friendly and responsive design</li>
                <li class="hex-to-binary-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="hex-to-binary-features-item">Completely free to use</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="hex-to-binary-notification" id="hexToBinaryNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Hex to Binary Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('hexToBinaryInput'),
                output: () => document.getElementById('hexToBinaryOutput'),
                notification: () => document.getElementById('hexToBinaryNotification')
            };

            window.HexToBinaryConverter = {
                convert() {
                    const input = elements.input();
                    const output = elements.output();
                    const hex = input.value;

                    if (!hex.trim()) {
                        output.textContent = 'Please enter a hex value to convert.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        addSpaces: document.getElementById('hexAddSpaces').checked,
                        addPrefix: document.getElementById('hexAddPrefix').checked,
                    };

                    const result = this.processHex(hex, options);
                    
                    if (result.startsWith('Invalid')) {
                        output.style.color = '#dc2626';
                    }
                    
                    output.textContent = result;
                },

                processHex(hex, options) {
                    const cleanedHex = hex.replace(/\s/g, '').toUpperCase();
                    if (!/^[0-9A-F]+$/.test(cleanedHex)) {
                        return 'Invalid hexadecimal input. Please use characters 0-9 and A-F.';
                    }

                    const hexMap = {
                        '0': '0000', '1': '0001', '2': '0010', '3': '0011',
                        '4': '0100', '5': '0101', '6': '0110', '7': '0111',
                        '8': '1000', '9': '1001', 'A': '1010', 'B': '1011',
                        'C': '1100', 'D': '1101', 'E': '1110', 'F': '1111'
                    };

                    let binaryString = '';
                    for (const char of cleanedHex) {
                        binaryString += hexMap[char];
                    }
                    
                    if (options.addSpaces && binaryString.length > 0) {
                        const chunks = binaryString.match(/.{1,8}/g);
                        if(chunks) binaryString = chunks.join(' ');
                    }
                    
                    if (options.addPrefix) {
                        binaryString = '0b' + (options.addSpaces ? ' ' : '') + binaryString;
                    }

                    return binaryString;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your binary code will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text.includes('will appear here') || text.includes('Please enter') || text.includes('Invalid')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        HexToBinaryConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>