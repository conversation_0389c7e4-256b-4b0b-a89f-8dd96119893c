<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text to HEX Converter - Free Online Tool</title>
    <meta name="description" content="Convert any text or string into hexadecimal (HEX) code instantly. Our free online Text to HEX converter is perfect for developers, web designers, and data encoding tasks.">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Text to HEX Converter - Encode Text to Hexadecimal Online",
        "description": "Convert any text or string into hexadecimal (HEX) code instantly. Our free online Text to HEX converter is perfect for developers, web designers, and data encoding tasks.",
        "url": "https://www.webtoolskit.org/p/text-to-hex.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-19",
        "dateModified": "2025-06-19",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Text to HEX Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Text to HEX" },
            { "@type": "CopyAction", "name": "Copy HEX Code" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert text to hex?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert text to hex, each character in the text is replaced by its two-digit hexadecimal representation. This is done by first finding the character's decimal value in a standard character set like ASCII or Unicode, and then converting that decimal number to its base-16 (hexadecimal) equivalent."
          }
        },
        {
          "@type": "Question",
          "name": "What is 'hello' in hex?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The text 'hello' in hexadecimal is '68 65 6c 6c 6f'. Each pair of hex digits corresponds to one letter: 'h' is 68, 'e' is 65, 'l' is 6c, and 'o' is 6f."
          }
        },
        {
          "@type": "Question",
          "name": "Why is text converted to hex?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Text is converted to hex for several reasons in computing. It's used for representing data in a compact, human-readable format for debugging, in URL encoding to safely transmit data, in HTML for color codes (e.g., #FFFFFF), and for data obfuscation. Because one byte (8 bits) can be perfectly represented by two hex digits, it's a very efficient way to display binary data."
          }
        },
        {
          "@type": "Question",
          "name": "Is hex the same as ASCII?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No. ASCII is a character encoding standard that assigns a unique number to each character (e.g., 'A' is 65). Hexadecimal (hex) is a base-16 number system. When converting text to hex, you are representing the character's ASCII number in the hexadecimal system. So, the ASCII value 65 becomes 41 in hex."
          }
        },
        {
          "@type": "Question",
          "name": "What is the hex code for the letter A?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The hex code for the uppercase letter 'A' is 41. This is because its decimal value in the ASCII standard is 65, and 65 in decimal is equal to 41 in hexadecimal."
          }
        }
      ]
    }
    </script>


    <style>
        /* Text to HEX Widget - Simplified & Template Compatible */
        .text-to-hex-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .text-to-hex-widget-container * { box-sizing: border-box; }

        .text-to-hex-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-to-hex-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .text-to-hex-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .text-to-hex-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .text-to-hex-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }
        
        .text-to-hex-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }
        
        .text-to-hex-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .text-to-hex-separator-input {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            font-size: var(--font-size-base);
        }
        
        .text-to-hex-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .text-to-hex-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .text-to-hex-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .text-to-hex-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .text-to-hex-btn:hover { transform: translateY(-2px); }

        .text-to-hex-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .text-to-hex-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .text-to-hex-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .text-to-hex-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .text-to-hex-btn-success {
            background-color: #10b981;
            color: white;
        }

        .text-to-hex-btn-success:hover {
            background-color: #059669;
        }

        .text-to-hex-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .text-to-hex-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-to-hex-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .text-to-hex-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .text-to-hex-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .text-to-hex-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .text-to-hex-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-to-hex-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .text-to-hex-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .text-to-hex-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .text-to-hex-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="hex-to-text"] .text-to-hex-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="text-to-binary"] .text-to-hex-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="text-to-ascii"] .text-to-hex-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .text-to-hex-related-tool-item:hover .text-to-hex-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="hex-to-text"]:hover .text-to-hex-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="text-to-binary"]:hover .text-to-hex-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="text-to-ascii"]:hover .text-to-hex-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .text-to-hex-related-tool-item { box-shadow: none; border: none; }
        .text-to-hex-related-tool-item:hover { box-shadow: none; border: none; }
        .text-to-hex-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .text-to-hex-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .text-to-hex-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .text-to-hex-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .text-to-hex-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .text-to-hex-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .text-to-hex-related-tool-item:hover .text-to-hex-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .text-to-hex-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .text-to-hex-widget-title { font-size: 1.875rem; }
            .text-to-hex-buttons { flex-direction: column; }
            .text-to-hex-btn { flex: none; }
            .text-to-hex-options { grid-template-columns: 1fr; }
            .text-to-hex-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .text-to-hex-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .text-to-hex-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .text-to-hex-related-tool-name { font-size: 0.875rem; }
            .text-to-hex-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .text-to-hex-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .text-to-hex-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .text-to-hex-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .text-to-hex-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .text-to-hex-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .text-to-hex-btn:focus, .text-to-hex-separator-input:focus, .text-to-hex-checkbox:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .text-to-hex-output::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="text-to-hex-widget-container">
        <h1 class="text-to-hex-widget-title">Text to HEX Converter</h1>
        <p class="text-to-hex-widget-description">
            Encode any text, string, or characters into a hexadecimal (HEX) format instantly. A perfect tool for developers and data analysts.
        </p>
        
        <div class="text-to-hex-input-group">
            <label for="textToHexInput" class="text-to-hex-label">Enter your text:</label>
            <textarea 
                id="textToHexInput" 
                class="text-to-hex-textarea"
                placeholder="Type or paste your text here..."
                rows="4"
            ></textarea>
        </div>

        <div class="text-to-hex-options">
            <div class="text-to-hex-option">
                <label for="textToHexSeparator" class="text-to-hex-option-label">Separator:</label>
                <input 
                    type="text" 
                    id="textToHexSeparator" 
                    class="text-to-hex-separator-input" 
                    value=" "
                    placeholder="e.g., space, comma..."
                >
            </div>
            <div class="text-to-hex-option">
                <input type="checkbox" id="textToHexUppercase" class="text-to-hex-checkbox" checked>
                <label for="textToHexUppercase" class="text-to-hex-option-label">Uppercase HEX (A-F)</label>
            </div>
        </div>

        <div class="text-to-hex-buttons">
            <button class="text-to-hex-btn text-to-hex-btn-primary" onclick="TextToHexConverter.convert()">
                Convert to HEX
            </button>
            <button class="text-to-hex-btn text-to-hex-btn-secondary" onclick="TextToHexConverter.clear()">
                Clear All
            </button>
            <button class="text-to-hex-btn text-to-hex-btn-success" onclick="TextToHexConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="text-to-hex-result">
            <h3 class="text-to-hex-result-title">HEX Output:</h3>
            <div class="text-to-hex-output" id="textToHexOutput">
                Your HEX code will appear here...
            </div>
        </div>

        <div class="text-to-hex-related-tools">
            <h3 class="text-to-hex-related-tools-title">Related Tools</h3>
            <div class="text-to-hex-related-tools-grid">
                <a href="/p/hex-to-text.html" class="text-to-hex-related-tool-item" rel="noopener">
                    <div class="text-to-hex-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="text-to-hex-related-tool-name">HEX to Text</div>
                </a>

                <a href="/p/text-to-binary.html" class="text-to-hex-related-tool-item" rel="noopener">
                    <div class="text-to-hex-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="text-to-hex-related-tool-name">Text to Binary</div>
                </a>

                <a href="/p/text-to-ascii.html" class="text-to-hex-related-tool-item" rel="noopener">
                    <div class="text-to-hex-related-tool-icon">
                        <i class="fas fa-keyboard"></i>
                    </div>
                    <div class="text-to-hex-related-tool-name">Text to ASCII</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>From Plain Text to HEX Code in an Instant</h2>
            <p>Our <strong>Text to HEX Converter</strong> provides a quick and reliable way to encode standard text into its hexadecimal (base-16) equivalent. This process is essential in the world of computing and web development, where data must often be represented in a machine-friendly format. Hexadecimal is particularly useful because it can represent byte values (0-255) in a compact, two-digit format, making it more human-readable than binary.</p>
            <p>This tool is invaluable for programmers debugging data streams, web designers specifying colors, and anyone needing to encode text for safe transmission over the web. Simply enter your text, choose your formatting options, and get the corresponding hex code immediately.</p>
            
            <h3>How to Use the Text to HEX Converter</h3>
            <ol>
                <li><strong>Enter Text:</strong> Type or paste the text you wish to convert into the input area.</li>
                <li><strong>Set Options:</strong> You can specify a character to separate each hex value (a space is default) and choose whether the A-F digits should be uppercase or lowercase.</li>
                <li><strong>Convert:</strong> Click the "Convert to HEX" button.</li>
                <li><strong>Copy the Result:</strong> The converted HEX code will appear below, ready for you to copy with a single click.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Text to HEX Conversion</h3>
            
            <h4>How do you convert text to hex?</h4>
            <p>To convert text to hex, each character in the text is replaced by its two-digit hexadecimal representation. This is done by first finding the character's decimal value in a standard character set like ASCII or Unicode, and then converting that decimal number to its base-16 (hexadecimal) equivalent.</p>
            
            <h4>What is "hello" in hex?</h4>
            <p>The text 'hello' in hexadecimal is <code>68 65 6c 6c 6f</code>. Each pair of hex digits corresponds to one letter: 'h' is 68, 'e' is 65, 'l' is 6c, and 'o' is 6f.</p>
            
            <h4>Why is text converted to hex?</h4>
            <p>Text is converted to hex for several reasons in computing. It's used for representing data in a compact, human-readable format for debugging, in URL encoding to safely transmit data, in HTML for color codes (e.g., #FFFFFF), and for data obfuscation. Because one byte (8 bits) can be perfectly represented by two hex digits, it's a very efficient way to display binary data.</p>
            
            <h4>Is hex the same as ASCII?</h4>
            <p>No. ASCII is a character encoding standard that assigns a unique number to each character (e.g., 'A' is 65). Hexadecimal (hex) is a base-16 number system. When converting text to hex, you are representing the character's ASCII number in the hexadecimal system. So, the ASCII value 65 becomes <code>41</code> in hex.</p>
            
            <h4>What is the hex code for the letter A?</h4>
            <p>The hex code for the uppercase letter 'A' is <code>41</code>. This is because its decimal value in the ASCII standard is 65, and 65 in decimal is equal to 41 in hexadecimal.</p>
        </div>


        <div class="text-to-hex-features">
            <h3 class="text-to-hex-features-title">Key Features:</h3>
            <ul class="text-to-hex-features-list">
                <li class="text-to-hex-features-item">Instant text to HEX encoding</li>
                <li class="text-to-hex-features-item">Supports all Unicode characters</li>
                <li class="text-to-hex-features-item">Customizable separator option</li>
                <li class="text-to-hex-features-item">Option for uppercase/lowercase hex</li>
                <li class="text-to-hex-features-item">One-click copy to clipboard</li>
                <li class="text-to-hex-features-item">User-friendly and responsive design</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="text-to-hex-notification" id="textToHexNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Text to HEX Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('textToHexInput'),
                output: () => document.getElementById('textToHexOutput'),
                notification: () => document.getElementById('textToHexNotification'),
                separator: () => document.getElementById('textToHexSeparator'),
                uppercase: () => document.getElementById('textToHexUppercase')
            };

            window.TextToHexConverter = {
                convert() {
                    const inputEl = elements.input();
                    const outputEl = elements.output();
                    const separator = elements.separator().value;
                    const useUppercase = elements.uppercase().checked;
                    const text = inputEl.value;

                    if (!text.trim()) {
                        outputEl.textContent = 'Please enter text to convert.';
                        outputEl.style.color = '#dc2626';
                        return;
                    }

                    outputEl.style.color = '';
                    
                    try {
                        const hexCodes = [];
                        for (let i = 0; i < text.length; i++) {
                            const charCode = text.charCodeAt(i);
                            let hexCode = charCode.toString(16).padStart(2, '0');
                            if(useUppercase) {
                                hexCode = hexCode.toUpperCase();
                            }
                            hexCodes.push(hexCode);
                        }
                        outputEl.textContent = hexCodes.join(separator);
                    } catch (error) {
                        outputEl.textContent = `Error: An unexpected error occurred.`;
                        outputEl.style.color = '#dc2626';
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your HEX code will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your HEX code will appear here...', 'Please enter text to convert.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        TextToHexConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>