<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary to Hex Converter - Free Online Conversion Tool</title>
    <meta name="description" content="Convert binary code to hexadecimal format instantly with our free online tool. Handles padding, spacing, and offers formatting options like '0x' prefix and letter casing.">
    <meta name="keywords" content="binary to hex, binary to hex converter, convert binary to hex, binary to hexadecimal, bintohex, online tool, free tool">
    <link rel="canonical" href="https://www.webtoolskit.org/p/binary-to-hex.html" />
    
    <!-- Page-specific Open Graph Meta Tags -->
    <meta property="og:url" content="https://www.webtoolskit.org/p/binary-to-hex.html" />
    <meta property="og:title" content="Free Binary to Hex Converter - Convert Binary to Hexadecimal Online" />
    <meta property="og:description" content="A simple and powerful tool to convert binary numbers to hexadecimal format in real-time. Includes formatting options and one-click copy." />
    <meta property="og:image" content="https://www.webtoolskit.org/images/binary-og.jpg" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Binary to Hex Converter - Convert Binary Code to Hexadecimal",
        "description": "Convert binary code to hexadecimal format instantly with our free online tool. Handles padding, spacing, and offers formatting options like '0x' prefix and letter casing.",
        "url": "https://www.webtoolskit.org/p/binary-to-hex.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-19",
        "dateModified": "2025-06-19",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Binary to Hex Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Binary to Hex" },
            { "@type": "CopyAction", "name": "Copy Hex Code" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert binary to hex?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert binary to hex, you group the binary digits into sets of four (called nibbles), starting from the right. If the last group has fewer than four digits, you pad it with leading zeros. Then, you convert each 4-bit group into its single hexadecimal equivalent. For example, binary 1011 becomes B in hex."
          }
        },
        {
          "@type": "Question",
          "name": "What is binary 11111111 in hexadecimal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The binary number 11111111 is FF in hexadecimal. You group it into two 4-bit sets: 1111 and 1111. Each '1111' in binary is equivalent to 'F' in hexadecimal, so combined they become 'FF'."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert 1101010 binary to hexadecimal?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "First, pad the binary number 1101010 with a leading zero to make its length a multiple of four: 01101010. Then, group it into 4-bit sets: 0110 and 1010. Convert each group: 0110 is 6 in hex, and 1010 is A in hex. So, the result is 6A."
          }
        },
        {
          "@type": "Question",
          "name": "What is the hexadecimal equivalent of the binary number 11001001?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert 11001001, you split it into two 4-bit groups: 1100 and 1001. The binary group 1100 is C in hexadecimal, and 1001 is 9 in hexadecimal. Therefore, the hexadecimal equivalent is C9."
          }
        },
        {
          "@type": "Question",
          "name": "How many binary in a hex?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "One single hexadecimal digit represents exactly four binary digits (bits). This is why converting between the two systems is so straightforward and common in computer science."
          }
        }
      ]
    }
    </script>

    <style>
        /* Binary to Hex Widget - Simplified & Template Compatible */
        .binary-to-hex-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .binary-to-hex-widget-container * { box-sizing: border-box; }

        .binary-to-hex-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .binary-to-hex-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .binary-to-hex-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .binary-to-hex-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .binary-to-hex-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .binary-to-hex-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .binary-to-hex-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .binary-to-hex-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .binary-to-hex-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .binary-to-hex-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .binary-to-hex-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .binary-to-hex-btn:hover { transform: translateY(-2px); }

        .binary-to-hex-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .binary-to-hex-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .binary-to-hex-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .binary-to-hex-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .binary-to-hex-btn-success {
            background-color: #10b981;
            color: white;
        }

        .binary-to-hex-btn-success:hover {
            background-color: #059669;
        }

        .binary-to-hex-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .binary-to-hex-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .binary-to-hex-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .binary-to-hex-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .binary-to-hex-notification.show { transform: translateX(0); }
        
        .seo-content { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); color: var(--text-color-light); line-height: 1.7; }
        .seo-content h2, .seo-content h3, .seo-content h4 { color: var(--text-color); margin-bottom: var(--spacing-md); }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code { background-color: var(--background-color-alt); padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 6px; font-family: 'SF Mono', Monaco, monospace; }

        .binary-to-hex-features { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .binary-to-hex-features-title { color: var(--text-color); margin-bottom: var(--spacing-md); font-size: 1.25rem; font-weight: 700; }
        .binary-to-hex-features-list { list-style: none; padding: 0; margin: 0; columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; }
        .binary-to-hex-features-item { padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg); color: var(--text-color-light); position: relative; margin-bottom: 0.3em; }
        .binary-to-hex-features-item:before { content: ""; position: absolute; left: 0; top: calc(var(--spacing-sm) + 2px); width: 12px; height: 6px; border-left: 2px solid #10b981; border-bottom: 2px solid #10b981; transform: rotate(-45deg); }
        
        .binary-to-hex-related-tool-icon { width: 72px; height: 72px; margin: 0 auto var(--spacing-sm); border-radius: 18px; display: flex; align-items: center; justify-content: center; font-size: 2.25rem; color: white; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15); border: none; }
        a[href*="hex-to-binary"] .binary-to-hex-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="binary-to-decimal"] .binary-to-hex-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="binary-to-text"] .binary-to-hex-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }
        .binary-to-hex-related-tool-item:hover .binary-to-hex-related-tool-icon { transform: translateY(-5px) scale(1.05); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15); }
        a[href*="hex-to-binary"]:hover .binary-to-hex-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="binary-to-decimal"]:hover .binary-to-hex-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="binary-to-text"]:hover .binary-to-hex-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .binary-to-hex-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .binary-to-hex-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .binary-to-hex-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .binary-to-hex-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .binary-to-hex-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .binary-to-hex-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .binary-to-hex-related-tool-item:hover .binary-to-hex-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .binary-to-hex-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .binary-to-hex-widget-title { font-size: 1.875rem; }
            .binary-to-hex-buttons { flex-direction: column; }
            .binary-to-hex-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .binary-to-hex-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .binary-to-hex-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .binary-to-hex-related-tool-name { font-size: 0.875rem; }
        }
        @media (max-width: 600px) { 
            .binary-to-hex-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }
        @media (max-width: 480px) {
            .binary-to-hex-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .binary-to-hex-related-tool-item { padding: var(--spacing-sm); }
            .binary-to-hex-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .binary-to-hex-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="binary-to-hex-widget-container">
        <h1 class="binary-to-hex-widget-title">Binary to Hex Converter</h1>
        <p class="binary-to-hex-widget-description">
            Quickly and accurately convert binary numbers into hexadecimal format. This tool is perfect for developers, students, and anyone working with different number systems.
        </p>
        
        <div class="binary-to-hex-input-group">
            <label for="binaryToHexInput" class="binary-to-hex-label">Enter Binary Code:</label>
            <textarea 
                id="binaryToHexInput" 
                class="binary-to-hex-textarea"
                placeholder="Type your binary code here (e.g., 11001001)..."
                rows="4"
            ></textarea>
        </div>

        <div class="binary-to-hex-options">
            <div class="binary-to-hex-option">
                <input type="checkbox" id="hexAddPrefix" class="binary-to-hex-checkbox">
                <label for="hexAddPrefix" class="binary-to-hex-option-label">Add "0x" prefix</label>
            </div>
            <div class="binary-to-hex-option">
                <input type="checkbox" id="hexUppercase" class="binary-to-hex-checkbox" checked>
                <label for="hexUppercase" class="binary-to-hex-option-label">Use uppercase letters (A-F)</label>
            </div>
        </div>

        <div class="binary-to-hex-buttons">
            <button class="binary-to-hex-btn binary-to-hex-btn-primary" onclick="BinaryToHexConverter.convert()">
                Convert to Hex
            </button>
            <button class="binary-to-hex-btn binary-to-hex-btn-secondary" onclick="BinaryToHexConverter.clear()">
                Clear All
            </button>
            <button class="binary-to-hex-btn binary-to-hex-btn-success" onclick="BinaryToHexConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="binary-to-hex-result">
            <h3 class="binary-to-hex-result-title">Hexadecimal Code:</h3>
            <div class="binary-to-hex-output" id="binaryToHexOutput">
                Your hex code will appear here...
            </div>
        </div>
        
        <div class="binary-to-hex-related-tools">
            <h3 class="binary-to-hex-related-tools-title">Related Tools</h3>
            <div class="binary-to-hex-related-tools-grid">
                <a href="/p/hex-to-binary.html" class="binary-to-hex-related-tool-item" rel="noopener">
                    <div class="binary-to-hex-related-tool-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="binary-to-hex-related-tool-name">Hex to Binary</div>
                </a>
                <a href="/p/binary-to-decimal.html" class="binary-to-hex-related-tool-item" rel="noopener">
                    <div class="binary-to-hex-related-tool-icon"><i class="fas fa-calculator"></i></div>
                    <div class="binary-to-hex-related-tool-name">Binary to Decimal</div>
                </a>
                <a href="/p/binary-to-text.html" class="binary-to-hex-related-tool-item" rel="noopener">
                    <div class="binary-to-hex-related-tool-icon"><i class="fas fa-font"></i></div>
                    <div class="binary-to-hex-related-tool-name">Binary to Text</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert Binary to Hex with Ease</h2>
            <p>Our <strong>Binary to Hex Converter</strong> provides a fast, free, and reliable way to translate binary numbers into their hexadecimal equivalents. In computing, binary (base-2) is the fundamental language of digital systems, while hexadecimal (base-16) offers a more human-readable way to represent long binary strings. This conversion is crucial for developers, network administrators, and hardware engineers who need to work with memory addresses, color codes, and low-level data.</p>
            <p>Since one hex digit represents exactly four binary digits (a nibble), hexadecimal provides a much more compact format. For example, the 8-bit binary number <code>11001001</code> can be neatly expressed as just two hex digits: <code>C9</code>. Our tool automates this grouping and conversion process, saving you time and preventing manual errors.</p>
            
            <h3>How to Use the Binary to Hex Converter</h3>
            <ol>
                <li><strong>Enter Binary Code:</strong> Paste or type your binary string into the input area. Our tool automatically handles spaces and line breaks.</li>
                <li><strong>Select Options:</strong> Choose your desired output format. You can add a standard <code>0x</code> prefix or switch between uppercase (A-F) and lowercase (a-f) letters.</li>
                <li><strong>Click Convert:</strong> Press the "Convert to Hex" button to get an instant result. You can then copy it to your clipboard with a single click.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Binary to Hex Conversion</h3>
            <h4>How do you convert binary to hex?</h4>
            <p>To convert binary to hex, you group the binary digits into sets of four (called nibbles), starting from the right. If the last group has fewer than four digits, you pad it with leading zeros. Then, you convert each 4-bit group into its single hexadecimal equivalent. For example, binary 1011 becomes B in hex.</p>
            
            <h4>What is binary 11111111 in hexadecimal?</h4>
            <p>The binary number 11111111 is FF in hexadecimal. You group it into two 4-bit sets: 1111 and 1111. Each '1111' in binary is equivalent to 'F' in hexadecimal, so combined they become 'FF'.</p>
            
            <h4>How do you convert 1101010 binary to hexadecimal?</h4>
            <p>First, pad the binary number 1101010 with a leading zero to make its length a multiple of four: 01101010. Then, group it into 4-bit sets: 0110 and 1010. Convert each group: 0110 is 6 in hex, and 1010 is A in hex. So, the result is 6A.</p>
            
            <h4>What is the hexadecimal equivalent of the binary number 11001001?</h4>
            <p>To convert 11001001, you split it into two 4-bit groups: 1100 and 1001. The binary group 1100 is C in hexadecimal, and 1001 is 9 in hexadecimal. Therefore, the hexadecimal equivalent is C9.</p>
            
            <h4>How many binary in a hex?</h4>
            <p>One single hexadecimal digit represents exactly four binary digits (bits). This is why converting between the two systems is so straightforward and common in computer science.</p>
        </div>

        <div class="binary-to-hex-features">
            <h3 class="binary-to-hex-features-title">Key Features:</h3>
            <ul class="binary-to-hex-features-list">
                <li class="binary-to-hex-features-item">Instant, accurate conversions</li>
                <li class="binary-to-hex-features-item">Automatic input padding</li>
                <li class="binary-to-hex-features-item">Handles spaced or unspaced binary</li>
                <li class="binary-to-hex-features-item">Option to add '0x' prefix</li>
                <li class="binary-to-hex-features-item">Uppercase/lowercase control</li>
                <li class="binary-to-hex-features-item">One-click copy to clipboard</li>
                <li class="binary-to-hex-features-item">Clear error notifications</li>
                <li class="binary-to-hex-features-item">Fully responsive design</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="binary-to-hex-notification" id="binaryToHexNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('binaryToHexInput'),
                output: () => document.getElementById('binaryToHexOutput'),
                notification: () => document.getElementById('binaryToHexNotification')
            };

            window.BinaryToHexConverter = {
                convert() {
                    const input = elements.input();
                    const output = elements.output();
                    const binary = input.value;

                    if (!binary.trim()) {
                        output.textContent = 'Please enter a binary value to convert.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        addPrefix: document.getElementById('hexAddPrefix').checked,
                        uppercase: document.getElementById('hexUppercase').checked,
                    };

                    const result = this.processBinary(binary, options);
                    
                    if (result.startsWith('Invalid')) {
                        output.style.color = '#dc2626';
                    }
                    
                    output.textContent = result;
                },

                processBinary(binary, options) {
                    const cleanedBinary = binary.replace(/\s/g, '');
                    if (/[^01]/.test(cleanedBinary)) {
                        return 'Invalid binary input. Please use only 0s and 1s.';
                    }

                    let paddedBinary = cleanedBinary;
                    const remainder = cleanedBinary.length % 4;
                    if (remainder !== 0) {
                        paddedBinary = '0'.repeat(4 - remainder) + cleanedBinary;
                    }

                    let hexString = '';
                    for (let i = 0; i < paddedBinary.length; i += 4) {
                        const nibble = paddedBinary.substr(i, 4);
                        const hexDigit = parseInt(nibble, 2).toString(16);
                        hexString += hexDigit;
                    }
                    
                    if (options.uppercase) {
                        hexString = hexString.toUpperCase();
                    }
                    
                    if (options.addPrefix) {
                        hexString = '0x' + hexString;
                    }

                    return hexString;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your hex code will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text.includes('will appear here') || text.includes('Please enter') || text.includes('Invalid')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        BinaryToHexConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>