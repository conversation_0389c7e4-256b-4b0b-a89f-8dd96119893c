<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text to Binary Converter Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Text to Binary Converter - Encode Text to Binary Code",
        "description": "Convert text to binary code instantly. Free online tool with real-time conversion, multiple formats, and one-click copying.",
        "url": "https://www.webtoolskit.org/p/text-to-binary.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Text to Binary Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Text to Binary" },
            { "@type": "CopyAction", "name": "Copy Binary Code" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Can you convert text to binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, absolutely! Our text to binary converter can transform any text into binary code instantly. Each character is converted to its corresponding 8-bit binary representation based on ASCII values. Simply enter your text and click convert to see the binary output."
          }
        },
        {
          "@type": "Question",
          "name": "How do you say \"I love you\" in binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "\"I love you\" in binary is: 01001001 00100000 01101100 01101111 01110110 01100101 00100000 01111001 01101111 01110101. Each character is represented by its 8-bit ASCII binary equivalent, including the spaces between words."
          }
        },
        {
          "@type": "Question",
          "name": "What is the letter p in binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The letter 'p' (lowercase) in binary is 01110000. This represents the ASCII value 112 in binary format. The uppercase 'P' would be 01010000 (ASCII 80)."
          }
        },
        {
          "@type": "Question",
          "name": "What is hello in binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The word 'hello' in binary is: 01101000 01100101 01101100 01101100 01101111. Each letter is converted to its 8-bit binary representation: h=01101000, e=01100101, l=01101100, l=01101100, o=01101111."
          }
        },
        {
          "@type": "Question",
          "name": "How to say hello in binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To say 'hello' in binary, you would write: 01101000 01100101 01101100 01101100 01101111. This is the binary representation of each character in the word 'hello' using standard 8-bit ASCII encoding."
          }
        }
      ]
    }
    </script>


    <style>
        /* Text to Binary Widget - Simplified & Template Compatible */
        .text-to-binary-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .text-to-binary-widget-container * { box-sizing: border-box; }

        .text-to-binary-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-to-binary-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .text-to-binary-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .text-to-binary-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .text-to-binary-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .text-to-binary-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .text-to-binary-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .text-to-binary-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .text-to-binary-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .text-to-binary-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .text-to-binary-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .text-to-binary-btn:hover { transform: translateY(-2px); }

        .text-to-binary-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .text-to-binary-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .text-to-binary-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .text-to-binary-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .text-to-binary-btn-success {
            background-color: #10b981;
            color: white;
        }

        .text-to-binary-btn-success:hover {
            background-color: #059669;
        }

        .text-to-binary-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .text-to-binary-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-to-binary-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .text-to-binary-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .text-to-binary-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .text-to-binary-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .text-to-binary-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-to-binary-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .text-to-binary-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .text-to-binary-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .text-to-binary-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="binary-to-text"] .text-to-binary-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="text-to-ascii"] .text-to-binary-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="ascii-to-binary"] .text-to-binary-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .text-to-binary-related-tool-item:hover .text-to-binary-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="binary-to-text"]:hover .text-to-binary-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="text-to-ascii"]:hover .text-to-binary-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="ascii-to-binary"]:hover .text-to-binary-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .text-to-binary-related-tool-item { box-shadow: none; border: none; }
        .text-to-binary-related-tool-item:hover { box-shadow: none; border: none; }
        .text-to-binary-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .text-to-binary-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .text-to-binary-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .text-to-binary-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .text-to-binary-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .text-to-binary-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .text-to-binary-related-tool-item:hover .text-to-binary-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .text-to-binary-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .text-to-binary-widget-title { font-size: 1.875rem; }
            .text-to-binary-buttons { flex-direction: column; }
            .text-to-binary-btn { flex: none; }
            .text-to-binary-options { grid-template-columns: 1fr; }
            .text-to-binary-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .text-to-binary-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .text-to-binary-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .text-to-binary-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .text-to-binary-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .text-to-binary-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .text-to-binary-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .text-to-binary-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .text-to-binary-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .text-to-binary-checkbox:focus, .text-to-binary-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .text-to-binary-output::selection { background-color: var(--primary-color); color: white; }
        .text-to-binary-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .text-to-binary-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="text-to-binary-widget-container">
        <h1 class="text-to-binary-widget-title">Text to Binary Converter</h1>
        <p class="text-to-binary-widget-description">
            Convert any text to binary code instantly. Transform letters, words, and sentences into binary format for programming, encoding, and educational purposes.
        </p>
        
        <div class="text-to-binary-input-group">
            <label for="textToBinaryInput" class="text-to-binary-label">Enter text to convert:</label>
            <textarea 
                id="textToBinaryInput" 
                class="text-to-binary-textarea"
                placeholder="Type your text here (e.g., Hello World)..."
                rows="4"
            ></textarea>
        </div>

        <div class="text-to-binary-options">
            <div class="text-to-binary-option">
                <input type="checkbox" id="binaryAddSpaces" class="text-to-binary-checkbox" checked>
                <label for="binaryAddSpaces" class="text-to-binary-option-label">Add spaces between bytes</label>
            </div>
            <div class="text-to-binary-option">
                <input type="checkbox" id="binaryShowASCII" class="text-to-binary-checkbox">
                <label for="binaryShowASCII" class="text-to-binary-option-label">Show ASCII values</label>
            </div>
            <div class="text-to-binary-option">
                <input type="checkbox" id="binaryUppercase" class="text-to-binary-checkbox">
                <label for="binaryUppercase" class="text-to-binary-option-label">Convert to uppercase first</label>
            </div>
            <div class="text-to-binary-option">
                <input type="checkbox" id="binaryPadding" class="text-to-binary-checkbox" checked>
                <label for="binaryPadding" class="text-to-binary-option-label">Use 8-bit padding</label>
            </div>
        </div>

        <div class="text-to-binary-buttons">
            <button class="text-to-binary-btn text-to-binary-btn-primary" onclick="TextToBinaryConverter.convert()">
                Convert to Binary
            </button>
            <button class="text-to-binary-btn text-to-binary-btn-secondary" onclick="TextToBinaryConverter.clear()">
                Clear All
            </button>
            <button class="text-to-binary-btn text-to-binary-btn-success" onclick="TextToBinaryConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="text-to-binary-result">
            <h3 class="text-to-binary-result-title">Binary Code:</h3>
            <div class="text-to-binary-output" id="textToBinaryOutput">
                Your binary code will appear here...
            </div>
        </div>

        <div class="text-to-binary-related-tools">
            <h3 class="text-to-binary-related-tools-title">Related Tools</h3>
            <div class="text-to-binary-related-tools-grid">
                <a href="/p/binary-to-text.html" class="text-to-binary-related-tool-item" rel="noopener">
                    <div class="text-to-binary-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="text-to-binary-related-tool-name">Binary to Text</div>
                </a>

                <a href="/p/text-to-ascii.html" class="text-to-binary-related-tool-item" rel="noopener">
                    <div class="text-to-binary-related-tool-icon">
                        <i class="fas fa-font"></i>
                    </div>
                    <div class="text-to-binary-related-tool-name">Text to ASCII</div>
                </a>

                <a href="/p/ascii-to-binary.html" class="text-to-binary-related-tool-item" rel="noopener">
                    <div class="text-to-binary-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="text-to-binary-related-tool-name">ASCII to Binary</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert Text to Binary Code with Our Free Online Tool</h2>
            <p>Binary code is the foundation of all computer systems, representing information using only two digits: 0 and 1. Our <strong>Text to Binary</strong> converter makes it easy to transform any text into binary format, perfect for programming education, data encoding, or simply understanding how computers process text. Each character in your text is converted to its corresponding 8-bit binary representation based on ASCII values.</p>
            <p>Whether you're learning about computer science, working on programming projects, or need to encode text for data transmission, this tool provides instant and accurate text-to-binary conversion. The converter handles all printable ASCII characters, including letters, numbers, punctuation, and special symbols, making it versatile for various applications.</p>
            
            <h3>How to Use the Text to Binary Converter</h3>
            <ol>
                <li><strong>Enter Your Text:</strong> Type or paste any text into the input field. The tool supports all standard ASCII characters.</li>
                <li><strong>Choose Options:</strong> Select formatting preferences like adding spaces between bytes, showing ASCII values, or converting to uppercase first.</li>
                <li><strong>Convert and Copy:</strong> Click "Convert to Binary" to generate the binary code. Copy the result with one click for use in your projects.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Text to Binary</h3>
            
            <h4>Can you convert text to binary?</h4>
            <p>Yes, absolutely! Our text to binary converter can transform any text into binary code instantly. Each character is converted to its corresponding 8-bit binary representation based on ASCII values. Simply enter your text and click convert to see the binary output.</p>
            
            <h4>How do you say "I love you" in binary?</h4>
            <p>"I love you" in binary is: 01001001 00100000 01101100 01101111 01110110 01100101 00100000 01111001 01101111 01110101. Each character is represented by its 8-bit ASCII binary equivalent, including the spaces between words.</p>
            
            <h4>What is the letter p in binary?</h4>
            <p>The letter 'p' (lowercase) in binary is 01110000. This represents the ASCII value 112 in binary format. The uppercase 'P' would be 01010000 (ASCII 80).</p>
            
            <h4>What is hello in binary?</h4>
            <p>The word 'hello' in binary is: 01101000 01100101 01101100 01101100 01101111. Each letter is converted to its 8-bit binary representation: h=01101000, e=01100101, l=01101100, l=01101100, o=01101111.</p>
            
            <h4>How to say hello in binary?</h4>
            <p>To say 'hello' in binary, you would write: 01101000 01100101 01101100 01101100 01101111. This is the binary representation of each character in the word 'hello' using standard 8-bit ASCII encoding.</p>
        </div>


        <div class="text-to-binary-features">
            <h3 class="text-to-binary-features-title">Key Features:</h3>
            <ul class="text-to-binary-features-list">
                <li class="text-to-binary-features-item" style="margin-bottom: 0.3em;">Instant text-to-binary conversion</li>
                <li class="text-to-binary-features-item" style="margin-bottom: 0.3em;">Support for all ASCII characters</li>
                <li class="text-to-binary-features-item" style="margin-bottom: 0.3em;">Customizable output formatting</li>
                <li class="text-to-binary-features-item" style="margin-bottom: 0.3em;">ASCII value display option</li>
                <li class="text-to-binary-features-item" style="margin-bottom: 0.3em;">8-bit padding control</li>
                <li class="text-to-binary-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="text-to-binary-features-item">Real-time processing</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="text-to-binary-notification" id="textToBinaryNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Text to Binary Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('textToBinaryInput'),
                output: () => document.getElementById('textToBinaryOutput'),
                notification: () => document.getElementById('textToBinaryNotification')
            };

            window.TextToBinaryConverter = {
                convert() {
                    const input = elements.input();
                    const output = elements.output();
                    const text = input.value;

                    if (!text.trim()) {
                        output.textContent = 'Please enter text to convert.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        addSpaces: document.getElementById('binaryAddSpaces').checked,
                        showASCII: document.getElementById('binaryShowASCII').checked,
                        uppercase: document.getElementById('binaryUppercase').checked,
                        padding: document.getElementById('binaryPadding').checked
                    };

                    const result = this.processText(text, options);
                    output.textContent = result;
                },

                processText(text, options) {
                    let processedText = text;
                    if (options.uppercase) {
                        processedText = processedText.toUpperCase();
                    }

                    let binaryResult = [];
                    let asciiDetails = [];

                    for (let i = 0; i < processedText.length; i++) {
                        const char = processedText[i];
                        const asciiValue = char.charCodeAt(0);
                        let binaryValue = asciiValue.toString(2);

                        // Add padding if enabled
                        if (options.padding) {
                            binaryValue = binaryValue.padStart(8, '0');
                        }

                        binaryResult.push(binaryValue);

                        if (options.showASCII) {
                            asciiDetails.push(`${char} = ${binaryValue} (ASCII ${asciiValue})`);
                        }
                    }

                    let result = options.addSpaces ? binaryResult.join(' ') : binaryResult.join('');

                    if (options.showASCII && asciiDetails.length > 0) {
                        result += `\n\nASCII Details:\n${asciiDetails.join('\n')}`;
                    }

                    return result;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your binary code will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your binary code will appear here...', 'Please enter text to convert.'].includes(text)) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                const checkboxes = document.querySelectorAll('.text-to-binary-checkbox');

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        TextToBinaryConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>