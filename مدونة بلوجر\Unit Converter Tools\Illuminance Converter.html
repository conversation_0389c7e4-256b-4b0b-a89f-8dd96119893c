<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Illuminance Converter - Convert Lux, Foot-candle, and More</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Illuminance Converter - Convert Lux, Foot-candle, and More",
        "description": "Instantly convert between various illuminance units like lux, foot-candle, phot, and more. Free online tool for photographers, lighting designers, and scientists.",
        "url": "https://www.webtoolskit.org/p/illuminance-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-14",
        "dateModified": "2025-06-20",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Illuminance Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Illuminance Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert EV to lux?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Converting Exposure Value (EV) to lux is not direct as EV also depends on ISO speed. The formula is: Lux = 2.5 * 2^EV. This formula assumes a standard light meter calibration with a C constant of 250 and ISO 100. For example, an EV of 15 would be approximately 2.5 * 2^15 = 81,920 lux, which corresponds to bright sunlight."
          }
        },
        {
          "@type": "Question",
          "name": "How do you calculate illuminance?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Illuminance (E) is calculated by dividing the total luminous flux (Φ) in lumens that falls on a surface by the area (A) of that surface. The formula is E = Φ / A. The resulting unit is lumens per square meter, which is known as lux (lx). For example, if 1000 lumens are spread evenly over a 2 square meter surface, the illuminance is 1000 lm / 2 m² = 500 lux."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between luminance and illuminance?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Illuminance is the amount of light that falls onto a surface (measured in lux or foot-candles). It describes how much the surface is 'lit up'. Luminance is the amount of light that is emitted or reflected off that surface in a particular direction (measured in candela per square meter). It describes how bright the surface appears to the eye. A white wall and a black wall in the same room have the same illuminance, but the white wall has much higher luminance because it reflects more light."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert lumen into lux?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You cannot directly convert lumens to lux because they measure different things. Lumens are a measure of total light output from a source, while lux is a measure of light intensity over a specific area. However, you can calculate lux if you know the area: Lux = Lumens / Area (in m²). For example, a 500 lumen light source illuminating a 5 square meter area provides an average illuminance of 100 lux."
          }
        },
        {
          "@type": "Question",
          "name": "How many lux is full sun?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The illuminance from full, direct sunlight can vary depending on time of day, location, and atmospheric conditions, but it is typically between 100,000 and 120,000 lux. On an overcast day, this can drop significantly to around 1,000-2,000 lux. A typical well-lit office space is around 500 lux."
          }
        }
      ]
    }
    </script>

    <style>
        /* Illuminance Converter Widget - Simplified & Template Compatible */
        .illuminance-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .illuminance-converter-widget-container * { box-sizing: border-box; }

        .illuminance-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .illuminance-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .illuminance-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .illuminance-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .illuminance-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .illuminance-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .illuminance-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .illuminance-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .illuminance-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .illuminance-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .illuminance-converter-btn:hover { transform: translateY(-2px); }

        .illuminance-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .illuminance-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .illuminance-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .illuminance-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .illuminance-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .illuminance-converter-btn-success:hover {
            background-color: #059669;
        }

        .illuminance-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .illuminance-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .illuminance-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .illuminance-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .illuminance-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .illuminance-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .illuminance-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .illuminance-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .illuminance-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .illuminance-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .illuminance-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="power-converter"] .illuminance-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="energy-converter"] .illuminance-converter-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="angle-converter"] .illuminance-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }

        .illuminance-converter-related-tool-item:hover .illuminance-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="power-converter"]:hover .illuminance-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        a[href*="energy-converter"]:hover .illuminance-converter-related-tool-icon { background: linear-gradient(145deg, #2dd4bf, #14b8a6); }
        a[href*="angle-converter"]:hover .illuminance-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        
        .illuminance-converter-related-tool-item { box-shadow: none; border: none; }
        .illuminance-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .illuminance-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .illuminance-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .illuminance-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .illuminance-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .illuminance-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .illuminance-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .illuminance-converter-related-tool-item:hover .illuminance-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .illuminance-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .illuminance-converter-widget-title { font-size: 1.875rem; }
            .illuminance-converter-buttons { flex-direction: column; }
            .illuminance-converter-btn { flex: none; }
            .illuminance-converter-input-group { grid-template-columns: 1fr; }
            .illuminance-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .illuminance-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .illuminance-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .illuminance-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .illuminance-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .illuminance-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .illuminance-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .illuminance-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .illuminance-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .illuminance-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .illuminance-converter-output::selection { background-color: var(--primary-color); color: white; }
        .illuminance-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .illuminance-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="illuminance-converter-widget-container">
        <h1 class="illuminance-converter-widget-title">Illuminance Converter</h1>
        <p class="illuminance-converter-widget-description">
            A simple tool for converting illuminance units, including lux, foot-candle, phot, and more, for lighting and photography.
        </p>
        
        <div class="illuminance-converter-input-group">
            <label for="illuminanceFromInput" class="illuminance-converter-label">From:</label>
            <input 
                type="number" 
                id="illuminanceFromInput" 
                class="illuminance-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="illuminanceFromUnit" class="illuminance-converter-select">
                <option value="lx" selected>Lux (lx)</option>
                <option value="klx">Kilolux (klx)</option>
                <option value="mlx">Millilux (mlx)</option>
                <option value="fc">Foot-candle (fc)</option>
                <option value="ph">Phot (ph)</option>
            </select>
        </div>

        <div class="illuminance-converter-input-group">
            <label for="illuminanceToInput" class="illuminance-converter-label">To:</label>
            <input 
                type="number" 
                id="illuminanceToInput" 
                class="illuminance-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="illuminanceToUnit" class="illuminance-converter-select">
                <option value="lx">Lux (lx)</option>
                <option value="klx">Kilolux (klx)</option>
                <option value="mlx">Millilux (mlx)</option>
                <option value="fc" selected>Foot-candle (fc)</option>
                <option value="ph">Phot (ph)</option>
            </select>
        </div>

        <div class="illuminance-converter-buttons">
            <button class="illuminance-converter-btn illuminance-converter-btn-primary" onclick="IlluminanceConverter.convert()">
                Convert Illuminance
            </button>
            <button class="illuminance-converter-btn illuminance-converter-btn-secondary" onclick="IlluminanceConverter.clear()">
                Clear All
            </button>
            <button class="illuminance-converter-btn illuminance-converter-btn-success" onclick="IlluminanceConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="illuminance-converter-result">
            <h3 class="illuminance-converter-result-title">Conversion Result:</h3>
            <div class="illuminance-converter-output" id="illuminanceConverterOutput">
                Your converted illuminance will appear here...
            </div>
        </div>

        <div class="illuminance-converter-related-tools">
            <h3 class="illuminance-converter-related-tools-title">Related Tools</h3>
            <div class="illuminance-converter-related-tools-grid">
                <a href="/p/power-converter.html" class="illuminance-converter-related-tool-item" rel="noopener">
                    <div class="illuminance-converter-related-tool-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="illuminance-converter-related-tool-name">Power Converter</div>
                </a>
                <a href="/p/energy-converter.html" class="illuminance-converter-related-tool-item" rel="noopener">
                    <div class="illuminance-converter-related-tool-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="illuminance-converter-related-tool-name">Energy Converter</div>
                </a>
                <a href="/p/angle-converter.html" class="illuminance-converter-related-tool-item" rel="noopener">
                    <div class="illuminance-converter-related-tool-icon">
                        <i class="fas fa-drafting-compass"></i>
                    </div>
                    <div class="illuminance-converter-related-tool-name">Angle Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Accurate Illuminance Conversion for Lighting Professionals</h2>
            <p>Illuminance is a measure of how much luminous flux—or light—is spread over a given area. It's a critical factor in fields like photography, architecture, lighting design, and horticulture. Our free <strong>Illuminance Converter</strong> is an essential tool for professionals and hobbyists who need to switch between different units of light measurement quickly and accurately. Whether you're working with the international standard of Lux, the imperial unit Foot-candle, or other scientific units, this tool ensures your calculations are precise.</p>
            <p>Understanding and converting illuminance values is key to achieving correct camera exposure, meeting building lighting codes, designing comfortable spaces, or providing optimal light for plant growth. This converter simplifies the process, handling the complex math so you can focus on your work.</p>

            <h3>How to Use the Illuminance Converter</h3>
            <ol>
                <li><strong>Enter Your Value:</strong> Type the numeric value of the illuminance you need to convert into the "From" field.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., Lux) and the unit you want to convert to (e.g., Foot-candle) from the dropdown lists.</li>
                <li><strong>Convert:</strong> Click the "Convert Illuminance" button to get your precise result instantly.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to easily transfer the converted value to your clipboard for use in your projects.</li>
            </ol>

            <h3>Frequently Asked Questions About Illuminance</h3>

            <h4>How do you convert EV to lux?</h4>
            <p>Converting Exposure Value (EV) to lux is not direct as EV also depends on ISO speed. The formula is: Lux = 2.5 * 2^EV. This formula assumes a standard light meter calibration with a C constant of 250 and ISO 100. For example, an EV of 15 would be approximately 2.5 * 2^15 = 81,920 lux, which corresponds to bright sunlight.</p>

            <h4>How do you calculate illuminance?</h4>
            <p>Illuminance (E) is calculated by dividing the total luminous flux (Φ) in lumens that falls on a surface by the area (A) of that surface. The formula is E = Φ / A. The resulting unit is lumens per square meter, which is known as lux (lx). For example, if 1000 lumens are spread evenly over a 2 square meter surface, the illuminance is 1000 lm / 2 m² = 500 lux.</p>

            <h4>What is the difference between luminance and illuminance?</h4>
            <p>Illuminance is the amount of light that falls onto a surface (measured in lux or foot-candles). It describes how much the surface is 'lit up'. Luminance is the amount of light that is emitted or reflected off that surface in a particular direction (measured in candela per square meter). It describes how bright the surface appears to the eye. A white wall and a black wall in the same room have the same illuminance, but the white wall has much higher luminance because it reflects more light.</p>

            <h4>How to convert lumen into lux?</h4>
            <p>You cannot directly convert lumens to lux because they measure different things. Lumens are a measure of total light output from a source, while lux is a measure of light intensity over a specific area. However, you can calculate lux if you know the area: Lux = Lumens / Area (in m²). For example, a 500 lumen light source illuminating a 5 square meter area provides an average illuminance of 100 lux.</p>

            <h4>How many lux is full sun?</h4>
            <p>The illuminance from full, direct sunlight can vary depending on time of day, location, and atmospheric conditions, but it is typically between 100,000 and 120,000 lux. On an overcast day, this can drop significantly to around 1,000-2,000 lux. A typical well-lit office space is around 500 lux.</p>
        </div>

        <div class="illuminance-converter-features">
            <h3 class="illuminance-converter-features-title">Key Features:</h3>
            <ul class="illuminance-converter-features-list">
                <li class="illuminance-converter-features-item" style="margin-bottom: 0.3em;">Converts Lux, Foot-candle, etc.</li>
                <li class="illuminance-converter-features-item" style="margin-bottom: 0.3em;">Ideal for photographers & designers</li>
                <li class="illuminance-converter-features-item" style="margin-bottom: 0.3em;">High-precision calculations</li>
                <li class="illuminance-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="illuminance-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side processing</li>
                <li class="illuminance-converter-features-item" style="margin-bottom: 0.3em;">Responsive on all devices</li>
                <li class="illuminance-converter-features-item">100% free and secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="illuminance-converter-notification" id="illuminanceConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Illuminance Converter
        (function() {
            'use strict';

            // Conversion factors to Lux (lx)
            const conversionFactors = {
                'lx': 1,
                'klx': 1000,
                'mlx': 0.001,
                'fc': 10.7639104, // Foot-candle
                'ph': 10000       // Phot
            };

            const elements = {
                fromInput: () => document.getElementById('illuminanceFromInput'),
                toInput: () => document.getElementById('illuminanceToInput'),
                fromUnit: () => document.getElementById('illuminanceFromUnit'),
                toUnit: () => document.getElementById('illuminanceToUnit'),
                output: () => document.getElementById('illuminanceConverterOutput'),
                notification: () => document.getElementById('illuminanceConverterNotification')
            };

            window.IlluminanceConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to base unit (Lux) first, then to target unit
                    const valueInLux = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInLux / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-6 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(10)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'lx': 'lx', 'klx': 'klx', 'mlx': 'mlx', 'fc': 'fc', 'ph': 'ph' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted illuminance will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        IlluminanceConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>