<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frequency Converter - Convert Hz, kHz, MHz, RPM & More</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Frequency Converter - Convert Hz, kHz, MHz, RPM & More",
        "description": "Instantly convert between various frequency units like hertz (Hz), kilohertz (kHz), megahertz (MHz), and RPM. Free online tool for engineers, scientists, and hobbyists.",
        "url": "https://www.webtoolskit.org/p/frequency-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-13",
        "dateModified": "2025-06-16",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Frequency Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Frequency Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What does a frequency converter do?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A physical frequency converter is an electronic device that changes the frequency of an AC power source to another frequency. For example, it can convert 60Hz power to 50Hz power to run equipment from a different region. This online tool, however, is a unit converter; it mathematically converts the measurement of frequency from one unit (like MHz) to another (like kHz)."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert 60Hz to 50Hz?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Converting 60Hz to 50Hz requires a physical device called a static frequency converter or a motor-generator set. It's not a simple calculation. The modern electronic method involves a two-step process: first, the incoming AC power (60Hz) is converted to DC power (rectification); second, the DC power is converted back into clean, new AC power at the desired frequency (50Hz) using an inverter."
          }
        },
        {
          "@type": "Question",
          "name": "What is the basic principle of frequency conversion?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The basic principle of a modern electronic frequency converter is an AC-to-DC-to-AC conversion. The first stage (rectifier) converts the source AC voltage into a stable DC voltage. The second stage (inverter), using high-speed switches like IGBTs, chops up the DC voltage and reassembles it into a new AC sine wave at the required output frequency and voltage."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between a frequency converter and an inverter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "An inverter is a core component of a frequency converter. An inverter's job is to convert DC power to AC power. A frequency converter is a complete system that typically converts AC power of one frequency to AC power of another frequency. To do this, it uses a rectifier (AC to DC) followed by an inverter (DC to AC). So, an inverter is just half of the process."
          }
        },
        {
          "@type": "Question",
          "name": "Why are frequency converters necessary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Frequency converters are necessary for several key reasons: 1. International Power Compatibility: To run equipment designed for 50Hz in a 60Hz country, or vice versa. 2. Motor Speed Control: Variable Frequency Drives (VFDs) are frequency converters that adjust the frequency to precisely control the speed of AC motors, saving energy. 3. Specialized Applications: For testing or operating equipment in aviation and marine industries that use different power standards (e.g., 400Hz)."
          }
        }
      ]
    }
    </script>

    <style>
        /* Frequency Converter Widget - Simplified & Template Compatible */
        .frequency-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .frequency-converter-widget-container * { box-sizing: border-box; }

        .frequency-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .frequency-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .frequency-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .frequency-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .frequency-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .frequency-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .frequency-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .frequency-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .frequency-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .frequency-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .frequency-converter-btn:hover { transform: translateY(-2px); }

        .frequency-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .frequency-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .frequency-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .frequency-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .frequency-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .frequency-converter-btn-success:hover {
            background-color: #059669;
        }

        .frequency-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .frequency-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .frequency-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .frequency-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .frequency-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .frequency-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .frequency-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .frequency-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .frequency-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .frequency-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .frequency-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="speed-converter"] .frequency-converter-related-tool-icon { background: linear-gradient(145deg, #4F46E5, #4338CA); }
        a[href*="power-converter"] .frequency-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="time-converter"] .frequency-converter-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }

        .frequency-converter-related-tool-item:hover .frequency-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="speed-converter"]:hover .frequency-converter-related-tool-icon { background: linear-gradient(145deg, #5b52f6, #4f46e5); }
        a[href*="power-converter"]:hover .frequency-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        a[href*="time-converter"]:hover .frequency-converter-related-tool-icon { background: linear-gradient(145deg, #7476f2, #6366f1); }
        
        .frequency-converter-related-tool-item { box-shadow: none; border: none; }
        .frequency-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .frequency-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .frequency-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .frequency-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .frequency-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .frequency-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .frequency-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .frequency-converter-related-tool-item:hover .frequency-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .frequency-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .frequency-converter-widget-title { font-size: 1.875rem; }
            .frequency-converter-buttons { flex-direction: column; }
            .frequency-converter-btn { flex: none; }
            .frequency-converter-input-group { grid-template-columns: 1fr; }
            .frequency-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .frequency-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .frequency-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .frequency-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .frequency-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .frequency-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .frequency-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .frequency-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .frequency-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .frequency-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .frequency-converter-output::selection { background-color: var(--primary-color); color: white; }
        .frequency-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .frequency-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="frequency-converter-widget-container">
        <h1 class="frequency-converter-widget-title">Frequency Converter</h1>
        <p class="frequency-converter-widget-description">
            A precise tool to convert between frequency units like Hertz (Hz), kilohertz (kHz), megahertz (MHz), RPM, and more.
        </p>
        
        <div class="frequency-converter-input-group">
            <label for="frequencyFromInput" class="frequency-converter-label">From:</label>
            <input 
                type="number" 
                id="frequencyFromInput" 
                class="frequency-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="frequencyFromUnit" class="frequency-converter-select">
                <option value="hz" selected>Hertz (Hz)</option>
                <option value="khz">Kilohertz (kHz)</option>
                <option value="mhz">Megahertz (MHz)</option>
                <option value="ghz">Gigahertz (GHz)</option>
                <option value="rpm">Revolutions/minute (RPM)</option>
                <option value="rad_s">Radians/second (rad/s)</option>
            </select>
        </div>

        <div class="frequency-converter-input-group">
            <label for="frequencyToInput" class="frequency-converter-label">To:</label>
            <input 
                type="number" 
                id="frequencyToInput" 
                class="frequency-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="frequencyToUnit" class="frequency-converter-select">
                <option value="hz">Hertz (Hz)</option>
                <option value="khz" selected>Kilohertz (kHz)</option>
                <option value="mhz">Megahertz (MHz)</option>
                <option value="ghz">Gigahertz (GHz)</option>
                <option value="rpm">Revolutions/minute (RPM)</option>
                <option value="rad_s">Radians/second (rad/s)</option>
            </select>
        </div>

        <div class="frequency-converter-buttons">
            <button class="frequency-converter-btn frequency-converter-btn-primary" onclick="FrequencyConverter.convert()">
                Convert Frequency
            </button>
            <button class="frequency-converter-btn frequency-converter-btn-secondary" onclick="FrequencyConverter.clear()">
                Clear All
            </button>
            <button class="frequency-converter-btn frequency-converter-btn-success" onclick="FrequencyConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="frequency-converter-result">
            <h3 class="frequency-converter-result-title">Conversion Result:</h3>
            <div class="frequency-converter-output" id="frequencyConverterOutput">
                Your converted frequency will appear here...
            </div>
        </div>

        <div class="frequency-converter-related-tools">
            <h3 class="frequency-converter-related-tools-title">Related Tools</h3>
            <div class="frequency-converter-related-tools-grid">
                <a href="/p/speed-converter.html" class="frequency-converter-related-tool-item" rel="noopener">
                    <div class="frequency-converter-related-tool-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="frequency-converter-related-tool-name">Speed Converter</div>
                </a>
                <a href="/p/power-converter.html" class="frequency-converter-related-tool-item" rel="noopener">
                    <div class="frequency-converter-related-tool-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="frequency-converter-related-tool-name">Power Converter</div>
                </a>
                <a href="/p/time-converter.html" class="frequency-converter-related-tool-item" rel="noopener">
                    <div class="frequency-converter-related-tool-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="frequency-converter-related-tool-name">Time Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Instant Frequency Unit Conversion Tool</h2>
            <p>Frequency, the measure of cycles per second, is a cornerstone of physics, engineering, telecommunications, and mechanical systems. Whether you're a radio hobbyist tuning into a broadcast in megahertz (MHz), an engineer analyzing motor speed in revolutions per minute (RPM), or a scientist studying wave phenomena in hertz (Hz), you often need to switch between units. Our free <strong>Frequency Converter</strong> makes these calculations effortless and accurate.</p>
            <p>This tool seamlessly converts between the most common frequency units, including hertz (Hz), kilohertz (kHz), megahertz (MHz), gigahertz (GHz), revolutions per minute (RPM), and radians per second (rad/s). Eliminate manual errors and complex formulas from your workflow with our reliable, user-friendly converter.</p>

            <h3>How to Use the Frequency Converter</h3>
            <ol>
                <li><strong>Enter Your Value:</strong> Type the numeric value of the frequency you want to convert into the "From" input field.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., MHz) and your target unit (e.g., kHz) from the dropdown lists.</li>
                <li><strong>Convert:</strong> Click the "Convert Frequency" button for an immediate, precise calculation.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to quickly copy the converted value for your notes, designs, or reports.</li>
            </ol>

            <h3>Frequently Asked Questions About Frequency Conversion</h3>
            
            <h4>What does a frequency converter do?</h4>
            <p>A physical frequency converter is an electronic device that changes the frequency of an AC power source to another frequency. For example, it can convert 60Hz power to 50Hz power to run equipment from a different region. This online tool, however, is a unit converter; it mathematically converts the measurement of frequency from one unit (like MHz) to another (like kHz).</p>

            <h4>How to convert 60Hz to 50Hz?</h4>
            <p>Converting 60Hz to 50Hz requires a physical device called a static frequency converter or a motor-generator set. It's not a simple calculation. The modern electronic method involves a two-step process: first, the incoming AC power (60Hz) is converted to DC power (rectification); second, the DC power is converted back into clean, new AC power at the desired frequency (50Hz) using an inverter.</p>

            <h4>What is the basic principle of frequency conversion?</h4>
            <p>The basic principle of a modern electronic frequency converter is an AC-to-DC-to-AC conversion. The first stage (rectifier) converts the source AC voltage into a stable DC voltage. The second stage (inverter), using high-speed switches like IGBTs, chops up the DC voltage and reassembles it into a new AC sine wave at the required output frequency and voltage.</p>

            <h4>What is the difference between a frequency converter and an inverter?</h4>
            <p>An inverter is a core component of a frequency converter. An inverter's job is to convert DC power to AC power. A frequency converter is a complete system that typically converts AC power of one frequency to AC power of another frequency. To do this, it uses a rectifier (AC to DC) followed by an inverter (DC to AC). So, an inverter is just half of the process.</p>

            <h4>Why are frequency converters necessary?</h4>
            <p>Frequency converters are necessary for several key reasons: 1. International Power Compatibility: To run equipment designed for 50Hz in a 60Hz country, or vice versa. 2. Motor Speed Control: Variable Frequency Drives (VFDs) are frequency converters that adjust the frequency to precisely control the speed of AC motors, saving energy. 3. Specialized Applications: For testing or operating equipment in aviation and marine industries that use different power standards (e.g., 400Hz).</p>
        </div>

        <div class="frequency-converter-features">
            <h3 class="frequency-converter-features-title">Key Features:</h3>
            <ul class="frequency-converter-features-list">
                <li class="frequency-converter-features-item" style="margin-bottom: 0.3em;">Converts Hz, kHz, MHz, GHz</li>
                <li class="frequency-converter-features-item" style="margin-bottom: 0.3em;">Includes RPM and rad/s units</li>
                <li class="frequency-converter-features-item" style="margin-bottom: 0.3em;">High-precision results</li>
                <li class="frequency-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="frequency-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side processing</li>
                <li class="frequency-converter-features-item" style="margin-bottom: 0.3em;">Ideal for engineering & science</li>
                <li class="frequency-converter-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="frequency-converter-notification" id="frequencyConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Frequency Converter
        (function() {
            'use strict';

            // Conversion factors to Hertz (Hz)
            const conversionFactors = {
                'hz': 1,
                'khz': 1000,
                'mhz': 1e6,
                'ghz': 1e9,
                'rpm': 1 / 60,
                'rad_s': 1 / (2 * Math.PI)
            };

            const elements = {
                fromInput: () => document.getElementById('frequencyFromInput'),
                toInput: () => document.getElementById('frequencyToInput'),
                fromUnit: () => document.getElementById('frequencyFromUnit'),
                toUnit: () => document.getElementById('frequencyToUnit'),
                output: () => document.getElementById('frequencyConverterOutput'),
                notification: () => document.getElementById('frequencyConverterNotification')
            };

            window.FrequencyConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to base unit (Hz) first, then to target unit
                    const valueInHz = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInHz / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-9 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(12)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'hz': 'Hz', 'khz': 'kHz', 'mhz': 'MHz', 'ghz': 'GHz', 'rpm': 'RPM', 'rad_s': 'rad/s' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted frequency will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        FrequencyConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>