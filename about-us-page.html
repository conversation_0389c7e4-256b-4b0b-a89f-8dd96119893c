<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>

  <!-- Performance and Core Web Vitals optimization -->
  <meta name="format-detection" content="telephone=no"/>
  <meta name="theme-color" content="#0047AB"/>
  <meta name="color-scheme" content="light dark"/>

  <title>About Us - WebToolsKit | Professional Online Tools</title>
  <meta name="description" content="Learn about WebToolsKit - your trusted source for free, professional online tools. Discover our mission to provide high-quality web utilities for developers, designers, and everyday users."/>
  <meta name="keywords" content="about webtoolskit, online tools, web utilities, free tools, professional tools, web development"/>
  <meta name="author" content="WebToolsKit"/>
  <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"/>
  <meta name="generator" content="WebToolsKit"/>
  <meta name="referrer" content="no-referrer-when-downgrade"/>
  <link rel="canonical" href="https://www.webtoolskit.org/p/about-us.html"/>

  <!-- Enhanced Open Graph Meta Tags -->
  <meta property="og:title" content="About Us - WebToolsKit | Professional Online Tools"/>
  <meta property="og:description" content="Learn about WebToolsKit - your trusted source for free, professional online tools. Discover our mission to provide high-quality web utilities for developers, designers, and everyday users."/>
  <meta property="og:image" content="https://www.webtoolskit.org/images/about-us-og.jpg"/>
  <meta property="og:image:width" content="1200"/>
  <meta property="og:image:height" content="630"/>
  <meta property="og:image:alt" content="About WebToolsKit - Professional Online Tools"/>
  <meta property="og:url" content="https://www.webtoolskit.org/p/about-us.html"/>
  <meta property="og:type" content="website"/>
  <meta property="og:locale" content="en_US"/>

  <!-- Enhanced Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image"/>
  <meta name="twitter:url" content="https://www.webtoolskit.org/p/about-us.html"/>
  <meta name="twitter:title" content="About Us - WebToolsKit | Professional Online Tools"/>
  <meta name="twitter:description" content="Learn about WebToolsKit - your trusted source for free, professional online tools. Discover our mission to provide high-quality web utilities for developers, designers, and everyday users."/>
  <meta name="twitter:image" content="https://www.webtoolskit.org/images/about-us-og.jpg"/>
  <meta name="twitter:image:alt" content="About WebToolsKit - Professional Online Tools"/>
  <meta name="twitter:site" content="@webtoolskit"/>
  <meta name="twitter:creator" content="@webtoolskit"/>

  <!-- Enhanced Favicon and App Icons -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico"/>
  <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png"/>
  <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png"/>
  <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png"/>
  <link rel="manifest" href="/manifest.json"/>
  <meta name="msapplication-TileColor" content="#0047AB"/>
  <meta name="msapplication-config" content="/browserconfig.xml"/>

  <!-- Performance Optimization - Preconnect to important domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com"/>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""/>
  <link rel="preconnect" href="https://cdnjs.cloudflare.com"/>
  <link rel="dns-prefetch" href="https://www.google-analytics.com"/>

  <!-- Font Awesome with enhanced security -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer"/>

  <!-- Accessibility and Performance Enhancements -->
  <meta name="mobile-web-app-capable" content="yes"/>
  <meta name="apple-mobile-web-app-capable" content="yes"/>
  <meta name="apple-mobile-web-app-status-bar-style" content="default"/>
  <meta name="apple-mobile-web-app-title" content="WebToolsKit"/>

  <!-- Organization Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "WebToolsKit",
    "alternateName": "Web Tools Kit",
    "description": "WebToolsKit provides free, professional online tools and utilities for developers, designers, and everyday users. Our comprehensive suite includes text tools, image editors, calculators, converters, and more.",
    "url": "https://www.webtoolskit.org",
    "logo": "https://www.webtoolskit.org/images/logo.png",
    "image": "https://www.webtoolskit.org/images/webtoolskit-og.jpg",
    "foundingDate": "2023",
    "founder": {
      "@type": "Person",
      "name": "WebToolsKit Team"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-0123",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["English"],
      "areaServed": "Worldwide"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US",
      "addressRegion": "Global"
    },
    "sameAs": [
      "https://twitter.com/webtoolskit",
      "https://facebook.com/webtoolskit",
      "https://linkedin.com/company/webtoolskit",
      "https://github.com/webtoolskit"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Free Online Tools",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Text Content Tools",
            "description": "Comprehensive text manipulation tools including case converters, word counters, and text generators."
          },
          "price": "0",
          "priceCurrency": "USD"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Image Editing Tools",
            "description": "Professional image editing utilities for resizing, converting, and optimizing images without quality loss."
          },
          "price": "0",
          "priceCurrency": "USD"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Online Calculators",
            "description": "Advanced calculators for mathematical, financial, and scientific calculations with precision and ease."
          },
          "price": "0",
          "priceCurrency": "USD"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Unit Converters",
            "description": "Universal unit converters for length, weight, temperature, and digital data with accurate results."
          },
          "price": "0",
          "priceCurrency": "USD"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Developer Tools",
            "description": "Essential development utilities including JSON formatters, code validators, and debugging tools."
          },
          "price": "0",
          "priceCurrency": "USD"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "SEO Tools",
            "description": "Powerful SEO utilities to optimize website performance and search engine visibility."
          },
          "price": "0",
          "priceCurrency": "USD"
        }
      ]
    },
    "knowsAbout": [
      "Web Development",
      "Text Processing",
      "Image Editing",
      "Data Conversion",
      "SEO Optimization",
      "Online Calculators",
      "Developer Tools",
      "Web Utilities"
    ],
    "areaServed": {
      "@type": "Place",
      "name": "Worldwide"
    },
    "audience": {
      "@type": "Audience",
      "audienceType": "Developers, Designers, Content Creators, Students, Professionals"
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "https://www.webtoolskit.org/p/about-us.html"
    },
    "potentialAction": {
      "@type": "UseAction",
      "target": "https://www.webtoolskit.org",
      "object": "Free Online Tools"
    }
  }
  </script>

  <!-- Skip to main content for accessibility -->
  <style>
    .skip-link {
      position: absolute;
      top: -40px;
      left: 6px;
      background: var(--primary-color);
      color: white;
      padding: 8px;
      text-decoration: none;
      border-radius: 0 0 4px 4px;
      z-index: 10000;
      font-weight: 600;
    }
    .skip-link:focus {
      top: 0;
    }

    /* Screen reader only class for accessibility */
    .sr-only {
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    }
  </style>

  <style>
    /* CSS Variables for consistent theming */
    :root {
      --primary-color: #0047AB;
      --secondary-color: #4338ca;
      --text-color: #111827;
      --text-color-light: #4b5563;
      --background-color: #fff;
      --background-color-alt: #f3f4f6;
      --border-color: #e5e7eb;
      --card-bg: #fff;
      --font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      --font-size-base: 16px;
      --line-height-base: 1.6;
      --spacing-xs: .25rem;
      --spacing-sm: .5rem;
      --spacing-md: 1rem;
      --spacing-lg: 1.5rem;
      --spacing-xl: 2rem;
      --spacing-2xl: 3rem;
      --border-radius-sm: .25rem;
      --border-radius-md: .375rem;
      --border-radius-lg: .5rem;
      --transition-base: all .3s ease;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    /* Dark mode support */
    [data-theme="dark"] {
      --primary-color: #60a5fa;
      --secondary-color: #818cf8;
      --text-color: #ffffff;
      --text-color-light: #d1d5db;
      --background-color: #111827;
      --background-color-alt: #1f2937;
      --border-color: #374151;
      --card-bg: #1f2937;
    }

    /* Base styles */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: var(--font-family);
      font-size: var(--font-size-base);
      line-height: var(--line-height-base);
      color: var(--text-color);
      background-color: var(--background-color);
      padding: 0;
      margin: 0;
      width: 100%;
      min-height: 100vh;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
    }

    .container {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 var(--spacing-md);
      box-sizing: border-box;
    }

    /* About page specific styles */
    .about-page {
      padding: var(--spacing-xl) 0;
      width: 100%;
    }

    /* Hero Section */
    .about-hero {
      text-align: center;
      padding: var(--spacing-2xl) 0;
      background: linear-gradient(135deg, var(--background-color-alt) 0%, var(--background-color) 100%);
      border-radius: var(--border-radius-lg);
      margin-bottom: var(--spacing-2xl);
      position: relative;
      overflow: hidden;
    }

    .about-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, var(--primary-color)10, transparent 70%);
      opacity: 0.05;
      pointer-events: none;
    }

    .about-hero-content {
      position: relative;
      z-index: 1;
    }

    .about-title {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: var(--spacing-lg);
      color: var(--primary-color);
      position: relative;
      display: inline-block;
    }

    .about-subtitle {
      font-size: 1.25rem;
      color: var(--text-color-light);
      margin-bottom: var(--spacing-xl);
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.7;
    }

    /* Content Sections */
    .about-section {
      margin-bottom: var(--spacing-2xl);
      padding: var(--spacing-xl);
      background-color: var(--card-bg);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-color);
      transition: var(--transition-base);
    }

    .about-section:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    .section-title {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: var(--spacing-lg);
      color: var(--primary-color);
      position: relative;
      padding-bottom: var(--spacing-sm);
    }

    .section-title::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 60px;
      height: 3px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      border-radius: 2px;
    }

    .section-content {
      font-size: 1.1rem;
      line-height: 1.8;
      color: var(--text-color);
    }

    .section-content p {
      margin-bottom: var(--spacing-lg);
    }

    .section-content p:last-child {
      margin-bottom: 0;
    }

    /* Features Grid */
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-xl);
      margin-top: var(--spacing-xl);
    }

    .feature-card {
      background-color: var(--background-color-alt);
      padding: var(--spacing-xl);
      border-radius: var(--border-radius-md);
      text-align: center;
      transition: var(--transition-base);
      border: 1px solid var(--border-color);
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-lg);
    }

    .feature-icon {
      font-size: 3rem;
      color: var(--primary-color);
      margin-bottom: var(--spacing-lg);
    }

    .feature-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: var(--spacing-md);
      color: var(--text-color);
    }

    .feature-description {
      color: var(--text-color-light);
      line-height: 1.6;
    }

    /* Stats Section */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-lg);
      margin-top: var(--spacing-xl);
    }

    .stat-item {
      text-align: center;
      padding: var(--spacing-lg);
      background-color: var(--background-color-alt);
      border-radius: var(--border-radius-md);
      border: 1px solid var(--border-color);
    }

    .stat-number {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--primary-color);
      display: block;
      margin-bottom: var(--spacing-sm);
    }

    .stat-label {
      color: var(--text-color-light);
      font-weight: 500;
    }

    /* CTA Section */
    .cta-section {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      text-align: center;
      padding: var(--spacing-2xl);
      border-radius: var(--border-radius-lg);
      margin-top: var(--spacing-2xl);
    }

    .cta-title {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: var(--spacing-lg);
    }

    .cta-description {
      font-size: 1.1rem;
      margin-bottom: var(--spacing-xl);
      opacity: 0.9;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .cta-button {
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-sm);
      background-color: white;
      color: var(--primary-color);
      padding: var(--spacing-lg) var(--spacing-xl);
      border-radius: var(--border-radius-md);
      text-decoration: none;
      font-weight: 600;
      font-size: 1.1rem;
      transition: var(--transition-base);
      box-shadow: var(--shadow-md);
    }

    .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
      color: var(--primary-color);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .container {
        padding: 0 var(--spacing-sm);
      }

      .about-title {
        font-size: 2rem;
      }

      .about-subtitle {
        font-size: 1.1rem;
      }

      .section-title {
        font-size: 1.5rem;
      }

      .about-section {
        padding: var(--spacing-lg);
      }

      .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
      }

      .cta-title {
        font-size: 1.5rem;
      }

      .cta-description {
        font-size: 1rem;
      }
    }

    @media (max-width: 480px) {
      .about-title {
        font-size: 1.75rem;
      }

      .stats-grid {
        grid-template-columns: 1fr;
      }

      .stat-number {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body role="document">
  <!-- Skip to main content for accessibility -->
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <div class="container">
    <main class="about-page" id="main-content" role="main">
      <!-- Hero Section -->
      <section class="about-hero" aria-labelledby="about-title">
        <div class="about-hero-content">
          <h1 class="about-title" id="about-title">About WebToolsKit</h1>
          <p class="about-subtitle">Your trusted source for professional, free online tools designed to simplify your digital workflow and boost productivity.</p>
        </div>
      </section>

      <!-- Mission Section -->
      <section class="about-section" aria-labelledby="mission-title">
        <h2 class="section-title" id="mission-title">Our Mission</h2>
        <div class="section-content">
          <p>At WebToolsKit, we believe that powerful tools shouldn't come with a hefty price tag. Our mission is to democratize access to professional-grade web utilities, making them available to everyone from seasoned developers to everyday users.</p>
          <p>We're committed to providing a comprehensive suite of online tools that are not only free but also fast, reliable, and user-friendly. Whether you're working on a complex development project or simply need to convert a file format, we've got you covered.</p>
        </div>
      </section>

      <!-- What We Offer Section -->
      <section class="about-section" aria-labelledby="offer-title">
        <h2 class="section-title" id="offer-title">What We Offer</h2>
        <div class="section-content">
          <p>WebToolsKit is your one-stop destination for a wide range of online utilities designed to streamline your workflow and save you time. Our carefully curated collection includes:</p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-font" aria-hidden="true"></i>
            </div>
            <h3 class="feature-title">Text Tools</h3>
            <p class="feature-description">Comprehensive text manipulation tools including case converters, word counters, and text generators.</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-image" aria-hidden="true"></i>
            </div>
            <h3 class="feature-title">Image Tools</h3>
            <p class="feature-description">Professional image editing utilities for resizing, converting, and optimizing images without quality loss.</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-calculator" aria-hidden="true"></i>
            </div>
            <h3 class="feature-title">Calculators</h3>
            <p class="feature-description">Advanced calculators for mathematical, financial, and scientific calculations with precision and ease.</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-exchange-alt"></i>
            </div>
            <h3 class="feature-title">Converters</h3>
            <p class="feature-description">Universal unit converters for length, weight, temperature, and digital data with accurate results.</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-code"></i>
            </div>
            <h3 class="feature-title">Developer Tools</h3>
            <p class="feature-description">Essential development utilities including JSON formatters, code validators, and debugging tools.</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-search"></i>
            </div>
            <h3 class="feature-title">SEO Tools</h3>
            <p class="feature-description">Powerful SEO utilities to optimize your website's performance and search engine visibility.</p>
          </div>
        </div>
      </section>

      <!-- Why Choose Us Section -->
      <section class="about-section">
        <h2 class="section-title">Why Choose WebToolsKit?</h2>
        <div class="section-content">
          <p>In a world filled with online tools, WebToolsKit stands out for several key reasons that make us the preferred choice for millions of users worldwide:</p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-bolt"></i>
            </div>
            <h3 class="feature-title">Lightning Fast</h3>
            <p class="feature-description">Our tools are optimized for speed, delivering results in seconds without compromising on quality or accuracy.</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <h3 class="feature-title">Secure & Private</h3>
            <p class="feature-description">Your data privacy is our priority. All processing happens locally in your browser with no data stored on our servers.</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-mobile-alt"></i>
            </div>
            <h3 class="feature-title">Mobile Friendly</h3>
            <p class="feature-description">Access all our tools seamlessly across devices - desktop, tablet, or mobile - with responsive design.</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-heart"></i>
            </div>
            <h3 class="feature-title">Always Free</h3>
            <p class="feature-description">No hidden fees, no subscriptions, no limits. All our tools are completely free to use forever.</p>
          </div>
        </div>
      </section>

      <!-- Stats Section -->
      <section class="about-section">
        <h2 class="section-title">Our Impact</h2>
        <div class="section-content">
          <p>Since our launch, WebToolsKit has become a trusted resource for users around the globe. Here are some numbers that showcase our growing community:</p>
        </div>

        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-number">50+</span>
            <span class="stat-label">Professional Tools</span>
          </div>

          <div class="stat-item">
            <span class="stat-number">1M+</span>
            <span class="stat-label">Monthly Users</span>
          </div>

          <div class="stat-item">
            <span class="stat-number">10M+</span>
            <span class="stat-label">Tools Used</span>
          </div>

          <div class="stat-item">
            <span class="stat-number">99.9%</span>
            <span class="stat-label">Uptime</span>
          </div>
        </div>
      </section>

      <!-- Our Commitment Section -->
      <section class="about-section">
        <h2 class="section-title">Our Commitment</h2>
        <div class="section-content">
          <p>We're dedicated to continuous improvement and innovation. Our team works tirelessly to ensure that WebToolsKit remains at the forefront of online utility tools, constantly adding new features and improving existing ones based on user feedback.</p>
          <p>We believe in transparency, reliability, and user-centric design. Every tool we create undergoes rigorous testing to ensure it meets our high standards for performance, accuracy, and ease of use.</p>
          <p>Our commitment extends beyond just providing tools - we're building a community where users can accomplish their tasks efficiently, learn new skills, and focus on what matters most to them.</p>
        </div>
      </section>

      <!-- Call to Action Section -->
      <section class="cta-section">
        <h2 class="cta-title">Ready to Get Started?</h2>
        <p class="cta-description">Join millions of users who trust WebToolsKit for their daily online tool needs. Explore our comprehensive collection and discover how we can help streamline your workflow.</p>
        <a href="/" class="cta-button">
          <i class="fas fa-rocket"></i>
          Explore Our Tools
        </a>
      </section>

      <!-- Contact Section -->
      <section class="about-section">
        <h2 class="section-title">Get in Touch</h2>
        <div class="section-content">
          <p>Have questions, suggestions, or feedback? We'd love to hear from you! Our team is always ready to help and continuously improve our services based on your input.</p>
          <p>Whether you're experiencing technical issues, have ideas for new tools, or simply want to share your success story with WebToolsKit, don't hesitate to reach out.</p>
          <div style="margin-top: 2rem; text-align: center;">
            <a href="https://www.webtoolskit.org/p/contact-us.html" class="cta-button" style="background-color: var(--primary-color); color: white;">
              <i class="fas fa-envelope"></i>
              Contact Us
            </a>
          </div>
        </div>
      </section>
    </div>
  </div>

  <!-- Dark Mode Toggle Script -->
  <script>
    // Dark mode functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Check for saved theme preference or default to light mode
      const savedTheme = localStorage.getItem('theme') || 'light';
      document.documentElement.setAttribute('data-theme', savedTheme);

      // Create theme toggle button if it doesn't exist
      if (!document.getElementById('theme-toggle')) {
        const themeToggle = document.createElement('button');
        themeToggle.id = 'theme-toggle';
        themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        themeToggle.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: var(--primary-color);
          color: white;
          border: none;
          border-radius: 50%;
          width: 50px;
          height: 50px;
          cursor: pointer;
          box-shadow: var(--shadow-lg);
          z-index: 1000;
          transition: var(--transition-base);
        `;

        themeToggle.addEventListener('click', function() {
          const currentTheme = document.documentElement.getAttribute('data-theme');
          const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

          document.documentElement.setAttribute('data-theme', newTheme);
          localStorage.setItem('theme', newTheme);

          // Update icon
          const icon = this.querySelector('i');
          icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        });

        document.body.appendChild(themeToggle);

        // Update icon based on current theme
        const icon = themeToggle.querySelector('i');
        icon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
      }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Add animation on scroll
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, observerOptions);

    // Observe all sections for animation
    document.addEventListener('DOMContentLoaded', function() {
      const sections = document.querySelectorAll('.about-section, .about-hero, .cta-section');
      sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(section);
      });
    });
  </script>
</body>
</html>
