<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parts Per Converter - Convert PPM, PPB, PPT & Percentage</title>
    <meta name="description" content="Instantly convert between concentration units like Parts Per Million (PPM), Parts Per Billion (PPB), Parts Per Trillion (PPT), and percentage. Free tool for science and engineering.">
    <meta name="keywords" content="parts per converter, ppm converter, ppb to ppm, convert ppm to percent, concentration converter, ppt calculator">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Parts Per Converter - PPM, PPB, PPT, Percent",
        "description": "Convert between various concentration units including Parts Per Million (PPM), Parts Per Billion (PPB), Parts Per Trillion (PPT), and percentage. A free, accurate online tool for scientific calculations.",
        "url": "https://www.webtoolskit.org/p/parts-per-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-25",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Parts Per Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Concentration Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the difference between parts per million and parts per billion?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The difference is a factor of 1,000. Parts per million (PPM) means 1 part in 1 million parts, while parts per billion (PPB) means 1 part in 1 billion parts. Therefore, a concentration of 1 PPB is 1,000 times smaller than a concentration of 1 PPM. For example, 1 PPM is equivalent to 1,000 PPB."
          }
        },
        {
          "@type": "Question",
          "name": "What is 1 ppb equal to?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "1 part per billion (ppb) is equivalent to 0.001 parts per million (ppm). In terms of mass concentration in water, it is often equivalent to 1 microgram per liter (μg/L)."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert parts into percentage?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert a 'parts-per' value to a percentage, you need to know that a percentage is 'parts per hundred'. The easiest way is to convert from Parts Per Million (PPM). The formula is: Percentage (%) = PPM / 10,000. For example, 500 PPM is 500 / 10,000 = 0.05%."
          }
        },
        {
          "@type": "Question",
          "name": "What does 10 parts per billion mean?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "10 parts per billion (ppb) means that for every billion units of a whole mixture or solution, there are 10 units of the substance you are measuring. For example, it could mean 10 milligrams of a contaminant in one billion milligrams (1,000 kg) of soil."
          }
        },
        {
          "@type": "Question",
          "name": "What percent is 100 parts per million?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert 100 PPM to percent, you divide by 10,000. The calculation is: 100 / 10,000 = 0.01. So, 100 parts per million is equal to 0.01%."
          }
        }
      ]
    }
    </script>

    <style>
        /* Parts Per Converter Widget - Simplified & Template Compatible */
        .parts-per-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .parts-per-converter-widget-container * { box-sizing: border-box; }

        .parts-per-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .parts-per-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .parts-per-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .parts-per-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .parts-per-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .parts-per-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .parts-per-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .parts-per-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .parts-per-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .parts-per-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .parts-per-converter-btn:hover { transform: translateY(-2px); }

        .parts-per-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .parts-per-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .parts-per-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .parts-per-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .parts-per-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .parts-per-converter-btn-success:hover {
            background-color: #059669;
        }

        .parts-per-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .parts-per-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .parts-per-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .parts-per-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .parts-per-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .parts-per-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .parts-per-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .parts-per-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .parts-per-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .parts-per-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .parts-per-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="volume-converter"] .parts-per-converter-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="weight-converter"] .parts-per-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="pressure-converter"] .parts-per-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }

        .parts-per-converter-related-tool-item:hover .parts-per-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="volume-converter"]:hover .parts-per-converter-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="weight-converter"]:hover .parts-per-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        a[href*="pressure-converter"]:hover .parts-per-converter-related-tool-icon { background: linear-gradient(145deg, #f56565, #ef4444); }
        
        .parts-per-converter-related-tool-item { box-shadow: none; border: none; }
        .parts-per-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .parts-per-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .parts-per-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .parts-per-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .parts-per-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .parts-per-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .parts-per-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .parts-per-converter-related-tool-item:hover .parts-per-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .parts-per-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .parts-per-converter-widget-title { font-size: 1.875rem; }
            .parts-per-converter-buttons { flex-direction: column; }
            .parts-per-converter-btn { flex: none; }
            .parts-per-converter-input-group { grid-template-columns: 1fr; }
            .parts-per-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .parts-per-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .parts-per-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .parts-per-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .parts-per-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .parts-per-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .parts-per-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .parts-per-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .parts-per-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .parts-per-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .parts-per-converter-output::selection { background-color: var(--primary-color); color: white; }
        .parts-per-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .parts-per-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="parts-per-converter-widget-container">
        <h1 class="parts-per-converter-widget-title">Parts-Per Converter</h1>
        <p class="parts-per-converter-widget-description">
            Easily convert between different 'parts-per' notations used to measure small concentrations, such as PPM, PPB, PPT, and percentage.
        </p>
        
        <div class="parts-per-converter-input-group">
            <label for="partsPerFromInput" class="parts-per-converter-label">From:</label>
            <input 
                type="number" 
                id="partsPerFromInput" 
                class="parts-per-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="partsPerFromUnit" class="parts-per-converter-select">
                <option value="pct">Percent (%)</option>
                <option value="ppt_thousand">Parts Per Thousand (ppt, ‰)</option>
                <option value="ppm" selected>Parts Per Million (ppm)</option>
                <option value="ppb">Parts Per Billion (ppb)</option>
                <option value="ppt_trillion">Parts Per Trillion (ppt)</option>
                <option value="ppq">Parts Per Quadrillion (ppq)</option>
            </select>
        </div>

        <div class="parts-per-converter-input-group">
            <label for="partsPerToInput" class="parts-per-converter-label">To:</label>
            <input 
                type="number" 
                id="partsPerToInput" 
                class="parts-per-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="partsPerToUnit" class="parts-per-converter-select">
                <option value="pct">Percent (%)</option>
                <option value="ppt_thousand">Parts Per Thousand (ppt, ‰)</option>
                <option value="ppm">Parts Per Million (ppm)</option>
                <option value="ppb" selected>Parts Per Billion (ppb)</option>
                <option value="ppt_trillion">Parts Per Trillion (ppt)</option>
                <option value="ppq">Parts Per Quadrillion (ppq)</option>
            </select>
        </div>

        <div class="parts-per-converter-buttons">
            <button class="parts-per-converter-btn parts-per-converter-btn-primary" onclick="PartsPerConverter.convert()">
                Convert Concentration
            </button>
            <button class="parts-per-converter-btn parts-per-converter-btn-secondary" onclick="PartsPerConverter.clear()">
                Clear All
            </button>
            <button class="parts-per-converter-btn parts-per-converter-btn-success" onclick="PartsPerConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="parts-per-converter-result">
            <h3 class="parts-per-converter-result-title">Conversion Result:</h3>
            <div class="parts-per-converter-output" id="partsPerConverterOutput">
                Your converted concentration will appear here...
            </div>
        </div>

        <div class="parts-per-converter-related-tools">
            <h3 class="parts-per-converter-related-tools-title">Related Tools</h3>
            <div class="parts-per-converter-related-tools-grid">
                <a href="/p/volume-converter.html" class="parts-per-converter-related-tool-item" rel="noopener">
                    <div class="parts-per-converter-related-tool-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="parts-per-converter-related-tool-name">Volume Converter</div>
                </a>

                <a href="/p/weight-converter.html" class="parts-per-converter-related-tool-item" rel="noopener">
                    <div class="parts-per-converter-related-tool-icon">
                        <i class="fas fa-weight"></i>
                    </div>
                    <div class="parts-per-converter-related-tool-name">Weight Converter</div>
                </a>

                <a href="/p/pressure-converter.html" class="parts-per-converter-related-tool-item" rel="noopener">
                    <div class="parts-per-converter-related-tool-icon">
                        <i class="fas fa-gauge-high"></i>
                    </div>
                    <div class="parts-per-converter-related-tool-name">Pressure Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Making Sense of Small Concentrations</h2>
            <p>In fields like environmental science, chemistry, and manufacturing, it's crucial to measure and describe incredibly small concentrations of substances. This is where "parts-per" notation comes in. Our <strong>Parts-Per Converter</strong> is a free online tool designed to help you quickly and accurately convert between these dimensionless quantities, including Parts Per Million (PPM), Parts Per Billion (PPB), Parts Per Trillion (PPT), and even percentage. This tool is invaluable for scientists, engineers, and students who need to work with these units confidently.</p>
            <p>Forget the confusion of multiplying or dividing by large powers of ten. This converter handles the complex math for you, ensuring your calculations for water quality reports, chemical solutions, or air quality monitoring are always precise.</p>

            <h3>How to Use the Parts-Per Converter</h3>
            <ol>
                <li><strong>Enter a Value:</strong> Type the concentration value you want to convert into the "From" input field.</li>
                <li><strong>Select Your Units:</strong> Choose the starting unit (e.g., PPM) and the unit you want to convert to (e.g., PPB).</li>
                <li><strong>Click Convert:</strong> Press the "Convert Concentration" button to get your accurate result instantly.</li>
                <li><strong>Copy or Reset:</strong> Use the "Copy Result" button to save the value or "Clear All" for a new calculation.</li>
            </ol>

            <h3>Frequently Asked Questions About Parts-Per Notation</h3>

            <h4>What is the difference between parts per million and parts per billion?</h4>
            <p>The difference is a factor of 1,000. Parts per million (PPM) means 1 part in 1 million parts, while parts per billion (PPB) means 1 part in 1 billion parts. Therefore, a concentration of 1 PPB is 1,000 times smaller than a concentration of 1 PPM. For example, 1 PPM is equivalent to 1,000 PPB.</p>

            <h4>What is 1 ppb equal to?</h4>
            <p>1 part per billion (ppb) is equivalent to 0.001 parts per million (ppm). In terms of mass concentration in water, it is often equivalent to 1 microgram per liter (μg/L).</p>

            <h4>How to convert parts into percentage?</h4>
            <p>To convert a 'parts-per' value to a percentage, you need to know that a percentage is 'parts per hundred'. The easiest way is to convert from Parts Per Million (PPM). The formula is: Percentage (%) = PPM / 10,000. For example, 500 PPM is 500 / 10,000 = 0.05%.</p>

            <h4>What does 10 parts per billion mean?</h4>
            <p>10 parts per billion (ppb) means that for every billion units of a whole mixture or solution, there are 10 units of the substance you are measuring. For example, it could mean 10 milligrams of a contaminant in one billion milligrams (1,000 kg) of soil.</p>

            <h4>What percent is 100 parts per million?</h4>
            <p>To convert 100 PPM to percent, you divide by 10,000. The calculation is: 100 / 10,000 = 0.01. So, 100 parts per million is equal to 0.01%.</p>
        </div>

        <div class="parts-per-converter-features">
            <h3 class="parts-per-converter-features-title">Key Features:</h3>
            <ul class="parts-per-converter-features-list">
                <li class="parts-per-converter-features-item" style="margin-bottom: 0.3em;">Converts PPM, PPB, PPT, PPQ</li>
                <li class="parts-per-converter-features-item" style="margin-bottom: 0.3em;">Includes Percent & Permille</li>
                <li class="parts-per-converter-features-item" style="margin-bottom: 0.3em;">Ideal for scientific calculations</li>
                <li class="parts-per-converter-features-item" style="margin-bottom: 0.3em;">High-precision results</li>
                <li class="parts-per-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="parts-per-converter-features-item" style="margin-bottom: 0.3em;">Simple, responsive interface</li>
                <li class="parts-per-converter-features-item">100% private and secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="parts-per-converter-notification" id="partsPerConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Parts Per Converter
        (function() {
            'use strict';

            // Conversion factors to a base unit of 'parts per one'
            const conversionFactors = {
                'pct': 1e-2,          // Percent
                'ppt_thousand': 1e-3, // Parts Per Thousand
                'ppm': 1e-6,          // Parts Per Million
                'ppb': 1e-9,          // Parts Per Billion
                'ppt_trillion': 1e-12,// Parts Per Trillion
                'ppq': 1e-15          // Parts Per Quadrillion
            };

            const elements = {
                fromInput: () => document.getElementById('partsPerFromInput'),
                toInput: () => document.getElementById('partsPerToInput'),
                fromUnit: () => document.getElementById('partsPerFromUnit'),
                toUnit: () => document.getElementById('partsPerToUnit'),
                output: () => document.getElementById('partsPerConverterOutput'),
                notification: () => document.getElementById('partsPerConverterNotification')
            };

            window.PartsPerConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to base unit first, then to target unit
                    const valueInBaseUnit = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInBaseUnit / conversionFactors[toUnit.value];

                    const formattedResult = this.formatResult(convertedValue);
                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (Math.abs(value) > 1e15 || (Math.abs(value) < 1e-15 && value !== 0)) {
                        return value.toExponential(6);
                    } else if (Number.isInteger(value)) {
                        return value.toString();
                    } else {
                        return parseFloat(value.toPrecision(15)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = {
                        'pct': '%',
                        'ppt_thousand': '‰',
                        'ppm': 'ppm',
                        'ppb': 'ppb',
                        'ppt_trillion': 'ppt',
                        'ppq': 'ppq'
                    };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted concentration will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        PartsPerConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>