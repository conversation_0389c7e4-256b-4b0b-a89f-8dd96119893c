<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Decimal to Binary Converter Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Decimal to Binary Converter - Convert Decimal Numbers Online",
        "description": "Convert decimal numbers to binary instantly. Free online tool with real-time conversion, fractional support, and one-click copying.",
        "url": "https://www.webtoolskit.org/p/decimal-to-binary.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Decimal to Binary Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Decimal to Binary" },
            { "@type": "CopyAction", "name": "Copy Binary Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert decimal to binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert decimal to binary, repeatedly divide the decimal number by 2 and record the remainders. Read the remainders from bottom to top to get the binary result. For example, 13 ÷ 2 = 6 remainder 1, 6 ÷ 2 = 3 remainder 0, 3 ÷ 2 = 1 remainder 1, 1 ÷ 2 = 0 remainder 1. Reading upward: 1101."
          }
        },
        {
          "@type": "Question",
          "name": "How to change 7 to binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert 7 to binary: 7 ÷ 2 = 3 remainder 1, 3 ÷ 2 = 1 remainder 1, 1 ÷ 2 = 0 remainder 1. Reading the remainders from bottom to top gives us 111. Therefore, 7 in binary is 111."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert 12.25 to binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert 12.25 to binary: First convert 12: 12 = 1100 in binary. Then convert 0.25: 0.25 × 2 = 0.5 (0), 0.5 × 2 = 1.0 (1). So 0.25 = 0.01 in binary. Therefore, 12.25 = 1100.01 in binary."
          }
        },
        {
          "@type": "Question",
          "name": "How do you convert decimals to binary tests?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert decimal fractions to binary, multiply the fractional part by 2 repeatedly. If the result is ≥1, write down 1 and subtract 1 from the result. If <1, write down 0. Continue until you get 0 or reach desired precision. For example, 0.375 × 2 = 0.75 (0), 0.75 × 2 = 1.5 (1), 0.5 × 2 = 1.0 (1). So 0.375 = 0.011 in binary."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert 0.1 into binary?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Converting 0.1 to binary: 0.1 × 2 = 0.2 (0), 0.2 × 2 = 0.4 (0), 0.4 × 2 = 0.8 (0), 0.8 × 2 = 1.6 (1), 0.6 × 2 = 1.2 (1), 0.2 × 2 = 0.4 (0)... This creates a repeating pattern. 0.1 in binary is approximately 0.000110011001100... (repeating)."
          }
        }
      ]
    }
    </script>


    <style>
        /* Decimal to Binary Widget - Simplified & Template Compatible */
        .decimal-to-binary-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .decimal-to-binary-widget-container * { box-sizing: border-box; }

        .decimal-to-binary-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .decimal-to-binary-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .decimal-to-binary-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .decimal-to-binary-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .decimal-to-binary-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .decimal-to-binary-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .decimal-to-binary-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .decimal-to-binary-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .decimal-to-binary-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .decimal-to-binary-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .decimal-to-binary-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .decimal-to-binary-btn:hover { transform: translateY(-2px); }

        .decimal-to-binary-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .decimal-to-binary-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .decimal-to-binary-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .decimal-to-binary-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .decimal-to-binary-btn-success {
            background-color: #10b981;
            color: white;
        }

        .decimal-to-binary-btn-success:hover {
            background-color: #059669;
        }

        .decimal-to-binary-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .decimal-to-binary-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .decimal-to-binary-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .decimal-to-binary-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .decimal-to-binary-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .decimal-to-binary-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .decimal-to-binary-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .decimal-to-binary-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .decimal-to-binary-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .decimal-to-binary-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .decimal-to-binary-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="binary-to-decimal"] .decimal-to-binary-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="decimal-to-hex"] .decimal-to-binary-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="decimal-to-octal"] .decimal-to-binary-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .decimal-to-binary-related-tool-item:hover .decimal-to-binary-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="binary-to-decimal"]:hover .decimal-to-binary-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="decimal-to-hex"]:hover .decimal-to-binary-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="decimal-to-octal"]:hover .decimal-to-binary-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .decimal-to-binary-related-tool-item { box-shadow: none; border: none; }
        .decimal-to-binary-related-tool-item:hover { box-shadow: none; border: none; }
        .decimal-to-binary-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .decimal-to-binary-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .decimal-to-binary-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .decimal-to-binary-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .decimal-to-binary-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .decimal-to-binary-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .decimal-to-binary-related-tool-item:hover .decimal-to-binary-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .decimal-to-binary-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .decimal-to-binary-widget-title { font-size: 1.875rem; }
            .decimal-to-binary-buttons { flex-direction: column; }
            .decimal-to-binary-btn { flex: none; }
            .decimal-to-binary-options { grid-template-columns: 1fr; }
            .decimal-to-binary-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .decimal-to-binary-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .decimal-to-binary-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .decimal-to-binary-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .decimal-to-binary-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .decimal-to-binary-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .decimal-to-binary-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .decimal-to-binary-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .decimal-to-binary-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .decimal-to-binary-checkbox:focus, .decimal-to-binary-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .decimal-to-binary-output::selection { background-color: var(--primary-color); color: white; }
        .decimal-to-binary-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .decimal-to-binary-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="decimal-to-binary-widget-container">
        <h1 class="decimal-to-binary-widget-title">Decimal to Binary Converter</h1>
        <p class="decimal-to-binary-widget-description">
            Convert decimal numbers to binary format instantly. Support for integers, decimals, and fractions with step-by-step conversion process.
        </p>
        
        <div class="decimal-to-binary-input-group">
            <label for="decimalToBinaryInput" class="decimal-to-binary-label">Enter decimal number:</label>
            <textarea 
                id="decimalToBinaryInput" 
                class="decimal-to-binary-textarea"
                placeholder="Enter decimal number here (e.g., 7, 12.25, 0.1)..."
                rows="4"
            ></textarea>
        </div>

        <div class="decimal-to-binary-options">
            <div class="decimal-to-binary-option">
                <input type="checkbox" id="decimalShowSteps" class="decimal-to-binary-checkbox">
                <label for="decimalShowSteps" class="decimal-to-binary-option-label">Show conversion steps</label>
            </div>
            <div class="decimal-to-binary-option">
                <input type="checkbox" id="decimalMultipleNumbers" class="decimal-to-binary-checkbox">
                <label for="decimalMultipleNumbers" class="decimal-to-binary-option-label">Convert multiple numbers</label>
            </div>
            <div class="decimal-to-binary-option">
                <input type="checkbox" id="decimalFractionPrecision" class="decimal-to-binary-checkbox" checked>
                <label for="decimalFractionPrecision" class="decimal-to-binary-option-label">Limit fraction precision</label>
            </div>
            <div class="decimal-to-binary-option">
                <input type="checkbox" id="decimalValidateInput" class="decimal-to-binary-checkbox" checked>
                <label for="decimalValidateInput" class="decimal-to-binary-option-label">Validate decimal input</label>
            </div>
        </div>

        <div class="decimal-to-binary-buttons">
            <button class="decimal-to-binary-btn decimal-to-binary-btn-primary" onclick="DecimalToBinaryConverter.convert()">
                Convert to Binary
            </button>
            <button class="decimal-to-binary-btn decimal-to-binary-btn-secondary" onclick="DecimalToBinaryConverter.clear()">
                Clear All
            </button>
            <button class="decimal-to-binary-btn decimal-to-binary-btn-success" onclick="DecimalToBinaryConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="decimal-to-binary-result">
            <h3 class="decimal-to-binary-result-title">Binary Result:</h3>
            <div class="decimal-to-binary-output" id="decimalToBinaryOutput">
                Your binary result will appear here...
            </div>
        </div>

        <div class="decimal-to-binary-related-tools">
            <h3 class="decimal-to-binary-related-tools-title">Related Tools</h3>
            <div class="decimal-to-binary-related-tools-grid">
                <a href="/p/binary-to-decimal.html" class="decimal-to-binary-related-tool-item" rel="noopener">
                    <div class="decimal-to-binary-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="decimal-to-binary-related-tool-name">Binary to Decimal</div>
                </a>

                <a href="/p/decimal-to-hex.html" class="decimal-to-binary-related-tool-item" rel="noopener">
                    <div class="decimal-to-binary-related-tool-icon">
                        <i class="fas fa-hashtag"></i>
                    </div>
                    <div class="decimal-to-binary-related-tool-name">Decimal to HEX</div>
                </a>

                <a href="/p/decimal-to-octal.html" class="decimal-to-binary-related-tool-item" rel="noopener">
                    <div class="decimal-to-binary-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="decimal-to-binary-related-tool-name">Decimal to Octal</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert Decimal to Binary Numbers with Our Free Online Tool</h2>
            <p>Converting decimal numbers to binary is a fundamental skill in computer science and digital mathematics. Our <strong>Decimal to Binary</strong> converter makes this process effortless, handling both integers and fractional decimal numbers with precision. Whether you're learning number systems, working on programming assignments, or need quick conversions for technical projects, this tool provides accurate results with optional step-by-step explanations.</p>
            <p>The conversion process uses division by 2 for integer parts and multiplication by 2 for fractional parts. Our tool handles complex decimal numbers, including those with repeating binary representations, and can show you exactly how each conversion is performed. This makes it perfect for educational purposes and professional development work.</p>
            
            <h3>How to Use the Decimal to Binary Converter</h3>
            <ol>
                <li><strong>Enter Decimal Number:</strong> Type your decimal number in the input field. You can enter integers, decimals, or multiple numbers separated by spaces.</li>
                <li><strong>Configure Options:</strong> Choose whether to show conversion steps, handle multiple numbers, limit fractional precision, or validate input format.</li>
                <li><strong>Convert and Copy:</strong> Click "Convert to Binary" to get your result. The tool displays the binary equivalent and optionally shows the complete conversion process.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Decimal to Binary</h3>
            
            <h4>How to convert decimal to binary?</h4>
            <p>To convert decimal to binary, repeatedly divide the decimal number by 2 and record the remainders. Read the remainders from bottom to top to get the binary result. For example, 13 ÷ 2 = 6 remainder 1, 6 ÷ 2 = 3 remainder 0, 3 ÷ 2 = 1 remainder 1, 1 ÷ 2 = 0 remainder 1. Reading upward: 1101.</p>
            
            <h4>How to change 7 to binary?</h4>
            <p>To convert 7 to binary: 7 ÷ 2 = 3 remainder 1, 3 ÷ 2 = 1 remainder 1, 1 ÷ 2 = 0 remainder 1. Reading the remainders from bottom to top gives us 111. Therefore, 7 in binary is 111.</p>
            
            <h4>How to convert 12.25 to binary?</h4>
            <p>To convert 12.25 to binary: First convert 12: 12 = 1100 in binary. Then convert 0.25: 0.25 × 2 = 0.5 (0), 0.5 × 2 = 1.0 (1). So 0.25 = 0.01 in binary. Therefore, 12.25 = 1100.01 in binary.</p>
            
            <h4>How do you convert decimals to binary tests?</h4>
            <p>To convert decimal fractions to binary, multiply the fractional part by 2 repeatedly. If the result is ≥1, write down 1 and subtract 1 from the result. If <1, write down 0. Continue until you get 0 or reach desired precision. For example, 0.375 × 2 = 0.75 (0), 0.75 × 2 = 1.5 (1), 0.5 × 2 = 1.0 (1). So 0.375 = 0.011 in binary.</p>
            
            <h4>How to convert 0.1 into binary?</h4>
            <p>Converting 0.1 to binary: 0.1 × 2 = 0.2 (0), 0.2 × 2 = 0.4 (0), 0.4 × 2 = 0.8 (0), 0.8 × 2 = 1.6 (1), 0.6 × 2 = 1.2 (1), 0.2 × 2 = 0.4 (0)... This creates a repeating pattern. 0.1 in binary is approximately 0.000110011001100... (repeating).</p>
        </div>


        <div class="decimal-to-binary-features">
            <h3 class="decimal-to-binary-features-title">Key Features:</h3>
            <ul class="decimal-to-binary-features-list">
                <li class="decimal-to-binary-features-item" style="margin-bottom: 0.3em;">Instant decimal-to-binary conversion</li>
                <li class="decimal-to-binary-features-item" style="margin-bottom: 0.3em;">Support for integers and decimals</li>
                <li class="decimal-to-binary-features-item" style="margin-bottom: 0.3em;">Step-by-step conversion process</li>
                <li class="decimal-to-binary-features-item" style="margin-bottom: 0.3em;">Multiple number processing</li>
                <li class="decimal-to-binary-features-item" style="margin-bottom: 0.3em;">Fractional precision control</li>
                <li class="decimal-to-binary-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="decimal-to-binary-features-item">Input validation and error handling</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="decimal-to-binary-notification" id="decimalToBinaryNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Decimal to Binary Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('decimalToBinaryInput'),
                output: () => document.getElementById('decimalToBinaryOutput'),
                notification: () => document.getElementById('decimalToBinaryNotification')
            };

            window.DecimalToBinaryConverter = {
                convert() {
                    const input = elements.input();
                    const output = elements.output();
                    const decimalInput = input.value;

                    if (!decimalInput.trim()) {
                        output.textContent = 'Please enter a decimal number to convert.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        showSteps: document.getElementById('decimalShowSteps').checked,
                        multipleNumbers: document.getElementById('decimalMultipleNumbers').checked,
                        fractionPrecision: document.getElementById('decimalFractionPrecision').checked,
                        validateInput: document.getElementById('decimalValidateInput').checked
                    };

                    try {
                        const result = this.processDecimal(decimalInput, options);
                        output.textContent = result;
                    } catch (error) {
                        output.textContent = `Error: ${error.message}`;
                        output.style.color = '#dc2626';
                    }
                },

                processDecimal(decimalInput, options) {
                    // Handle multiple numbers
                    let decimalNumbers = [];
                    if (options.multipleNumbers) {
                        decimalNumbers = decimalInput.split(/[\s,\n]+/).filter(num => num.trim().length > 0);
                    } else {
                        decimalNumbers = [decimalInput.trim()];
                    }

                    let results = [];
                    let details = [];

                    for (let decimalStr of decimalNumbers) {
                        let cleanDecimal = decimalStr.trim();
                        
                        // Validate decimal input
                        if (options.validateInput && !/^-?\d+(\.\d+)?$/.test(cleanDecimal)) {
                            throw new Error(`Invalid decimal number: ${cleanDecimal}`);
                        }

                        const decimal = parseFloat(cleanDecimal);
                        if (isNaN(decimal)) {
                            throw new Error(`Unable to parse: ${cleanDecimal}`);
                        }

                        // Handle negative numbers
                        const isNegative = decimal < 0;
                        const absoluteDecimal = Math.abs(decimal);

                        // Split into integer and fractional parts
                        const integerPart = Math.floor(absoluteDecimal);
                        const fractionalPart = absoluteDecimal - integerPart;

                        // Convert integer part
                        const integerBinary = integerPart.toString(2);
                        
                        // Convert fractional part
                        let fractionalBinary = '';
                        let currentFraction = fractionalPart;
                        let maxIterations = options.fractionPrecision ? 10 : 20;
                        let iterations = 0;

                        while (currentFraction > 0 && iterations < maxIterations) {
                            currentFraction *= 2;
                            if (currentFraction >= 1) {
                                fractionalBinary += '1';
                                currentFraction -= 1;
                            } else {
                                fractionalBinary += '0';
                            }
                            iterations++;
                        }

                        // Combine results
                        let binary = integerBinary;
                        if (fractionalBinary) {
                            binary += '.' + fractionalBinary;
                            if (currentFraction > 0) {
                                binary += '...'; // Indicate repeating/continuing
                            }
                        }

                        if (isNegative) {
                            binary = '-' + binary;
                        }

                        results.push(binary);

                        // Generate step-by-step explanation
                        if (options.showSteps) {
                            details.push(`Decimal: ${decimal}`);
                            
                            if (isNegative) {
                                details.push(`Working with absolute value: ${absoluteDecimal}`);
                            }

                            // Integer part steps
                            if (integerPart > 0) {
                                details.push(`Integer part (${integerPart}):`);
                                let tempInt = integerPart;
                                let divisionSteps = [];
                                while (tempInt > 0) {
                                    const remainder = tempInt % 2;
                                    tempInt = Math.floor(tempInt / 2);
                                    divisionSteps.push(`${tempInt * 2 + remainder} ÷ 2 = ${tempInt} remainder ${remainder}`);
                                }
                                details.push(divisionSteps.reverse().join(', '));
                                details.push(`Reading remainders upward: ${integerBinary}`);
                            }

                            // Fractional part steps
                            if (fractionalPart > 0) {
                                details.push(`Fractional part (${fractionalPart}):`);
                                let tempFraction = fractionalPart;
                                let multiplicationSteps = [];
                                let stepCount = 0;
                                while (tempFraction > 0 && stepCount < 5) {
                                    tempFraction *= 2;
                                    let bit = tempFraction >= 1 ? 1 : 0;
                                    multiplicationSteps.push(`${tempFraction >= 1 ? (tempFraction - 1) + 1 : tempFraction} × 2 = ${tempFraction} (${bit})`);
                                    if (tempFraction >= 1) tempFraction -= 1;
                                    stepCount++;
                                }
                                details.push(multiplicationSteps.join(', '));
                                details.push(`Fractional binary: 0.${fractionalBinary}`);
                            }

                            details.push(`Final result: ${binary}`);
                            details.push('---');
                        }
                    }

                    let output = '';
                    if (options.multipleNumbers) {
                        output = `Results: ${results.join(', ')}`;
                    } else {
                        output = `${results[0]}`;
                    }

                    if (options.showSteps && details.length > 0) {
                        output += `\n\nDetailed Steps:\n${details.join('\n')}`;
                    }

                    return output;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your binary result will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your binary result will appear here...', 'Please enter a decimal number to convert.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                const checkboxes = document.querySelectorAll('.decimal-to-binary-checkbox');

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        DecimalToBinaryConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>