<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flip Image - Free Online Image Flipper & Mirror Tool</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Image Flipper - Flip & Mirror Images Online",
        "description": "Flip images horizontally or vertically online. Create mirror images, rotate JPG, PNG, GIF files instantly. Works on computer and mobile devices.",
        "url": "https://www.webtoolskit.org/p/flip-image.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-21",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Image Flipper Tool",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "EditAction", "name": "Flip Image Horizontally" },
            { "@type": "EditAction", "name": "Flip Image Vertically" },
            { "@type": "DownloadAction", "name": "Download Flipped Image" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How can I flip the image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Upload your image using our flip tool, then choose to flip horizontally (mirror effect) or vertically. Preview the result and download the flipped image in your preferred format."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert an image into a mirror image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To create a mirror image, upload your photo and click 'Flip Horizontally'. This will flip the image from left to right, creating a mirror effect. You can then download the mirrored image."
          }
        },
        {
          "@type": "Question",
          "name": "How do I flip a JPEG or PNG file?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our tool supports all image formats including JPEG, PNG, GIF, WebP, and BMP. Simply upload your JPEG or PNG file, choose your flip direction, and download the result in the same or different format."
          }
        },
        {
          "@type": "Question",
          "name": "Which command is used to flip an image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use the 'Flip Horizontally' button to create a mirror image or 'Flip Vertically' button to flip upside down. You can also use 'Reset' to return to the original image at any time."
          }
        },
        {
          "@type": "Question",
          "name": "How do I flip an image on my computer or phone?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our online flip tool works on any device - computer, tablet, or phone. Just visit our website, upload your image, and use the flip buttons. The tool is fully responsive and touch-friendly for mobile devices."
          }
        }
      ]
    }
    </script>

    <style>
        /* Flip Image Widget - Matching design with other tools */
        .flip-image-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .flip-image-widget-container * { box-sizing: border-box; }

        .flip-image-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .flip-image-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .flip-image-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            background-color: var(--background-color-alt);
            text-align: center;
            cursor: pointer;
            transition: var(--transition-base);
            position: relative;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .flip-image-upload-area:hover,
        .flip-image-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .flip-image-file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .flip-image-upload-icon {
            font-size: 3rem;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-md);
        }

        .flip-image-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .flip-image-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.9rem;
        }

        .flip-image-preview {
            display: none;
            text-align: center;
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .flip-image-preview.show { display: block; }

        .flip-image-preview-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.125rem;
            font-weight: 600;
        }

        .flip-image-preview-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }

        .flip-image-preview-section {
            text-align: center;
        }

        .flip-image-preview-label {
            color: var(--text-color-light);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .flip-image-preview-image {
            max-width: 100%;
            max-height: 250px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            padding: var(--spacing-sm);
            display: block;
            margin: 0 auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .flip-image-preview-info {
            color: var(--text-color-light);
            font-size: 0.9rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .flip-image-info-item {
            background: var(--card-bg);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .flip-image-controls {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
            justify-content: center;
        }

        .flip-image-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            min-width: 140px;
        }

        .flip-image-btn:hover { transform: translateY(-2px); }

        .flip-image-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .flip-image-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .flip-image-btn-primary:hover:not(:disabled) {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .flip-image-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .flip-image-btn-secondary:hover:not(:disabled) {
            background-color: var(--border-color);
        }

        .flip-image-btn-success {
            background-color: #10b981;
            color: white;
        }

        .flip-image-btn-success:hover:not(:disabled) {
            background-color: #059669;
        }

        .flip-image-btn-warning {
            background-color: #f59e0b;
            color: white;
        }

        .flip-image-btn-warning:hover:not(:disabled) {
            background-color: #d97706;
        }

        .flip-image-download-options {
            display: none;
            margin-bottom: var(--spacing-xl);
        }

        .flip-image-download-options.show { display: block; }

        .flip-image-download-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.125rem;
            font-weight: 600;
            text-align: center;
        }

        .flip-image-download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--spacing-md);
        }

        .flip-image-download-btn {
            padding: var(--spacing-md);
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            color: var(--text-color);
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            transition: var(--transition-base);
            cursor: pointer;
            display: block;
        }

        .flip-image-download-btn:hover {
            border-color: var(--primary-color);
            background-color: var(--background-color-alt);
            transform: translateY(-2px);
            text-decoration: none;
            color: var(--primary-color);
        }

        .flip-image-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .flip-image-notification.show { transform: translateX(0); }
        
        .flip-image-notification.error {
            background-color: #ef4444;
        }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .flip-image-related-tool-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            margin: 0 auto var(--spacing-sm);
            transition: var(--transition-base);
            background: linear-gradient(145deg, #667eea, #764ba2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1), inset 0 1px 1px rgba(255, 255, 255, 0.1);
        }
        
        a[href*="image-to-base64"] .flip-image-related-tool-icon { background: linear-gradient(145deg, #ff6b6b, #ee5a52); }
        a[href*="base64-to-image"] .flip-image-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="ico-converter"] .flip-image-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }

        a[href*="image-to-base64"]:hover .flip-image-related-tool-icon { background: linear-gradient(145deg, #ff7979, #fd6c6c); }
        a[href*="base64-to-image"]:hover .flip-image-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="ico-converter"]:hover .flip-image-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        
        .flip-image-related-tool-item:hover .flip-image-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .flip-image-related-tool-item { box-shadow: none; border: none; }
        .flip-image-related-tool-item:hover { box-shadow: none; border: none; }
        .flip-image-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .flip-image-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .flip-image-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .flip-image-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .flip-image-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .flip-image-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .flip-image-related-tool-item:hover .flip-image-related-tool-name { color: var(--primary-color); }

        .flip-image-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .flip-image-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .flip-image-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .flip-image-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .flip-image-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .flip-image-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .flip-image-widget-title { font-size: 1.875rem; }
            .flip-image-controls { flex-direction: column; }
            .flip-image-btn { flex: none; }
            .flip-image-preview-container { grid-template-columns: 1fr; }
            .flip-image-upload-area { min-height: 140px; padding: var(--spacing-lg); }
            .flip-image-upload-icon { font-size: 2.5rem; }
            .flip-image-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .flip-image-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .flip-image-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .flip-image-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { 
            .flip-image-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } 
            .flip-image-download-grid { grid-template-columns: repeat(2, 1fr); }
        }

        @media (max-width: 480px) {
            .flip-image-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .flip-image-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .flip-image-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .flip-image-related-tool-name { font-size: 0.75rem; }
            .flip-image-download-grid { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .flip-image-upload-area:hover,
        [data-theme="dark"] .flip-image-upload-area.dragover { background-color: rgba(96, 165, 250, 0.1); }
        .flip-image-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
    </style>
</head>
<body>
    <div class="flip-image-widget-container">
        <h1 class="flip-image-widget-title">Flip Image</h1>
        <p class="flip-image-widget-description">
            Flip images horizontally or vertically online. Create mirror effects, flip JPEG, PNG, GIF files instantly. Works on any device.
        </p>
        
        <div class="flip-image-upload-area" id="uploadArea">
            <input type="file" class="flip-image-file-input" id="fileInput" accept="image/*">
            <div class="flip-image-upload-icon">🖼️</div>
            <div class="flip-image-upload-text">Drop an image here or click to browse</div>
            <div class="flip-image-upload-subtext">Supports PNG, JPG, GIF, WebP, SVG, BMP</div>
        </div>

        <div class="flip-image-preview" id="preview">
            <div class="flip-image-preview-title">Image Preview:</div>
            <div class="flip-image-preview-container">
                <div class="flip-image-preview-section">
                    <div class="flip-image-preview-label">Original</div>
                    <img class="flip-image-preview-image" id="originalImage" alt="Original image">
                </div>
                <div class="flip-image-preview-section">
                    <div class="flip-image-preview-label">Preview</div>
                    <img class="flip-image-preview-image" id="previewImage" alt="Flipped image preview">
                </div>
            </div>
            <div class="flip-image-preview-info" id="previewInfo"></div>
        </div>

        <div class="flip-image-controls">
            <button class="flip-image-btn flip-image-btn-primary" id="flipHorizontalBtn" onclick="FlipImageTool.flipHorizontal()" disabled>
                Flip Horizontally
            </button>
            <button class="flip-image-btn flip-image-btn-warning" id="flipVerticalBtn" onclick="FlipImageTool.flipVertical()" disabled>
                Flip Vertically
            </button>
            <button class="flip-image-btn flip-image-btn-secondary" onclick="FlipImageTool.reset()" disabled id="resetBtn">
                Reset
            </button>
            <button class="flip-image-btn flip-image-btn-secondary" onclick="FlipImageTool.clear()">
                Clear All
            </button>
        </div>

        <div class="flip-image-download-options" id="downloadOptions">
            <h3 class="flip-image-download-title">Download Flipped Image:</h3>
            <div class="flip-image-download-grid">
                <button class="flip-image-download-btn" onclick="FlipImageTool.download('png')">
                    Download PNG
                </button>
                <button class="flip-image-download-btn" onclick="FlipImageTool.download('jpg')">
                    Download JPG
                </button>
                <button class="flip-image-download-btn" onclick="FlipImageTool.download('original')">
                    Download Original Format
                </button>
            </div>
        </div>

        <div class="flip-image-related-tools">
            <h3 class="flip-image-related-tools-title">Related Tools</h3>
            <div class="flip-image-related-tools-grid">
                <a href="/p/image-to-base64.html" class="flip-image-related-tool-item" rel="noopener">
                    <div class="flip-image-related-tool-icon">
                        <i class="fas fa-file-code"></i>
                    </div>
                    <div class="flip-image-related-tool-name">Image to Base64</div>
                </a>

                <a href="/p/base64-to-image.html" class="flip-image-related-tool-item" rel="noopener">
                    <div class="flip-image-related-tool-icon">
                        <i class="fas fa-file-image"></i>
                    </div>
                    <div class="flip-image-related-tool-name">Base64 to Image</div>
                </a>

                <a href="/p/ico-converter.html" class="flip-image-related-tool-item" rel="noopener">
                    <div class="flip-image-related-tool-icon">
                        <i class="fas fa-icons"></i>
                    </div>
                    <div class="flip-image-related-tool-name">ICO Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>How to Flip Images Online</h2>
            <p>Flipping images is a common image editing task used to create mirror effects, correct orientation, or create artistic variations. Our online flip tool makes it easy to flip any image horizontally or vertically without installing software.</p>
            
            <h3>Simple 3-Step Process:</h3>
            <ol>
                <li><strong>Upload Image:</strong> Drag and drop your image file or click to browse. Supports all common formats including PNG, JPG, GIF, WebP, and SVG.</li>
                <li><strong>Choose Flip Direction:</strong> Click "Flip Horizontally" for mirror effect or "Flip Vertically" to flip upside down. Preview both original and flipped versions side by side.</li>
                <li><strong>Download Result:</strong> Download your flipped image in PNG, JPG, or the original format.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Image Flipping</h3>
            
            <h4>How can I flip the image?</h4>
            <p>Upload your image using our flip tool, then choose to flip horizontally (mirror effect) or vertically. Preview the result and download the flipped image in your preferred format. The process is instant and works directly in your browser.</p>
            
            <h4>How to convert an image into a mirror image?</h4>
            <p>To create a mirror image, upload your photo and click 'Flip Horizontally'. This will flip the image from left to right, creating a perfect mirror effect. This is useful for creating symmetrical designs or correcting images that appear backwards.</p>
            
            <h4>How do I flip a JPEG or PNG file?</h4>
            <p>Our tool supports all image formats including JPEG, PNG, GIF, WebP, and BMP. Simply upload your JPEG or PNG file, choose your flip direction (horizontal or vertical), and download the result in the same or different format. The quality is preserved during the flipping process.</p>
            
            <h4>Which command is used to flip an image?</h4>
            <p>Use the 'Flip Horizontally' button to create a mirror image or 'Flip Vertically' button to flip upside down. You can also use 'Reset' to return to the original image at any time, or combine both flips for a 180-degree rotation effect.</p>
            
            <h4>How do I flip an image on my computer or phone?</h4>
            <p>Our online flip tool works on any device - computer, tablet, or phone. Just visit our website, upload your image, and use the flip buttons. The tool is fully responsive and touch-friendly for mobile devices, making it easy to flip images wherever you are.</p>
            
            <h3>Common Use Cases for Image Flipping</h3>
            <ul>
                <li><strong>Mirror Effects:</strong> Create artistic mirror images for design projects</li>
                <li><strong>Photo Correction:</strong> Fix images that appear backwards or upside down</li>
                <li><strong>Social Media:</strong> Create unique variations of photos for posts</li>
                <li><strong>Design Work:</strong> Flip logos, icons, or graphics for different layouts</li>
                <li><strong>Digital Art:</strong> Create symmetrical designs and patterns</li>
                <li><strong>Web Development:</strong> Prepare images for different website layouts</li>
                <li><strong>Print Materials:</strong> Correct image orientation for printing</li>
            </ul>

            <h3>Supported Image Formats</h3>
            <p>Our flip tool supports all major image formats:</p>
            <ul>
                <li><strong>JPEG/JPG:</strong> Perfect for photographs and complex images</li>
                <li><strong>PNG:</strong> Ideal for images with transparency and graphics</li>
                <li><strong>GIF:</strong> Great for simple graphics and animations</li>
                <li><strong>WebP:</strong> Modern format with excellent compression</li>
                <li><strong>SVG:</strong> Vector graphics that scale perfectly</li>
                <li><strong>BMP:</strong> Uncompressed bitmap format</li>
            </ul>

            <h3>Flip Types Explained</h3>
            <ul>
                <li><strong>Horizontal Flip:</strong> Mirrors the image left to right, like looking in a mirror</li>
                <li><strong>Vertical Flip:</strong> Flips the image top to bottom, turning it upside down</li>
                <li><strong>Combined Flip:</strong> Apply both flips for a 180-degree rotation effect</li>
            </ul>
            
            <h3>Tips for Best Results</h3>
            <ul>
                <li>Use PNG format to preserve transparency when flipping logos or icons</li>
                <li>For photos, JPEG format maintains good quality with smaller file sizes</li>
                <li>Preview your flipped image before downloading to ensure it looks correct</li>
                <li>Use the reset function to quickly return to the original image</li>
                <li>Try both horizontal and vertical flips to find the best artistic effect</li>
            </ul>
        </div>

        <div class="flip-image-features">
            <h3 class="flip-image-features-title">Key Features:</h3>
            <ul class="flip-image-features-list">
                <li class="flip-image-features-item" style="margin-bottom: 0.3em;">Instant image flipping</li>
                <li class="flip-image-features-item" style="margin-bottom: 0.3em;">Horizontal & vertical flip options</li>
                <li class="flip-image-features-item" style="margin-bottom: 0.3em;">Side-by-side preview</li>
                <li class="flip-image-features-item" style="margin-bottom: 0.3em;">Multiple output formats</li>
                <li class="flip-image-features-item" style="margin-bottom: 0.3em;">Drag & drop interface</li>
                <li class="flip-image-features-item" style="margin-bottom: 0.3em;">Mobile-friendly design</li>
                <li class="flip-image-features-item" style="margin-bottom: 0.3em;">No quality loss</li>
                <li class="flip-image-features-item">Works offline after loading</li>
            </ul>
        </div>
    </div>

    <!-- Notification -->
    <div class="flip-image-notification" id="notification">
        Success!
    </div>

    <script>
        // Flip Image Tool - Self-contained IIFE
        (function() {
            'use strict';

            let originalImageData = null;
            let currentImageData = null;
            let originalFileName = '';
            let originalFileType = '';
            let isFlippedHorizontal = false;
            let isFlippedVertical = false;

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                preview: () => document.getElementById('preview'),
                originalImage: () => document.getElementById('originalImage'),
                previewImage: () => document.getElementById('previewImage'),
                previewInfo: () => document.getElementById('previewInfo'),
                flipHorizontalBtn: () => document.getElementById('flipHorizontalBtn'),
                flipVerticalBtn: () => document.getElementById('flipVerticalBtn'),
                resetBtn: () => document.getElementById('resetBtn'),
                downloadOptions: () => document.getElementById('downloadOptions'),
                notification: () => document.getElementById('notification')
            };

            window.FlipImageTool = {
                loadImage(file) {
                    originalFileName = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;
                    originalFileType = file.type;
                    
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        originalImageData = e.target.result;
                        currentImageData = originalImageData;
                        
                        elements.originalImage().src = originalImageData;
                        elements.previewImage().src = originalImageData;
                        
                        // Get image dimensions
                        const img = new Image();
                        img.onload = () => {
                            elements.previewInfo().innerHTML = `
                                <div class="flip-image-info-item"><strong>Name:</strong> ${file.name}</div>
                                <div class="flip-image-info-item"><strong>Size:</strong> ${(file.size / 1024).toFixed(1)} KB</div>
                                <div class="flip-image-info-item"><strong>Dimensions:</strong> ${img.width} × ${img.height}px</div>
                                <div class="flip-image-info-item"><strong>Type:</strong> ${file.type}</div>
                            `;
                        };
                        img.src = originalImageData;
                        
                        elements.preview().classList.add('show');
                        elements.flipHorizontalBtn().disabled = false;
                        elements.flipVerticalBtn().disabled = false;
                        elements.resetBtn().disabled = false;
                        elements.downloadOptions().classList.add('show');
                        
                        // Reset flip states
                        isFlippedHorizontal = false;
                        isFlippedVertical = false;
                    };
                    reader.readAsDataURL(file);
                },

                flipHorizontal() {
                    this.applyFlip(!isFlippedHorizontal, isFlippedVertical);
                    isFlippedHorizontal = !isFlippedHorizontal;
                    this.showNotification(`Image ${isFlippedHorizontal ? 'flipped' : 'restored'} horizontally`);
                },

                flipVertical() {
                    this.applyFlip(isFlippedHorizontal, !isFlippedVertical);
                    isFlippedVertical = !isFlippedVertical;
                    this.showNotification(`Image ${isFlippedVertical ? 'flipped' : 'restored'} vertically`);
                },

                applyFlip(horizontal, vertical) {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const img = new Image();
                    
                    img.onload = () => {
                        canvas.width = img.width;
                        canvas.height = img.height;
                        
                        ctx.save();
                        
                        // Apply transformations
                        if (horizontal && vertical) {
                            ctx.scale(-1, -1);
                            ctx.translate(-canvas.width, -canvas.height);
                        } else if (horizontal) {
                            ctx.scale(-1, 1);
                            ctx.translate(-canvas.width, 0);
                        } else if (vertical) {
                            ctx.scale(1, -1);
                            ctx.translate(0, -canvas.height);
                        }
                        
                        ctx.drawImage(img, 0, 0);
                        ctx.restore();
                        
                        currentImageData = canvas.toDataURL(originalFileType);
                        elements.previewImage().src = currentImageData;
                    };
                    
                    img.src = originalImageData;
                },

                reset() {
                    currentImageData = originalImageData;
                    elements.previewImage().src = originalImageData;
                    isFlippedHorizontal = false;
                    isFlippedVertical = false;
                    this.showNotification('Image reset to original');
                },

                download(format) {
                    if (!currentImageData) {
                        this.showNotification('Please select an image first', 'error');
                        return;
                    }
                    
                    let fileName = originalFileName;
                    let mimeType = originalFileType;
                    
                    if (format === 'png') {
                        fileName += '_flipped.png';
                        mimeType = 'image/png';
                    } else if (format === 'jpg') {
                        fileName += '_flipped.jpg';
                        mimeType = 'image/jpeg';
                    } else {
                        const ext = originalFileType.split('/')[1] || 'png';
                        fileName += `_flipped.${ext}`;
                    }
                    
                    if (format === 'original' || mimeType === originalFileType) {
                        this.downloadDirect(fileName);
                    } else {
                        this.downloadAsFormat(fileName, mimeType, format);
                    }
                },

                downloadDirect(fileName) {
                    const link = document.createElement('a');
                    link.href = currentImageData;
                    link.download = fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    this.showNotification('Image downloaded successfully');
                },

                downloadAsFormat(fileName, mimeType, format) {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const img = new Image();
                    
                    img.onload = () => {
                        canvas.width = img.width;
                        canvas.height = img.height;
                        
                        // Add white background for JPG
                        if (format === 'jpg') {
                            ctx.fillStyle = 'white';
                            ctx.fillRect(0, 0, canvas.width, canvas.height);
                        }
                        
                        ctx.drawImage(img, 0, 0);
                        
                        canvas.toBlob((blob) => {
                            const url = URL.createObjectURL(blob);
                            const link = document.createElement('a');
                            link.href = url;
                            link.download = fileName;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(url);
                            
                            this.showNotification(`Downloaded as ${format.toUpperCase()}`);
                        }, mimeType, format === 'jpg' ? 0.9 : undefined);
                    };
                    
                    img.src = currentImageData;
                },

                clear() {
                    elements.fileInput().value = '';
                    elements.preview().classList.remove('show');
                    elements.downloadOptions().classList.remove('show');
                    elements.flipHorizontalBtn().disabled = true;
                    elements.flipVerticalBtn().disabled = true;
                    elements.resetBtn().disabled = true;
                    
                    originalImageData = null;
                    currentImageData = null;
                    originalFileName = '';
                    originalFileType = '';
                    isFlippedHorizontal = false;
                    isFlippedVertical = false;
                },

                showNotification(message, type = 'success') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.remove('show', 'error');
                    if (type === 'error') notification.classList.add('error');
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 3000);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // File input change
                fileInput.addEventListener('change', function() {
                    if (this.files.length > 0 && this.files[0].type.startsWith('image/')) {
                        FlipImageTool.loadImage(this.files[0]);
                    }
                });

                // Drag and drop
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    
                    const files = e.dataTransfer.files;
                    if (files.length > 0 && files[0].type.startsWith('image/')) {
                        fileInput.files = files;
                        FlipImageTool.loadImage(files[0]);
                    }
                });

                // Click to upload
                uploadArea.addEventListener('click', function() {
                    fileInput.click();
                });
            });
        })();
    </script>
</body>
</html>