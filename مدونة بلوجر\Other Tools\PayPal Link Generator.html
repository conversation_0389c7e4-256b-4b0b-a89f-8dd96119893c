<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free PayPal Link Generator - Create PayPal Payment Links Online</title>
    <meta name="description" content="Generate PayPal payment links instantly with our free PayPal Link Generator. Create custom PayPal.me links, donation links, and business payment links with amounts and descriptions.">
    <meta name="keywords" content="paypal link generator, paypal.me link, paypal payment link, paypal donation link, paypal business link, create paypal link">
    <link rel="canonical" href="https://www.webtoolskit.org/p/paypal-link-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free PayPal Link Generator - Create PayPal Payment Links Online",
        "description": "Generate PayPal payment links instantly with our free PayPal Link Generator. Create custom PayPal.me links, donation links, and business payment links with amounts and descriptions.",
        "url": "https://www.webtoolskit.org/p/paypal-link-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "PayPal Link Generator",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "PayPal payment link creation",
                "Custom amount specification",
                "Business payment links",
                "Donation link generation",
                "PayPal.me link customization"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate PayPal Link" },
            { "@type": "CopyAction", "name": "Copy PayPal Link" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I create a PayPal payment link?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Enter your PayPal email or username, specify the amount and currency, add a description, and click 'Generate PayPal Link'. Our tool will create a custom PayPal payment link that customers can use to send you money directly."
          }
        },
        {
          "@type": "Question",
          "name": "Can I generate PayPal links for donations?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can create PayPal donation links by leaving the amount field empty or setting it to 0. This allows donors to choose their own donation amount. Add a description like 'Support our cause' to personalize the donation request."
          }
        },
        {
          "@type": "Question",
          "name": "How do I make a PayPal link for my business?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use your business PayPal email address, set specific amounts for products or services, and include professional descriptions. The generated links can be used on your website, in emails, or on social media to accept business payments."
          }
        },
        {
          "@type": "Question",
          "name": "What's the difference between PayPal.me and payment links?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "PayPal.me links are personalized URLs (paypal.me/YourName) for general payments. Payment links are more specific and can include preset amounts, currencies, and descriptions for particular transactions or products."
          }
        },
        {
          "@type": "Question",
          "name": "Can I customize PayPal payment links with amounts?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can specify exact amounts, choose different currencies (USD, EUR, GBP, etc.), and add custom descriptions. This makes the payment process smoother for customers as they see the amount and purpose before completing the transaction."
          }
        }
      ]
    }
    </script>

    <style>
        /* PayPal Link Generator Widget - Simplified & Template Compatible */
        .paypal-link-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .paypal-link-widget-container * { box-sizing: border-box; }

        .paypal-link-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .paypal-link-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .paypal-link-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .paypal-link-field {
            display: flex;
            flex-direction: column;
        }

        .paypal-link-field-row {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-md);
        }

        .paypal-link-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .paypal-link-input,
        .paypal-link-select,
        .paypal-link-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .paypal-link-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .paypal-link-input:focus,
        .paypal-link-select:focus,
        .paypal-link-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .paypal-link-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .paypal-link-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .paypal-link-btn:hover { transform: translateY(-2px); }

        .paypal-link-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .paypal-link-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .paypal-link-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .paypal-link-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .paypal-link-btn-success {
            background-color: #10b981;
            color: white;
        }

        .paypal-link-btn-success:hover {
            background-color: #059669;
        }

        .paypal-link-btn-paypal {
            background-color: #0070ba;
            color: white;
        }

        .paypal-link-btn-paypal:hover {
            background-color: #005ea6;
        }

        .paypal-link-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .paypal-link-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .paypal-link-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .paypal-link-preview {
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
        }

        .paypal-link-preview-title {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .paypal-link-preview-content {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .paypal-link-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .paypal-link-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        @media (max-width: 768px) {
            .paypal-link-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .paypal-link-widget-title { font-size: 1.875rem; }
            .paypal-link-buttons { flex-direction: column; }
            .paypal-link-btn { flex: none; }
            .paypal-link-field-row { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .paypal-link-input:focus,
        [data-theme="dark"] .paypal-link-select:focus,
        [data-theme="dark"] .paypal-link-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .paypal-link-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .paypal-link-output::selection { background-color: var(--primary-color); color: white; }

        .paypal-link-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="whatsapp-link-generator"] .paypal-link-related-tool-icon { background: linear-gradient(145deg, #25D366, #128C7E); }
        a[href*="password-generator"] .paypal-link-related-tool-icon { background: linear-gradient(145deg, #0EA5E9, #0284C7); }
        a[href*="base64-encode"] .paypal-link-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }

        .paypal-link-related-tool-item:hover .paypal-link-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="whatsapp-link-generator"]:hover .paypal-link-related-tool-icon { background: linear-gradient(145deg, #34d399, #10b981); }
        a[href*="password-generator"]:hover .paypal-link-related-tool-icon { background: linear-gradient(145deg, #38bdf8, #0ea5e9); }
        a[href*="base64-encode"]:hover .paypal-link-related-tool-icon { background: linear-gradient(145deg, #fbbf24, #f59e0b); }

        .paypal-link-related-tool-item { box-shadow: none; border: none; }
        .paypal-link-related-tool-item:hover { box-shadow: none; border: none; }
        .paypal-link-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .paypal-link-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .paypal-link-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .paypal-link-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .paypal-link-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .paypal-link-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .paypal-link-related-tool-item:hover .paypal-link-related-tool-name { color: var(--primary-color); }

        .paypal-link-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .paypal-link-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .paypal-link-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .paypal-link-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .paypal-link-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .paypal-link-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .paypal-link-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .paypal-link-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .paypal-link-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .paypal-link-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .paypal-link-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .paypal-link-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .paypal-link-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .paypal-link-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="paypal-link-widget-container">
        <h1 class="paypal-link-widget-title">PayPal Link Generator</h1>
        <p class="paypal-link-widget-description">
            Create custom PayPal payment links with amounts, descriptions, and currency options. Perfect for businesses, freelancers, and donations.
        </p>

        <form class="paypal-link-form">
            <div class="paypal-link-field">
                <label for="paypalEmail" class="paypal-link-label">PayPal Email or Username:</label>
                <input
                    type="email"
                    id="paypalEmail"
                    class="paypal-link-input"
                    placeholder="<EMAIL> or YourPayPalUsername"
                />
            </div>

            <div class="paypal-link-field-row">
                <div class="paypal-link-field">
                    <label for="paymentAmount" class="paypal-link-label">Amount (optional):</label>
                    <input
                        type="number"
                        id="paymentAmount"
                        class="paypal-link-input"
                        placeholder="0.00"
                        step="0.01"
                        min="0"
                    />
                </div>
                <div class="paypal-link-field">
                    <label for="paymentCurrency" class="paypal-link-label">Currency:</label>
                    <select id="paymentCurrency" class="paypal-link-select">
                        <option value="USD">USD - US Dollar</option>
                        <option value="EUR">EUR - Euro</option>
                        <option value="GBP">GBP - British Pound</option>
                        <option value="CAD">CAD - Canadian Dollar</option>
                        <option value="AUD">AUD - Australian Dollar</option>
                        <option value="JPY">JPY - Japanese Yen</option>
                    </select>
                </div>
            </div>

            <div class="paypal-link-field">
                <label for="paymentDescription" class="paypal-link-label">Description (optional):</label>
                <textarea
                    id="paymentDescription"
                    class="paypal-link-textarea"
                    placeholder="Payment for services, donation, etc."
                ></textarea>
            </div>
        </form>

        <div class="paypal-link-buttons">
            <button class="paypal-link-btn paypal-link-btn-primary" onclick="PayPalLinkGenerator.generate()">
                Generate PayPal Link
            </button>
            <button class="paypal-link-btn paypal-link-btn-secondary" onclick="PayPalLinkGenerator.clear()">
                Clear All
            </button>
            <button class="paypal-link-btn paypal-link-btn-success" onclick="PayPalLinkGenerator.copy()">
                Copy Link
            </button>
            <button class="paypal-link-btn paypal-link-btn-paypal" onclick="PayPalLinkGenerator.test()">
                Test Link
            </button>
        </div>

        <div class="paypal-link-result">
            <h3 class="paypal-link-result-title">Generated PayPal Link:</h3>
            <div class="paypal-link-output" id="paypalOutput">Click "Generate PayPal Link" to create your payment link...</div>

            <div class="paypal-link-preview" id="linkPreview" style="display: none;">
                <div class="paypal-link-preview-title">Payment Details:</div>
                <div class="paypal-link-preview-content" id="previewContent"></div>
            </div>
        </div>

        <div class="paypal-link-related-tools">
            <h3 class="paypal-link-related-tools-title">Related Tools</h3>
            <div class="paypal-link-related-tools-grid">
                <a href="/p/whatsapp-link-generator.html" class="paypal-link-related-tool-item" rel="noopener">
                    <div class="paypal-link-related-tool-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="paypal-link-related-tool-name">WhatsApp Link Generator</div>
                </a>

                <a href="/p/password-generator.html" class="paypal-link-related-tool-item" rel="noopener">
                    <div class="paypal-link-related-tool-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="paypal-link-related-tool-name">Password Generator</div>
                </a>

                <a href="/p/base64-encode.html" class="paypal-link-related-tool-item" rel="noopener">
                    <div class="paypal-link-related-tool-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="paypal-link-related-tool-name">Base64 Encode</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional PayPal Link Generator for Business Payments</h2>
            <p>Our <strong>PayPal Link Generator</strong> creates custom payment links that streamline the payment process for your customers. Whether you're running a business, freelancing, or collecting donations, these PayPal links eliminate the need for complex checkout processes and enable direct payments with preset amounts and descriptions.</p>
            <p>Perfect for small businesses, freelancers, content creators, and non-profit organizations who need quick and professional payment solutions. Generate PayPal.me links or custom payment links that can be shared via email, social media, websites, or messaging apps to accept payments instantly.</p>

            <h3>How to Use the PayPal Link Generator</h3>
            <ol>
                <li><strong>Enter PayPal Details:</strong> Input your PayPal email address or username that will receive payments.</li>
                <li><strong>Set Amount & Currency:</strong> Specify the payment amount and select your preferred currency (optional).</li>
                <li><strong>Add Description:</strong> Include a description of what the payment is for (optional).</li>
                <li><strong>Generate Link:</strong> Click "Generate PayPal Link" to create your custom payment link.</li>
            </ol>

            <h3>Frequently Asked Questions About PayPal Link Generation</h3>

            <h4>How do I create a PayPal payment link?</h4>
            <p>Enter your PayPal email or username, specify the amount and currency, add a description, and click 'Generate PayPal Link'. Our tool will create a custom PayPal payment link that customers can use to send you money directly.</p>

            <h4>Can I generate PayPal links for donations?</h4>
            <p>Yes, you can create PayPal donation links by leaving the amount field empty or setting it to 0. This allows donors to choose their own donation amount. Add a description like 'Support our cause' to personalize the donation request.</p>

            <h4>How do I make a PayPal link for my business?</h4>
            <p>Use your business PayPal email address, set specific amounts for products or services, and include professional descriptions. The generated links can be used on your website, in emails, or on social media to accept business payments.</p>

            <h4>What's the difference between PayPal.me and payment links?</h4>
            <p>PayPal.me links are personalized URLs (paypal.me/YourName) for general payments. Payment links are more specific and can include preset amounts, currencies, and descriptions for particular transactions or products.</p>

            <h4>Can I customize PayPal payment links with amounts?</h4>
            <p>Yes, you can specify exact amounts, choose different currencies (USD, EUR, GBP, etc.), and add custom descriptions. This makes the payment process smoother for customers as they see the amount and purpose before completing the transaction.</p>
        </div>

        <div class="paypal-link-features">
            <h3 class="paypal-link-features-title">Key Features:</h3>
            <ul class="paypal-link-features-list">
                <li class="paypal-link-features-item" style="margin-bottom: 0.3em;">Custom Payment Link Creation</li>
                <li class="paypal-link-features-item" style="margin-bottom: 0.3em;">Multiple Currency Support</li>
                <li class="paypal-link-features-item" style="margin-bottom: 0.3em;">Preset Amount Configuration</li>
                <li class="paypal-link-features-item" style="margin-bottom: 0.3em;">Payment Description Fields</li>
                <li class="paypal-link-features-item" style="margin-bottom: 0.3em;">Business Payment Solutions</li>
                <li class="paypal-link-features-item" style="margin-bottom: 0.3em;">Donation Link Generation</li>
                <li class="paypal-link-features-item">100% Free and Instant</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="paypal-link-notification" id="paypalNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                emailInput: () => document.getElementById('paypalEmail'),
                amountInput: () => document.getElementById('paymentAmount'),
                currencySelect: () => document.getElementById('paymentCurrency'),
                descriptionInput: () => document.getElementById('paymentDescription'),
                output: () => document.getElementById('paypalOutput'),
                preview: () => document.getElementById('linkPreview'),
                previewContent: () => document.getElementById('previewContent'),
                notification: () => document.getElementById('paypalNotification')
            };

            function validateEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email) || email.length > 0; // Allow usernames too
            }

            function generatePayPalLink(email, amount, currency, description) {
                let link = 'https://www.paypal.com/paypalme/';

                // Handle email vs username
                if (email.includes('@')) {
                    // For email addresses, we need to use the standard PayPal payment URL
                    link = `https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&business=${encodeURIComponent(email)}`;

                    if (amount && parseFloat(amount) > 0) {
                        link += `&amount=${encodeURIComponent(amount)}&currency_code=${encodeURIComponent(currency)}`;
                    }

                    if (description && description.trim()) {
                        link += `&item_name=${encodeURIComponent(description.trim())}`;
                    }
                } else {
                    // For usernames, use PayPal.me format
                    link += encodeURIComponent(email);

                    if (amount && parseFloat(amount) > 0) {
                        link += `/${encodeURIComponent(amount)}${encodeURIComponent(currency)}`;
                    }
                }

                return link;
            }

            function updatePreview(email, amount, currency, description) {
                const preview = elements.preview();
                const previewContent = elements.previewContent();

                if (email.trim()) {
                    let previewText = `Recipient: ${email}`;

                    if (amount && parseFloat(amount) > 0) {
                        previewText += `\nAmount: ${amount} ${currency}`;
                    } else {
                        previewText += `\nAmount: Customer chooses amount`;
                    }

                    if (description && description.trim()) {
                        previewText += `\nDescription: "${description.trim()}"`;
                    } else {
                        previewText += `\nDescription: (No description)`;
                    }

                    previewContent.textContent = previewText;
                    preview.style.display = 'block';
                } else {
                    preview.style.display = 'none';
                }
            }

            window.PayPalLinkGenerator = {
                generate() {
                    const email = elements.emailInput().value.trim();
                    const amount = elements.amountInput().value.trim();
                    const currency = elements.currencySelect().value;
                    const description = elements.descriptionInput().value;
                    const output = elements.output();

                    if (!email) {
                        output.textContent = 'Please enter a PayPal email address or username to generate a payment link.';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                        return;
                    }

                    if (!validateEmail(email) && !email.includes('@')) {
                        // Basic validation for username (no spaces, reasonable length)
                        if (email.includes(' ') || email.length < 3) {
                            output.textContent = 'Please enter a valid PayPal email address or username.';
                            output.style.color = '#dc2626';
                            elements.preview().style.display = 'none';
                            return;
                        }
                    }

                    if (amount && (isNaN(parseFloat(amount)) || parseFloat(amount) < 0)) {
                        output.textContent = 'Please enter a valid amount (or leave empty for flexible amount).';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                        return;
                    }

                    try {
                        const paypalLink = generatePayPalLink(email, amount, currency, description);
                        output.textContent = paypalLink;
                        output.style.color = '';
                        updatePreview(email, amount, currency, description);
                    } catch (error) {
                        output.textContent = 'Error generating PayPal link. Please check your input.';
                        output.style.color = '#dc2626';
                        elements.preview().style.display = 'none';
                    }
                },

                clear() {
                    elements.emailInput().value = '';
                    elements.amountInput().value = '';
                    elements.currencySelect().value = 'USD';
                    elements.descriptionInput().value = '';
                    elements.output().textContent = 'Click "Generate PayPal Link" to create your payment link...';
                    elements.output().style.color = '';
                    elements.preview().style.display = 'none';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Click "Generate PayPal Link" to create your payment link...' || text.includes('Please enter') || text.includes('Error')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                test() {
                    const text = elements.output().textContent;
                    if (text === 'Click "Generate PayPal Link" to create your payment link...' || text.includes('Please enter') || text.includes('Error')) {
                        alert('Please generate a PayPal link first.');
                        return;
                    }

                    // Open the PayPal link in a new tab
                    window.open(text, '_blank', 'noopener,noreferrer');
                    this.showNotification('Link opened!');
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message = '✓ Copied to clipboard!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Real-time preview updates
                const inputs = [elements.emailInput(), elements.amountInput(), elements.descriptionInput()];
                const currencySelect = elements.currencySelect();

                inputs.forEach(input => {
                    input.addEventListener('input', function() {
                        const email = elements.emailInput().value.trim();
                        const amount = elements.amountInput().value.trim();
                        const currency = elements.currencySelect().value;
                        const description = elements.descriptionInput().value;

                        if (email) {
                            updatePreview(email, amount, currency, description);
                        } else {
                            elements.preview().style.display = 'none';
                        }
                    });
                });

                currencySelect.addEventListener('change', function() {
                    const email = elements.emailInput().value.trim();
                    if (email) {
                        const amount = elements.amountInput().value.trim();
                        const currency = this.value;
                        const description = elements.descriptionInput().value;
                        updatePreview(email, amount, currency, description);
                    }
                });

                // Enter key shortcuts
                elements.emailInput().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        PayPalLinkGenerator.generate();
                    }
                });

                elements.amountInput().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        PayPalLinkGenerator.generate();
                    }
                });

                elements.descriptionInput().addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        PayPalLinkGenerator.generate();
                    }
                });

                // Global keyboard shortcut
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        PayPalLinkGenerator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
