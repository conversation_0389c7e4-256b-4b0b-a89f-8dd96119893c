<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Current Converter - Convert Amperes, <PERSON><PERSON><PERSON><PERSON>, and More</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Current Converter - Convert Amperes, Milliamperes, and More",
        "description": "Instantly convert between electrical current units like amperes (A), milliamperes (mA), microamperes (μA), and kiloamperes (kA). Free tool for electronics, engineering, and science.",
        "url": "https://www.webtoolskit.org/p/current-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-18",
        "dateModified": "2025-06-18",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Current Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Current Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a current converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A current converter, in the context of this tool, is a utility that converts a measurement of electrical current from one unit to another (e.g., from amperes to milliamperes). This is different from a physical device also called a current converter, which might change the level of current in a circuit. Our tool is for unit conversion calculations."
          }
        },
        {
          "@type": "Question",
          "name": "Do I need a current converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You need a current unit converter if you are an electronics hobbyist, engineer, student, or scientist working with electrical circuits. It's essential when a component's specification is in one unit (like milliamperes) but your calculation or measurement device uses another (like amperes). It ensures accuracy and prevents errors in your work."
          }
        },
        {
          "@type": "Question",
          "name": "How to make a voltage to current converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A physical voltage-to-current (V-to-I) converter can be built using an operational amplifier (op-amp). A simple configuration involves connecting the input voltage to the non-inverting input of the op-amp and using a feedback resistor to create a stable output current that is proportional to the input voltage. This circuit is often called a transconductance amplifier."
          }
        },
        {
          "@type": "Question",
          "name": "What is an example of a current to voltage converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The simplest example of a current-to-voltage converter is a resistor. According to Ohm's Law (V = I × R), when a current (I) flows through a resistor (R), it produces a voltage (V) across it. For more advanced applications, a transimpedance amplifier, which also uses an op-amp, provides a more precise and stable conversion of an input current to a proportional output voltage."
          }
        },
        {
          "@type": "Question",
          "name": "What is the purpose of a voltage converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The purpose of a physical voltage converter (or transformer/power supply) is to change the voltage level of an electrical power source. This is crucial for safely powering devices. For example, a phone charger converts high AC voltage from a wall outlet to low DC voltage for the phone's battery. Converters can step voltage up (increase it) or step it down (decrease it) to match the requirements of an electronic device."
          }
        }
      ]
    }
    </script>

    <style>
        /* Current Converter Widget - Simplified & Template Compatible */
        .current-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .current-converter-widget-container * { box-sizing: border-box; }

        .current-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .current-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .current-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .current-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .current-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .current-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .current-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .current-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .current-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .current-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .current-converter-btn:hover { transform: translateY(-2px); }

        .current-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .current-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .current-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .current-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .current-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .current-converter-btn-success:hover {
            background-color: #059669;
        }

        .current-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .current-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .current-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .current-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .current-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .current-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .current-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .current-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .current-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .current-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .current-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="voltage-converter"] .current-converter-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="power-converter"] .current-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="charge-converter"] .current-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }

        .current-converter-related-tool-item:hover .current-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="voltage-converter"]:hover .current-converter-related-tool-icon { background: linear-gradient(145deg, #fbbd24, #f59e0b); }
        a[href*="power-converter"]:hover .current-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        a[href*="charge-converter"]:hover .current-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        
        .current-converter-related-tool-item { box-shadow: none; border: none; }
        .current-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .current-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .current-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .current-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .current-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .current-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .current-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .current-converter-related-tool-item:hover .current-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .current-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .current-converter-widget-title { font-size: 1.875rem; }
            .current-converter-buttons { flex-direction: column; }
            .current-converter-btn { flex: none; }
            .current-converter-input-group { grid-template-columns: 1fr; }
            .current-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .current-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .current-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .current-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .current-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .current-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .current-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .current-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .current-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .current-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .current-converter-output::selection { background-color: var(--primary-color); color: white; }
        .current-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .current-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="current-converter-widget-container">
        <h1 class="current-converter-widget-title">Current Converter</h1>
        <p class="current-converter-widget-description">
            Effortlessly convert electrical current units—from amperes to milliamperes, microamperes, and more—with this simple online tool.
        </p>
        
        <div class="current-converter-input-group">
            <label for="currentFromInput" class="current-converter-label">From:</label>
            <input 
                type="number" 
                id="currentFromInput" 
                class="current-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="currentFromUnit" class="current-converter-select">
                <option value="a" selected>Ampere (A)</option>
                <option value="ma">Milliampere (mA)</option>
                <option value="ua">Microampere (μA)</option>
                <option value="ka">Kiloampere (kA)</option>
            </select>
        </div>

        <div class="current-converter-input-group">
            <label for="currentToInput" class="current-converter-label">To:</label>
            <input 
                type="number" 
                id="currentToInput" 
                class="current-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="currentToUnit" class="current-converter-select">
                <option value="a">Ampere (A)</option>
                <option value="ma" selected>Milliampere (mA)</option>
                <option value="ua">Microampere (μA)</option>
                <option value="ka">Kiloampere (kA)</option>
            </select>
        </div>

        <div class="current-converter-buttons">
            <button class="current-converter-btn current-converter-btn-primary" onclick="CurrentConverter.convert()">
                Convert Current
            </button>
            <button class="current-converter-btn current-converter-btn-secondary" onclick="CurrentConverter.clear()">
                Clear All
            </button>
            <button class="current-converter-btn current-converter-btn-success" onclick="CurrentConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="current-converter-result">
            <h3 class="current-converter-result-title">Conversion Result:</h3>
            <div class="current-converter-output" id="currentConverterOutput">
                Your converted current will appear here...
            </div>
        </div>

        <div class="current-converter-related-tools">
            <h3 class="current-converter-related-tools-title">Related Tools</h3>
            <div class="current-converter-related-tools-grid">
                <a href="/p/voltage-converter.html" class="current-converter-related-tool-item" rel="noopener">
                    <div class="current-converter-related-tool-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <div class="current-converter-related-tool-name">Voltage Converter</div>
                </a>
                <a href="/p/power-converter.html" class="current-converter-related-tool-item" rel="noopener">
                    <div class="current-converter-related-tool-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="current-converter-related-tool-name">Power Converter</div>
                </a>
                <a href="/p/charge-converter.html" class="current-converter-related-tool-item" rel="noopener">
                    <div class="current-converter-related-tool-icon">
                        <i class="fas fa-battery-half"></i>
                    </div>
                    <div class="current-converter-related-tool-name">Charge Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Instant Electrical Current Unit Conversions</h2>
            <p>Working with electronics requires precision, and a key part of that is using the correct units for electrical current. Our free <strong>Current Converter</strong> is an essential tool for anyone in the field of electronics, from students and hobbyists to professional engineers. It allows you to instantly convert between different units of electrical current, such as amperes (A), milliamperes (mA), microamperes (μA), and kiloamperes (kA). This eliminates guesswork and the potential for errors when working with circuit diagrams, component specifications, or measurement tools.</p>
            <p>Whether you're calculating the current draw of a tiny sensor in microamperes or working with high-power industrial systems in kiloamperes, this tool ensures your numbers are always in the right scale. Just enter a value, select your units, and get an immediate, accurate conversion.</p>

            <h3>How to Use the Current Converter</h3>
            <ol>
                <li><strong>Enter Your Value:</strong> Type the numeric value of the current you want to convert into the "From" field.</li>
                <li><strong>Select Units:</strong> Choose the starting unit (e.g., Amperes) from the first dropdown and the unit you want to convert to (e.g., Milliamperes) from the second.</li>
                <li><strong>Convert:</strong> Click the "Convert Current" button. The tool will instantly calculate and display the result.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to copy the converted value to your clipboard for easy use in your documents or designs.</li>
            </ol>

            <h3>Frequently Asked Questions About Current Conversion</h3>
            
            <h4>What is a current converter?</h4>
            <p>A current converter, in the context of this tool, is a utility that converts a measurement of electrical current from one unit to another (e.g., from amperes to milliamperes). This is different from a physical device also called a current converter, which might change the level of current in a circuit. Our tool is for unit conversion calculations.</p>
            
            <h4>Do I need a current converter?</h4>
            <p>You need a current unit converter if you are an electronics hobbyist, engineer, student, or scientist working with electrical circuits. It's essential when a component's specification is in one unit (like milliamperes) but your calculation or measurement device uses another (like amperes). It ensures accuracy and prevents errors in your work.</p>

            <h4>How to make a voltage to current converter?</h4>
            <p>A physical voltage-to-current (V-to-I) converter can be built using an operational amplifier (op-amp). A simple configuration involves connecting the input voltage to the non-inverting input of the op-amp and using a feedback resistor to create a stable output current that is proportional to the input voltage. This circuit is often called a transconductance amplifier.</p>

            <h4>What is an example of a current to voltage converter?</h4>
            <p>The simplest example of a current-to-voltage converter is a resistor. According to Ohm's Law (V = I × R), when a current (I) flows through a resistor (R), it produces a voltage (V) across it. For more advanced applications, a transimpedance amplifier, which also uses an op-amp, provides a more precise and stable conversion of an input current to a proportional output voltage.</p>
            
            <h4>What is the purpose of a voltage converter?</h4>
            <p>The purpose of a physical voltage converter (or transformer/power supply) is to change the voltage level of an electrical power source. This is crucial for safely powering devices. For example, a phone charger converts high AC voltage from a wall outlet to low DC voltage for the phone's battery. Converters can step voltage up (increase it) or step it down (decrease it) to match the requirements of an electronic device.</p>
        </div>

        <div class="current-converter-features">
            <h3 class="current-converter-features-title">Key Features:</h3>
            <ul class="current-converter-features-list">
                <li class="current-converter-features-item" style="margin-bottom: 0.3em;">Converts A, mA, µA, and kA</li>
                <li class="current-converter-features-item" style="margin-bottom: 0.3em;">High-precision calculations</li>
                <li class="current-converter-features-item" style="margin-bottom: 0.3em;">Ideal for electronics projects</li>
                <li class="current-converter-features-item" style="margin-bottom: 0.3em;">One-click result copying</li>
                <li class="current-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side processing</li>
                <li class="current-converter-features-item" style="margin-bottom: 0.3em;">Fully responsive design</li>
                <li class="current-converter-features-item">100% free and private to use</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="current-converter-notification" id="currentConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Current Converter
        (function() {
            'use strict';

            // Conversion factors to Amperes (A)
            const conversionFactors = {
                'a': 1,
                'ma': 0.001,
                'ua': 0.000001,
                'ka': 1000
            };

            const elements = {
                fromInput: () => document.getElementById('currentFromInput'),
                toInput: () => document.getElementById('currentToInput'),
                fromUnit: () => document.getElementById('currentFromUnit'),
                toUnit: () => document.getElementById('currentToUnit'),
                output: () => document.getElementById('currentConverterOutput'),
                notification: () => document.getElementById('currentConverterNotification')
            };

            window.CurrentConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to Amperes first, then to target unit
                    const valueInAmperes = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInAmperes / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-9 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(12)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'a': 'A', 'ma': 'mA', 'ua': 'μA', 'ka': 'kA' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted current will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        CurrentConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>