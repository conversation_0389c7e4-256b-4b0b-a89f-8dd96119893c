<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confidence Interval Calculator Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Confidence Interval Calculator - Statistical Analysis Tool",
        "description": "Calculate confidence intervals for statistical analysis with Z-score and T-score methods. Free online tool with 95%, 99% confidence levels and custom options.",
        "url": "https://www.webtoolskit.org/p/confidence-interval-calculator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Confidence Interval Calculator",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CalculateAction", "name": "Calculate Confidence Interval" },
            { "@type": "CopyAction", "name": "Copy Statistical Results" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to calculate a 95% confidence interval?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate a 95% confidence interval, use the formula: CI = x-bar +/- (critical value x standard error). For large samples, use z-score of 1.96. For small samples, use the appropriate t-score. The interval gives you a range where the true population parameter likely falls with 95% confidence."
          }
        },
        {
          "@type": "Question",
          "name": "What is the z-score for 95% confidence interval?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The z-score for a 95% confidence interval is 1.96. This means that 95% of the data falls within 1.96 standard deviations of the mean in a normal distribution. For 90% confidence, use 1.645, and for 99% confidence, use 2.576."
          }
        },
        {
          "@type": "Question",
          "name": "How to find 95% confidence interval on TI-84?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "On a TI-84 calculator, go to STAT → TESTS → ZInterval (for z-test) or TInterval (for t-test). Enter your sample statistics: sample mean, standard deviation, and sample size. Select C-Level: 0.95 for 95% confidence. The calculator will display the confidence interval bounds."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate confidence interval in Excel?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "In Excel, use the CONFIDENCE function: =CONFIDENCE(alpha, standard_dev, size). For 95% confidence, alpha = 0.05. The result gives the margin of error. Add and subtract this from your sample mean to get the confidence interval bounds. Excel also has CONFIDENCE.T for t-distribution calculations."
          }
        },
        {
          "@type": "Question",
          "name": "When to use z interval vs t interval?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use z-interval when: sample size ≥ 30, population standard deviation is known, or population is normally distributed. Use t-interval when: sample size < 30, population standard deviation is unknown, or dealing with small samples from normal populations. T-interval is more conservative and accounts for additional uncertainty."
          }
        }
      ]
    }
    </script>


    <style>
        /* Confidence Interval Calculator Widget - Simplified & Template Compatible */
        .confidence-interval-calculator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .confidence-interval-calculator-widget-container * { box-sizing: border-box; }

        .confidence-interval-calculator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .confidence-interval-calculator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .confidence-interval-calculator-input-group {
            margin-bottom: var(--spacing-lg);
        }

        .confidence-interval-calculator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .confidence-interval-calculator-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .confidence-interval-calculator-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .confidence-interval-calculator-select {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            cursor: pointer;
        }

        .confidence-interval-calculator-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .confidence-interval-calculator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .confidence-interval-calculator-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .confidence-interval-calculator-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .confidence-interval-calculator-radio {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .confidence-interval-calculator-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .confidence-interval-calculator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .confidence-interval-calculator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .confidence-interval-calculator-btn:hover { transform: translateY(-2px); }

        .confidence-interval-calculator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .confidence-interval-calculator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .confidence-interval-calculator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .confidence-interval-calculator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .confidence-interval-calculator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .confidence-interval-calculator-btn-success:hover {
            background-color: #059669;
        }

        .confidence-interval-calculator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-md);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
            margin: var(--spacing-sm) 0;
        }

        .confidence-interval-calculator-result-title {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .confidence-interval-calculator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 120px;
            color: var(--text-color);
            line-height: 1.8;
            white-space: pre-wrap;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            margin: 0;
        }

        .confidence-interval-calculator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .confidence-interval-calculator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .confidence-interval-calculator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .confidence-interval-calculator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .confidence-interval-calculator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .confidence-interval-calculator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .confidence-interval-calculator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .confidence-interval-calculator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="average-calculator"] .confidence-interval-calculator-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="probability-calculator"] .confidence-interval-calculator-related-tool-icon { background: linear-gradient(145deg, #06B6D4, #0891B2); }
        a[href*="percentage-calculator"] .confidence-interval-calculator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .confidence-interval-calculator-related-tool-item:hover .confidence-interval-calculator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="average-calculator"]:hover .confidence-interval-calculator-related-tool-icon { background: linear-gradient(145deg, #f061a0, #e1288a); }
        a[href*="probability-calculator"]:hover .confidence-interval-calculator-related-tool-icon { background: linear-gradient(145deg, #22d3ee, #0e7490); }
        a[href*="percentage-calculator"]:hover .confidence-interval-calculator-related-tool-icon { background: linear-gradient(145deg, #9d6ff7, #8e4aee); }
        
        .confidence-interval-calculator-related-tool-item { box-shadow: none; border: none; }
        .confidence-interval-calculator-related-tool-item:hover { box-shadow: none; border: none; }
        .confidence-interval-calculator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .confidence-interval-calculator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .confidence-interval-calculator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .confidence-interval-calculator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .confidence-interval-calculator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .confidence-interval-calculator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .confidence-interval-calculator-related-tool-item:hover .confidence-interval-calculator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .confidence-interval-calculator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .confidence-interval-calculator-widget-title { font-size: 1.875rem; }
            .confidence-interval-calculator-buttons { flex-direction: column; }
            .confidence-interval-calculator-btn { flex: none; }
            .confidence-interval-calculator-options { grid-template-columns: 1fr; }
            .confidence-interval-calculator-grid { grid-template-columns: 1fr; }
            .confidence-interval-calculator-output { 
                padding: var(--spacing-xs) var(--spacing-sm); 
                font-size: 0.875rem; 
                min-height: 100px; 
                line-height: 1.6; 
            }
            .confidence-interval-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .confidence-interval-calculator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .confidence-interval-calculator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .confidence-interval-calculator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .confidence-interval-calculator-output { 
                padding: var(--spacing-xs) var(--spacing-xs); 
                font-size: 0.8rem; 
                min-height: 80px; 
                line-height: 1.5; 
            }
            .confidence-interval-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .confidence-interval-calculator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .confidence-interval-calculator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .confidence-interval-calculator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .confidence-interval-calculator-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        [data-theme="dark"] .confidence-interval-calculator-select:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .confidence-interval-calculator-radio:focus, .confidence-interval-calculator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .confidence-interval-calculator-output::selection { background-color: var(--primary-color); color: white; }
        .confidence-interval-calculator-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .confidence-interval-calculator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="confidence-interval-calculator-widget-container">
        <h1 class="confidence-interval-calculator-widget-title">Confidence Interval Calculator</h1>
        <p class="confidence-interval-calculator-widget-description">
            Calculate confidence intervals for statistical analysis and data interpretation. Support for Z-score and T-score methods with customizable confidence levels.
        </p>
        
        <div class="confidence-interval-calculator-grid">
            <div class="confidence-interval-calculator-input-group">
                <label for="sampleMean" class="confidence-interval-calculator-label">Sample Mean (x-bar):</label>
                <input 
                    type="number" 
                    id="sampleMean" 
                    class="confidence-interval-calculator-input"
                    placeholder="Enter sample mean"
                    step="any"
                >
            </div>
            
            <div class="confidence-interval-calculator-input-group">
                <label for="sampleSize" class="confidence-interval-calculator-label">Sample Size (n):</label>
                <input 
                    type="number" 
                    id="sampleSize" 
                    class="confidence-interval-calculator-input"
                    placeholder="Enter sample size"
                    min="1"
                    step="1"
                >
            </div>
            
            <div class="confidence-interval-calculator-input-group">
                <label for="standardDeviation" class="confidence-interval-calculator-label">Standard Deviation:</label>
                <input 
                    type="number" 
                    id="standardDeviation" 
                    class="confidence-interval-calculator-input"
                    placeholder="Enter standard deviation (σ or s)"
                    step="any"
                    min="0"
                >
            </div>
            
            <div class="confidence-interval-calculator-input-group">
                <label for="confidenceLevel" class="confidence-interval-calculator-label">Confidence Level:</label>
                <select id="confidenceLevel" class="confidence-interval-calculator-select">
                    <option value="90">90%</option>
                    <option value="95" selected>95%</option>
                    <option value="99">99%</option>
                    <option value="custom">Custom</option>
                </select>
            </div>
        </div>

        <div class="confidence-interval-calculator-input-group" id="customLevelGroup" style="display: none;">
            <label for="customConfidenceLevel" class="confidence-interval-calculator-label">Custom Confidence Level (%):</label>
            <input 
                type="number" 
                id="customConfidenceLevel" 
                class="confidence-interval-calculator-input"
                placeholder="Enter confidence level (1-99)"
                min="1"
                max="99"
                step="0.1"
            >
        </div>

        <div class="confidence-interval-calculator-options">
            <div class="confidence-interval-calculator-option">
                <input type="radio" id="zInterval" name="intervalType" class="confidence-interval-calculator-radio" value="z" checked>
                <label for="zInterval" class="confidence-interval-calculator-option-label">Z-Interval (large sample/known σ)</label>
            </div>
            <div class="confidence-interval-calculator-option">
                <input type="radio" id="tInterval" name="intervalType" class="confidence-interval-calculator-radio" value="t">
                <label for="tInterval" class="confidence-interval-calculator-option-label">T-Interval (small sample/unknown σ)</label>
            </div>
        </div>

        <div class="confidence-interval-calculator-buttons">
            <button class="confidence-interval-calculator-btn confidence-interval-calculator-btn-primary" onclick="ConfidenceIntervalCalculator.calculate()">
                Calculate Confidence Interval
            </button>
            <button class="confidence-interval-calculator-btn confidence-interval-calculator-btn-secondary" onclick="ConfidenceIntervalCalculator.clear()">
                Clear All
            </button>
            <button class="confidence-interval-calculator-btn confidence-interval-calculator-btn-success" onclick="ConfidenceIntervalCalculator.copy()">
                Copy Result
            </button>
        </div>

        <div class="confidence-interval-calculator-result">
            <h3 class="confidence-interval-calculator-result-title">Confidence Interval Results:</h3>
            <div class="confidence-interval-calculator-output" id="confidenceIntervalCalculatorOutput">
                Your confidence interval results will appear here...
            </div>
        </div>

        <div class="confidence-interval-calculator-related-tools">
            <h3 class="confidence-interval-calculator-related-tools-title">Related Tools</h3>
            <div class="confidence-interval-calculator-related-tools-grid">
                <a href="/p/average-calculator.html" class="confidence-interval-calculator-related-tool-item" rel="noopener">
                    <div class="confidence-interval-calculator-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="confidence-interval-calculator-related-tool-name">Average Calculator</div>
                </a>

                <a href="/p/probability-calculator.html" class="confidence-interval-calculator-related-tool-item" rel="noopener">
                    <div class="confidence-interval-calculator-related-tool-icon">
                        <i class="fas fa-dice"></i>
                    </div>
                    <div class="confidence-interval-calculator-related-tool-name">Probability Calculator</div>
                </a>

                <a href="/p/percentage-calculator.html" class="confidence-interval-calculator-related-tool-item" rel="noopener">
                    <div class="confidence-interval-calculator-related-tool-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="confidence-interval-calculator-related-tool-name">Percentage Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Statistical Analysis Made Simple with Confidence Intervals</h2>
            <p>A <strong>Confidence Interval Calculator</strong> is an essential tool for researchers, analysts, and students conducting statistical analysis. Confidence intervals provide a range of values that likely contains the true population parameter with a specified level of confidence. Whether you're analyzing survey data, conducting research, or performing quality control tests, understanding confidence intervals is crucial for making informed decisions based on sample data.</p>
            <p>Our calculator supports both Z-intervals and T-intervals, automatically applying the appropriate statistical method based on your sample size and data characteristics. The tool handles common confidence levels like 95% and 99%, while also allowing custom confidence levels for specialized analysis needs.</p>
            
            <h3>How to Use the Confidence Interval Calculator</h3>
            <ol>
                <li><strong>Enter Sample Statistics:</strong> Input your sample mean, sample size, and standard deviation. These values form the foundation of your confidence interval calculation.</li>
                <li><strong>Select Confidence Level:</strong> Choose from common levels (90%, 95%, 99%) or enter a custom percentage based on your analysis requirements.</li>
                <li><strong>Choose Interval Type:</strong> Select Z-interval for large samples (n≥30) or known population standard deviation, or T-interval for small samples with unknown population standard deviation.</li>
                <li><strong>Calculate and Interpret:</strong> Click "Calculate Confidence Interval" to get detailed results including the interval bounds, margin of error, and statistical interpretation.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Confidence Intervals</h3>
            
            <h4>How to calculate a 95% confidence interval?</h4>
            <p>To calculate a 95% confidence interval, use the formula: CI = x-bar +/- (critical value x standard error). For large samples, use z-score of 1.96. For small samples, use the appropriate t-score. The interval gives you a range where the true population parameter likely falls with 95% confidence.</p>
            
            <h4>What is the z-score for 95% confidence interval?</h4>
            <p>The z-score for a 95% confidence interval is 1.96. This means that 95% of the data falls within 1.96 standard deviations of the mean in a normal distribution. For 90% confidence, use 1.645, and for 99% confidence, use 2.576.</p>
            
            <h4>How to find 95% confidence interval on TI-84?</h4>
            <p>On a TI-84 calculator, go to STAT → TESTS → ZInterval (for z-test) or TInterval (for t-test). Enter your sample statistics: sample mean, standard deviation, and sample size. Select C-Level: 0.95 for 95% confidence. The calculator will display the confidence interval bounds.</p>
            
            <h4>How to calculate confidence interval in Excel?</h4>
            <p>In Excel, use the CONFIDENCE function: =CONFIDENCE(alpha, standard_dev, size). For 95% confidence, alpha = 0.05. The result gives the margin of error. Add and subtract this from your sample mean to get the confidence interval bounds. Excel also has CONFIDENCE.T for t-distribution calculations.</p>
            
            <h4>When to use z interval vs t interval?</h4>
            <p>Use z-interval when: sample size ≥ 30, population standard deviation is known, or population is normally distributed. Use t-interval when: sample size < 30, population standard deviation is unknown, or dealing with small samples from normal populations. T-interval is more conservative and accounts for additional uncertainty.</p>
        </div>


        <div class="confidence-interval-calculator-features">
            <h3 class="confidence-interval-calculator-features-title">Key Features:</h3>
            <ul class="confidence-interval-calculator-features-list">
                <li class="confidence-interval-calculator-features-item" style="margin-bottom: 0.3em;">Z-interval and T-interval calculations</li>
                <li class="confidence-interval-calculator-features-item" style="margin-bottom: 0.3em;">Multiple confidence level options</li>
                <li class="confidence-interval-calculator-features-item" style="margin-bottom: 0.3em;">Detailed statistical interpretation</li>
                <li class="confidence-interval-calculator-features-item" style="margin-bottom: 0.3em;">Margin of error calculation</li>
                <li class="confidence-interval-calculator-features-item" style="margin-bottom: 0.3em;">Custom confidence level support</li>
                <li class="confidence-interval-calculator-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="confidence-interval-calculator-features-item">Professional statistical analysis</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="confidence-interval-calculator-notification" id="confidenceIntervalCalculatorNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Confidence Interval Calculator
        (function() {
            'use strict';

            const elements = {
                sampleMean: () => document.getElementById('sampleMean'),
                sampleSize: () => document.getElementById('sampleSize'),
                standardDeviation: () => document.getElementById('standardDeviation'),
                confidenceLevel: () => document.getElementById('confidenceLevel'),
                customConfidenceLevel: () => document.getElementById('customConfidenceLevel'),
                customLevelGroup: () => document.getElementById('customLevelGroup'),
                intervalType: () => document.querySelector('input[name="intervalType"]:checked'),
                output: () => document.getElementById('confidenceIntervalCalculatorOutput'),
                notification: () => document.getElementById('confidenceIntervalCalculatorNotification')
            };

            // Z-scores for common confidence levels
            const zScores = {
                90: 1.645,
                95: 1.96,
                99: 2.576
            };

            // T-distribution critical values (approximation for degrees of freedom)
            const getTScore = (df, alpha) => {
                // Simplified t-score approximation
                const tTable = {
                    1: {0.1: 6.314, 0.05: 12.706, 0.01: 63.657},
                    2: {0.1: 2.920, 0.05: 4.303, 0.01: 9.925},
                    3: {0.1: 2.353, 0.05: 3.182, 0.01: 5.841},
                    4: {0.1: 2.132, 0.05: 2.776, 0.01: 4.604},
                    5: {0.1: 2.015, 0.05: 2.571, 0.01: 4.032},
                    10: {0.1: 1.812, 0.05: 2.228, 0.01: 3.169},
                    15: {0.1: 1.753, 0.05: 2.131, 0.01: 2.947},
                    20: {0.1: 1.725, 0.05: 2.086, 0.01: 2.845},
                    25: {0.1: 1.708, 0.05: 2.060, 0.01: 2.787},
                    30: {0.1: 1.697, 0.05: 2.042, 0.01: 2.750}
                };

                if (df >= 30) {
                    return zScores[Math.round((1 - alpha) * 100)] || 1.96;
                }

                // Find closest df in table
                const availableDf = Object.keys(tTable).map(Number);
                const closestDf = availableDf.reduce((prev, curr) => 
                    Math.abs(curr - df) < Math.abs(prev - df) ? curr : prev);

                return tTable[closestDf][alpha] || 2.0;
            };

            window.ConfidenceIntervalCalculator = {
                calculate() {
                    const output = elements.output();
                    
                    // Get input values
                    const sampleMean = parseFloat(elements.sampleMean().value);
                    const sampleSize = parseInt(elements.sampleSize().value);
                    const standardDeviation = parseFloat(elements.standardDeviation().value);
                    const intervalType = elements.intervalType().value;
                    
                    let confidenceLevel;
                    if (elements.confidenceLevel().value === 'custom') {
                        confidenceLevel = parseFloat(elements.customConfidenceLevel().value);
                    } else {
                        confidenceLevel = parseFloat(elements.confidenceLevel().value);
                    }

                    // Validate inputs
                    if (isNaN(sampleMean) || isNaN(sampleSize) || isNaN(standardDeviation) || isNaN(confidenceLevel)) {
                        output.textContent = 'Please enter valid numbers for all required fields.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    if (sampleSize < 1) {
                        output.textContent = 'Sample size must be at least 1.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    if (standardDeviation <= 0) {
                        output.textContent = 'Standard deviation must be greater than 0.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    if (confidenceLevel <= 0 || confidenceLevel >= 100) {
                        output.textContent = 'Confidence level must be between 0 and 100.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const results = this.calculateConfidenceInterval(sampleMean, sampleSize, standardDeviation, confidenceLevel, intervalType);
                    output.textContent = results;
                },

                calculateConfidenceInterval(mean, n, sd, confidenceLevel, intervalType) {
                    const alpha = (100 - confidenceLevel) / 100;
                    const standardError = sd / Math.sqrt(n);
                    
                    let criticalValue;
                    let methodUsed;
                    
                    if (intervalType === 'z') {
                        // Z-interval
                        if (zScores[confidenceLevel]) {
                            criticalValue = zScores[confidenceLevel];
                        } else {
                            // Calculate z-score for custom confidence level
                            criticalValue = this.getZScore(alpha / 2);
                        }
                        methodUsed = 'Z-Distribution';
                    } else {
                        // T-interval
                        const degreesOfFreedom = n - 1;
                        criticalValue = getTScore(degreesOfFreedom, alpha / 2);
                        methodUsed = `T-Distribution (df = ${degreesOfFreedom})`;
                    }

                    const marginOfError = criticalValue * standardError;
                    const lowerBound = mean - marginOfError;
                    const upperBound = mean + marginOfError;

                    let results = [];
                    results.push(`CONFIDENCE INTERVAL CALCULATION`);
                    results.push(`=======================================`);
                    results.push('');
                    results.push(`Input Values:`);
                    results.push(`  Sample Mean (x-bar): ${mean}`);
                    results.push(`  Sample Size (n): ${n}`);
                    results.push(`  Standard Deviation (${intervalType === 'z' ? 'σ' : 's'}): ${sd}`);
                    results.push(`  Confidence Level: ${confidenceLevel}%`);
                    results.push(`  Method: ${methodUsed}`);
                    results.push('');
                    results.push(`Calculations:`);
                    results.push(`  Standard Error (SE) = ${intervalType === 'z' ? 'σ' : 's'}/sqrt(n) = ${sd}/sqrt(${n}) = ${standardError.toFixed(6)}`);
                    results.push(`  Critical Value (${intervalType === 'z' ? 'z' : 't'}${confidenceLevel === 95 ? '0.025' : ''}) = ${criticalValue.toFixed(4)}`);
                    results.push(`  Margin of Error (E) = ${criticalValue.toFixed(4)} x ${standardError.toFixed(6)} = ${marginOfError.toFixed(6)}`);
                    results.push('');
                    results.push(`CONFIDENCE INTERVAL:`);
                    results.push(`  ${confidenceLevel}% CI = ${mean} +/- ${marginOfError.toFixed(6)}`);
                    results.push(`  ${confidenceLevel}% CI = (${lowerBound.toFixed(6)}, ${upperBound.toFixed(6)})`);
                    results.push('');
                    results.push(`Interpretation:`);
                    results.push(`  We are ${confidenceLevel}% confident that the true population mean`);
                    results.push(`  lies between ${lowerBound.toFixed(4)} and ${upperBound.toFixed(4)}.`);
                    results.push('');
                    results.push(`Additional Information:`);
                    results.push(`  - Lower Bound: ${lowerBound.toFixed(6)}`);
                    results.push(`  - Upper Bound: ${upperBound.toFixed(6)}`);
                    results.push(`  - Margin of Error: +/-${marginOfError.toFixed(6)}`);
                    results.push(`  - Interval Width: ${(upperBound - lowerBound).toFixed(6)}`);

                    return results.join('\n');
                },

                getZScore(alpha) {
                    // Approximate z-score calculation for custom confidence levels
                    // This is a simplified approximation
                    if (alpha <= 0.005) return 2.576;
                    if (alpha <= 0.01) return 2.326;
                    if (alpha <= 0.025) return 1.96;
                    if (alpha <= 0.05) return 1.645;
                    if (alpha <= 0.10) return 1.282;
                    return 1.96; // Default to 95% confidence
                },

                clear() {
                    elements.sampleMean().value = '';
                    elements.sampleSize().value = '';
                    elements.standardDeviation().value = '';
                    elements.confidenceLevel().value = '95';
                    elements.customConfidenceLevel().value = '';
                    elements.customLevelGroup().style.display = 'none';
                    document.getElementById('zInterval').checked = true;
                    elements.output().textContent = 'Your confidence interval results will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your confidence interval results will appear here...', 'Please enter valid numbers for all required fields.', 'Sample size must be at least 1.', 'Standard deviation must be greater than 0.', 'Confidence level must be between 0 and 100.'].includes(text)) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                // Handle custom confidence level visibility
                elements.confidenceLevel().addEventListener('change', function() {
                    if (this.value === 'custom') {
                        elements.customLevelGroup().style.display = 'block';
                    } else {
                        elements.customLevelGroup().style.display = 'none';
                    }
                });

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key for calculation
                const inputs = document.querySelectorAll('.confidence-interval-calculator-input');
                inputs.forEach(input => {
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                            e.preventDefault();
                            ConfidenceIntervalCalculator.calculate();
                        }
                    });
                });
            });
        })();
    </script>
</body>
</html>