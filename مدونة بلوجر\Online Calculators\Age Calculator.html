<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Age Calculator - Calculate Your Exact Age in Years, Months, Days</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Age Calculator - Calculate Your Exact Age in Years, Months, Days",
        "description": "Calculate your exact age in years, months, days, hours, and minutes from your birth date. Free online age calculator with precise results and easy-to-use interface.",
        "url": "https://www.webtoolskit.org/p/age-calculator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Age Calculator",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CalculateAction", "name": "Calculate Age" },
            { "@type": "CopyAction", "name": "Copy Age Results" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How old am I if I was born in 2000?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "If you were born in 2000, your age depends on the current date and your exact birth date. As of 2024, you would be either 23 or 24 years old. Use our age calculator to get your precise age by entering your exact birth date."
          }
        },
        {
          "@type": "Question",
          "name": "How old would I be if I was born in 2008 in 2025?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "If you were born in 2008, you would be 16 or 17 years old in 2025, depending on your exact birth date and the specific date in 2025. Our calculator can determine your exact age for any future or past date."
          }
        },
        {
          "@type": "Question",
          "name": "What is my age if I was born in 1995?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "If you were born in 1995, you would be approximately 28-29 years old as of 2024. Enter your exact birth date in our age calculator to get your precise age in years, months, and days."
          }
        },
        {
          "@type": "Question",
          "name": "How to calculate age from date of birth?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate age from date of birth, subtract your birth date from the current date (or any target date). Our age calculator does this automatically, showing your age in years, months, days, hours, and minutes with complete accuracy."
          }
        },
        {
          "@type": "Question",
          "name": "How old am I today?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To find out how old you are today, simply enter your birth date in our age calculator. It will instantly show your current age in multiple formats: years, months, days, and even down to hours and minutes."
          }
        }
      ]
    }
    </script>


    <style>
        /* Age Calculator Widget - Simplified & Template Compatible */
        .age-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .age-widget-container * { box-sizing: border-box; }

        .age-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .age-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .age-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .age-input-group {
            margin-bottom: var(--spacing-lg);
        }

        .age-input-wrapper {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .age-input-field {
            position: relative;
        }

        .age-input-field input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .age-input-field input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .age-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .age-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .age-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .age-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .age-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .age-btn {
            padding: 12px 24px;
            border: 2px solid transparent;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 140px;
            display: inline-block;
            text-align: center;
            text-decoration: none;
            box-sizing: border-box;
        }

        .age-btn:hover { transform: translateY(-2px); }

        .age-btn-primary {
            background-color: #0047AB;
            color: white;
            border: 2px solid #0047AB;
        }

        .age-btn-primary:hover {
            background-color: #003d96;
            border-color: #003d96;
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        /* Dark mode support for primary button */
        [data-theme="dark"] .age-btn-primary {
            background-color: #60a5fa;
            color: white;
            border-color: #60a5fa;
        }

        [data-theme="dark"] .age-btn-primary:hover {
            background-color: #3b82f6;
            border-color: #3b82f6;
            box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
        }

        .age-btn-secondary {
            background-color: #f8f9fa;
            color: #495057;
            border: 2px solid #dee2e6;
        }

        .age-btn-secondary:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
        }

        .age-btn-success {
            background-color: #10b981;
            color: white;
            border: 2px solid #10b981;
        }

        .age-btn-success:hover {
            background-color: #059669;
            border-color: #059669;
        }

        /* Dark mode support for buttons */
        [data-theme="dark"] .age-btn-secondary {
            background-color: #374151;
            color: #f9fafb;
            border-color: #6b7280;
        }

        [data-theme="dark"] .age-btn-secondary:hover {
            background-color: #4b5563;
            border-color: #9ca3af;
        }

        [data-theme="dark"] .age-btn-success {
            background-color: #10b981;
            color: white;
            border-color: #10b981;
        }

        [data-theme="dark"] .age-btn-success:hover {
            background-color: #059669;
            border-color: #059669;
        }

        .age-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .age-result-title {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .age-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            margin: 0;
        }

        .age-results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .age-result-item {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            text-align: center;
        }

        .age-result-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .age-result-label {
            font-size: 0.875rem;
            color: var(--text-color-light);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .age-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .age-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        .age-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .age-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .age-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .age-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .age-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .age-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="bmi-calculator"] .age-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="calorie-calculator"] .age-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="percentage-calculator"] .age-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }

        .age-related-tool-item:hover .age-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="bmi-calculator"]:hover .age-related-tool-icon { background: linear-gradient(145deg, #f472b6, #ec4899); }
        a[href*="calorie-calculator"]:hover .age-related-tool-icon { background: linear-gradient(145deg, #fbbf24, #f59e0b); }
        a[href*="percentage-calculator"]:hover .age-related-tool-icon { background: linear-gradient(145deg, #34d399, #10b981); }
        
        .age-related-tool-item { box-shadow: none; border: none; }
        .age-related-tool-item:hover { box-shadow: none; border: none; }
        .age-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .age-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .age-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .age-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .age-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .age-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .age-related-tool-item:hover .age-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .age-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .age-widget-title { font-size: 1.875rem; }
            .age-buttons { flex-direction: column; }
            .age-btn { flex: none; }
            .age-options { grid-template-columns: 1fr; }
            .age-output { 
                padding: var(--spacing-xs) var(--spacing-sm); 
                font-size: 0.875rem; 
                min-height: 50px; 
                line-height: 1.4; 
            }
            .age-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .age-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .age-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .age-related-tool-name { font-size: 0.875rem; }
            .age-input-wrapper { grid-template-columns: 1fr; }
            .age-results-grid { grid-template-columns: repeat(2, 1fr); }
        }

        @media (max-width: 480px) {
            .age-output { 
                padding: var(--spacing-xs) var(--spacing-xs); 
                font-size: 0.8rem; 
                min-height: 40px; 
                line-height: 1.3; 
            }
            .age-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .age-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .age-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .age-related-tool-name { font-size: 0.75rem; }
            .age-results-grid { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .age-input-field input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .age-checkbox:focus, .age-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .age-output::selection { background-color: var(--primary-color); color: white; }
        .age-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .age-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="age-widget-container">
        <h1 class="age-widget-title">Age Calculator</h1>
        <p class="age-widget-description">
            Calculate your exact age in years, months, days, hours, and minutes from your birth date. Get precise age calculations for any date with our easy-to-use age calculator.
        </p>
        
        <div class="age-input-group">
            <label class="age-label">Enter Your Birth Date:</label>
            <div class="age-input-wrapper">
                <div class="age-input-field">
                    <input type="date" id="birthDate" placeholder="Select your birth date">
                </div>
                <div class="age-input-field">
                    <input type="date" id="targetDate" placeholder="Calculate age as of date">
                </div>
            </div>
        </div>

        <div class="age-options">
            <div class="age-option">
                <input type="checkbox" id="showMonths" class="age-checkbox" checked>
                <label for="showMonths" class="age-option-label">Show months breakdown</label>
            </div>
            <div class="age-option">
                <input type="checkbox" id="showDays" class="age-checkbox" checked>
                <label for="showDays" class="age-option-label">Show days breakdown</label>
            </div>
            <div class="age-option">
                <input type="checkbox" id="showTime" class="age-checkbox">
                <label for="showTime" class="age-option-label">Show hours & minutes</label>
            </div>
            <div class="age-option">
                <input type="checkbox" id="showTotal" class="age-checkbox" checked>
                <label for="showTotal" class="age-option-label">Show total days lived</label>
            </div>
        </div>

        <div class="age-buttons">
            <button class="age-btn age-btn-primary" onclick="AgeCalculator.calculate()">
                Calculate Age
            </button>
            <button class="age-btn age-btn-secondary" onclick="AgeCalculator.clear()">
                Clear All
            </button>
            <button class="age-btn age-btn-success" onclick="AgeCalculator.copy()">
                Copy Results
            </button>
        </div>

        <div class="age-result">
            <h3 class="age-result-title">Your Age:</h3>
            <div class="age-output" id="ageOutput">
                Your age calculation will appear here...
            </div>
            <div class="age-results-grid" id="ageResultsGrid" style="display: none;">
                <div class="age-result-item">
                    <div class="age-result-value" id="yearsResult">0</div>
                    <div class="age-result-label">Years</div>
                </div>
                <div class="age-result-item" id="monthsItem">
                    <div class="age-result-value" id="monthsResult">0</div>
                    <div class="age-result-label">Months</div>
                </div>
                <div class="age-result-item" id="daysItem">
                    <div class="age-result-value" id="daysResult">0</div>
                    <div class="age-result-label">Days</div>
                </div>
                <div class="age-result-item" id="totalDaysItem">
                    <div class="age-result-value" id="totalDaysResult">0</div>
                    <div class="age-result-label">Total Days</div>
                </div>
            </div>
        </div>

        <div class="age-related-tools">
            <h3 class="age-related-tools-title">Related Tools</h3>
            <div class="age-related-tools-grid">
                <a href="/p/bmi-calculator.html" class="age-related-tool-item" rel="noopener">
                    <div class="age-related-tool-icon">
                        <i class="fas fa-weight"></i>
                    </div>
                    <div class="age-related-tool-name">BMI Calculator</div>
                </a>

                <a href="/p/calorie-calculator.html" class="age-related-tool-item" rel="noopener">
                    <div class="age-related-tool-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="age-related-tool-name">Calorie Calculator</div>
                </a>

                <a href="/p/percentage-calculator.html" class="age-related-tool-item" rel="noopener">
                    <div class="age-related-tool-icon">
                        <i class="fas fa-percent"></i>
                    </div>
                    <div class="age-related-tool-name">Percentage Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Precise Age Calculation Tool</h2>
            <p>Our comprehensive <strong>Age Calculator</strong> provides accurate age calculations down to the exact day, hour, and minute. Whether you need to know your current age, calculate age differences, or determine how old someone will be on a specific date, this tool delivers precise results instantly. Understanding exact age is important for legal documents, medical records, insurance applications, and personal milestones.</p>
            <p>The calculator handles leap years, different month lengths, and time zones automatically, ensuring accuracy in all scenarios. You can calculate age for any date in the past or future, making it perfect for planning birthdays, anniversaries, or determining eligibility requirements based on age.</p>
            
            <h3>How to Use the Age Calculator</h3>
            <ol>
                <li><strong>Enter Birth Date:</strong> Select your birth date using the date picker for accurate calculations.</li>
                <li><strong>Choose Target Date:</strong> Select the date you want to calculate your age as of (defaults to today).</li>
                <li><strong>Customize Display:</strong> Use checkboxes to show or hide months, days, time details, and total days lived.</li>
                <li><strong>Get Results:</strong> Click "Calculate Age" to see your exact age in multiple formats with detailed breakdowns.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Age Calculator</h3>
            
            <h4>How old am I if I was born in 2000?</h4>
            <p>If you were born in 2000, your age depends on the current date and your exact birth date. As of 2024, you would be either 23 or 24 years old. Use our age calculator to get your precise age by entering your exact birth date for the most accurate result.</p>
            
            <h4>How old would I be if I was born in 2008 in 2025?</h4>
            <p>If you were born in 2008, you would be 16 or 17 years old in 2025, depending on your exact birth date and the specific date in 2025. Our calculator can determine your exact age for any future or past date by setting the target date accordingly.</p>
            
            <h4>What is my age if I was born in 1995?</h4>
            <p>If you were born in 1995, you would be approximately 28-29 years old as of 2024. Enter your exact birth date in our age calculator to get your precise age in years, months, and days, accounting for the specific day and month of your birth.</p>
            
            <h4>How to calculate age from date of birth?</h4>
            <p>To calculate age from date of birth, subtract your birth date from the current date (or any target date). Our age calculator does this automatically, showing your age in years, months, days, hours, and minutes with complete accuracy, handling leap years and varying month lengths.</p>
            
            <h4>How old am I today?</h4>
            <p>To find out how old you are today, simply enter your birth date in our age calculator. It will instantly show your current age in multiple formats: years, months, days, and even down to hours and minutes, giving you the most precise age calculation possible.</p>
        </div>

        <div class="age-features">
            <h3 class="age-features-title">Key Features:</h3>
            <ul class="age-features-list">
                <li class="age-features-item" style="margin-bottom: 0.3em;">Exact age calculation in multiple formats</li>
                <li class="age-features-item" style="margin-bottom: 0.3em;">Years, months, days breakdown</li>
                <li class="age-features-item" style="margin-bottom: 0.3em;">Hours and minutes precision</li>
                <li class="age-features-item" style="margin-bottom: 0.3em;">Total days lived counter</li>
                <li class="age-features-item" style="margin-bottom: 0.3em;">Future and past date calculations</li>
                <li class="age-features-item" style="margin-bottom: 0.3em;">Leap year handling</li>
                <li class="age-features-item">Mobile-responsive design</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="age-notification" id="ageNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Age Calculator
        (function() {
            'use strict';

            const elements = {
                birthDate: () => document.getElementById('birthDate'),
                targetDate: () => document.getElementById('targetDate'),
                output: () => document.getElementById('ageOutput'),
                notification: () => document.getElementById('ageNotification'),
                resultsGrid: () => document.getElementById('ageResultsGrid'),
                yearsResult: () => document.getElementById('yearsResult'),
                monthsResult: () => document.getElementById('monthsResult'),
                daysResult: () => document.getElementById('daysResult'),
                totalDaysResult: () => document.getElementById('totalDaysResult'),
                monthsItem: () => document.getElementById('monthsItem'),
                daysItem: () => document.getElementById('daysItem'),
                totalDaysItem: () => document.getElementById('totalDaysItem')
            };

            window.AgeCalculator = {
                calculate() {
                    const birthDate = new Date(elements.birthDate().value);
                    const targetDate = new Date(elements.targetDate().value);
                    const output = elements.output();

                    if (!elements.birthDate().value) {
                        output.textContent = 'Please enter your birth date.';
                        output.style.color = '#dc2626';
                        elements.resultsGrid().style.display = 'none';
                        return;
                    }

                    if (!elements.targetDate().value) {
                        output.textContent = 'Please enter the target date.';
                        output.style.color = '#dc2626';
                        elements.resultsGrid().style.display = 'none';
                        return;
                    }

                    if (birthDate > targetDate) {
                        output.textContent = 'Birth date cannot be after the target date.';
                        output.style.color = '#dc2626';
                        elements.resultsGrid().style.display = 'none';
                        return;
                    }

                    output.style.color = '';
                    const ageData = this.calculateAge(birthDate, targetDate);
                    this.displayResults(ageData);
                },

                calculateAge(birthDate, targetDate) {
                    let years = targetDate.getFullYear() - birthDate.getFullYear();
                    let months = targetDate.getMonth() - birthDate.getMonth();
                    let days = targetDate.getDate() - birthDate.getDate();

                    if (days < 0) {
                        months--;
                        const lastMonth = new Date(targetDate.getFullYear(), targetDate.getMonth(), 0);
                        days += lastMonth.getDate();
                    }

                    if (months < 0) {
                        years--;
                        months += 12;
                    }

                    // Calculate total days
                    const timeDifference = targetDate.getTime() - birthDate.getTime();
                    const totalDays = Math.floor(timeDifference / (1000 * 3600 * 24));

                    // Calculate hours and minutes
                    const totalHours = Math.floor(timeDifference / (1000 * 3600));
                    const totalMinutes = Math.floor(timeDifference / (1000 * 60));

                    return {
                        years,
                        months,
                        days,
                        totalDays,
                        totalHours,
                        totalMinutes,
                        birthDate: birthDate.toDateString(),
                        targetDate: targetDate.toDateString()
                    };
                },

                displayResults(ageData) {
                    const output = elements.output();
                    const resultsGrid = elements.resultsGrid();
                    
                    let resultText = `You are ${ageData.years} years`;
                    
                    const showMonths = document.getElementById('showMonths').checked;
                    const showDays = document.getElementById('showDays').checked;
                    const showTime = document.getElementById('showTime').checked;
                    const showTotal = document.getElementById('showTotal').checked;
                    
                    if (showMonths) {
                        resultText += `, ${ageData.months} months`;
                    }
                    
                    if (showDays) {
                        resultText += `, ${ageData.days} days`;
                    }
                    
                    resultText += ' old.';
                    
                    if (showTime) {
                        resultText += `\n\nTime lived: ${ageData.totalHours.toLocaleString()} hours (${ageData.totalMinutes.toLocaleString()} minutes)`;
                    }
                    
                    if (showTotal) {
                        resultText += `\nTotal days lived: ${ageData.totalDays.toLocaleString()} days`;
                    }
                    
                    resultText += `\n\nFrom: ${ageData.birthDate}\nTo: ${ageData.targetDate}`;
                    
                    output.textContent = resultText;

                    // Update result items
                    elements.yearsResult().textContent = ageData.years;
                    elements.monthsResult().textContent = ageData.months;
                    elements.daysResult().textContent = ageData.days;
                    elements.totalDaysResult().textContent = ageData.totalDays.toLocaleString();

                    // Show/hide items based on options
                    elements.monthsItem().style.display = showMonths ? 'block' : 'none';
                    elements.daysItem().style.display = showDays ? 'block' : 'none';
                    elements.totalDaysItem().style.display = showTotal ? 'block' : 'none';

                    resultsGrid.style.display = 'grid';
                },

                clear() {
                    elements.birthDate().value = '';
                    elements.targetDate().value = new Date().toISOString().split('T')[0];
                    elements.output().textContent = 'Your age calculation will appear here...';
                    elements.output().style.color = '';
                    elements.resultsGrid().style.display = 'none';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Your age calculation will appear here...' || text.includes('Please enter') || text.includes('cannot be')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize - NO AUTO-CALCULATION
            document.addEventListener('DOMContentLoaded', function() {
                const birthDateInput = elements.birthDate();
                const targetDateInput = elements.targetDate();

                // Set max date to today for birth date
                const today = new Date().toISOString().split('T')[0];
                birthDateInput.max = today;
                targetDateInput.value = today;

                // Theme compatibility only
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }
                
                // Calculator only works when "Calculate Age" button is pressed
            });
        })();
    </script>
</body>
</html>