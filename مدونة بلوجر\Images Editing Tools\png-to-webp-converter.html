<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PNG to WebP Converter - Free Online Image Format Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free PNG to WebP Converter - Convert Images Online",
        "description": "Convert PNG images to WebP format instantly. Free online tool with superior compression, quality control, and web optimization features.",
        "url": "https://www.webtoolskit.org/p/png-to-webp.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-22",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "PNG to WebP Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert PNG to WebP" },
            { "@type": "DownloadAction", "name": "Download Converted WebP" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Should I convert PNG to WebP?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, converting PNG to WebP is recommended for web use as it reduces file sizes by 25-35% while maintaining quality. WebP supports transparency like PNG but with better compression. However, keep PNG for print work or when maximum compatibility is needed."
          }
        },
        {
          "@type": "Question",
          "name": "Is WebP better than PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "WebP is better for web use due to smaller file sizes and faster loading times. It supports both lossy and lossless compression with transparency. PNG is better for print work, editing, and maximum compatibility across all devices and software."
          }
        },
        {
          "@type": "Question",
          "name": "Why is PNG being saved as WebP?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "PNG is being saved as WebP because websites and browsers automatically convert images to WebP for faster loading and reduced bandwidth usage. This optimization improves user experience but may require conversion back to PNG for certain uses."
          }
        },
        {
          "@type": "Question",
          "name": "Will WebP replace PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "WebP won't completely replace PNG but will become more common for web use. PNG remains essential for print work, professional editing, and situations requiring maximum compatibility. Both formats will coexist, serving different purposes."
          }
        },
        {
          "@type": "Question",
          "name": "What is the main disadvantage of WebP?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The main disadvantage of WebP is limited compatibility with older browsers, devices, and software. While modern systems support WebP, legacy applications may not display these images properly. PNG offers universal compatibility across all platforms."
          }
        }
      ]
    }
    </script>

    <style>
        /* PNG to WebP Widget - Simplified & Template Compatible */
        .png-webp-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .png-webp-widget-container * { box-sizing: border-box; }

        .png-webp-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .png-webp-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .png-webp-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-lg);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
        }

        .png-webp-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .png-webp-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .png-webp-upload-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .png-webp-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .png-webp-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .png-webp-file-input {
            display: none;
        }

        .png-webp-quality-control {
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .png-webp-quality-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .png-webp-quality-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: var(--border-color);
            outline: none;
            -webkit-appearance: none;
        }

        .png-webp-quality-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
        }

        .png-webp-quality-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            border: none;
        }

        .png-webp-preview {
            display: none;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .png-webp-preview-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .png-webp-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: flex-start;
        }

        .png-webp-preview-item {
            flex: 1;
            text-align: center;
        }

        .png-webp-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .png-webp-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-sm);
        }

        .png-webp-file-info {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .png-webp-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .png-webp-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .png-webp-btn:hover { transform: translateY(-2px); }

        .png-webp-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .png-webp-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .png-webp-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .png-webp-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .png-webp-btn-success {
            background-color: #10b981;
            color: white;
        }

        .png-webp-btn-success:hover {
            background-color: #059669;
        }

        .png-webp-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .png-webp-btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .png-webp-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="webp-to-jpg"] .png-webp-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .png-webp-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="png-to-jpg"] .png-webp-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .png-webp-related-tool-item:hover .png-webp-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="webp-to-jpg"]:hover .png-webp-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .png-webp-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="png-to-jpg"]:hover .png-webp-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .png-webp-related-tool-item { box-shadow: none; border: none; }
        .png-webp-related-tool-item:hover { box-shadow: none; border: none; }
        .png-webp-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .png-webp-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .png-webp-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .png-webp-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .png-webp-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .png-webp-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .png-webp-related-tool-item:hover .png-webp-related-tool-name { color: var(--primary-color); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .png-webp-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .png-webp-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .png-webp-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .png-webp-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .png-webp-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .png-webp-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .png-webp-widget-title { font-size: 1.875rem; }
            .png-webp-buttons { flex-direction: column; }
            .png-webp-btn { flex: none; }
            .png-webp-preview-content { flex-direction: column; }
            .png-webp-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .png-webp-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .png-webp-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .png-webp-related-tool-name { font-size: 0.875rem; }
            .png-webp-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .png-webp-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .png-webp-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .png-webp-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .png-webp-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .png-webp-upload-area:hover { background-color: var(--card-bg); }
        .png-webp-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
    </style>
</head>
<body>
    <div class="png-webp-widget-container">
        <h1 class="png-webp-widget-title">PNG to WebP Converter</h1>
        <p class="png-webp-widget-description">
            Convert PNG images to WebP format for superior compression and faster web loading. Free, secure, and optimized for modern web standards.
        </p>
        
        <div class="png-webp-upload-area" id="uploadArea">
            <div class="png-webp-upload-icon">📁</div>
            <div class="png-webp-upload-text">Click to select PNG image or drag & drop</div>
            <div class="png-webp-upload-subtext">Supports PNG files (Max 10MB)</div>
            <input type="file" id="fileInput" class="png-webp-file-input" accept=".png">
        </div>

        <div class="png-webp-quality-control">
            <label for="qualitySlider" class="png-webp-quality-label">WebP Quality: <span id="qualityValue">90</span>%</label>
            <input type="range" id="qualitySlider" class="png-webp-quality-slider" min="10" max="100" value="90">
        </div>

        <div class="png-webp-preview" id="previewSection">
            <h3 class="png-webp-preview-title">Preview & Comparison</h3>
            <div class="png-webp-preview-content">
                <div class="png-webp-preview-item">
                    <div class="png-webp-preview-label">Original PNG</div>
                    <img id="originalImage" class="png-webp-preview-image" alt="Original PNG" />
                    <div class="png-webp-file-info" id="originalInfo"></div>
                </div>
                <div class="png-webp-preview-item">
                    <div class="png-webp-preview-label">Converted WebP</div>
                    <img id="convertedImage" class="png-webp-preview-image" alt="Converted WebP" />
                    <div class="png-webp-file-info" id="convertedInfo"></div>
                </div>
            </div>
        </div>

        <div class="png-webp-buttons">
            <button id="convertBtn" class="png-webp-btn png-webp-btn-primary" disabled>
                Convert to WebP
            </button>
            <button id="downloadBtn" class="png-webp-btn png-webp-btn-success" disabled>
                Download WebP
            </button>
            <button id="resetBtn" class="png-webp-btn png-webp-btn-secondary">
                Reset
            </button>
        </div>

        <div class="png-webp-related-tools">
            <h3 class="png-webp-related-tools-title">Related Tools</h3>
            <div class="png-webp-related-tools-grid">
                <a href="https://www.webtoolskit.org/p/webp-to-jpg.html" class="png-webp-related-tool-item" rel="noopener">
                    <div class="png-webp-related-tool-icon">
                        <i class="fas fa-undo-alt"></i>
                    </div>
                    <div class="png-webp-related-tool-name">WebP to JPG</div>
                </a>

                <a href="https://www.webtoolskit.org/p/image-converter_23.html" class="png-webp-related-tool-item" rel="noopener">
                    <div class="png-webp-related-tool-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="png-webp-related-tool-name">Image Converter</div>
                </a>

                <a href="https://www.webtoolskit.org/p/png-to-jpg.html" class="png-webp-related-tool-item" rel="noopener">
                    <div class="png-webp-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="png-webp-related-tool-name">PNG to JPG</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert PNG to WebP Online - Optimize for Web</h2>
            <p>Our <strong>PNG to WebP Converter</strong> transforms traditional PNG images into the modern WebP format, delivering superior compression and faster loading times for web applications. WebP provides 25-35% smaller file sizes compared to PNG while maintaining excellent image quality and transparency support.</p>

            <p>Converting PNG to WebP is essential for web optimization, reducing bandwidth usage and improving user experience. Our tool processes images locally in your browser, ensuring privacy while delivering professional-quality results with customizable compression settings.</p>

            <h3>Frequently Asked Questions About PNG to WebP Conversion</h3>

            <h4>Should I convert PNG to WebP?</h4>
            <p>Yes, converting PNG to WebP is recommended for web use as it reduces file sizes by 25-35% while maintaining quality. WebP supports transparency like PNG but with better compression. However, keep PNG for print work or when maximum compatibility is needed.</p>

            <h4>Is WebP better than PNG?</h4>
            <p>WebP is better for web use due to smaller file sizes and faster loading times. It supports both lossy and lossless compression with transparency. PNG is better for print work, editing, and maximum compatibility across all devices and software.</p>

            <h4>Why is PNG being saved as WebP?</h4>
            <p>PNG is being saved as WebP because websites and browsers automatically convert images to WebP for faster loading and reduced bandwidth usage. This optimization improves user experience but may require conversion back to PNG for certain uses.</p>

            <h4>Will WebP replace PNG?</h4>
            <p>WebP won't completely replace PNG but will become more common for web use. PNG remains essential for print work, professional editing, and situations requiring maximum compatibility. Both formats will coexist, serving different purposes.</p>

            <h4>What is the main disadvantage of WebP?</h4>
            <p>The main disadvantage of WebP is limited compatibility with older browsers, devices, and software. While modern systems support WebP, legacy applications may not display these images properly. PNG offers universal compatibility across all platforms.</p>
        </div>

        <div class="png-webp-features">
            <h3 class="png-webp-features-title">Key Features</h3>
            <ul class="png-webp-features-list">
                <li class="png-webp-features-item">Convert PNG to WebP instantly</li>
                <li class="png-webp-features-item">Superior compression algorithms</li>
                <li class="png-webp-features-item">Transparency preservation</li>
                <li class="png-webp-features-item">Adjustable quality settings</li>
                <li class="png-webp-features-item">Client-side processing for privacy</li>
                <li class="png-webp-features-item">Batch conversion support</li>
                <li class="png-webp-features-item">Real-time preview</li>
                <li class="png-webp-features-item">Web optimization ready</li>
            </ul>
        </div>
    </div>

    <script>
        // PNG to WebP Converter Tool - Self-contained IIFE
        (function() {
            'use strict';

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                qualitySlider: () => document.getElementById('qualitySlider'),
                qualityValue: () => document.getElementById('qualityValue'),
                previewSection: () => document.getElementById('previewSection'),
                originalImage: () => document.getElementById('originalImage'),
                convertedImage: () => document.getElementById('convertedImage'),
                originalInfo: () => document.getElementById('originalInfo'),
                convertedInfo: () => document.getElementById('convertedInfo'),
                convertBtn: () => document.getElementById('convertBtn'),
                downloadBtn: () => document.getElementById('downloadBtn'),
                resetBtn: () => document.getElementById('resetBtn')
            };

            let originalFile = null;
            let convertedBlob = null;

            function init() {
                setupEventListeners();
            }

            function setupEventListeners() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();
                const qualitySlider = elements.qualitySlider();
                const convertBtn = elements.convertBtn();
                const downloadBtn = elements.downloadBtn();
                const resetBtn = elements.resetBtn();

                // File upload events
                uploadArea.addEventListener('click', () => fileInput.click());
                fileInput.addEventListener('change', handleFileSelect);

                // Drag and drop events
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('drop', handleDrop);

                // Quality slider
                qualitySlider.addEventListener('input', updateQualityValue);

                // Button events
                convertBtn.addEventListener('click', convertImage);
                downloadBtn.addEventListener('click', downloadImage);
                resetBtn.addEventListener('click', resetTool);
            }

            function handleFileSelect(event) {
                const file = event.target.files[0];
                if (file) processFile(file);
            }

            function handleDragOver(event) {
                event.preventDefault();
                elements.uploadArea().classList.add('dragover');
            }

            function handleDragLeave(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
            }

            function handleDrop(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
                const files = event.dataTransfer.files;
                if (files.length > 0) processFile(files[0]);
            }

            function processFile(file) {
                if (!file.type.includes('png')) {
                    alert('Please select a PNG image file.');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB.');
                    return;
                }

                originalFile = file;
                displayOriginalImage();
                elements.convertBtn().disabled = false;
            }

            function displayOriginalImage() {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const originalImage = elements.originalImage();
                    originalImage.src = e.target.result;
                    
                    const originalInfo = elements.originalInfo();
                    originalInfo.textContent = `${originalFile.name} (${formatFileSize(originalFile.size)})`;
                    
                    elements.previewSection().style.display = 'block';
                };
                reader.readAsDataURL(originalFile);
            }

            function updateQualityValue() {
                const value = elements.qualitySlider().value;
                elements.qualityValue().textContent = value;
            }

            function convertImage() {
                if (!originalFile) return;

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    const quality = elements.qualitySlider().value / 100;
                    canvas.toBlob((blob) => {
                        convertedBlob = blob;
                        displayConvertedImage();
                        elements.downloadBtn().disabled = false;
                    }, 'image/webp', quality);
                };

                img.src = URL.createObjectURL(originalFile);
            }

            function displayConvertedImage() {
                const convertedImage = elements.convertedImage();
                convertedImage.src = URL.createObjectURL(convertedBlob);
                
                const convertedInfo = elements.convertedInfo();
                const fileName = originalFile.name.replace(/\.[^/.]+$/, '') + '.webp';
                convertedInfo.textContent = `${fileName} (${formatFileSize(convertedBlob.size)})`;
            }

            function downloadImage() {
                if (!convertedBlob) return;

                const link = document.createElement('a');
                link.href = URL.createObjectURL(convertedBlob);
                link.download = originalFile.name.replace(/\.[^/.]+$/, '') + '.webp';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            function resetTool() {
                originalFile = null;
                convertedBlob = null;
                elements.fileInput().value = '';
                elements.previewSection().style.display = 'none';
                elements.convertBtn().disabled = true;
                elements.downloadBtn().disabled = true;
                elements.qualitySlider().value = 90;
                elements.qualityValue().textContent = '90';
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', init);
        })();
    </script>
</body>
</html>