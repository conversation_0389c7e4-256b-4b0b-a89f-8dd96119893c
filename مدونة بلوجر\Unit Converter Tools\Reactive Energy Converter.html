<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reactive Energy Converter - Convert VARh, kVARh, and MVARh</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Reactive Energy Converter - Convert VARh, kVARh, and MVARh",
        "description": "Instantly convert between reactive energy units like Volt-Ampere Reactive hours (VARh), kilovar-hours (kVARh), and megavar-hours (MVARh). A free tool for electrical utility analysis.",
        "url": "https://www.webtoolskit.org/p/reactive-energy-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-12",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Reactive Energy Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Reactive Energy Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is meant by reactive energy?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Reactive energy is the accumulation of reactive power over a period of time. It is measured in Volt-Ampere Reactive hours (VARh) or, more commonly, kilovar-hours (kVARh). While reactive power is the instantaneous rate of non-working power, reactive energy represents the total amount of this non-working energy supplied over an hour, day, or billing cycle, and it is often what utilities measure to determine power factor penalties."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between active energy and reactive energy?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Active energy (measured in kWh) is the energy that performs useful work, such as creating light, heat, or motion. It is the energy that is consumed. Reactive energy (measured in kVARh) is the energy that sustains the magnetic fields in inductive equipment like motors. It is not consumed but cycles between the source and the load. Think of active energy as the beer you drink and reactive energy as the foam that takes up space in the glass."
          }
        },
        {
          "@type": "Question",
          "name": "What is an example of a reactive power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Reactive power is the power required by inductive loads. A classic example is the power an induction motor draws to create the rotating magnetic field that turns its shaft. This power doesn't contribute to the motor's mechanical output but is essential for it to operate. Other examples include the power drawn by transformers and fluorescent light ballasts."
          }
        },
        {
          "@type": "Question",
          "name": "How to generate reactive power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Reactive power can be generated locally to support voltage on the grid. The most common method is using capacitor banks, which generate reactive power to offset the reactive power consumed by inductive loads. Additionally, over-excited synchronous generators and synchronous condensers can also be controlled to inject reactive power into the grid."
          }
        },
        {
          "@type": "Question",
          "name": "How to reduce reactive power?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The goal is to reduce the net reactive power drawn from the utility grid. This is achieved through a process called power factor correction. The most common method is to install capacitor banks near large inductive loads (like motors). These capacitors supply the necessary reactive power locally, so the facility does not have to pull it from the grid, which reduces energy losses and avoids utility penalties."
          }
        }
      ]
    }
    </script>

    <style>
        /* Reactive Energy Converter Widget - Simplified & Template Compatible */
        .reactive-energy-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .reactive-energy-converter-widget-container * { box-sizing: border-box; }

        .reactive-energy-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .reactive-energy-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .reactive-energy-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .reactive-energy-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .reactive-energy-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .reactive-energy-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .reactive-energy-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .reactive-energy-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .reactive-energy-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .reactive-energy-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .reactive-energy-converter-btn:hover { transform: translateY(-2px); }

        .reactive-energy-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .reactive-energy-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .reactive-energy-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .reactive-energy-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .reactive-energy-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .reactive-energy-converter-btn-success:hover {
            background-color: #059669;
        }

        .reactive-energy-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .reactive-energy-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .reactive-energy-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .reactive-energy-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .reactive-energy-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .reactive-energy-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .reactive-energy-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .reactive-energy-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .reactive-energy-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .reactive-energy-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .reactive-energy-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="reactive-power-converter"] .reactive-energy-converter-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="apparent-power-converter"] .reactive-energy-converter-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="power-converter"] .reactive-energy-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }

        .reactive-energy-converter-related-tool-item:hover .reactive-energy-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="reactive-power-converter"]:hover .reactive-energy-converter-related-tool-icon { background: linear-gradient(145deg, #9d6bff, #8b5cf6); }
        a[href*="apparent-power-converter"]:hover .reactive-energy-converter-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="power-converter"]:hover .reactive-energy-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        
        .reactive-energy-converter-related-tool-item { box-shadow: none; border: none; }
        .reactive-energy-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .reactive-energy-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .reactive-energy-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .reactive-energy-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .reactive-energy-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .reactive-energy-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .reactive-energy-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .reactive-energy-converter-related-tool-item:hover .reactive-energy-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .reactive-energy-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .reactive-energy-converter-widget-title { font-size: 1.875rem; }
            .reactive-energy-converter-buttons { flex-direction: column; }
            .reactive-energy-converter-btn { flex: none; }
            .reactive-energy-converter-input-group { grid-template-columns: 1fr; }
            .reactive-energy-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .reactive-energy-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .reactive-energy-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .reactive-energy-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .reactive-energy-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .reactive-energy-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .reactive-energy-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .reactive-energy-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .reactive-energy-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .reactive-energy-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .reactive-energy-converter-output::selection { background-color: var(--primary-color); color: white; }
        .reactive-energy-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .reactive-energy-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="reactive-energy-converter-widget-container">
        <h1 class="reactive-energy-converter-widget-title">Reactive Energy Converter</h1>
        <p class="reactive-energy-converter-widget-description">
            A simple tool for converting reactive energy units, including VARh, kilovar-hours (kVARh), and megavar-hours (MVARh).
        </p>
        
        <div class="reactive-energy-converter-input-group">
            <label for="reactiveEnergyFromInput" class="reactive-energy-converter-label">From:</label>
            <input 
                type="number" 
                id="reactiveEnergyFromInput" 
                class="reactive-energy-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="reactiveEnergyFromUnit" class="reactive-energy-converter-select">
                <option value="varh" selected>VAR-hour (VARh)</option>
                <option value="kvarh">Kilovar-hour (kVARh)</option>
                <option value="mvarh">Megavar-hour (MVARh)</option>
            </select>
        </div>

        <div class="reactive-energy-converter-input-group">
            <label for="reactiveEnergyToInput" class="reactive-energy-converter-label">To:</label>
            <input 
                type="number" 
                id="reactiveEnergyToInput" 
                class="reactive-energy-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="reactiveEnergyToUnit" class="reactive-energy-converter-select">
                <option value="varh">VAR-hour (VARh)</option>
                <option value="kvarh" selected>Kilovar-hour (kVARh)</option>
                <option value="mvarh">Megavar-hour (MVARh)</option>
            </select>
        </div>

        <div class="reactive-energy-converter-buttons">
            <button class="reactive-energy-converter-btn reactive-energy-converter-btn-primary" onclick="ReactiveEnergyConverter.convert()">
                Convert Energy
            </button>
            <button class="reactive-energy-converter-btn reactive-energy-converter-btn-secondary" onclick="ReactiveEnergyConverter.clear()">
                Clear All
            </button>
            <button class="reactive-energy-converter-btn reactive-energy-converter-btn-success" onclick="ReactiveEnergyConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="reactive-energy-converter-result">
            <h3 class="reactive-energy-converter-result-title">Conversion Result:</h3>
            <div class="reactive-energy-converter-output" id="reactiveEnergyConverterOutput">
                Your converted reactive energy will appear here...
            </div>
        </div>

        <div class="reactive-energy-converter-related-tools">
            <h3 class="reactive-energy-converter-related-tools-title">Related Tools</h3>
            <div class="reactive-energy-converter-related-tools-grid">
                <a href="/p/reactive-power-converter.html" class="reactive-energy-converter-related-tool-item" rel="noopener">
                    <div class="reactive-energy-converter-related-tool-icon">
                        <i class="fas fa-wave-square"></i>
                    </div>
                    <div class="reactive-energy-converter-related-tool-name">Reactive Power Converter</div>
                </a>
                <a href="/p/apparent-power-converter.html" class="reactive-energy-converter-related-tool-item" rel="noopener">
                    <div class="reactive-energy-converter-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="reactive-energy-converter-related-tool-name">Apparent Power Converter</div>
                </a>
                <a href="/p/power-converter.html" class="reactive-energy-converter-related-tool-item" rel="noopener">
                    <div class="reactive-energy-converter-related-tool-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="reactive-energy-converter-related-tool-name">Power Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert Reactive Energy Units for Power System Analysis</h2>
            <p>Reactive energy is a key metric in electrical power systems, representing the total non-working energy supplied to a load over time. Utilities often measure it in kilovar-hours (kVARh) to assess system efficiency and apply power factor penalties. Our free <strong>Reactive Energy Converter</strong> provides a straightforward way for engineers, technicians, and energy managers to convert between Volt-Ampere Reactive hours (VARh), kilovar-hours (kVARh), and megavar-hours (MVARh).</p>
            <p>This tool is essential for analyzing electricity bills, designing power factor correction systems, and conducting power quality studies. By simplifying the conversion process, it helps ensure your calculations are accurate and efficient, saving you time on critical tasks. Get instant, reliable conversions with a single click.</p>

            <h3>How to Use the Reactive Energy Converter</h3>
            <ol>
                <li><strong>Enter a Value:</strong> Type the numeric reactive energy value you want to convert into the "From" input field.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., kVARh) and your target unit (e.g., VARh) from the dropdowns.</li>
                <li><strong>Convert:</strong> Click the "Convert Energy" button to get your precise result immediately.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to easily copy the value for your reports or analyses.</li>
            </ol>

            <h3>Frequently Asked Questions About Reactive Energy</h3>

            <h4>What is meant by reactive energy?</h4>
            <p>Reactive energy is the accumulation of reactive power over a period of time. It is measured in Volt-Ampere Reactive hours (VARh) or, more commonly, kilovar-hours (kVARh). While reactive power is the instantaneous rate of non-working power, reactive energy represents the total amount of this non-working energy supplied over an hour, day, or billing cycle, and it is often what utilities measure to determine power factor penalties.</p>

            <h4>What is the difference between active energy and reactive energy?</h4>
            <p>Active energy (measured in kWh) is the energy that performs useful work, such as creating light, heat, or motion. It is the energy that is consumed. Reactive energy (measured in kVARh) is the energy that sustains the magnetic fields in inductive equipment like motors. It is not consumed but cycles between the source and the load. Think of active energy as the beer you drink and reactive energy as the foam that takes up space in the glass.</p>

            <h4>What is an example of a reactive power?</h4>
            <p>Reactive power is the power required by inductive loads. A classic example is the power an induction motor draws to create the rotating magnetic field that turns its shaft. This power doesn't contribute to the motor's mechanical output but is essential for it to operate. Other examples include the power drawn by transformers and fluorescent light ballasts.</p>

            <h4>How to generate reactive power?</h4>
            <p>Reactive power can be generated locally to support voltage on the grid. The most common method is using capacitor banks, which generate reactive power to offset the reactive power consumed by inductive loads. Additionally, over-excited synchronous generators and synchronous condensers can also be controlled to inject reactive power into the grid.</p>

            <h4>How to reduce reactive power?</h4>
            <p>The goal is to reduce the net reactive power drawn from the utility grid. This is achieved through a process called power factor correction. The most common method is to install capacitor banks near large inductive loads (like motors). These capacitors supply the necessary reactive power locally, so the facility does not have to pull it from the grid, which reduces energy losses and avoids utility penalties.</p>
        </div>

        <div class="reactive-energy-converter-features">
            <h3 class="reactive-energy-converter-features-title">Key Features:</h3>
            <ul class="reactive-energy-converter-features-list">
                <li class="reactive-energy-converter-features-item" style="margin-bottom: 0.3em;">Converts VARh, kVARh, MVARh</li>
                <li class="reactive-energy-converter-features-item" style="margin-bottom: 0.3em;">Ideal for utility bill analysis</li>
                <li class="reactive-energy-converter-features-item" style="margin-bottom: 0.3em;">High-precision calculations</li>
                <li class="reactive-energy-converter-features-item" style="margin-bottom: 0.3em;">One-click result copying</li>
                <li class="reactive-energy-converter-features-item" style="margin-bottom: 0.3em;">Fast, browser-based operation</li>
                <li class="reactive-energy-converter-features-item" style="margin-bottom: 0.3em;">Fully responsive on all devices</li>
                <li class="reactive-energy-converter-features-item">100% free and secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="reactive-energy-converter-notification" id="reactiveEnergyConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Reactive Energy Converter
        (function() {
            'use strict';

            // Conversion factors to VARh
            const conversionFactors = {
                'varh': 1,
                'kvarh': 1000,
                'mvarh': 1000000
            };

            const elements = {
                fromInput: () => document.getElementById('reactiveEnergyFromInput'),
                toInput: () => document.getElementById('reactiveEnergyToInput'),
                fromUnit: () => document.getElementById('reactiveEnergyFromUnit'),
                toUnit: () => document.getElementById('reactiveEnergyToUnit'),
                output: () => document.getElementById('reactiveEnergyConverterOutput'),
                notification: () => document.getElementById('reactiveEnergyConverterNotification')
            };

            window.ReactiveEnergyConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to VARh first, then to target unit
                    const valueInVARh = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInVARh / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-9 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(12)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'varh': 'VARh', 'kvarh': 'kVARh', 'mvarh': 'MVARh' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted reactive energy will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        ReactiveEnergyConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>