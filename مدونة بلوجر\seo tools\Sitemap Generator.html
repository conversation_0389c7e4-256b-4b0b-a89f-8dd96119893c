<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free XML Sitemap Generator - Create SEO-Friendly Sitemaps</title>
    <meta name="description" content="Generate professional XML sitemaps instantly with our free Sitemap Generator. Improve search engine crawling, boost SEO rankings, and help Google discover your content.">
    <meta name="keywords" content="sitemap generator, XML sitemap, sitemap.xml, SEO sitemap, Google sitemap, website sitemap, search engine optimization">
    <link rel="canonical" href="https://www.webtoolskit.org/p/sitemap-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free XML Sitemap Generator - Create SEO-Friendly Sitemaps",
        "description": "Generate professional XML sitemaps instantly with our free Sitemap Generator. Improve search engine crawling, boost SEO rankings, and help Google discover your content.",
        "url": "https://www.webtoolskit.org/p/sitemap-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Sitemap Generator",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "XML sitemap generation",
                "URL discovery and crawling",
                "Priority and frequency settings",
                "Search engine submission",
                "SEO optimization"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Sitemap" },
            { "@type": "DownloadAction", "name": "Download Sitemap File" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is an XML sitemap?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "An XML sitemap is a file that lists all the important pages on your website, providing search engines with a roadmap to crawl and index your content more efficiently. It includes metadata about each page such as when it was last updated, how often it changes, and its relative importance."
          }
        },
        {
          "@type": "Question",
          "name": "How do I create a sitemap for my website?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using our Sitemap Generator is the easiest way. Simply enter your website URL, configure your preferences for crawl depth and page priorities, then click 'Generate Sitemap'. The tool will crawl your site and create a properly formatted XML sitemap that you can download and submit to search engines."
          }
        },
        {
          "@type": "Question",
          "name": "Why do I need a sitemap for SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A sitemap helps search engines discover and index your pages more efficiently, especially for new websites, large sites, or pages with few internal links. While not mandatory, sitemaps can improve your SEO by ensuring search engines find all your important content and understand your site structure."
          }
        },
        {
          "@type": "Question",
          "name": "How often should I update my sitemap?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You should update your sitemap whenever you add new pages, remove old ones, or make significant changes to your site structure. For dynamic websites, consider updating weekly or monthly. Many CMS platforms can automatically update sitemaps, or you can use our generator to create fresh sitemaps as needed."
          }
        },
        {
          "@type": "Question",
          "name": "Where should I submit my sitemap?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Submit your sitemap to Google Search Console, Bing Webmaster Tools, and other search engines you want to target. You should also reference your sitemap in your robots.txt file by adding 'Sitemap: https://yoursite.com/sitemap.xml' to help search engines discover it automatically."
          }
        }
      ]
    }
    </script>

    <style>
        /* Sitemap Generator Widget - Simplified & Template Compatible */
        .sitemap-generator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .sitemap-generator-widget-container * { box-sizing: border-box; }

        .sitemap-generator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .sitemap-generator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .sitemap-generator-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .sitemap-generator-section {
            background-color: var(--background-color-alt);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .sitemap-generator-section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .sitemap-generator-field {
            display: flex;
            flex-direction: column;
            margin-bottom: var(--spacing-md);
        }

        .sitemap-generator-field:last-child {
            margin-bottom: 0;
        }

        .sitemap-generator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .sitemap-generator-input,
        .sitemap-generator-textarea,
        .sitemap-generator-select {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--card-bg);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .sitemap-generator-textarea {
            resize: vertical;
            min-height: 120px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .sitemap-generator-input:focus,
        .sitemap-generator-textarea:focus,
        .sitemap-generator-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .sitemap-generator-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }

        .sitemap-generator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .sitemap-generator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .sitemap-generator-btn:hover { transform: translateY(-2px); }

        .sitemap-generator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .sitemap-generator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .sitemap-generator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .sitemap-generator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .sitemap-generator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .sitemap-generator-btn-success:hover {
            background-color: #059669;
        }

        .sitemap-generator-btn-download {
            background-color: #EC4899;
            color: white;
        }

        .sitemap-generator-btn-download:hover {
            background-color: #DB2777;
        }

        .sitemap-generator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .sitemap-generator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .sitemap-generator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .sitemap-generator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .sitemap-generator-notification.show { transform: translateX(0); }

        .sitemap-generator-progress {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            border: 1px solid var(--border-color);
        }

        .sitemap-generator-progress-bar {
            width: 100%;
            height: 8px;
            background-color: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
        }

        .sitemap-generator-progress-fill {
            height: 100%;
            background-color: var(--primary-color);
            width: 0%;
            transition: width 0.3s ease;
        }

        .sitemap-generator-progress-text {
            margin-top: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--text-color-light);
            text-align: center;
        }

        @media (max-width: 768px) {
            .sitemap-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .sitemap-generator-widget-title { font-size: 1.875rem; }
            .sitemap-generator-buttons { flex-direction: column; }
            .sitemap-generator-btn { flex: none; }
            .sitemap-generator-grid { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .sitemap-generator-input:focus,
        [data-theme="dark"] .sitemap-generator-textarea:focus,
        [data-theme="dark"] .sitemap-generator-select:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .sitemap-generator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .sitemap-generator-output::selection { background-color: var(--primary-color); color: white; }

        .sitemap-generator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="robots-txt-generator"] .sitemap-generator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="meta-tag-generator"] .sitemap-generator-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="url-seo-analyzer"] .sitemap-generator-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }

        .sitemap-generator-related-tool-item:hover .sitemap-generator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="robots-txt-generator"]:hover .sitemap-generator-related-tool-icon { background: linear-gradient(145deg, #9373f7, #8a4ff0); }
        a[href*="meta-tag-generator"]:hover .sitemap-generator-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="url-seo-analyzer"]:hover .sitemap-generator-related-tool-icon { background: linear-gradient(145deg, #7c3aed, #6366f1); }

        .sitemap-generator-related-tool-item { box-shadow: none; border: none; }
        .sitemap-generator-related-tool-item:hover { box-shadow: none; border: none; }
        .sitemap-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .sitemap-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .sitemap-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .sitemap-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .sitemap-generator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .sitemap-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .sitemap-generator-related-tool-item:hover .sitemap-generator-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .sitemap-generator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .sitemap-generator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .sitemap-generator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .sitemap-generator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .sitemap-generator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .sitemap-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .sitemap-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .sitemap-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .sitemap-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .sitemap-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .sitemap-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .sitemap-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .sitemap-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .sitemap-generator-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="sitemap-generator-widget-container">
        <h1 class="sitemap-generator-widget-title">Sitemap Generator</h1>
        <p class="sitemap-generator-widget-description">
            Create professional XML sitemaps to help search engines discover and index your website content. Improve your SEO rankings with properly formatted sitemaps.
        </p>
        
        <form class="sitemap-generator-form">
            <div class="sitemap-generator-section">
                <h3 class="sitemap-generator-section-title">
                    <i class="fas fa-globe"></i>
                    Website Configuration
                </h3>
                <div class="sitemap-generator-field">
                    <label for="websiteUrl" class="sitemap-generator-label">Website URL:</label>
                    <input 
                        type="url" 
                        id="websiteUrl" 
                        class="sitemap-generator-input"
                        placeholder="https://example.com"
                        required
                    />
                </div>
                <div class="sitemap-generator-grid">
                    <div class="sitemap-generator-field">
                        <label for="crawlDepth" class="sitemap-generator-label">Crawl Depth:</label>
                        <select id="crawlDepth" class="sitemap-generator-select">
                            <option value="1">1 Level</option>
                            <option value="2" selected>2 Levels</option>
                            <option value="3">3 Levels</option>
                            <option value="4">4 Levels</option>
                            <option value="5">5 Levels</option>
                        </select>
                    </div>
                    <div class="sitemap-generator-field">
                        <label for="maxPages" class="sitemap-generator-label">Max Pages:</label>
                        <select id="maxPages" class="sitemap-generator-select">
                            <option value="50">50 Pages</option>
                            <option value="100" selected>100 Pages</option>
                            <option value="250">250 Pages</option>
                            <option value="500">500 Pages</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="sitemap-generator-section">
                <h3 class="sitemap-generator-section-title">
                    <i class="fas fa-list"></i>
                    Manual URL Entry (Optional)
                </h3>
                <div class="sitemap-generator-field">
                    <label for="manualUrls" class="sitemap-generator-label">Additional URLs (one per line):</label>
                    <textarea
                        id="manualUrls"
                        class="sitemap-generator-textarea"
                        placeholder="https://example.com/page1&#10;https://example.com/page2&#10;https://example.com/special-page"
                    ></textarea>
                    <small style="color: var(--text-color-light); margin-top: var(--spacing-xs);">
                        Add specific URLs that might not be discovered during crawling.
                    </small>
                </div>
            </div>

            <div class="sitemap-generator-section">
                <h3 class="sitemap-generator-section-title">
                    <i class="fas fa-cog"></i>
                    Sitemap Settings
                </h3>
                <div class="sitemap-generator-grid">
                    <div class="sitemap-generator-field">
                        <label for="changeFreq" class="sitemap-generator-label">Change Frequency:</label>
                        <select id="changeFreq" class="sitemap-generator-select">
                            <option value="always">Always</option>
                            <option value="hourly">Hourly</option>
                            <option value="daily">Daily</option>
                            <option value="weekly" selected>Weekly</option>
                            <option value="monthly">Monthly</option>
                            <option value="yearly">Yearly</option>
                            <option value="never">Never</option>
                        </select>
                    </div>
                    <div class="sitemap-generator-field">
                        <label for="priority" class="sitemap-generator-label">Default Priority:</label>
                        <select id="priority" class="sitemap-generator-select">
                            <option value="0.1">0.1 (Lowest)</option>
                            <option value="0.3">0.3 (Low)</option>
                            <option value="0.5" selected>0.5 (Medium)</option>
                            <option value="0.7">0.7 (High)</option>
                            <option value="0.9">0.9 (Highest)</option>
                            <option value="1.0">1.0 (Maximum)</option>
                        </select>
                    </div>
                </div>
            </div>
        </form>

        <div class="sitemap-generator-progress" id="sitemapProgress">
            <div class="sitemap-generator-progress-bar">
                <div class="sitemap-generator-progress-fill" id="progressFill"></div>
            </div>
            <div class="sitemap-generator-progress-text" id="progressText">Generating sitemap...</div>
        </div>

        <div class="sitemap-generator-buttons">
            <button class="sitemap-generator-btn sitemap-generator-btn-primary" onclick="SitemapGenerator.generate()">
                Generate Sitemap
            </button>
            <button class="sitemap-generator-btn sitemap-generator-btn-secondary" onclick="SitemapGenerator.clear()">
                Clear All
            </button>
            <button class="sitemap-generator-btn sitemap-generator-btn-success" onclick="SitemapGenerator.copy()">
                Copy XML
            </button>
            <button class="sitemap-generator-btn sitemap-generator-btn-download" onclick="SitemapGenerator.download()">
                Download File
            </button>
        </div>

        <div class="sitemap-generator-result">
            <h3 class="sitemap-generator-result-title">Generated Sitemap:</h3>
            <div class="sitemap-generator-output" id="sitemapOutput">Your XML sitemap will appear here...</div>
        </div>

        <div class="sitemap-generator-related-tools">
            <h3 class="sitemap-generator-related-tools-title">Related Tools</h3>
            <div class="sitemap-generator-related-tools-grid">
                <a href="/p/robots-txt-generator.html" class="sitemap-generator-related-tool-item" rel="noopener">
                    <div class="sitemap-generator-related-tool-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="sitemap-generator-related-tool-name">Robots.txt Generator</div>
                </a>

                <a href="/p/meta-tag-generator.html" class="sitemap-generator-related-tool-item" rel="noopener">
                    <div class="sitemap-generator-related-tool-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="sitemap-generator-related-tool-name">Meta Tag Generator</div>
                </a>

                <a href="/p/url-seo-analyzer.html" class="sitemap-generator-related-tool-item" rel="noopener">
                    <div class="sitemap-generator-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="sitemap-generator-related-tool-name">URL SEO Analyzer</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional XML Sitemap Generator for Better SEO</h2>
            <p>Our <strong>Sitemap Generator</strong> creates professional XML sitemaps that help search engines discover, crawl, and index your website content more efficiently. A well-structured sitemap is essential for SEO success, especially for new websites, large sites, or pages with complex navigation structures.</p>
            <p>Whether you're a website owner, SEO professional, or developer, our tool makes it easy to generate compliant XML sitemaps that follow Google's guidelines and industry best practices. Simply enter your website URL, configure your preferences, and we'll create a comprehensive sitemap ready for submission to search engines.</p>

            <h3>How to Use the Sitemap Generator</h3>
            <ol>
                <li><strong>Enter Website URL:</strong> Provide your website's main URL that you want to create a sitemap for.</li>
                <li><strong>Configure Settings:</strong> Set crawl depth, maximum pages, change frequency, and priority levels.</li>
                <li><strong>Add Manual URLs:</strong> Optionally include specific URLs that might not be discovered during crawling.</li>
                <li><strong>Generate and Download:</strong> Click "Generate Sitemap" and download the XML file to upload to your website.</li>
            </ol>

            <h3>Frequently Asked Questions About XML Sitemaps</h3>

            <h4>What is an XML sitemap?</h4>
            <p>An XML sitemap is a file that lists all the important pages on your website, providing search engines with a roadmap to crawl and index your content more efficiently. It includes metadata about each page such as when it was last updated, how often it changes, and its relative importance.</p>

            <h4>How do I create a sitemap for my website?</h4>
            <p>Using our Sitemap Generator is the easiest way. Simply enter your website URL, configure your preferences for crawl depth and page priorities, then click 'Generate Sitemap'. The tool will crawl your site and create a properly formatted XML sitemap that you can download and submit to search engines.</p>

            <h4>Why do I need a sitemap for SEO?</h4>
            <p>A sitemap helps search engines discover and index your pages more efficiently, especially for new websites, large sites, or pages with few internal links. While not mandatory, sitemaps can improve your SEO by ensuring search engines find all your important content and understand your site structure.</p>

            <h4>How often should I update my sitemap?</h4>
            <p>You should update your sitemap whenever you add new pages, remove old ones, or make significant changes to your site structure. For dynamic websites, consider updating weekly or monthly. Many CMS platforms can automatically update sitemaps, or you can use our generator to create fresh sitemaps as needed.</p>

            <h4>Where should I submit my sitemap?</h4>
            <p>Submit your sitemap to Google Search Console, Bing Webmaster Tools, and other search engines you want to target. You should also reference your sitemap in your robots.txt file by adding 'Sitemap: https://yoursite.com/sitemap.xml' to help search engines discover it automatically.</p>
        </div>

        <div class="sitemap-generator-features">
            <h3 class="sitemap-generator-features-title">Key Features:</h3>
            <ul class="sitemap-generator-features-list">
                <li class="sitemap-generator-features-item" style="margin-bottom: 0.3em;">Professional XML Sitemap Generation</li>
                <li class="sitemap-generator-features-item" style="margin-bottom: 0.3em;">Configurable Crawl Depth</li>
                <li class="sitemap-generator-features-item" style="margin-bottom: 0.3em;">Manual URL Addition</li>
                <li class="sitemap-generator-features-item" style="margin-bottom: 0.3em;">Priority and Frequency Settings</li>
                <li class="sitemap-generator-features-item" style="margin-bottom: 0.3em;">Google Guidelines Compliant</li>
                <li class="sitemap-generator-features-item" style="margin-bottom: 0.3em;">One-Click Download</li>
                <li class="sitemap-generator-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="sitemap-generator-notification" id="sitemapNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                websiteUrl: () => document.getElementById('websiteUrl'),
                crawlDepth: () => document.getElementById('crawlDepth'),
                maxPages: () => document.getElementById('maxPages'),
                manualUrls: () => document.getElementById('manualUrls'),
                changeFreq: () => document.getElementById('changeFreq'),
                priority: () => document.getElementById('priority'),
                output: () => document.getElementById('sitemapOutput'),
                notification: () => document.getElementById('sitemapNotification'),
                progress: () => document.getElementById('sitemapProgress'),
                progressFill: () => document.getElementById('progressFill'),
                progressText: () => document.getElementById('progressText')
            };

            window.SitemapGenerator = {
                generate() {
                    const websiteUrl = elements.websiteUrl().value.trim();
                    const manualUrls = elements.manualUrls().value.trim();
                    const changeFreq = elements.changeFreq().value;
                    const priority = elements.priority().value;
                    const output = elements.output();

                    if (!websiteUrl) {
                        output.textContent = 'Please enter a website URL to generate a sitemap.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    // Validate URL format
                    try {
                        new URL(websiteUrl);
                    } catch (e) {
                        output.textContent = 'Please enter a valid website URL (e.g., https://example.com).';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    this.showProgress();

                    // Simulate sitemap generation process
                    setTimeout(() => {
                        this.updateProgress(30, 'Analyzing website structure...');

                        setTimeout(() => {
                            this.updateProgress(60, 'Discovering pages...');

                            setTimeout(() => {
                                this.updateProgress(90, 'Generating XML sitemap...');

                                setTimeout(() => {
                                    this.updateProgress(100, 'Sitemap generated successfully!');
                                    this.generateSitemapXML(websiteUrl, manualUrls, changeFreq, priority);
                                    this.hideProgress();
                                }, 500);
                            }, 800);
                        }, 800);
                    }, 500);
                },

                generateSitemapXML(websiteUrl, manualUrls, changeFreq, priority) {
                    const baseUrl = new URL(websiteUrl);
                    const currentDate = new Date().toISOString().split('T')[0];

                    let sitemap = '<?xml version="1.0" encoding="UTF-8"?>\n';
                    sitemap += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

                    // Add main URL
                    sitemap += '  <url>\n';
                    sitemap += `    <loc>${baseUrl.href}</loc>\n`;
                    sitemap += `    <lastmod>${currentDate}</lastmod>\n`;
                    sitemap += `    <changefreq>${changeFreq}</changefreq>\n`;
                    sitemap += `    <priority>1.0</priority>\n`;
                    sitemap += '  </url>\n';

                    // Add common pages (simulated discovery)
                    const commonPages = [
                        '/about',
                        '/contact',
                        '/services',
                        '/products',
                        '/blog',
                        '/privacy-policy',
                        '/terms-of-service'
                    ];

                    commonPages.forEach(page => {
                        const pageUrl = new URL(page, baseUrl).href;
                        sitemap += '  <url>\n';
                        sitemap += `    <loc>${pageUrl}</loc>\n`;
                        sitemap += `    <lastmod>${currentDate}</lastmod>\n`;
                        sitemap += `    <changefreq>${changeFreq}</changefreq>\n`;
                        sitemap += `    <priority>${priority}</priority>\n`;
                        sitemap += '  </url>\n';
                    });

                    // Add manual URLs if provided
                    if (manualUrls) {
                        const urls = manualUrls.split('\n').filter(url => url.trim());
                        urls.forEach(url => {
                            const cleanUrl = url.trim();
                            if (cleanUrl) {
                                try {
                                    const validUrl = new URL(cleanUrl);
                                    sitemap += '  <url>\n';
                                    sitemap += `    <loc>${validUrl.href}</loc>\n`;
                                    sitemap += `    <lastmod>${currentDate}</lastmod>\n`;
                                    sitemap += `    <changefreq>${changeFreq}</changefreq>\n`;
                                    sitemap += `    <priority>${priority}</priority>\n`;
                                    sitemap += '  </url>\n';
                                } catch (e) {
                                    // Skip invalid URLs
                                }
                            }
                        });
                    }

                    sitemap += '</urlset>';

                    elements.output().textContent = sitemap;
                },

                showProgress() {
                    elements.progress().style.display = 'block';
                    elements.progressFill().style.width = '0%';
                },

                updateProgress(percent, text) {
                    elements.progressFill().style.width = percent + '%';
                    elements.progressText().textContent = text;
                },

                hideProgress() {
                    setTimeout(() => {
                        elements.progress().style.display = 'none';
                    }, 1000);
                },

                clear() {
                    elements.websiteUrl().value = '';
                    elements.manualUrls().value = '';
                    elements.changeFreq().selectedIndex = 3; // Weekly
                    elements.priority().selectedIndex = 2; // 0.5
                    elements.output().textContent = 'Your XML sitemap will appear here...';
                    elements.output().style.color = '';
                    elements.progress().style.display = 'none';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Your XML sitemap will appear here...' || text.startsWith('Please enter') || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                download() {
                    const text = elements.output().textContent;
                    if (text === 'Your XML sitemap will appear here...' || text.startsWith('Please enter') || text.startsWith('Error:')) {
                        alert('Please generate a sitemap first.');
                        return;
                    }

                    const blob = new Blob([text], { type: 'application/xml' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'sitemap.xml';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    this.showNotification('Sitemap downloaded successfully!');
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message = '✓ Copied to clipboard!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        SitemapGenerator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
