<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Each Converter - Convert Dozens, Gross, Scores & More</title>
    <meta name="description" content="Instantly convert between counting units like each, dozen, gross, and score. A free online tool for inventory, education, and general quantity calculations.">
    <meta name="keywords" content="each converter, dozen converter, gross to dozen, counting unit converter, convert score to years">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Each Converter - Dozen, Gross, Score, and More",
        "description": "Convert between various counting units including each, dozen, gross, great gross, and score. A free online tool for inventory and historical quantity conversions.",
        "url": "https://www.webtoolskit.org/p/each-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-25",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Each Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Counting Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How many are in a gross?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A gross is a counting unit equal to 12 dozen, or exactly 144 items. It was historically used in commerce and trade for selling bulk items like pencils or screws."
          }
        },
        {
          "@type": "Question",
          "name": "What is a 'score' as a unit of measurement?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A score is a unit of quantity equal to 20. It is most famously used in Abraham Lincoln's Gettysburg Address, which begins, 'Four score and seven years ago,' meaning 87 years (4 × 20 + 7)."
          }
        },
        {
          "@type": "Question",
          "name": "Why is a baker's dozen 13?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A baker's dozen is 13, one more than a standard dozen. The tradition likely began in medieval England when bakers added an extra item to a dozen to avoid severe penalties for selling customers 'short weight'."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between a gross and a great gross?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A gross is 12 dozen (144 items). A great gross is a larger unit equal to 12 gross. Therefore, a great gross is 144 dozen or 1,728 individual items."
          }
        },
        {
          "@type": "Question",
          "name": "How are counting units used today?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "While 'each' is the most common unit, 'dozen' is still widely used for food items like eggs and donuts. The term 'gross' is still found in some manufacturing and wholesale industries for ordering small, mass-produced goods."
          }
        }
      ]
    }
    </script>

    <style>
        /* Each Converter Widget - Simplified & Template Compatible */
        .each-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .each-converter-widget-container * { box-sizing: border-box; }

        .each-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .each-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .each-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .each-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .each-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .each-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .each-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .each-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .each-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .each-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .each-converter-btn:hover { transform: translateY(-2px); }

        .each-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .each-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .each-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .each-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .each-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .each-converter-btn-success:hover {
            background-color: #059669;
        }

        .each-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .each-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .each-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .each-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .each-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .each-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .each-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .each-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .each-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .each-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .each-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="number-to-word-converter"] .each-converter-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="digital-converter"] .each-converter-related-tool-icon { background: linear-gradient(145deg, #0EA5E9, #0284C7); }
        a[href*="parts-per-converter"] .each-converter-related-tool-icon { background: linear-gradient(145deg, #84CC16, #65A30D); }

        .each-converter-related-tool-item:hover .each-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="number-to-word-converter"]:hover .each-converter-related-tool-icon { background: linear-gradient(145deg, #2dd4bf, #14b8a6); }
        a[href*="digital-converter"]:hover .each-converter-related-tool-icon { background: linear-gradient(145deg, #25aef3, #0ea5e9); }
        a[href*="parts-per-converter"]:hover .each-converter-related-tool-icon { background: linear-gradient(145deg, #99e23a, #84cc16); }
        
        .each-converter-related-tool-item { box-shadow: none; border: none; }
        .each-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .each-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .each-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .each-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .each-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .each-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .each-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .each-converter-related-tool-item:hover .each-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .each-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .each-converter-widget-title { font-size: 1.875rem; }
            .each-converter-buttons { flex-direction: column; }
            .each-converter-btn { flex: none; }
            .each-converter-input-group { grid-template-columns: 1fr; }
            .each-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .each-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .each-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .each-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .each-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .each-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .each-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .each-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .each-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .each-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .each-converter-output::selection { background-color: var(--primary-color); color: white; }
        .each-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .each-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="each-converter-widget-container">
        <h1 class="each-converter-widget-title">Each Converter</h1>
        <p class="each-converter-widget-description">
            Easily convert between different counting units, including dozens, gross, scores, and more. A simple tool for inventory and quantity calculations.
        </p>
        
        <div class="each-converter-input-group">
            <label for="eachFromInput" class="each-converter-label">From:</label>
            <input 
                type="number" 
                id="eachFromInput" 
                class="each-converter-input"
                placeholder="Enter quantity..."
                step="any"
            />
            <select id="eachFromUnit" class="each-converter-select">
                <option value="ea" selected>Each</option>
                <option value="doz">Dozen</option>
                <option value="gro">Gross (12 doz)</option>
                <option value="grgro">Great Gross (12 gro)</option>
                <option value="sco">Score</option>
                <option value="bakdoz">Baker's Dozen</option>
            </select>
        </div>

        <div class="each-converter-input-group">
            <label for="eachToInput" class="each-converter-label">To:</label>
            <input 
                type="number" 
                id="eachToInput" 
                class="each-converter-input"
                placeholder="Converted quantity will appear here..."
                readonly
            />
            <select id="eachToUnit" class="each-converter-select">
                <option value="ea">Each</option>
                <option value="doz" selected>Dozen</option>
                <option value="gro">Gross (12 doz)</option>
                <option value="grgro">Great Gross (12 gro)</option>
                <option value="sco">Score</option>
                <option value="bakdoz">Baker's Dozen</option>
            </select>
        </div>

        <div class="each-converter-buttons">
            <button class="each-converter-btn each-converter-btn-primary" onclick="EachConverter.convert()">
                Convert Quantity
            </button>
            <button class="each-converter-btn each-converter-btn-secondary" onclick="EachConverter.clear()">
                Clear All
            </button>
            <button class="each-converter-btn each-converter-btn-success" onclick="EachConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="each-converter-result">
            <h3 class="each-converter-result-title">Conversion Result:</h3>
            <div class="each-converter-output" id="eachConverterOutput">
                Your converted quantity will appear here...
            </div>
        </div>

        <div class="each-converter-related-tools">
            <h3 class="each-converter-related-tools-title">Related Tools</h3>
            <div class="each-converter-related-tools-grid">
                <a href="/p/number-to-word-converter.html" class="each-converter-related-tool-item" rel="noopener">
                    <div class="each-converter-related-tool-icon">
                        <i class="fas fa-spell-check"></i>
                    </div>
                    <div class="each-converter-related-tool-name">Number to Word Converter</div>
                </a>

                <a href="/p/digital-converter.html" class="each-converter-related-tool-item" rel="noopener">
                    <div class="each-converter-related-tool-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="each-converter-related-tool-name">Digital Converter</div>
                </a>

                <a href="/p/parts-per-converter.html" class="each-converter-related-tool-item" rel="noopener">
                    <div class="each-converter-related-tool-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="each-converter-related-tool-name">Parts Per Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Understanding and Converting Counting Units</h2>
            <p>While we often count things one by one, humans have used collective counting units for centuries to manage large quantities more efficiently. Our <strong>Each Converter</strong> is a free tool designed to help you easily switch between these different units, from the common 'dozen' to the historical 'gross' and 'score'. Whether you're managing inventory, following an old recipe, or just curious about these terms, this converter provides quick and accurate answers.</p>
            <p>This tool is perfect for anyone in commerce who deals with bulk goods, for students learning about historical measurements, or for writers who want to use terms like 'score' correctly. It simplifies the process, eliminating mental math and ensuring precision for any quantity.</p>

            <h3>How to Use the Each Converter</h3>
            <ol>
                <li><strong>Enter Quantity:</strong> Type the number of items you want to convert in the "From" input field.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., Gross) and the unit you wish to convert to (e.g., Dozen).</li>
                <li><strong>Click Convert:</strong> Press the "Convert Quantity" button to get your result instantly.</li>
                <li><strong>Copy or Clear:</strong> Use the "Copy Result" button to save the converted value or "Clear All" to start over.</li>
            </ol>

            <h3>Frequently Asked Questions About Counting Units</h3>

            <h4>How many are in a gross?</h4>
            <p>A gross is a counting unit equal to 12 dozen, or exactly 144 items. It was historically used in commerce and trade for selling bulk items like pencils or screws.</p>

            <h4>What is a 'score' as a unit of measurement?</h4>
            <p>A score is a unit of quantity equal to 20. It is most famously used in Abraham Lincoln's Gettysburg Address, which begins, "Four score and seven years ago," meaning 87 years (4 × 20 + 7).</p>

            <h4>Why is a baker's dozen 13?</h4>
            <p>A baker's dozen is 13, one more than a standard dozen. The tradition likely began in medieval England when bakers added an extra item to a dozen to avoid severe penalties for selling customers 'short weight'.</p>

            <h4>What is the difference between a gross and a great gross?</h4>
            <p>A gross is 12 dozen (144 items). A great gross is a larger unit equal to 12 gross. Therefore, a great gross is 144 dozen or 1,728 individual items.</p>

            <h4>How are counting units used today?</h4>
            <p>While 'each' is the most common unit, 'dozen' is still widely used for food items like eggs and donuts. The term 'gross' is still found in some manufacturing and wholesale industries for ordering small, mass-produced goods.</p>
        </div>

        <div class="each-converter-features">
            <h3 class="each-converter-features-title">Key Features:</h3>
            <ul class="each-converter-features-list">
                <li class="each-converter-features-item" style="margin-bottom: 0.3em;">Converts Dozen, Gross, Score</li>
                <li class="each-converter-features-item" style="margin-bottom: 0.3em;">Includes Baker's Dozen</li>
                <li class="each-converter-features-item" style="margin-bottom: 0.3em;">Ideal for inventory management</li>
                <li class="each-converter-features-item" style="margin-bottom: 0.3em;">High-precision integer math</li>
                <li class="each-converter-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="each-converter-features-item" style="margin-bottom: 0.3em;">Simple, user-friendly interface</li>
                <li class="each-converter-features-item">100% private and secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="each-converter-notification" id="eachConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Each Converter
        (function() {
            'use strict';

            // Conversion factors to a base unit of 'each' (1)
            const conversionFactors = {
                'ea': 1,
                'doz': 12,
                'bakdoz': 13,
                'sco': 20,
                'gro': 144,      // 12 dozen
                'grgro': 1728    // 12 gross
            };

            const elements = {
                fromInput: () => document.getElementById('eachFromInput'),
                toInput: () => document.getElementById('eachToInput'),
                fromUnit: () => document.getElementById('eachFromUnit'),
                toUnit: () => document.getElementById('eachToUnit'),
                output: () => document.getElementById('eachConverterOutput'),
                notification: () => document.getElementById('eachConverterNotification')
            };

            window.EachConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to base unit (each) first, then to target unit
                    const valueInBaseUnit = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInBaseUnit / conversionFactors[toUnit.value];

                    const formattedResult = this.formatResult(convertedValue);
                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value, value)} = ${formattedResult} ${this.getUnitName(toUnit.value, convertedValue)}`;
                },

                formatResult(value) {
                    if (Number.isInteger(value)) {
                        return value.toString();
                    }
                    if (Math.abs(value) >= 1000000) {
                        return value.toExponential(6);
                    } else if (Math.abs(value) < 0.000001 && value !== 0) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toFixed(10)).toString();
                    }
                },

                getUnitName(unit, value) {
                    const plural = value !== 1;
                    const unitNames = {
                        'ea': plural ? 'each' : 'each',
                        'doz': plural ? 'dozen' : 'dozen',
                        'bakdoz': plural ? "baker's dozens" : "baker's dozen",
                        'sco': plural ? 'scores' : 'score',
                        'gro': plural ? 'gross' : 'gross',
                        'grgro': plural ? 'great gross' : 'great gross'
                    };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted quantity will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        EachConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>