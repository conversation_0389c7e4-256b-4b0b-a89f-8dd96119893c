<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Open Graph Tag Generator - Create Social Media Meta Tags</title>
    <meta name="description" content="Generate Open Graph meta tags for better social media sharing. Create og:title, og:description, og:image tags for Facebook, Twitter, LinkedIn and more with our free tool.">
    <meta name="keywords" content="open graph tag generator, og tags, facebook meta tags, social media optimization, og:title, og:description, og:image, social sharing">
    <link rel="canonical" href="https://www.webtoolskit.org/p/open-graph-tag-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Open Graph Tag Generator - Create Social Media Meta Tags",
        "description": "Generate Open Graph meta tags for better social media sharing. Create og:title, og:description, og:image tags for Facebook, Twitter, LinkedIn and more with our free tool.",
        "url": "https://www.webtoolskit.org/p/open-graph-tag-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Open Graph Tag Generator",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Open Graph tag generation",
                "Social media optimization",
                "Facebook meta tags",
                "Twitter Card tags",
                "LinkedIn sharing optimization"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Open Graph Tags" },
            { "@type": "CopyAction", "name": "Copy Generated Tags" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What are Open Graph tags?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Open Graph tags are HTML meta tags that control how your content appears when shared on social media platforms like Facebook, Twitter, and LinkedIn. They include og:title, og:description, og:image, and og:url, which determine the title, description, image, and URL displayed in social media previews."
          }
        },
        {
          "@type": "Question",
          "name": "Why are Open Graph tags important for SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Open Graph tags improve social media engagement by making your shared content more attractive and informative. Better social sharing can lead to increased traffic, brand awareness, and indirect SEO benefits through social signals. They ensure your content displays correctly across all social platforms."
          }
        },
        {
          "@type": "Question",
          "name": "What are the required Open Graph tags?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The four required Open Graph tags are: og:title (page title), og:type (content type, usually 'website'), og:image (preview image URL), and og:url (canonical page URL). Additionally, og:description is highly recommended for providing context about your content."
          }
        },
        {
          "@type": "Question",
          "name": "What size should Open Graph images be?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The recommended Open Graph image size is 1200x630 pixels (1.91:1 aspect ratio). This ensures optimal display across all social platforms. Images should be at least 600x315 pixels and no larger than 8MB. Use high-quality images in JPG or PNG format for best results."
          }
        },
        {
          "@type": "Question",
          "name": "Do Open Graph tags work for all social media platforms?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Open Graph tags work for most major social platforms including Facebook, LinkedIn, Pinterest, and many others. Twitter uses its own Twitter Card tags but will fall back to Open Graph tags if Twitter Cards aren't present. Our generator creates both Open Graph and Twitter Card tags for maximum compatibility."
          }
        }
      ]
    }
    </script>

    <style>
        /* Open Graph Tag Generator Widget - Simplified & Template Compatible */
        .og-tag-generator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .og-tag-generator-widget-container * { box-sizing: border-box; }

        .og-tag-generator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .og-tag-generator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .og-tag-generator-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .og-tag-generator-field {
            display: flex;
            flex-direction: column;
        }

        .og-tag-generator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .og-tag-generator-input,
        .og-tag-generator-textarea,
        .og-tag-generator-select {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .og-tag-generator-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .og-tag-generator-input:focus,
        .og-tag-generator-textarea:focus,
        .og-tag-generator-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .og-tag-generator-char-count {
            font-size: 0.875rem;
            color: var(--text-color-light);
            margin-top: var(--spacing-xs);
        }

        .og-tag-generator-char-count.warning {
            color: #f59e0b;
        }

        .og-tag-generator-char-count.error {
            color: #dc2626;
        }

        .og-tag-generator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .og-tag-generator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .og-tag-generator-btn:hover { transform: translateY(-2px); }

        .og-tag-generator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .og-tag-generator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .og-tag-generator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .og-tag-generator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .og-tag-generator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .og-tag-generator-btn-success:hover {
            background-color: #059669;
        }

        .og-tag-generator-preview {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .og-tag-generator-preview-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
            text-align: center;
        }

        .og-tag-generator-social-preview {
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            max-width: 500px;
            margin: 0 auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .og-tag-generator-preview-image {
            width: 100%;
            height: 200px;
            background-color: #f3f4f6;
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color-light);
            font-size: 0.875rem;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>');
            background-repeat: no-repeat;
            background-position: center;
            background-size: 48px;
        }

        .og-tag-generator-preview-image.has-image {
            background-image: none;
        }

        .og-tag-generator-preview-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: var(--border-radius-md);
        }

        .og-tag-generator-preview-content {
            padding: var(--spacing-sm) 0;
        }

        .og-tag-generator-preview-url {
            font-size: 0.75rem;
            color: #6b7280;
            text-transform: uppercase;
            margin-bottom: var(--spacing-xs);
        }

        .og-tag-generator-preview-title-text {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-xs);
            line-height: 1.3;
        }

        .og-tag-generator-preview-description {
            font-size: 0.875rem;
            color: var(--text-color-light);
            line-height: 1.4;
        }

        .og-tag-generator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .og-tag-generator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .og-tag-generator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .og-tag-generator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .og-tag-generator-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .og-tag-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .og-tag-generator-widget-title { font-size: 1.875rem; }
            .og-tag-generator-buttons { flex-direction: column; }
            .og-tag-generator-btn { flex: none; }
        }

        [data-theme="dark"] .og-tag-generator-input:focus,
        [data-theme="dark"] .og-tag-generator-textarea:focus,
        [data-theme="dark"] .og-tag-generator-select:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .og-tag-generator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .og-tag-generator-output::selection { background-color: var(--primary-color); color: white; }

        .og-tag-generator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="meta-tag-generator"] .og-tag-generator-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="title-meta-description-checker"] .og-tag-generator-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="url-seo-analyzer"] .og-tag-generator-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }

        .og-tag-generator-related-tool-item:hover .og-tag-generator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="meta-tag-generator"]:hover .og-tag-generator-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="title-meta-description-checker"]:hover .og-tag-generator-related-tool-icon { background: linear-gradient(145deg, #38d9a9, #20c997); }
        a[href*="url-seo-analyzer"]:hover .og-tag-generator-related-tool-icon { background: linear-gradient(145deg, #7c3aed, #6366f1); }

        .og-tag-generator-related-tool-item { box-shadow: none; border: none; }
        .og-tag-generator-related-tool-item:hover { box-shadow: none; border: none; }
        .og-tag-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .og-tag-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .og-tag-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .og-tag-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .og-tag-generator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .og-tag-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .og-tag-generator-related-tool-item:hover .og-tag-generator-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .og-tag-generator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .og-tag-generator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .og-tag-generator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .og-tag-generator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .og-tag-generator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .og-tag-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .og-tag-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .og-tag-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .og-tag-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .og-tag-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .og-tag-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .og-tag-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .og-tag-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .og-tag-generator-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="og-tag-generator-widget-container">
        <h1 class="og-tag-generator-widget-title">Open Graph Tag Generator</h1>
        <p class="og-tag-generator-widget-description">
            Generate Open Graph meta tags for better social media sharing. Create optimized og:title, og:description, og:image tags for Facebook, Twitter, LinkedIn and more.
        </p>
        
        <form class="og-tag-generator-form">
            <div class="og-tag-generator-field">
                <label for="ogTitle" class="og-tag-generator-label">Page Title (og:title):</label>
                <input 
                    type="text" 
                    id="ogTitle" 
                    class="og-tag-generator-input"
                    placeholder="Enter your page title (60 characters recommended)"
                    maxlength="100"
                />
                <div class="og-tag-generator-char-count" id="titleCharCount">0/60 characters</div>
            </div>

            <div class="og-tag-generator-field">
                <label for="ogDescription" class="og-tag-generator-label">Description (og:description):</label>
                <textarea 
                    id="ogDescription" 
                    class="og-tag-generator-textarea"
                    placeholder="Enter a compelling description (155 characters recommended)"
                    maxlength="200"
                ></textarea>
                <div class="og-tag-generator-char-count" id="descCharCount">0/155 characters</div>
            </div>

            <div class="og-tag-generator-field">
                <label for="ogUrl" class="og-tag-generator-label">Page URL (og:url):</label>
                <input 
                    type="url" 
                    id="ogUrl" 
                    class="og-tag-generator-input"
                    placeholder="https://example.com/page"
                />
            </div>

            <div class="og-tag-generator-field">
                <label for="ogImage" class="og-tag-generator-label">Image URL (og:image):</label>
                <input 
                    type="url" 
                    id="ogImage" 
                    class="og-tag-generator-input"
                    placeholder="https://example.com/image.jpg (1200x630px recommended)"
                />
            </div>

            <div class="og-tag-generator-field">
                <label for="ogType" class="og-tag-generator-label">Content Type (og:type):</label>
                <select id="ogType" class="og-tag-generator-select">
                    <option value="website">Website</option>
                    <option value="article">Article</option>
                    <option value="blog">Blog</option>
                    <option value="product">Product</option>
                    <option value="video">Video</option>
                    <option value="music">Music</option>
                    <option value="book">Book</option>
                </select>
            </div>

            <div class="og-tag-generator-field">
                <label for="ogSiteName" class="og-tag-generator-label">Site Name (og:site_name) - Optional:</label>
                <input 
                    type="text" 
                    id="ogSiteName" 
                    class="og-tag-generator-input"
                    placeholder="Your Website Name"
                />
            </div>
        </form>

        <div class="og-tag-generator-preview">
            <h3 class="og-tag-generator-preview-title">Social Media Preview</h3>
            <div class="og-tag-generator-social-preview">
                <div class="og-tag-generator-preview-image" id="previewImage">
                    No image selected
                </div>
                <div class="og-tag-generator-preview-content">
                    <div class="og-tag-generator-preview-url" id="previewUrl">example.com</div>
                    <div class="og-tag-generator-preview-title-text" id="previewTitle">Your page title will appear here</div>
                    <div class="og-tag-generator-preview-description" id="previewDescription">Your description will appear here. This is how your content will look when shared on social media.</div>
                </div>
            </div>
        </div>

        <div class="og-tag-generator-buttons">
            <button class="og-tag-generator-btn og-tag-generator-btn-primary" onclick="OpenGraphGenerator.generate()">
                Generate Tags
            </button>
            <button class="og-tag-generator-btn og-tag-generator-btn-secondary" onclick="OpenGraphGenerator.clear()">
                Clear All
            </button>
            <button class="og-tag-generator-btn og-tag-generator-btn-success" onclick="OpenGraphGenerator.copy()">
                Copy Tags
            </button>
        </div>

        <div class="og-tag-generator-result">
            <h3 class="og-tag-generator-result-title">Generated Open Graph Tags:</h3>
            <div class="og-tag-generator-output" id="ogOutput">Your generated Open Graph tags will appear here...</div>
        </div>

        <div class="og-tag-generator-related-tools">
            <h3 class="og-tag-generator-related-tools-title">Related Tools</h3>
            <div class="og-tag-generator-related-tools-grid">
                <a href="/p/meta-tag-generator.html" class="og-tag-generator-related-tool-item" rel="noopener">
                    <div class="og-tag-generator-related-tool-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="og-tag-generator-related-tool-name">Meta Tag Generator</div>
                </a>

                <a href="/p/title-meta-description-checker.html" class="og-tag-generator-related-tool-item" rel="noopener">
                    <div class="og-tag-generator-related-tool-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="og-tag-generator-related-tool-name">Title Meta Description Checker</div>
                </a>

                <a href="/p/url-seo-analyzer.html" class="og-tag-generator-related-tool-item" rel="noopener">
                    <div class="og-tag-generator-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="og-tag-generator-related-tool-name">URL SEO Analyzer</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Open Graph Tag Generator for Social Media</h2>
            <p>Our <strong>Open Graph Tag Generator</strong> helps you create optimized meta tags for better social media sharing across Facebook, Twitter, LinkedIn, and other platforms. Open Graph tags control how your content appears when shared, improving engagement and click-through rates from social media.</p>
            <p>Whether you're a social media manager, content creator, or website owner, our tool makes it easy to generate properly formatted Open Graph tags that ensure your content looks professional and attractive when shared on social platforms.</p>

            <h3>How to Use the Open Graph Tag Generator</h3>
            <ol>
                <li><strong>Enter Content Details:</strong> Fill in your page title, description, URL, image URL, and content type.</li>
                <li><strong>Preview Your Content:</strong> See how your content will appear on social media platforms in real-time.</li>
                <li><strong>Generate Tags:</strong> Click "Generate Tags" to create properly formatted Open Graph meta tags.</li>
                <li><strong>Copy and Implement:</strong> Copy the generated HTML code and paste it into your webpage's head section.</li>
            </ol>

            <h3>Frequently Asked Questions About Open Graph Tags</h3>

            <h4>What are Open Graph tags?</h4>
            <p>Open Graph tags are HTML meta tags that control how your content appears when shared on social media platforms like Facebook, Twitter, and LinkedIn. They include og:title, og:description, og:image, and og:url, which determine the title, description, image, and URL displayed in social media previews.</p>

            <h4>Why are Open Graph tags important for SEO?</h4>
            <p>Open Graph tags improve social media engagement by making your shared content more attractive and informative. Better social sharing can lead to increased traffic, brand awareness, and indirect SEO benefits through social signals. They ensure your content displays correctly across all social platforms.</p>

            <h4>What are the required Open Graph tags?</h4>
            <p>The four required Open Graph tags are: og:title (page title), og:type (content type, usually 'website'), og:image (preview image URL), and og:url (canonical page URL). Additionally, og:description is highly recommended for providing context about your content.</p>

            <h4>What size should Open Graph images be?</h4>
            <p>The recommended Open Graph image size is 1200x630 pixels (1.91:1 aspect ratio). This ensures optimal display across all social platforms. Images should be at least 600x315 pixels and no larger than 8MB. Use high-quality images in JPG or PNG format for best results.</p>

            <h4>Do Open Graph tags work for all social media platforms?</h4>
            <p>Open Graph tags work for most major social platforms including Facebook, LinkedIn, Pinterest, and many others. Twitter uses its own Twitter Card tags but will fall back to Open Graph tags if Twitter Cards aren't present. Our generator creates both Open Graph and Twitter Card tags for maximum compatibility.</p>
        </div>

        <div class="og-tag-generator-features">
            <h3 class="og-tag-generator-features-title">Key Features:</h3>
            <ul class="og-tag-generator-features-list">
                <li class="og-tag-generator-features-item" style="margin-bottom: 0.3em;">Complete Open Graph Tag Generation</li>
                <li class="og-tag-generator-features-item" style="margin-bottom: 0.3em;">Real-time Social Media Preview</li>
                <li class="og-tag-generator-features-item" style="margin-bottom: 0.3em;">Twitter Card Tags Included</li>
                <li class="og-tag-generator-features-item" style="margin-bottom: 0.3em;">Character Count Validation</li>
                <li class="og-tag-generator-features-item" style="margin-bottom: 0.3em;">Multiple Content Types Support</li>
                <li class="og-tag-generator-features-item" style="margin-bottom: 0.3em;">One-Click Copy to Clipboard</li>
                <li class="og-tag-generator-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="og-tag-generator-notification" id="ogNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                ogTitle: () => document.getElementById('ogTitle'),
                ogDescription: () => document.getElementById('ogDescription'),
                ogUrl: () => document.getElementById('ogUrl'),
                ogImage: () => document.getElementById('ogImage'),
                ogType: () => document.getElementById('ogType'),
                ogSiteName: () => document.getElementById('ogSiteName'),
                titleCharCount: () => document.getElementById('titleCharCount'),
                descCharCount: () => document.getElementById('descCharCount'),
                previewImage: () => document.getElementById('previewImage'),
                previewUrl: () => document.getElementById('previewUrl'),
                previewTitle: () => document.getElementById('previewTitle'),
                previewDescription: () => document.getElementById('previewDescription'),
                ogOutput: () => document.getElementById('ogOutput'),
                notification: () => document.getElementById('ogNotification')
            };

            function updateCharCount(input, counter, maxLength) {
                const length = input.value.length;
                counter.textContent = `${length}/${maxLength} characters`;

                counter.classList.remove('warning', 'error');
                if (length > maxLength * 0.9) {
                    counter.classList.add('warning');
                }
                if (length > maxLength) {
                    counter.classList.add('error');
                }
            }

            function updatePreview() {
                const title = elements.ogTitle().value.trim() || 'Your page title will appear here';
                const description = elements.ogDescription().value.trim() || 'Your description will appear here. This is how your content will look when shared on social media.';
                const url = elements.ogUrl().value.trim() || 'example.com';
                const image = elements.ogImage().value.trim();

                // Update preview content
                elements.previewTitle().textContent = title;
                elements.previewDescription().textContent = description;

                // Update URL display
                try {
                    const urlObj = new URL(url);
                    elements.previewUrl().textContent = urlObj.hostname;
                } catch (e) {
                    elements.previewUrl().textContent = url;
                }

                // Update image preview
                const previewImageEl = elements.previewImage();
                if (image) {
                    previewImageEl.innerHTML = `<img src="${image}" alt="Preview" onerror="this.parentElement.innerHTML='Invalid image URL'; this.parentElement.classList.remove('has-image');">`;
                    previewImageEl.classList.add('has-image');
                } else {
                    previewImageEl.innerHTML = 'No image selected';
                    previewImageEl.classList.remove('has-image');
                }
            }

            window.OpenGraphGenerator = {
                generate() {
                    const title = elements.ogTitle().value.trim();
                    const description = elements.ogDescription().value.trim();
                    const url = elements.ogUrl().value.trim();
                    const image = elements.ogImage().value.trim();
                    const type = elements.ogType().value;
                    const siteName = elements.ogSiteName().value.trim();
                    const output = elements.ogOutput();

                    if (!title && !description && !url) {
                        output.textContent = 'Please enter at least a title, description, or URL to generate Open Graph tags.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';

                    let ogTags = '';

                    // Basic Open Graph tags
                    if (title) {
                        ogTags += `<meta property="og:title" content="${this.escapeHtml(title)}">\n`;
                    }

                    if (description) {
                        ogTags += `<meta property="og:description" content="${this.escapeHtml(description)}">\n`;
                    }

                    if (url) {
                        ogTags += `<meta property="og:url" content="${this.escapeHtml(url)}">\n`;
                    }

                    if (image) {
                        ogTags += `<meta property="og:image" content="${this.escapeHtml(image)}">\n`;
                        ogTags += `<meta property="og:image:width" content="1200">\n`;
                        ogTags += `<meta property="og:image:height" content="630">\n`;
                    }

                    ogTags += `<meta property="og:type" content="${type}">\n`;

                    if (siteName) {
                        ogTags += `<meta property="og:site_name" content="${this.escapeHtml(siteName)}">\n`;
                    }

                    // Twitter Card tags for better compatibility
                    ogTags += `<meta name="twitter:card" content="summary_large_image">\n`;

                    if (title) {
                        ogTags += `<meta name="twitter:title" content="${this.escapeHtml(title)}">\n`;
                    }

                    if (description) {
                        ogTags += `<meta name="twitter:description" content="${this.escapeHtml(description)}">\n`;
                    }

                    if (image) {
                        ogTags += `<meta name="twitter:image" content="${this.escapeHtml(image)}">\n`;
                    }

                    if (siteName) {
                        ogTags += `<meta name="twitter:site" content="@${this.escapeHtml(siteName.replace(/\s+/g, ''))}">\n`;
                    }

                    output.textContent = ogTags;
                },

                escapeHtml(text) {
                    const div = document.createElement('div');
                    div.textContent = text;
                    return div.innerHTML;
                },

                clear() {
                    elements.ogTitle().value = '';
                    elements.ogDescription().value = '';
                    elements.ogUrl().value = '';
                    elements.ogImage().value = '';
                    elements.ogType().selectedIndex = 0;
                    elements.ogSiteName().value = '';
                    elements.ogOutput().textContent = 'Your generated Open Graph tags will appear here...';
                    elements.ogOutput().style.color = '';
                    elements.titleCharCount().textContent = '0/60 characters';
                    elements.descCharCount().textContent = '0/155 characters';
                    elements.titleCharCount().classList.remove('warning', 'error');
                    elements.descCharCount().classList.remove('warning', 'error');

                    // Reset preview
                    updatePreview();
                },

                copy() {
                    const text = elements.ogOutput().textContent;
                    if (text === 'Your generated Open Graph tags will appear here...' || text.startsWith('Please enter') || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Character count updates
                elements.ogTitle().addEventListener('input', function() {
                    updateCharCount(this, elements.titleCharCount(), 60);
                    updatePreview();
                });

                elements.ogDescription().addEventListener('input', function() {
                    updateCharCount(this, elements.descCharCount(), 155);
                    updatePreview();
                });

                // Update preview on all input changes
                elements.ogUrl().addEventListener('input', updatePreview);
                elements.ogImage().addEventListener('input', updatePreview);
                elements.ogType().addEventListener('change', updatePreview);
                elements.ogSiteName().addEventListener('input', updatePreview);

                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        OpenGraphGenerator.generate();
                    }
                });

                // Initialize preview
                updatePreview();
            });
        })();
    </script>
</body>
</html>
