<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal Fee Calculator Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free PayPal Fee Calculator - Calculate Transaction Fees & Net Amount",
        "description": "Calculate PayPal transaction fees and net amounts instantly. Free online PayPal fee calculator for business and personal payments with real-time calculations.",
        "url": "https://www.webtoolskit.org/p/paypal-fee-calculator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "PayPal Fee Calculator",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CalculateAction", "name": "Calculate PayPal Fee" },
            { "@type": "CalculateAction", "name": "Calculate Net Amount" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I calculate my PayPal fee?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To calculate your PayPal fee, multiply the transaction amount by the PayPal fee rate (typically 2.9% for domestic transactions) and add the fixed fee ($0.30 for USD). Formula: Fee = (Amount × 0.029) + $0.30. For example, on a $100 transaction: Fee = ($100 × 0.029) + $0.30 = $3.20."
          }
        },
        {
          "@type": "Question",
          "name": "How much is the PayPal fee for $100?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The PayPal fee for $100 is $3.20 for domestic transactions. This includes the 2.9% variable fee ($2.90) plus the $0.30 fixed fee. You would receive $96.80 after PayPal deducts the fee. International transactions may have higher fees depending on the country and currency."
          }
        },
        {
          "@type": "Question",
          "name": "Why is PayPal charging me a fee to receive money?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "PayPal charges fees to receive money because they provide payment processing services, fraud protection, buyer protection, and secure transaction handling. These fees cover operational costs, security measures, and customer support. Personal payments between friends and family are typically free when funded by bank accounts."
          }
        },
        {
          "@type": "Question",
          "name": "How do I avoid PayPal fees?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To avoid PayPal fees, use 'Friends & Family' for personal payments, fund transactions with bank accounts instead of credit cards, consider alternative payment methods like bank transfers, or negotiate with buyers to cover the fees. For businesses, you can adjust your pricing to account for PayPal fees."
          }
        },
        {
          "@type": "Question",
          "name": "Who pays PayPal fees, the sender or receiver?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "By default, the receiver pays PayPal fees. The fee is automatically deducted from the payment amount before it's credited to the recipient's account. However, senders can choose to pay the fees themselves by selecting the appropriate option during checkout, though this may result in higher total fees."
          }
        }
      ]
    }
    </script>


    <style>
        /* PayPal Fee Calculator Widget - Simplified & Template Compatible */
        .paypal-fee-calculator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .paypal-fee-calculator-widget-container * { box-sizing: border-box; }

        .paypal-fee-calculator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .paypal-fee-calculator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .paypal-fee-calculator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .paypal-fee-calculator-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .paypal-fee-calculator-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .paypal-fee-calculator-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .paypal-fee-calculator-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .paypal-fee-calculator-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .paypal-fee-calculator-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .paypal-fee-calculator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .paypal-fee-calculator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .paypal-fee-calculator-btn:hover { transform: translateY(-2px); }

        .paypal-fee-calculator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .paypal-fee-calculator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .paypal-fee-calculator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .paypal-fee-calculator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .paypal-fee-calculator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .paypal-fee-calculator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .paypal-fee-calculator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .paypal-fee-calculator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .paypal-fee-calculator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .paypal-fee-calculator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .paypal-fee-calculator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .paypal-fee-calculator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .paypal-fee-calculator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .paypal-fee-calculator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .paypal-fee-calculator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="margin-calculator"] .paypal-fee-calculator-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="sales-tax-calculator"] .paypal-fee-calculator-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="percentage-calculator"] .paypal-fee-calculator-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .paypal-fee-calculator-related-tool-item:hover .paypal-fee-calculator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="margin-calculator"]:hover .paypal-fee-calculator-related-tool-icon { background: linear-gradient(145deg, #059669, #047857); }
        a[href*="sales-tax-calculator"]:hover .paypal-fee-calculator-related-tool-icon { background: linear-gradient(145deg, #D97706, #B45309); }
        a[href*="percentage-calculator"]:hover .paypal-fee-calculator-related-tool-icon { background: linear-gradient(145deg, #7C3AED, #6D28D9); }
        
        .paypal-fee-calculator-related-tool-item { box-shadow: none; border: none; }
        .paypal-fee-calculator-related-tool-item:hover { box-shadow: none; border: none; }
        .paypal-fee-calculator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .paypal-fee-calculator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .paypal-fee-calculator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .paypal-fee-calculator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .paypal-fee-calculator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .paypal-fee-calculator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .paypal-fee-calculator-related-tool-item:hover .paypal-fee-calculator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .paypal-fee-calculator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .paypal-fee-calculator-widget-title { font-size: 1.875rem; }
            .paypal-fee-calculator-buttons { flex-direction: column; }
            .paypal-fee-calculator-btn { flex: none; }
            .paypal-fee-calculator-options { grid-template-columns: 1fr; }
            .paypal-fee-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .paypal-fee-calculator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .paypal-fee-calculator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .paypal-fee-calculator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .paypal-fee-calculator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .paypal-fee-calculator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .paypal-fee-calculator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .paypal-fee-calculator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .paypal-fee-calculator-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .paypal-fee-calculator-checkbox:focus, .paypal-fee-calculator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .paypal-fee-calculator-output::selection { background-color: var(--primary-color); color: white; }
        .paypal-fee-calculator-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .paypal-fee-calculator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="paypal-fee-calculator-widget-container">
        <h1 class="paypal-fee-calculator-widget-title">PayPal Fee Calculator</h1>
        <p class="paypal-fee-calculator-widget-description">
            Calculate PayPal transaction fees and determine the exact amount you'll receive after fees are deducted.
        </p>
        
        <div class="paypal-fee-calculator-input-group">
            <label for="paypalFeeCalculatorAmount" class="paypal-fee-calculator-label">Transaction Amount ($):</label>
            <input 
                type="number" 
                id="paypalFeeCalculatorAmount" 
                class="paypal-fee-calculator-input"
                placeholder="Enter the payment amount..."
                step="0.01"
                min="0"
            />
        </div>

        <div class="paypal-fee-calculator-options">
            <div class="paypal-fee-calculator-option">
                <input type="checkbox" id="paypalInternational" class="paypal-fee-calculator-checkbox">
                <label for="paypalInternational" class="paypal-fee-calculator-option-label">International transaction</label>
            </div>
            <div class="paypal-fee-calculator-option">
                <input type="checkbox" id="paypalCommercial" class="paypal-fee-calculator-checkbox" checked>
                <label for="paypalCommercial" class="paypal-fee-calculator-option-label">Commercial transaction</label>
            </div>
            <div class="paypal-fee-calculator-option">
                <input type="checkbox" id="paypalMicropayment" class="paypal-fee-calculator-checkbox">
                <label for="paypalMicropayment" class="paypal-fee-calculator-option-label">Micropayment (under $10)</label>
            </div>
        </div>

        <div class="paypal-fee-calculator-buttons">
            <button class="paypal-fee-calculator-btn paypal-fee-calculator-btn-primary" onclick="PayPalFeeCalculator.calculate()">
                Calculate Fee
            </button>
            <button class="paypal-fee-calculator-btn paypal-fee-calculator-btn-secondary" onclick="PayPalFeeCalculator.clear()">
                Clear All
            </button>
        </div>

        <div class="paypal-fee-calculator-result">
            <h3 class="paypal-fee-calculator-result-title">Fee Calculation:</h3>
            <div class="paypal-fee-calculator-output" id="paypalFeeCalculatorOutput">
                Your PayPal fee calculation will appear here...
            </div>
        </div>

        <div class="paypal-fee-calculator-related-tools">
            <h3 class="paypal-fee-calculator-related-tools-title">Related Tools</h3>
            <div class="paypal-fee-calculator-related-tools-grid">
                <a href="/p/margin-calculator.html" class="paypal-fee-calculator-related-tool-item" rel="noopener">
                    <div class="paypal-fee-calculator-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="paypal-fee-calculator-related-tool-name">Margin Calculator</div>
                </a>

                <a href="/p/sales-tax-calculator.html" class="paypal-fee-calculator-related-tool-item" rel="noopener">
                    <div class="paypal-fee-calculator-related-tool-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="paypal-fee-calculator-related-tool-name">Sales Tax Calculator</div>
                </a>

                <a href="/p/percentage-calculator.html" class="paypal-fee-calculator-related-tool-item" rel="noopener">
                    <div class="paypal-fee-calculator-related-tool-icon">
                        <i class="fas fa-percent"></i>
                    </div>
                    <div class="paypal-fee-calculator-related-tool-name">Percentage Calculator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Calculate PayPal Transaction Fees Accurately</h2>
            <p>Understanding PayPal fees is essential for businesses and individuals who regularly receive payments through the platform. Our <strong>PayPal fee calculator</strong> helps you determine the exact amount you'll receive after PayPal deducts their transaction fees. Whether you're selling products online, providing services, or receiving payments from clients, knowing the fee structure helps you price your offerings appropriately and manage your cash flow effectively.</p>
            <p>PayPal's fee structure varies based on transaction type, amount, and location. Domestic transactions typically incur lower fees than international ones, while micropayments have different rates optimized for small amounts. By calculating these fees in advance, you can make informed decisions about pricing and payment methods.</p>
            
            <h3>How to Use the PayPal Fee Calculator</h3>
            <ol>
                <li><strong>Enter Transaction Amount:</strong> Input the payment amount you're expecting to receive.</li>
                <li><strong>Select Transaction Type:</strong> Choose whether it's international, commercial, or a micropayment.</li>
                <li><strong>Calculate Fees:</strong> Click "Calculate Fee" to see the PayPal fee and net amount you'll receive.</li>
            </ol>
        
            <h3>Frequently Asked Questions About PayPal Fees</h3>
            
            <h4>How do I calculate my PayPal fee?</h4>
            <p>To calculate your PayPal fee, multiply the transaction amount by the PayPal fee rate (typically 2.9% for domestic transactions) and add the fixed fee ($0.30 for USD). Formula: Fee = (Amount × 0.029) + $0.30. For example, on a $100 transaction: Fee = ($100 × 0.029) + $0.30 = $3.20.</p>
            
            <h4>How much is the PayPal fee for $100?</h4>
            <p>The PayPal fee for $100 is $3.20 for domestic transactions. This includes the 2.9% variable fee ($2.90) plus the $0.30 fixed fee. You would receive $96.80 after PayPal deducts the fee. International transactions may have higher fees depending on the country and currency.</p>
            
            <h4>Why is PayPal charging me a fee to receive money?</h4>
            <p>PayPal charges fees to receive money because they provide payment processing services, fraud protection, buyer protection, and secure transaction handling. These fees cover operational costs, security measures, and customer support. Personal payments between friends and family are typically free when funded by bank accounts.</p>
            
            <h4>How do I avoid PayPal fees?</h4>
            <p>To avoid PayPal fees, use 'Friends & Family' for personal payments, fund transactions with bank accounts instead of credit cards, consider alternative payment methods like bank transfers, or negotiate with buyers to cover the fees. For businesses, you can adjust your pricing to account for PayPal fees.</p>
            
            <h4>Who pays PayPal fees, the sender or receiver?</h4>
            <p>By default, the receiver pays PayPal fees. The fee is automatically deducted from the payment amount before it's credited to the recipient's account. However, senders can choose to pay the fees themselves by selecting the appropriate option during checkout, though this may result in higher total fees.</p>
        </div>


        <div class="paypal-fee-calculator-features">
            <h3 class="paypal-fee-calculator-features-title">Key Features:</h3>
            <ul class="paypal-fee-calculator-features-list">
                <li class="paypal-fee-calculator-features-item" style="margin-bottom: 0.3em;">Accurate fee calculation</li>
                <li class="paypal-fee-calculator-features-item" style="margin-bottom: 0.3em;">Multiple transaction types</li>
                <li class="paypal-fee-calculator-features-item" style="margin-bottom: 0.3em;">International fee support</li>
                <li class="paypal-fee-calculator-features-item" style="margin-bottom: 0.3em;">Micropayment calculations</li>
                <li class="paypal-fee-calculator-features-item" style="margin-bottom: 0.3em;">Net amount display</li>
                <li class="paypal-fee-calculator-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="paypal-fee-calculator-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="paypal-fee-calculator-notification" id="paypalFeeCalculatorNotification">
        ✓ Calculation completed!
    </div>

    <script>
        // Simplified PayPal Fee Calculator
        (function() {
            'use strict';

            const elements = {
                amountInput: () => document.getElementById('paypalFeeCalculatorAmount'),
                output: () => document.getElementById('paypalFeeCalculatorOutput'),
                notification: () => document.getElementById('paypalFeeCalculatorNotification')
            };

            window.PayPalFeeCalculator = {
                calculate() {
                    const amount = parseFloat(elements.amountInput().value);
                    const output = elements.output();

                    if (!amount || amount <= 0) {
                        output.innerHTML = 'Please enter a valid transaction amount.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    
                    const isInternational = document.getElementById('paypalInternational').checked;
                    const isCommercial = document.getElementById('paypalCommercial').checked;
                    const isMicropayment = document.getElementById('paypalMicropayment').checked;

                    let feeRate, fixedFee;

                    if (isMicropayment && amount <= 10) {
                        // Micropayment rates
                        feeRate = 0.05; // 5%
                        fixedFee = 0.05; // $0.05
                    } else if (isInternational) {
                        // International rates
                        feeRate = 0.044; // 4.4%
                        fixedFee = 0.30; // $0.30
                    } else if (isCommercial) {
                        // Standard commercial rates
                        feeRate = 0.029; // 2.9%
                        fixedFee = 0.30; // $0.30
                    } else {
                        // Personal/Friends & Family (free for bank account funding)
                        feeRate = 0;
                        fixedFee = 0;
                    }

                    const variableFee = amount * feeRate;
                    const totalFee = variableFee + fixedFee;
                    const netAmount = amount - totalFee;
                    const feePercentage = (totalFee / amount) * 100;

                    output.innerHTML = `
                        <strong>Transaction Amount:</strong> $${amount.toFixed(2)}<br>
                        <strong>PayPal Fee:</strong> $${totalFee.toFixed(2)} (${feePercentage.toFixed(2)}%)<br>
                        <strong>Net Amount Received:</strong> $${netAmount.toFixed(2)}
                    `;

                    this.showNotification();
                },

                clear() {
                    elements.amountInput().value = '';
                    elements.output().innerHTML = 'Your PayPal fee calculation will appear here...';
                    elements.output().style.color = '';
                    
                    // Reset checkboxes to default
                    document.getElementById('paypalInternational').checked = false;
                    document.getElementById('paypalCommercial').checked = true;
                    document.getElementById('paypalMicropayment').checked = false;
                    
                    this.showNotification('Calculator cleared');
                },

                showNotification(message = '✓ Calculation completed!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const amountInput = elements.amountInput();
                const checkboxes = document.querySelectorAll('.paypal-fee-calculator-checkbox');

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Removed auto-calculate functionality - now only calculates when button is clicked

                // Handle Enter key
                amountInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        PayPalFeeCalculator.calculate();
                    }
                });
            });
        })();
    </script>
</body>
</html>