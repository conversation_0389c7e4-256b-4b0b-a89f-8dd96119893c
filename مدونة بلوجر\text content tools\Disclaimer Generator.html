<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disclaimer Generator Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Disclaimer Generator - Create Legal Disclaimers Instantly",
        "description": "Generate professional disclaimers for websites, blogs, and content instantly. Free online tool with customizable options, multiple disclaimer types, and one-click copying.",
        "url": "https://www.webtoolskit.org/p/disclaimer-generator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Disclaimer Generator",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Disclaimer" },
            { "@type": "CopyAction", "name": "Copy Generated Disclaimer" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I write a disclaimer?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To write a disclaimer, start by identifying the potential risks and liabilities associated with your content or service. Use a disclaimer generator to create a baseline document covering key areas like information accuracy, external links, and professional advice. Be clear, specific, and place the disclaimer where users can easily find it."
          }
        },
        {
          "@type": "Question",
          "name": "Can I write my own disclaimer?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can write your own disclaimer, and using a generator is an excellent way to start. It helps ensure you include standard legal clauses. However, because a disclaimer is a legal statement, we strongly recommend having the final text reviewed by a qualified attorney to ensure it provides adequate protection."
          }
        },
        {
          "@type": "Question",
          "name": "What is a good disclaimer example?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A good, clear disclaimer example is: 'The information provided by [Your Website Name] on [Your Website URL] is for general informational purposes only. All information on the site is provided in good faith, however, we make no representation or warranty of any kind regarding the accuracy, validity, or completeness of any information on the site.'"
          }
        },
        {
          "@type": "Question",
          "name": "What is a formal disclaimer?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A formal disclaimer is a legally-drafted statement intended to limit liability. It uses precise language to define the scope of responsibility for the information or services offered. Key components often include a limitation of liability, a warranty disclaimer, and a statement advising users not to rely on the information as professional advice."
          }
        },
        {
          "@type": "Question",
          "name": "What is an example of a disclaimer message?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A common example of a short disclaimer message, often used for affiliate marketing, is: 'This post contains affiliate links. If you use these links to buy something, we may earn a commission. Thanks.' For health content, an example is: 'This article is not a substitute for professional medical advice.'"
          }
        }
      ]
    }
    </script>


    <style>
        /* Mobile-First Reset */
        * {
            box-sizing: border-box;
        }

        html {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: var(--font-family, Arial, sans-serif);
            background: var(--background-color, #f6f8fa);
            color: var(--text-color, #222);
            overflow-x: hidden;
        }
        .widget-container {
            max-width: 800px;
            margin: var(--spacing-xl, 2rem) auto;
            padding: var(--spacing-xl, 2rem);
            background: var(--card-bg, #fff);
            border-radius: var(--border-radius-lg, 1rem);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family, Arial, sans-serif);
            border: 1px solid var(--border-color, #e5e7eb);
            width: 100%;
            box-sizing: border-box;
        }
        .widget-title {
            text-align: center;
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, #EF4444, #DC2626);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }
        .widget-description {
            text-align: center;
            color: #6b7280;
            margin-bottom: 2rem;
            font-size: 1.125rem;
            line-height: 1.6;
        }
        
        .form {
            display: grid;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        .label {
            font-weight: 600;
            color: #222;
            font-size: 0.95rem;
        }
        .input, .select {
            padding: 0.5rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            font-size: 1rem;
            background: #f3f4f6;
            color: #222;
        }
        .input:focus, .select:focus {
            outline: none;
            border-color: #EF4444;
            background: #fff;
        }
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .checkbox {
            width: 20px;
            height: 20px;
            accent-color: #EF4444;
            cursor: pointer;
        }
        .checkbox-label {
            font-weight: 500;
            color: #222;
            font-size: 0.95rem;
        }
        .buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        .btn {
            padding: var(--spacing-sm, 0.5rem) var(--spacing-lg, 1.5rem);
            border: none;
            border-radius: var(--border-radius-md, 0.5rem);
            font-size: var(--font-size-base, 1rem);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base, all 0.2s);
            flex: 1;
            min-width: 140px;
            min-height: 44px;
            touch-action: manipulation;
        }
        .btn-primary { background: #EF4444; color: #fff; }
        .btn-primary:hover { background: #DC2626; }
        .btn-secondary { background: #f3f4f6; color: #222; border: 1px solid #e5e7eb; }
        .btn-secondary:hover { background: #e5e7eb; }
        .btn-success { background: #10b981; color: #fff; }
        .btn-success:hover { background: #059669; }
        .result {
            background: #f3f4f6;
            border-radius: 1rem;
            padding: 1rem;
            border-left: 4px solid #EF4444;
            border: 1px solid #e5e7eb;
        }
        .result-title {
            margin: 0 0 1rem 0;
            color: #222;
            font-size: 1.25rem;
            font-weight: 700;
        }
        .output {
            background: #fff;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            font-size: 1rem;
            min-height: 120px;
            color: #222;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: #fff;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: transform 0.3s;
        }
        .notification.show { transform: translateX(0); }
        .disclaimer {
            margin-top: 2rem;
            padding: 1rem;
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 0.5rem;
            color: #92400e;
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        /* SEO Content Section Styles */
        .seo-content {
            margin-top: var(--spacing-xl, 2rem);
            padding-top: var(--spacing-xl, 2rem);
            border-top: 1px solid var(--border-color, #e5e7eb);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color, #222);
            margin-bottom: var(--spacing-md, 1rem);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg, 1.5rem); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg, 1.5rem); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md, 1rem); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm, 0.5rem); }
        
        /* === START: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        .related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="privacy-policy-generator"] .related-tool-icon { background: linear-gradient(145deg, #0EA5E9, #0284C7); }
        a[href*="terms-and-condition-generator"] .related-tool-icon { background: linear-gradient(145deg, #4F46E5, #4338CA); }
        a[href*="text-to-slug"] .related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }

        .related-tool-item:hover .related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="privacy-policy-generator"]:hover .related-tool-icon { background: linear-gradient(145deg, #38bdf8, #0EA5E9); }
        a[href*="terms-and-condition-generator"]:hover .related-tool-icon { background: linear-gradient(145deg, #6366f1, #4F46E5); }
        a[href*="text-to-slug"]:hover .related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        
        .related-tool-item {
            box-shadow: none; border: none; text-align: center; text-decoration: none; color: inherit;
            transition: var(--transition-base, all 0.3s); padding: var(--spacing-lg, 1.5rem);
            border-radius: var(--border-radius-lg, 1rem); display: block; width: 100%; max-width: 160px;
        }
        .related-tool-item:hover { transform: translateY(0); background-color: transparent; box-shadow: none; border: none; }
        .related-tools { margin-top: var(--spacing-xl, 2rem); padding-top: var(--spacing-xl, 2rem); border-top: 1px solid var(--border-color, #e5e7eb); }
        .related-tools-title { color: var(--text-color, #222); margin-bottom: var(--spacing-xl, 2rem); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg, 1.5rem); margin-top: var(--spacing-lg, 1.5rem); justify-items: center; }
        .related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color, #222); margin-top: var(--spacing-sm, 0.5rem); line-height: 1.3; }
        .related-tool-item:hover .related-tool-name { color: var(--primary-color, #EF4444); }
        
        /* === END: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        /* Key Features Section - Enhanced */
        .disclaimer-features {
            margin-top: var(--spacing-xl, 2rem);
            padding-top: var(--spacing-xl, 2rem);
            border-top: 1px solid var(--border-color, #e5e7eb);
        }

        .disclaimer-features-title {
            color: var(--text-color, #222);
            margin-bottom: var(--spacing-md, 1rem);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .disclaimer-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .disclaimer-features-item {
            padding: var(--spacing-sm, 0.5rem) 0 var(--spacing-sm, 0.5rem) var(--spacing-lg, 1.5rem);
            color: var(--text-color-light, #6b7280);
            position: relative;
            margin-bottom: 0.3em;
        }

        .disclaimer-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm, 0.5rem) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        /* Mobile-First Responsive Design */
        @media (max-width: 768px) {
            .widget-container { 
                margin: 10px; 
                padding: 20px; 
                max-width: calc(100% - 20px);
            }
            .widget-title { font-size: 1.75rem; }
            .widget-description { font-size: 1rem; margin-bottom: 20px; }
            .buttons { flex-direction: column; gap: 12px; }
            .btn { flex: none; min-width: auto; width: 100%; }
            .checkbox-group { 
                grid-template-columns: 1fr; 
                gap: 12px; 
            }
            .input, .select { 
                padding: 12px 15px; 
                font-size: 16px; 
                width: 100%;
                box-sizing: border-box;
            }
            .output { 
                padding: 15px; 
                font-size: 14px; 
                max-height: 300px;
            }
            .form-group {
                gap: 8px;
            }
            .related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md, 1rem); }
            .related-tool-item { padding: var(--spacing-md, 1rem); max-width: none; }
            .related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .related-tool-name { font-size: 0.875rem; }
            .disclaimer-features-list { columns: 1; -webkit-columns: 1; -moz-columns: 1; }
        }

        @media (max-width: 480px) {
            .widget-container { 
                margin: 5px; 
                padding: 15px; 
                max-width: calc(100% - 10px);
            }
            .widget-title { font-size: 1.5rem; }
            .widget-description { font-size: 0.95rem; }
            .form {
                gap: 1rem;
            }
            .form-group {
                gap: 6px;
            }
            .input, .select { 
                padding: 10px 12px; 
                font-size: 16px; 
                width: 100%;
                box-sizing: border-box;
            }
            .checkbox-group { 
                grid-template-columns: 1fr; 
                gap: 8px; 
            }
            .checkbox-item {
                gap: 8px;
            }
            .output { 
                padding: 12px; 
                font-size: 14px; 
                max-height: 250px;
            }
            .btn {
                padding: 12px 16px;
                font-size: 0.95rem;
            }
            .related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm, 0.5rem); }
            .related-tool-item { padding: var(--spacing-sm, 0.5rem); max-width: none; }
            .related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .related-tool-name { font-size: 0.75rem; }
            .notification { top: 10px; right: 10px; left: 10px; transform: translateY(-100px); text-align: center; }
            .notification.show { transform: translateY(0); }
        }
        
        /* Extra Small Screens */
        @media (max-width: 320px) {
            .widget-container { 
                margin: 2px; 
                padding: 12px; 
                max-width: calc(100% - 4px);
            }
            .widget-title { font-size: 1.25rem; }
            .widget-description { font-size: 0.9rem; }
            .input, .select { 
                padding: 8px 10px; 
                font-size: 16px; 
            }
            .output { 
                padding: 10px; 
                font-size: 13px; 
                max-height: 200px;
            }
            .btn {
                padding: 10px 12px;
                font-size: 0.9rem;
            }
            .related-tools-grid { grid-template-columns: repeat(2, 1fr); }
        }

    </style>
</head>
<body>
    <div class="widget-container">
        <h1 class="widget-title">Disclaimer Generator</h1>
        <p class="widget-description">
            Generate a customized disclaimer for your website, blog, or content. Fill in your details below.
        </p>

        <div class="form">
            <div class="form-group">
                <label for="entityName" class="label">Website/Entity Name *</label>
                <input type="text" id="entityName" class="input" placeholder="Enter your website or entity name" required>
            </div>
            <div class="form-group">
                <label for="websiteUrl" class="label">Website URL *</label>
                <input type="url" id="websiteUrl" class="input" placeholder="https://example.com" required>
            </div>
            <div class="form-group">
                <label for="contactEmail" class="label">Contact Email *</label>
                <input type="email" id="contactEmail" class="input" placeholder="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="disclaimerType" class="label">Disclaimer Type</label>
                <select id="disclaimerType" class="select">
                    <option value="General">General Website</option>
                    <option value="Blog">Blog/Content</option>
                    <option value="Educational">Educational</option>
                    <option value="Health">Health/Medical</option>
                    <option value="Financial">Financial</option>
                    <option value="Legal">Legal Information</option>
                </select>
            </div>
            <div class="form-group">
                <label class="label">Disclaimer Sections</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="generalLiability" class="checkbox" checked>
                        <label for="generalLiability" class="checkbox-label">General Liability</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="accuracy" class="checkbox" checked>
                        <label for="accuracy" class="checkbox-label">Information Accuracy</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="externalLinks" class="checkbox">
                        <label for="externalLinks" class="checkbox-label">External Links</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="professionalAdvice" class="checkbox">
                        <label for="professionalAdvice" class="checkbox-label">Professional Advice</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="affiliate" class="checkbox">
                        <label for="affiliate" class="checkbox-label">Affiliate Links</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="testimonials" class="checkbox">
                        <label for="testimonials" class="checkbox-label">Testimonials</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="buttons">
            <button class="btn btn-primary" onclick="Tool.generate()">Generate Disclaimer</button>
            <button class="btn btn-secondary" onclick="Tool.clear()">Clear Form</button>
            <button class="btn btn-success" onclick="Tool.copy()">Copy Disclaimer</button>
        </div>
        <div class="result">
            <h3 class="result-title">Generated Disclaimer:</h3>
            <div class="output" id="output">Fill in the form above and click "Generate Disclaimer" to create your customized disclaimer.</div>
        </div>
        <div class="related-tools">
            <h3 class="related-tools-title">Related Tools</h3>
            <div class="related-tools-grid">
                <a href="/p/privacy-policy-generator.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="related-tool-name">Privacy Policy</div>
                </a>

                <a href="/p/terms-and-condition-generator.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <div class="related-tool-name">Terms & Conditions</div>
                </a>

                <a href="/p/text-to-slug_30.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="related-tool-name">Text to Slug</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Protect Your Website with a Custom Disclaimer</h2>
            <p>A disclaimer is a crucial legal statement that helps limit your liability for the content you publish. Whether you run a blog, an e-commerce site, or a platform offering advice, a well-crafted disclaimer can protect you from legal claims by clarifying the scope and limitations of your content. Our free <strong>Disclaimer Generator</strong> makes it easy to create a professional and customized disclaimer tailored to your specific needs, such as health, financial, or educational topics.</p>
            <p>By clearly stating that your content is for informational purposes only and not professional advice, you can manage user expectations and reduce your legal risk.</p>
            
            <h3>How to Use Our Disclaimer Generator</h3>
            <ol>
                <li><strong>Enter Your Information:</strong> Provide your website or entity name, URL, and a contact email.</li>
                <li><strong>Select a Disclaimer Type:</strong> Choose the category that best fits your content, such as 'Blog/Content', 'Health/Medical', or 'Financial'. This helps tailor the clauses.</li>
                <li><strong>Choose Relevant Sections:</strong> Select the specific clauses you need, like 'External Links', 'Affiliate Links', or 'Testimonials'.</li>
                <li><strong>Generate and Copy:</strong> Click the "Generate Disclaimer" button. Your customized legal text will appear in the output box, ready to be copied and placed on your site.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Disclaimer Generator</h3>
            
            <h4>How do I write a disclaimer?</h4>
            <p>The easiest way to write a disclaimer is to use a generator like this one. It walks you through the necessary components, such as a limitation of liability, an accuracy statement, and clauses for external links or professional advice. Start by identifying potential risks associated with your content and select the clauses that address them.</p>
            
            <h4>Can I write my own disclaimer?</h4>
            <p>Yes, you can write your own disclaimer. A generator provides a solid foundation with standard legal language. However, because a disclaimer serves a legal purpose, it is highly recommended that you have the final document reviewed by a qualified attorney to ensure it is enforceable and appropriate for your situation.</p>
            
            <h4>What is a good disclaimer example?</h4>
            <p>A good, standard disclaimer example is: "The information provided by [Your Website] is for general informational purposes only. All information on the site is provided in good faith; however, we make no representation or warranty of any kind, express or implied, regarding the accuracy, adequacy, or completeness of any information on the site."</p>
            
            <h4>What is a formal disclaimer?</h4>
            <p>A formal disclaimer is a carefully worded legal notice designed to specify or limit the rights and obligations of parties in a relationship. In a web context, it formally declares that the website owner is not responsible for certain outcomes, such as the accuracy of information or consequences of acting upon the advice given.</p>
            
            <h4>What is an example of a disclaimer message?</h4>
            <p>A simple disclaimer message for affiliate links could be: "Disclosure: This post contains affiliate links. We may earn a commission if you buy something through our links, at no extra cost to you." For a fitness blog, it might be: "Consult with your physician before beginning any new exercise program."</p>
        </div>

        <div class="disclaimer-features">
            <h3 class="disclaimer-features-title">Key Features:</h3>
            <ul class="disclaimer-features-list">
                <li class="disclaimer-features-item">Instantly generate professional disclaimers</li>
                <li class="disclaimer-features-item">Customizable for websites, blogs, and more</li>
                <li class="disclaimer-features-item">Multiple disclaimer types supported</li>
                <li class="disclaimer-features-item">Mobile-responsive design</li>
                <li class="disclaimer-features-item">One-click copy to clipboard</li>
                <li class="disclaimer-features-item">Real-time preview</li>
                <li class="disclaimer-features-item">No data sent to servers</li>
                <li class="disclaimer-features-item">Free to use, no registration required</li>
            </ul>
        </div>
        <div class="disclaimer">
            <strong>Legal Disclaimer:</strong> This generated disclaimer is a basic template and should not be considered as legal advice. Please consult with a qualified attorney to ensure compliance with applicable laws and regulations in your jurisdiction.
        </div>
    </div>
    <div class="notification" id="notification">✓ Copied to clipboard!</div>
    <script>
        // Ultra Simplified Disclaimer Generator
        (function() {
            'use strict';
            window.Tool = {
                generate() {
                    const entityName = document.getElementById('entityName').value.trim();
                    const websiteUrl = document.getElementById('websiteUrl').value.trim();
                    const contactEmail = document.getElementById('contactEmail').value.trim();
                    const disclaimerType = document.getElementById('disclaimerType').value;
                    const output = document.getElementById('output');
                    if (!entityName || !websiteUrl || !contactEmail) {
                        output.textContent = 'Please fill in all required fields (marked with *).';
                        output.style.color = '#dc2626';
                        return;
                    }
                    output.style.color = '';
                    const options = {
                        generalLiability: document.getElementById('generalLiability').checked,
                        accuracy: document.getElementById('accuracy').checked,
                        externalLinks: document.getElementById('externalLinks').checked,
                        professionalAdvice: document.getElementById('professionalAdvice').checked,
                        affiliate: document.getElementById('affiliate').checked,
                        testimonials: document.getElementById('testimonials').checked
                    };
                    const disclaimer = this.generateDisclaimer(entityName, websiteUrl, contactEmail, disclaimerType, options);
                    output.textContent = disclaimer;
                },
                generateDisclaimer(entity, url, email, type, options) {
                    const currentDate = new Date().toLocaleDateString();
                    return `DISCLAIMER\n\nLast updated: ${currentDate}\n\nThe information contained on ${entity} website (${url}) is for general information purposes only. The information is provided by ${entity} and while we endeavor to keep the information up to date and correct, we make no representations or warranties of any kind, express or implied, about the completeness, accuracy, reliability, suitability or availability with respect to the website or the information, products, services, or related graphics contained on the website for any purpose.\n\n${options.generalLiability ? `LIMITATION OF LIABILITY\n\nIn no event will ${entity} be liable for any loss or damage including without limitation, indirect or consequential loss or damage, or any loss or damage whatsoever arising from loss of data or profits arising out of, or in connection with, the use of this website.\n\n` : ''}${options.accuracy ? `ACCURACY OF INFORMATION\n\nThrough this website you are able to link to other websites which are not under the control of ${entity}. We have no control over the nature, content and availability of those sites. The inclusion of any links does not necessarily imply a recommendation or endorse the views expressed within them.\n\n` : ''}${options.externalLinks ? `EXTERNAL LINKS\n\nThrough this website you are able to link to other websites which are not under the control of ${entity}. We have no control over the nature, content and availability of those sites. The inclusion of any links does not necessarily imply a recommendation or endorse the views expressed within them.\n\n` : ''}${options.professionalAdvice ? `PROFESSIONAL ADVICE DISCLAIMER\n\nThe information on this website is not intended as ${type.toLowerCase()} advice and should not be relied upon as such. You should seek professional advice before acting on any information contained on this website.\n\n` : ''}${options.affiliate ? `AFFILIATE LINKS DISCLOSURE\n\nThis website may contain affiliate links. If you click on an affiliate link and make a purchase, we may receive a commission at no additional cost to you. We only recommend products or services that we believe will add value to our readers.\n\n` : ''}${options.testimonials ? `TESTIMONIALS DISCLAIMER\n\nTestimonials appearing on this website may not be representative of other clients or customers and is not a guarantee of future performance or success.\n\n` : ''}WEBSITE AVAILABILITY\n\nEvery effort is made to keep the website up and running smoothly. However, ${entity} takes no responsibility for, and will not be liable for, the website being temporarily unavailable due to technical issues beyond our control.\n\nCHANGES TO DISCLAIMER\n\n${entity} reserves the right to modify this disclaimer at any time. Changes will be effective immediately upon posting on the website.\n\nGOVERNING LAW\n\nThis disclaimer is governed by and construed in accordance with the laws of the jurisdiction in which ${entity} operates.\n\nCONTACT INFORMATION\n\nIf you have any questions about this disclaimer, please contact us at:\nEmail: ${email}\nWebsite: ${url}\n\nThis disclaimer was last updated on ${currentDate}.\n\n---\nGenerated by Disclaimer Generator\nThis is a basic template and should be reviewed by legal counsel.`;
                },
                clear() {
                    document.getElementById('entityName').value = '';
                    document.getElementById('websiteUrl').value = '';
                    document.getElementById('contactEmail').value = '';
                    document.getElementById('disclaimerType').value = 'General';
                    document.getElementById('generalLiability').checked = true;
                    document.getElementById('accuracy').checked = true;
                    document.getElementById('externalLinks').checked = false;
                    document.getElementById('professionalAdvice').checked = false;
                    document.getElementById('affiliate').checked = false;
                    document.getElementById('testimonials').checked = false;
                    document.getElementById('output').textContent = 'Fill in the form above and click "Generate Disclaimer" to create your customized disclaimer.';
                    document.getElementById('output').style.color = '';
                },
                copy() {
                    const text = document.getElementById('output').textContent;
                    if (text === 'Fill in the form above and click "Generate Disclaimer" to create your customized disclaimer.' ||
                        text === 'Please fill in all required fields (marked with *).') return;
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(text).then(() => this.notify());
                    } else {
                        const el = document.createElement('textarea');
                        el.value = text;
                        el.style.cssText = 'position:fixed;left:-999px;top:-999px';
                        document.body.appendChild(el);
                        el.select();
                        document.execCommand('copy');
                        document.body.removeChild(el);
                        this.notify();
                    }
                },
                notify() {
                    const n = document.getElementById('notification');
                    n.classList.add('show');
                    setTimeout(() => n.classList.remove('show'), 2500);
                }
            };
            document.addEventListener('DOMContentLoaded', function() {
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }
            });
        })();
    </script>
</body>
</html>