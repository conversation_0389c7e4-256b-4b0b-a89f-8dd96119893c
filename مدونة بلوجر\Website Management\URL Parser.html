<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Parser - Online URL Analyzer & Extractor</title>
    <meta name="description" content="Break down any URL into its components. Our free online URL parser extracts the protocol, hostname, port, path, query parameters, and fragment for easy analysis.">
    <link rel="canonical" href="https://www.webtoolskit.org/p/url-parser.html">
    <!-- Font Awesome CDN for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "URL Parser - Online URL Analyzer & Extractor",
        "description": "Break down any URL into its components. Our free online URL parser extracts the protocol, hostname, port, path, query parameters, and fragment for easy analysis.",
        "url": "https://www.webtoolskit.org/p/url-parser.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-07-05",
        "dateModified": "2025-07-05",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "URL Parser",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Parse URL" },
            { "@type": "CopyAction", "name": "Copy URL Components" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a URL parser?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A URL parser is a tool or function that takes a full Uniform Resource Locator (URL) string and breaks it down into its individual components, such as the protocol (http, https), hostname (www.example.com), path (/page), query parameters (?key=value), and fragment (#section). This makes it easy to analyze, validate, or extract specific parts of a URL."
          }
        },
        {
          "@type": "Question",
          "name": "What are the components of a URL?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A URL typically consists of several components: the protocol (or scheme) like 'https', the hostname (e.g., 'www.example.com'), an optional port number (e.g., ':8080'), the path (e.g., '/products/shoes'), a query string for parameters (e.g., '?color=blue&size=10'), and a fragment identifier (e.g., '#details')."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between a URL and a URI?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A URI (Uniform Resource Identifier) is a a sequence of characters that identifies a resource. A URL (Uniform Resource Locator) is a specific type of URI that not only identifies a resource but also provides a method for locating it (e.g., via its web address). In simple terms, all URLs are URIs, but not all URIs are URLs."
          }
        },
        {
          "@type": "Question",
          "name": "How do you extract parameters from a URL?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You can easily extract parameters (also called the query string) from a URL by using a URL parser like this one. Paste the full URL into the tool, and it will automatically identify and list all the key-value pairs from the query string (the part after the '?') in a clear, readable format."
          }
        },
        {
          "@type": "Question",
          "name": "Are URL parameters case-sensitive?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, the keys and values in the query parameter part of a URL are generally considered case-sensitive. For example, '?user=John' and '?user=john' might be treated as different values by the web server. While the domain name part of a URL is case-insensitive, the path and query string are case-sensitive."
          }
        }
      ]
    }
    </script>


    <style>
        /* URL Parser Widget - Simplified & Template Compatible */
        .url-parser-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .url-parser-widget-container * { box-sizing: border-box; }

        .url-parser-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .url-parser-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .url-parser-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .url-parser-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 50px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .url-parser-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .url-parser-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .url-parser-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .url-parser-btn:hover { transform: translateY(-2px); }

        .url-parser-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .url-parser-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .url-parser-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .url-parser-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .url-parser-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .url-parser-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .url-parser-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            min-height: 60px;
        }

        .url-parser-output table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.95rem;
        }
        
        .url-parser-output th, .url-parser-output td {
            padding: var(--spacing-sm) var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            word-break: break-all;
        }

        .url-parser-output table tr:last-child th,
        .url-parser-output table tr:last-child td {
            border-bottom: none;
        }
        
        .url-parser-output th {
            width: 120px;
            font-weight: 600;
            color: var(--text-color);
        }

        .url-parser-output td {
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color-light);
        }

        .url-parser-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .url-parser-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .url-parser-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .url-parser-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .url-parser-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .url-parser-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .url-parser-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .url-parser-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="url-encoder"] .url-parser-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="url-decoder"] .url-parser-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="utm-builder"] .url-parser-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }


        .url-parser-related-tool-item:hover .url-parser-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="url-encoder"]:hover .url-parser-related-tool-icon { background: linear-gradient(145deg, #16a34a, #10B981); }
        a[href*="url-decoder"]:hover .url-parser-related-tool-icon { background: linear-gradient(145deg, #f472b6, #EC4899); }
        a[href*="utm-builder"]:hover .url-parser-related-tool-icon { background: linear-gradient(145deg, #16a34a, #10B981); }
        
        .url-parser-related-tool-item { box-shadow: none; border: none; }
        .url-parser-related-tool-item:hover { box-shadow: none; border: none; }
        .url-parser-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .url-parser-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .url-parser-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .url-parser-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .url-parser-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .url-parser-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .url-parser-related-tool-item:hover .url-parser-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .url-parser-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .url-parser-widget-title { font-size: 1.875rem; }
            .url-parser-buttons { flex-direction: column; }
            .url-parser-btn { flex: none; }
            .url-parser-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .url-parser-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .url-parser-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .url-parser-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 500px) {
             .url-parser-output table, .url-parser-output thead, .url-parser-output tbody, .url-parser-output th, .url-parser-output td, .url-parser-output tr {
                display: block;
            }
             .url-parser-output tr {
                border-bottom: 2px solid var(--border-color);
                padding-bottom: var(--spacing-sm);
                margin-bottom: var(--spacing-sm);
            }
            .url-parser-output tr:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }
            .url-parser-output th {
                width: 100%;
                border-bottom: none;
                padding-bottom: 0;
            }
            .url-parser-output td {
                 width: 100%;
                border-bottom: none;
                padding-top: 0;
            }
        }

        @media (max-width: 480px) {
            .url-parser-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .url-parser-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .url-parser-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .url-parser-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .url-parser-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .url-parser-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .url-parser-output::selection { background-color: var(--primary-color); color: white; }
        @media (max-width: 600px) { .url-parser-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="url-parser-widget-container">
        <h1 class="url-parser-widget-title">URL Parser</h1>
        <p class="url-parser-widget-description">
            Break down any URL into its constituent parts. An essential tool for developers, SEOs, and marketers to analyze and debug URLs.
        </p>
        
        <div class="url-parser-input-group">
            <label for="urlParserInput" class="url-parser-label">Enter a URL to parse:</label>
            <textarea 
                id="urlParserInput" 
                class="url-parser-textarea"
                placeholder="e.g., https://www.example.com:8080/path/to/page?query=value&id=123#section"
                rows="2"
            ></textarea>
        </div>

        <div class="url-parser-buttons">
            <button class="url-parser-btn url-parser-btn-primary" onclick="UrlParser.parse()">
                Parse URL
            </button>
            <button class="url-parser-btn url-parser-btn-secondary" onclick="UrlParser.clear()">
                Clear
            </button>
        </div>

        <div class="url-parser-result">
            <h3 class="url-parser-result-title">URL Components:</h3>
            <div class="url-parser-output" id="urlParserOutput">
                 <table>
                    <tbody>
                        <tr><th>Protocol</th><td id="proto"></td></tr>
                        <tr><th>Hostname</th><td id="host"></td></tr>
                        <tr><th>Port</th><td id="port"></td></tr>
                        <tr><th>Path</th><td id="path"></td></tr>
                        <tr><th>Query</th><td id="query"></td></tr>
                        <tr><th>Fragment</th><td id="frag"></td></tr>
                    </tbody>
                </table>
            </div>
        </div>
        
         <div class="url-parser-result" id="paramsResult" style="margin-top: 1.5rem; display: none;">
            <h3 class="url-parser-result-title">Query Parameters:</h3>
            <div class="url-parser-output" id="urlParamsOutput"></div>
        </div>

        <div class="url-parser-related-tools">
            <h3 class="url-parser-related-tools-title">Related Tools</h3>
            <div class="url-parser-related-tools-grid">
                <a href="/p/url-encoder.html" class="url-parser-related-tool-item" rel="noopener">
                    <div class="url-parser-related-tool-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="url-parser-related-tool-name">URL Encoder</div>
                </a>
                <a href="/p/url-decoder.html" class="url-parser-related-tool-item" rel="noopener">
                    <div class="url-parser-related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="url-parser-related-tool-name">URL Decode</div>
                </a>
                <a href="/p/utm-builder.html" class="url-parser-related-tool-item" rel="noopener">
                    <div class="url-parser-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="url-parser-related-tool-name">UTM Builder</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Deconstruct URLs with our Online URL Parser</h2>
            <p>A URL might seem like a simple string of text, but it's packed with distinct components that tell browsers how to locate a resource on the internet. Our <strong>URL Parser</strong> is a handy utility for developers, marketers, and SEO specialists that instantly deconstructs any URL into its fundamental parts. By breaking down a URL, you can easily inspect the protocol, domain, path, and any tracking parameters, which is essential for debugging, data analysis, and technical SEO.</p>
            
            <h3>How to Use the URL Parser</h3>
            <ol>
                <li><strong>Paste a URL:</strong> Copy the URL you wish to analyze and paste it into the input field above.</li>
                <li><strong>Click "Parse URL":</strong> The tool will instantly break the URL down into its components.</li>
                <li><strong>Analyze the Results:</strong> The output will clearly display the protocol, hostname, port, path, query string, and fragment. If any query parameters (like UTM tags) exist, they will be listed in a separate table for easy viewing.</li>
            </ol>
            
            <h3>Why Use a URL Parser?</h3>
            <p>Understanding the structure of a URL is vital for many tasks. Developers use it to debug routing and API calls. SEO professionals analyze URLs to ensure they are clean and search-engine friendly. Marketers use it to verify that tracking parameters (like UTM codes) are correctly structured for campaign analysis. Our tool removes the guesswork and provides a clear, structured view of any URL you provide.</p>
        
            <h3>Frequently Asked Questions About URL Parsing</h3>
            
            <h4>What is a URL parser?</h4>
            <p>A URL parser is a tool or function that takes a full Uniform Resource Locator (URL) string and breaks it down into its individual components, such as the protocol (http, https), hostname (www.example.com), path (/page), query parameters (?key=value), and fragment (#section). This makes it easy to analyze, validate, or extract specific parts of a URL.</p>
            
            <h4>What are the components of a URL?</h4>
            <p>A URL typically consists of several components: the protocol (or scheme) like 'https', the hostname (e.g., 'www.example.com'), an optional port number (e.g., ':8080'), the path (e.g., '/products/shoes'), a query string for parameters (e.g., '?color=blue&size=10'), and a fragment identifier (e.g., '#details').</p>
            
            <h4>What is the difference between a URL and a URI?</h4>
            <p>A URI (Uniform Resource Identifier) is a a sequence of characters that identifies a resource. A URL (Uniform Resource Locator) is a specific type of URI that not only identifies a resource but also provides a method for locating it (e.g., via its web address). In simple terms, all URLs are URIs, but not all URIs are URLs.</p>
            
            <h4>How do you extract parameters from a URL?</h4>
            <p>You can easily extract parameters (also called the query string) from a URL by using a URL parser like this one. Paste the full URL into the tool, and it will automatically identify and list all the key-value pairs from the query string (the part after the '?') in a clear, readable format.</p>
            
            <h4>Are URL parameters case-sensitive?</h4>
            <p>Yes, the keys and values in the query parameter part of a URL are generally considered case-sensitive. For example, '?user=John' and '?user=john' might be treated as different values by the web server. While the domain name part of a URL is case-insensitive, the path and query string are case-sensitive.</p>
        </div>

        <div class="url-parser-features">
            <h3 class="url-parser-features-title">Key Features:</h3>
            <ul class="url-parser-features-list">
                <li class="url-parser-features-item">Extracts all URL components</li>
                <li class="url-parser-features-item">Separates query parameters</li>
                <li class="url-parser-features-item">Clear, table-based output</li>
                <li class="url-parser-features-item">Great for debugging</li>
                <li class="url-parser-features-item">Useful for SEO & marketing</li>
                <li class="url-parser-features-item">Client-side for privacy</li>
                <li class="url-parser-features-item">Handles complex URLs</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="url-parser-notification" id="urlParserNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // URL Parser
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('urlParserInput'),
                proto: () => document.getElementById('proto'),
                host: () => document.getElementById('host'),
                port: () => document.getElementById('port'),
                path: () => document.getElementById('path'),
                query: () => document.getElementById('query'),
                frag: () => document.getElementById('frag'),
                paramsResult: () => document.getElementById('paramsResult'),
                paramsOutput: () => document.getElementById('urlParamsOutput'),
                notification: () => document.getElementById('urlParserNotification')
            };

            window.UrlParser = {
                parse() {
                    const urlString = elements.input().value.trim();
                    if (!urlString) {
                        this.clear();
                        elements.input().placeholder = 'Please enter a URL first.';
                        return;
                    }
                    
                    try {
                        const url = new URL(urlString);
                        elements.proto().textContent = url.protocol;
                        elements.host().textContent = url.hostname;
                        elements.port().textContent = url.port || '(default)';
                        elements.path().textContent = url.pathname;
                        elements.query().textContent = url.search;
                        elements.frag().textContent = url.hash;

                        const params = url.searchParams;
                        if (Array.from(params.keys()).length > 0) {
                            elements.paramsResult().style.display = 'block';
                            const table = document.createElement('table');
                            const tbody = document.createElement('tbody');
                            
                            params.forEach((value, key) => {
                                const row = tbody.insertRow();
                                const keyCell = row.insertCell();
                                const valueCell = row.insertCell();
                                keyCell.outerHTML = `<th>${decodeURIComponent(key)}</th>`;
                                valueCell.textContent = decodeURIComponent(value);
                            });
                            table.appendChild(tbody);
                            elements.paramsOutput().innerHTML = '';
                            elements.paramsOutput().appendChild(table);
                        } else {
                             elements.paramsResult().style.display = 'none';
                             elements.paramsOutput().innerHTML = '';
                        }

                    } catch (e) {
                         this.clear();
                         elements.input().value = '';
                         elements.input().placeholder = 'Error: Invalid URL. Please check the format.';
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.input().placeholder = 'e.g., https://www.example.com:8080/path/to/page?query=value&id=123#section';
                    elements.proto().textContent = '';
                    elements.host().textContent = '';
                    elements.port().textContent = '';
                    elements.path().textContent = '';
                    elements.query().textContent = '';
                    elements.frag().textContent = '';
                    elements.paramsResult().style.display = 'none';
                    elements.paramsOutput().innerHTML = '';
                },
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                
                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }
                
                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        UrlParser.parse();
                    }
                });
            });
        })();
    </script>
</body>
</html>