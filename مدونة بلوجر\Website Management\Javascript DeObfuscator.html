<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Javascript Deobfuscator - Unpack and Analyze JS Code</title>
    <meta name="description" content="Unpack and deobfuscate complex, minified, or obfuscated JavaScript code with our free online tool. Make JS readable for analysis and debugging.">
    <link rel="canonical" href="https://www.webtoolskit.org/p/javascript-deobfuscator.html">
    <!-- Font Awesome CDN for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Javascript Deobfuscator - Unpack and Analyze JS Code",
        "description": "Unpack and deobfuscate complex, minified, or obfuscated JavaScript code with our free online tool. Make JS readable for analysis and debugging.",
        "url": "https://www.webtoolskit.org/p/javascript-deobfuscator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-30",
        "dateModified": "2025-06-30",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Javascript Deobfuscator",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Deobfuscate JavaScript Code" },
            { "@type": "CopyAction", "name": "Copy Deobfuscated JavaScript" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is JavaScript deobfuscation?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "JavaScript deobfuscation is the process of taking obfuscated (deliberately made confusing) or minified code and transforming it back into a more readable format. The goal is to reverse-engineer the code to understand its purpose, logic, and functionality, which is especially useful for security analysis and debugging."
          }
        },
        {
          "@type": "Question",
          "name": "How do you un-obfuscate JavaScript code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The first and most effective step to un-obfuscate JavaScript is to use a beautifier or deobfuscator tool like this one. It formats the code with proper indentation and line breaks, revealing its structure. This often makes the code's logic much easier to follow. More advanced deobfuscation may require manual analysis to rename variables and simplify complex expressions."
          }
        },
        {
          "@type": "Question",
          "name": "Can all obfuscated JavaScript be reversed?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Not completely. While a deobfuscator can reformat the code to make it readable, it cannot recover original variable names, function names, or comments that were removed during the obfuscation process. Heavy obfuscation with techniques like control flow flattening can make the code's true logic extremely difficult, though not impossible, to fully reverse-engineer."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between a deobfuscator and a beautifier?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A beautifier focuses solely on formatting code to make it look clean and readable (e.g., adding indentation and spacing). A deobfuscator does everything a beautifier does but may also attempt to reverse simple obfuscation techniques, such as unpacking packed code or resolving constant string arrays. Essentially, a deobfuscator is a more powerful type of beautifier."
          }
        },
        {
          "@type": "Question",
          "name": "Is it legal to deobfuscate JavaScript?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The legality depends on the context and intent. Deobfuscating code for security analysis (e.g., to understand a malicious script) or for interoperability and learning purposes is generally considered acceptable (fair use). However, deobfuscating code to steal intellectual property or bypass licensing restrictions is illegal and violates software license agreements."
          }
        }
      ]
    }
    </script>


    <style>
        /* Javascript Deobfuscator Widget - Simplified & Template Compatible */
        .javascript-deobfuscator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .javascript-deobfuscator-widget-container * { box-sizing: border-box; }

        .javascript-deobfuscator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .javascript-deobfuscator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .javascript-deobfuscator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .javascript-deobfuscator-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 150px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .javascript-deobfuscator-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .javascript-deobfuscator-options {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .javascript-deobfuscator-option-label {
            font-weight: 500;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .javascript-deobfuscator-select {
            padding: var(--spacing-sm);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-family: var(--font-family);
            font-weight: 500;
        }

        .javascript-deobfuscator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .javascript-deobfuscator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .javascript-deobfuscator-btn:hover { transform: translateY(-2px); }

        .javascript-deobfuscator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .javascript-deobfuscator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .javascript-deobfuscator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .javascript-deobfuscator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .javascript-deobfuscator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .javascript-deobfuscator-btn-success:hover {
            background-color: #059669;
        }

        .javascript-deobfuscator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .javascript-deobfuscator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .javascript-deobfuscator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            white-space: pre-wrap;
            min-height: 150px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .javascript-deobfuscator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .javascript-deobfuscator-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .javascript-deobfuscator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .javascript-deobfuscator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .javascript-deobfuscator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .javascript-deobfuscator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .javascript-deobfuscator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .javascript-deobfuscator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="javascript-obfuscator"] .javascript-deobfuscator-related-tool-icon { background: linear-gradient(145deg, #84CC16, #65A30D); }
        a[href*="javascript-beautifier"] .javascript-deobfuscator-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="javascript-minifier"] .javascript-deobfuscator-related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }

        .javascript-deobfuscator-related-tool-item:hover .javascript-deobfuscator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="javascript-obfuscator"]:hover .javascript-deobfuscator-related-tool-icon { background: linear-gradient(145deg, #9fdd3b, #84CC16); }
        a[href*="javascript-beautifier"]:hover .javascript-deobfuscator-related-tool-icon { background: linear-gradient(145deg, #F87171, #EF4444); }
        a[href*="javascript-minifier"]:hover .javascript-deobfuscator-related-tool-icon { background: linear-gradient(145deg, #1DE9B6, #14B8A6); }
        
        .javascript-deobfuscator-related-tool-item { box-shadow: none; border: none; }
        .javascript-deobfuscator-related-tool-item:hover { box-shadow: none; border: none; }
        .javascript-deobfuscator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .javascript-deobfuscator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .javascript-deobfuscator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .javascript-deobfuscator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .javascript-deobfuscator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .javascript-deobfuscator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .javascript-deobfuscator-related-tool-item:hover .javascript-deobfuscator-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .javascript-deobfuscator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .javascript-deobfuscator-widget-title { font-size: 1.875rem; }
            .javascript-deobfuscator-buttons { flex-direction: column; }
            .javascript-deobfuscator-btn { flex: none; }
            .javascript-deobfuscator-options { flex-direction: column; align-items: flex-start; }
            .javascript-deobfuscator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .javascript-deobfuscator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .javascript-deobfuscator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .javascript-deobfuscator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .javascript-deobfuscator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .javascript-deobfuscator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .javascript-deobfuscator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .javascript-deobfuscator-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .javascript-deobfuscator-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .javascript-deobfuscator-select:focus, .javascript-deobfuscator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .javascript-deobfuscator-output::selection { background-color: var(--primary-color); color: white; }
        @media (max-width: 600px) { .javascript-deobfuscator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="javascript-deobfuscator-widget-container">
        <h1 class="javascript-deobfuscator-widget-title">Javascript Deobfuscator</h1>
        <p class="javascript-deobfuscator-widget-description">
            Unpack, analyze, and reverse-engineer complex JavaScript code. Our deobfuscator formats and clarifies obfuscated scripts to make them readable and understandable.
        </p>
        
        <div class="javascript-deobfuscator-input-group">
            <label for="jsDeobfuscatorInput" class="javascript-deobfuscator-label">Paste obfuscated JavaScript code:</label>
            <textarea 
                id="jsDeobfuscatorInput" 
                class="javascript-deobfuscator-textarea"
                placeholder="var _0x123a=['Hello','World'];(function(a,b){var c=function(d){while(--d){a['push'](a['shift']());}};c(++b);}(_0x123a,0x123));var _0x456b=function(a,b){a=a-0x0;var c=_0x123a[a];return c;};console.log(_0x456b('0x0'),_0x456b('0x1'));"
                rows="8"
            ></textarea>
        </div>

        <div class="javascript-deobfuscator-options">
            <label for="jsIndentSize" class="javascript-deobfuscator-option-label">Indentation:</label>
            <select id="jsIndentSize" class="javascript-deobfuscator-select">
                <option value="2">2 Spaces</option>
                <option value="4" selected>4 Spaces</option>
                <option value="tab">Tabs</option>
            </select>
        </div>

        <div class="javascript-deobfuscator-buttons">
            <button class="javascript-deobfuscator-btn javascript-deobfuscator-btn-primary" onclick="JsDeobfuscator.deobfuscate()">
                Deobfuscate JS
            </button>
            <button class="javascript-deobfuscator-btn javascript-deobfuscator-btn-secondary" onclick="JsDeobfuscator.clear()">
                Clear All
            </button>
            <button class="javascript-deobfuscator-btn javascript-deobfuscator-btn-success" onclick="JsDeobfuscator.copy()">
                Copy Result
            </button>
        </div>

        <div class="javascript-deobfuscator-result">
            <h3 class="javascript-deobfuscator-result-title">Deobfuscated JavaScript:</h3>
            <div class="javascript-deobfuscator-output" id="jsDeobfuscatorOutput">
                Your readable JavaScript will appear here...
            </div>
        </div>

        <div class="javascript-deobfuscator-related-tools">
            <h3 class="javascript-deobfuscator-related-tools-title">Related Tools</h3>
            <div class="javascript-deobfuscator-related-tools-grid">
                <a href="/p/javascript-obfuscator.html" class="javascript-deobfuscator-related-tool-item" rel="noopener">
                    <div class="javascript-deobfuscator-related-tool-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="javascript-deobfuscator-related-tool-name">Javascript Obfuscator</div>
                </a>
                <a href="/p/javascript-beautifier.html" class="javascript-deobfuscator-related-tool-item" rel="noopener">
                    <div class="javascript-deobfuscator-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="javascript-deobfuscator-related-tool-name">JavaScript Beautifier</div>
                </a>
                <a href="/p/javascript-minifier.html" class="javascript-deobfuscator-related-tool-item" rel="noopener">
                    <div class="javascript-deobfuscator-related-tool-icon">
                        <i class="fas fa-compress-alt"></i>
                    </div>
                    <div class="javascript-deobfuscator-related-tool-name">JavaScript Minifier</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Analyze Scripts with Our Javascript Deobfuscator</h2>
            <p>Have you ever encountered JavaScript that looks like complete nonsense? You may be looking at obfuscated code. Our <strong>Javascript Deobfuscator</strong> is an essential tool for security researchers, web developers, and curious coders who need to understand what a script is truly doing. By taking tangled, intentionally confusing code and reformatting it into a structured and readable layout, this tool reveals the underlying logic, making it possible to analyze, debug, and reverse-engineer complex scripts.</p>
            
            <h3>How to Use the Javascript Deobfuscator</h3>
            <ol>
                <li><strong>Paste the Obfuscated Code:</strong> Copy the confusing script and paste it into the input area above.</li>
                <li><strong>Choose Formatting Options:</strong> Select the indentation style that you prefer for the output.</li>
                <li><strong>Deobfuscate and Analyze:</strong> Click the "Deobfuscate JS" button. The tool will instantly beautify the code, displaying a readable version in the output box for you to analyze and copy.</li>
            </ol>
            
            <h3>Why Deobfuscate JavaScript?</h3>
            <p>The primary use for a deobfuscator is to make unreadable code readable. This is critical in several scenarios. For security professionals, it's a vital step in analyzing potentially malicious scripts found on websites to understand their payload. For developers, it can help in debugging minified code from third-party libraries when source maps are unavailable. It transforms walls of text into structured blocks, clarifying function calls, loops, and conditional logic, which is the first step toward understanding any piece of software.</p>
        
            <h3>Frequently Asked Questions About Javascript Deobfuscator</h3>
            
            <h4>What is JavaScript deobfuscation?</h4>
            <p>JavaScript deobfuscation is the process of taking obfuscated (deliberately made confusing) or minified code and transforming it back into a more readable format. The goal is to reverse-engineer the code to understand its purpose, logic, and functionality, which is especially useful for security analysis and debugging.</p>
            
            <h4>How do you un-obfuscate JavaScript code?</h4>
            <p>The first and most effective step to un-obfuscate JavaScript is to use a beautifier or deobfuscator tool like this one. It formats the code with proper indentation and line breaks, revealing its structure. This often makes the code's logic much easier to follow. More advanced deobfuscation may require manual analysis to rename variables and simplify complex expressions.</p>
            
            <h4>Can all obfuscated JavaScript be reversed?</h4>
            <p>Not completely. While a deobfuscator can reformat the code to make it readable, it cannot recover original variable names, function names, or comments that were removed during the obfuscation process. Heavy obfuscation with techniques like control flow flattening can make the code's true logic extremely difficult, though not impossible, to fully reverse-engineer.</p>
            
            <h4>What is the difference between a deobfuscator and a beautifier?</h4>
            <p>A beautifier focuses solely on formatting code to make it look clean and readable (e.g., adding indentation and spacing). A deobfuscator does everything a beautifier does but may also attempt to reverse simple obfuscation techniques, such as unpacking packed code or resolving constant string arrays. Essentially, a deobfuscator is a more powerful type of beautifier.</p>
            
            <h4>Is it legal to deobfuscate JavaScript?</h4>
            <p>The legality depends on the context and intent. Deobfuscating code for security analysis (e.g., to understand a malicious script) or for interoperability and learning purposes is generally considered acceptable (fair use). However, deobfuscating code to steal intellectual property or bypass licensing restrictions is illegal and violates software license agreements.</p>
        </div>

        <div class="javascript-deobfuscator-features">
            <h3 class="javascript-deobfuscator-features-title">Key Features:</h3>
            <ul class="javascript-deobfuscator-features-list">
                <li class="javascript-deobfuscator-features-item">Unpacks common obfuscation</li>
                <li class="javascript-deobfuscator-features-item">Formats code for readability</li>
                <li class="javascript-deobfuscator-features-item">Reveals code structure</li>
                <li class="javascript-deobfuscator-features-item">Essential for security analysis</li>
                <li class="javascript-deobfuscator-features-item">Helps in debugging libraries</li>
                <li class="javascript-deobfuscator-features-item">Customizable indentation</li>
                <li class="javascript-deobfuscator-features-item">One-click copy to clipboard</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="javascript-deobfuscator-notification" id="jsDeobfuscatorNotification">
        ✓ Copied to clipboard!
    </div>

    <!-- JS Beautify Library (CDN) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.7/beautify.min.js"></script>

    <script>
        // Javascript Deobfuscator
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('jsDeobfuscatorInput'),
                output: () => document.getElementById('jsDeobfuscatorOutput'),
                notification: () => document.getElementById('jsDeobfuscatorNotification'),
                indentSize: () => document.getElementById('jsIndentSize')
            };

            window.JsDeobfuscator = {
                deobfuscate() {
                    const input = elements.input();
                    const output = elements.output();
                    const jsText = input.value;

                    if (!jsText.trim()) {
                        output.textContent = 'Please enter JavaScript code to deobfuscate.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        indent: elements.indentSize().value
                    };

                    try {
                        const result = this.processJs(jsText, options);
                        output.textContent = result || 'Could not deobfuscate the provided JavaScript.';
                    } catch (error) {
                        output.textContent = `Error: Failed to process code. It may contain syntax errors or use advanced obfuscation not supported by this tool.`;
                        output.style.color = '#dc2626';
                        console.error("Deobfuscate Error:", error);
                    }
                },

                processJs(text, options) {
                    if (typeof js_beautify === 'undefined') {
                        throw new Error('Beautifier library not loaded.');
                    }

                    const beautifyOptions = {
                        indent_char: ' ',
                        indent_size: 4,
                        indent_with_tabs: false,
                        brace_style: 'collapse',
                        keep_array_indentation: false,
                        end_with_newline: true,
                        space_after_anon_function: true,
                        unescape_strings: true,
                    };

                    if (options.indent === '2') {
                        beautifyOptions.indent_size = 2;
                    } else if (options.indent === 'tab') {
                        beautifyOptions.indent_with_tabs = true;
                        beautifyOptions.indent_size = 1; 
                    }
                    
                    return js_beautify(text, beautifyOptions);
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your readable JavaScript will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your readable JavaScript will appear here...', 'Please enter JavaScript code to deobfuscate.', 'Could not deobfuscate the provided JavaScript.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        JsDeobfuscator.deobfuscate();
                    }
                });
            });
        })();
    </script>
</body>
</html>