<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy Converter - Convert Joules, Calories, kWh, & More</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Energy Converter - Convert Joules, Calories, kWh, & More",
        "description": "Instantly convert between various energy units like joules, calories, kilowatt-hours (kWh), and BTUs. A free online tool for science, nutrition, and engineering calculations.",
        "url": "https://www.webtoolskit.org/p/energy-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-12",
        "dateModified": "2025-06-14",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Energy Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Energy Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What does an energy converter do?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "An energy converter is a device or system that changes energy from one form to another. For example, a light bulb converts electrical energy into light and heat energy. This tool, however, is a unit converter; it mathematically converts the measurement of energy from one unit (like Joules) to another (like calories) without changing the energy itself."
          }
        },
        {
          "@type": "Question",
          "name": "How does energy conversion work?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Energy conversion works based on the First Law of Thermodynamics, which states that energy cannot be created or destroyed, only transformed. A device or process facilitates this change. For instance, in a car engine, the chemical energy stored in fuel is converted into thermal energy (heat) and then into mechanical energy to move the car."
          }
        },
        {
          "@type": "Question",
          "name": "What is an example of an energy converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "There are many examples of energy converters in daily life. A solar panel converts light (radiant) energy into electrical energy. A human body converts chemical energy from food into mechanical energy for movement and thermal energy for warmth. A microphone converts sound (mechanical) energy into electrical energy."
          }
        },
        {
          "@type": "Question",
          "name": "Is energy conversion 100% efficient?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, energy conversion is never 100% efficient in practice. The Second Law of Thermodynamics dictates that during any energy conversion, some energy is inevitably lost to the environment as a less useful form, typically as waste heat. This is why a light bulb gets hot while producing light, and a car engine requires a cooling system."
          }
        },
        {
          "@type": "Question",
          "name": "How do I convert energy to electricity?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Energy is converted to electricity through various methods: 1. Generators: Convert mechanical energy (from turbines powered by wind, water, or steam) into electricity. 2. Solar Panels (Photovoltaics): Convert sunlight directly into electricity. 3. Batteries: Convert stored chemical energy into electricity. 4. Thermoelectric Generators: Convert a temperature difference (heat energy) directly into electricity."
          }
        }
      ]
    }
    </script>

    <style>
        /* Energy Converter Widget - Simplified & Template Compatible */
        .energy-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .energy-converter-widget-container * { box-sizing: border-box; }

        .energy-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .energy-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .energy-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .energy-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .energy-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .energy-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .energy-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .energy-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .energy-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .energy-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .energy-converter-btn:hover { transform: translateY(-2px); }

        .energy-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .energy-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .energy-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .energy-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .energy-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .energy-converter-btn-success:hover {
            background-color: #059669;
        }

        .energy-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .energy-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .energy-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .energy-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .energy-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .energy-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .energy-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .energy-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .energy-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .energy-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .energy-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="power-converter"] .energy-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="voltage-converter"] .energy-converter-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="current-converter"] .energy-converter-related-tool-icon { background: linear-gradient(145deg, #3B82F6, #2563EB); }

        .energy-converter-related-tool-item:hover .energy-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="power-converter"]:hover .energy-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        a[href*="voltage-converter"]:hover .energy-converter-related-tool-icon { background: linear-gradient(145deg, #fbbd24, #f59e0b); }
        a[href*="current-converter"]:hover .energy-converter-related-tool-icon { background: linear-gradient(145deg, #60a5fa, #3b82f6); }
        
        .energy-converter-related-tool-item { box-shadow: none; border: none; }
        .energy-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .energy-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .energy-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .energy-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .energy-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .energy-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .energy-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .energy-converter-related-tool-item:hover .energy-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .energy-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .energy-converter-widget-title { font-size: 1.875rem; }
            .energy-converter-buttons { flex-direction: column; }
            .energy-converter-btn { flex: none; }
            .energy-converter-input-group { grid-template-columns: 1fr; }
            .energy-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .energy-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .energy-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .energy-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .energy-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .energy-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .energy-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .energy-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .energy-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .energy-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .energy-converter-output::selection { background-color: var(--primary-color); color: white; }
        .energy-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .energy-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="energy-converter-widget-container">
        <h1 class="energy-converter-widget-title">Energy Converter</h1>
        <p class="energy-converter-widget-description">
            Convert between different units of energy, such as Joules, Calories, Kilowatt-hours (kWh), and BTUs, with this simple tool.
        </p>
        
        <div class="energy-converter-input-group">
            <label for="energyFromInput" class="energy-converter-label">From:</label>
            <input 
                type="number" 
                id="energyFromInput" 
                class="energy-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="energyFromUnit" class="energy-converter-select">
                <option value="j" selected>Joule (J)</option>
                <option value="kj">Kilojoule (kJ)</option>
                <option value="cal">Calorie (cal)</option>
                <option value="kcal">Kilocalorie (kcal)</option>
                <option value="wh">Watt-hour (Wh)</option>
                <option value="kwh">Kilowatt-hour (kWh)</option>
                <option value="btu">BTU</option>
            </select>
        </div>

        <div class="energy-converter-input-group">
            <label for="energyToInput" class="energy-converter-label">To:</label>
            <input 
                type="number" 
                id="energyToInput" 
                class="energy-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="energyToUnit" class="energy-converter-select">
                <option value="j">Joule (J)</option>
                <option value="kj">Kilojoule (kJ)</option>
                <option value="cal">Calorie (cal)</option>
                <option value="kcal" selected>Kilocalorie (kcal)</option>
                <option value="wh">Watt-hour (Wh)</option>
                <option value="kwh">Kilowatt-hour (kWh)</option>
                <option value="btu">BTU</option>
            </select>
        </div>

        <div class="energy-converter-buttons">
            <button class="energy-converter-btn energy-converter-btn-primary" onclick="EnergyConverter.convert()">
                Convert Energy
            </button>
            <button class="energy-converter-btn energy-converter-btn-secondary" onclick="EnergyConverter.clear()">
                Clear All
            </button>
            <button class="energy-converter-btn energy-converter-btn-success" onclick="EnergyConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="energy-converter-result">
            <h3 class="energy-converter-result-title">Conversion Result:</h3>
            <div class="energy-converter-output" id="energyConverterOutput">
                Your converted energy will appear here...
            </div>
        </div>

        <div class="energy-converter-related-tools">
            <h3 class="energy-converter-related-tools-title">Related Tools</h3>
            <div class="energy-converter-related-tools-grid">
                <a href="/p/power-converter.html" class="energy-converter-related-tool-item" rel="noopener">
                    <div class="energy-converter-related-tool-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="energy-converter-related-tool-name">Power Converter</div>
                </a>
                <a href="/p/voltage-converter.html" class="energy-converter-related-tool-item" rel="noopener">
                    <div class="energy-converter-related-tool-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <div class="energy-converter-related-tool-name">Voltage Converter</div>
                </a>
                <a href="/p/current-converter.html" class="energy-converter-related-tool-item" rel="noopener">
                    <div class="energy-converter-related-tool-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="energy-converter-related-tool-name">Current Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Your Go-To Tool for Converting Energy Units</h2>
            <p>Energy is a fundamental quantity in science and everyday life, but it is measured in many different units. Whether you're a physicist working with Joules, a dietician tracking Calories, or a homeowner checking your electricity bill in kilowatt-hours (kWh), you need a way to compare these values. Our free <strong>Energy Converter</strong> is a versatile tool that makes these conversions simple and instantaneous.</p>
            <p>This converter handles the most common energy units, including Joules (J), kilojoules (kJ), Calories (cal), kilocalories (kcal), Watt-hours (Wh), kilowatt-hours (kWh), and British Thermal Units (BTU). Stop wrestling with complex conversion factors and let our tool provide the quick, accurate results you need for your scientific, nutritional, or engineering calculations.</p>

            <h3>How to Use the Energy Converter</h3>
            <ol>
                <li><strong>Enter Your Value:</strong> Type the numeric value of the energy you want to convert into the "From" field.</li>
                <li><strong>Select Your Units:</strong> Choose your starting unit (e.g., Kilowatt-hour) and your target unit (e.g., Joules) from the dropdowns.</li>
                <li><strong>Convert:</strong> Click the "Convert Energy" button for an immediate and precise result.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to quickly save the converted value for your work.</li>
            </ol>

            <h3>Frequently Asked Questions About Energy Conversion</h3>
            
            <h4>What does an energy converter do?</h4>
            <p>An energy converter is a device or system that changes energy from one form to another. For example, a light bulb converts electrical energy into light and heat energy. This tool, however, is a unit converter; it mathematically converts the measurement of energy from one unit (like Joules) to another (like calories) without changing the energy itself.</p>

            <h4>How does energy conversion work?</h4>
            <p>Energy conversion works based on the First Law of Thermodynamics, which states that energy cannot be created or destroyed, only transformed. A device or process facilitates this change. For instance, in a car engine, the chemical energy stored in fuel is converted into thermal energy (heat) and then into mechanical energy to move the car.</p>

            <h4>What is an example of an energy converter?</h4>
            <p>There are many examples of energy converters in daily life. A solar panel converts light (radiant) energy into electrical energy. A human body converts chemical energy from food into mechanical energy for movement and thermal energy for warmth. A microphone converts sound (mechanical) energy into electrical energy.</p>

            <h4>Is energy conversion 100% efficient?</h4>
            <p>No, energy conversion is never 100% efficient in practice. The Second Law of Thermodynamics dictates that during any energy conversion, some energy is inevitably lost to the environment as a less useful form, typically as waste heat. This is why a light bulb gets hot while producing light, and a car engine requires a cooling system.</p>

            <h4>How do I convert energy to electricity?</h4>
            <p>Energy is converted to electricity through various methods: 1. Generators: Convert mechanical energy (from turbines powered by wind, water, or steam) into electricity. 2. Solar Panels (Photovoltaics): Convert sunlight directly into electricity. 3. Batteries: Convert stored chemical energy into electricity. 4. Thermoelectric Generators: Convert a temperature difference (heat energy) directly into electricity.</p>
        </div>

        <div class="energy-converter-features">
            <h3 class="energy-converter-features-title">Key Features:</h3>
            <ul class="energy-converter-features-list">
                <li class="energy-converter-features-item" style="margin-bottom: 0.3em;">Converts 7 common energy units</li>
                <li class="energy-converter-features-item" style="margin-bottom: 0.3em;">Includes scientific & nutritional units</li>
                <li class="energy-converter-features-item" style="margin-bottom: 0.3em;">High-precision calculations</li>
                <li class="energy-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="energy-converter-features-item" style="margin-bottom: 0.3em;">Fast, browser-based processing</li>
                <li class="energy-converter-features-item" style="margin-bottom: 0.3em;">Fully responsive for any device</li>
                <li class="energy-converter-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="energy-converter-notification" id="energyConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Energy Converter
        (function() {
            'use strict';

            // Conversion factors to Joules (J)
            const conversionFactors = {
                'j': 1,
                'kj': 1000,
                'cal': 4.184,
                'kcal': 4184,
                'wh': 3600,
                'kwh': 3600000,
                'btu': 1055.05585
            };

            const elements = {
                fromInput: () => document.getElementById('energyFromInput'),
                toInput: () => document.getElementById('energyToInput'),
                fromUnit: () => document.getElementById('energyFromUnit'),
                toUnit: () => document.getElementById('energyToUnit'),
                output: () => document.getElementById('energyConverterOutput'),
                notification: () => document.getElementById('energyConverterNotification')
            };

            window.EnergyConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to Joules first, then to target unit
                    const valueInJoules = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInJoules / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-6 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(10)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'j': 'J', 'kj': 'kJ', 'cal': 'cal', 'kcal': 'kcal', 'wh': 'Wh', 'kwh': 'kWh', 'btu': 'BTU' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted energy will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        EnergyConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>