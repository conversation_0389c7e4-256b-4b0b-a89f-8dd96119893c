<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Beautifier - Online CSS Formatter &amp; Prettifier</title>
    <meta name="description" content="Instantly format and beautify messy CSS code with our free online tool. Clean, indent, and prettify your stylesheets for improved readability and maintenance.">
    <link rel="canonical" href="https://www.webtoolskit.org/p/css-beautifier.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "CSS Beautifier - Online CSS Formatter & Prettifier",
        "description": "Instantly format and beautify messy CSS code with our free online tool. Clean, indent, and prettify your stylesheets for improved readability and maintenance.",
        "url": "https://www.webtoolskit.org/p/css-beautifier.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "CSS Beautifier",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ControlAction", "name": "Beautify CSS Code" },
            { "@type": "CopyAction", "name": "Copy Formatted CSS" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you make CSS code look pretty?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You can make CSS code look pretty by formatting it with consistent indentation, spacing, and line breaks. A CSS Beautifier tool automates this process by taking raw or minified CSS and applying formatting rules, such as adding two or four spaces for indentation, placing each declaration on a new line, and ensuring proper spacing around selectors and braces."
          }
        },
        {
          "@type": "Question",
          "name": "What is the best CSS formatter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The 'best' CSS formatter often depends on personal or team preference. Prettier is a widely adopted, opinionated code formatter that is considered a standard in the industry for its consistent output. Many online tools, like this CSS Beautifier, and IDE extensions (for VS Code, etc.) use Prettier or similar logic to provide high-quality formatting."
          }
        },
        {
          "@type": "Question",
          "name": "How do I format a CSS file in VS Code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To format a CSS file in Visual Studio Code, you can use the built-in formatter or install an extension like Prettier - Code formatter. Once installed, you can format your code by right-clicking in the file and selecting 'Format Document' or by using the shortcut Shift + Alt + F (on Windows) or Shift + Option + F (on Mac)."
          }
        },
        {
          "@type": "Question",
          "name": "What does it mean to beautify code?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Beautifying code, also known as pretty-printing, is the process of reformatting source code to make it more readable for humans. This involves adding indentation, line breaks, and consistent spacing without changing the code's functionality. The goal is to improve code maintenance, debugging, and collaboration."
          }
        },
        {
          "@type": "Question",
          "name": "Is there a Prettier for CSS online?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, there are many online tools that use the principles or libraries of Prettier to format CSS. This CSS Beautifier is an example of such a tool. It provides a web-based interface where you can paste your CSS and get a 'prettified' output instantly, without needing to install any software or command-line tools."
          }
        }
      ]
    }
    </script>


    <style>
        /* CSS Beautifier Widget - Simplified & Template Compatible */
        .css-beautifier-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .css-beautifier-widget-container * { box-sizing: border-box; }

        .css-beautifier-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .css-beautifier-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .css-beautifier-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .css-beautifier-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 150px;
            background-color: var(--background-color-alt);
            font-family: 'SF Mono', Monaco, monospace;
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .css-beautifier-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .css-beautifier-options {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .css-beautifier-option-label {
            font-weight: 500;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .css-beautifier-select {
            padding: var(--spacing-sm);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-family: var(--font-family);
            font-weight: 500;
        }

        .css-beautifier-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .css-beautifier-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .css-beautifier-btn:hover { transform: translateY(-2px); }

        .css-beautifier-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .css-beautifier-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .css-beautifier-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .css-beautifier-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .css-beautifier-btn-success {
            background-color: #10b981;
            color: white;
        }

        .css-beautifier-btn-success:hover {
            background-color: #059669;
        }

        .css-beautifier-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .css-beautifier-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .css-beautifier-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            white-space: pre-wrap;
            min-height: 150px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .css-beautifier-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .css-beautifier-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .css-beautifier-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .css-beautifier-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .css-beautifier-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .css-beautifier-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .css-beautifier-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .css-beautifier-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="css-minifier"] .css-beautifier-related-tool-icon { background: linear-gradient(145deg, #4F46E5, #4338CA); }
        a[href*="html-beautifier"] .css-beautifier-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="javascript-beautifier"] .css-beautifier-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }

        .css-beautifier-related-tool-item:hover .css-beautifier-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="css-minifier"]:hover .css-beautifier-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }
        a[href*="html-beautifier"]:hover .css-beautifier-related-tool-icon { background: linear-gradient(145deg, #F97316, #EA580C); }
        a[href*="javascript-beautifier"]:hover .css-beautifier-related-tool-icon { background: linear-gradient(145deg, #F87171, #EF4444); }
        
        .css-beautifier-related-tool-item { box-shadow: none; border: none; }
        .css-beautifier-related-tool-item:hover { box-shadow: none; border: none; }
        .css-beautifier-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .css-beautifier-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .css-beautifier-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .css-beautifier-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .css-beautifier-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .css-beautifier-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .css-beautifier-related-tool-item:hover .css-beautifier-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .css-beautifier-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .css-beautifier-widget-title { font-size: 1.875rem; }
            .css-beautifier-buttons { flex-direction: column; }
            .css-beautifier-btn { flex: none; }
            .css-beautifier-options { flex-direction: column; align-items: flex-start; }
            .css-beautifier-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .css-beautifier-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .css-beautifier-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .css-beautifier-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .css-beautifier-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .css-beautifier-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .css-beautifier-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .css-beautifier-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .css-beautifier-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .css-beautifier-select:focus, .css-beautifier-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .css-beautifier-output::selection { background-color: var(--primary-color); color: white; }
        @media (max-width: 600px) { .css-beautifier-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="css-beautifier-widget-container">
        <h1 class="css-beautifier-widget-title">CSS Beautifier</h1>
        <p class="css-beautifier-widget-description">
            Clean up messy, minified, or unformatted CSS. Our tool instantly formats your code into a readable, well-structured, and easy-to-maintain stylesheet.
        </p>
        
        <div class="css-beautifier-input-group">
            <label for="cssBeautifierInput" class="css-beautifier-label">Enter your CSS code:</label>
            <textarea 
                id="cssBeautifierInput" 
                class="css-beautifier-textarea"
                placeholder="/* Paste your messy CSS code here */&#10;body{font-family: 'Arial',sans-serif;background:#fff;color:#333}a{color:blue;text-decoration:none}a:hover{text-decoration:underline}"
                rows="8"
            ></textarea>
        </div>

        <div class="css-beautifier-options">
            <label for="cssIndentSize" class="css-beautifier-option-label">Indentation:</label>
            <select id="cssIndentSize" class="css-beautifier-select">
                <option value="2">2 Spaces</option>
                <option value="4" selected>4 Spaces</option>
                <option value="tab">Tabs</option>
            </select>
        </div>

        <div class="css-beautifier-buttons">
            <button class="css-beautifier-btn css-beautifier-btn-primary" onclick="CssBeautifier.beautify()">
                Beautify CSS
            </button>
            <button class="css-beautifier-btn css-beautifier-btn-secondary" onclick="CssBeautifier.clear()">
                Clear All
            </button>
            <button class="css-beautifier-btn css-beautifier-btn-success" onclick="CssBeautifier.copy()">
                Copy Result
            </button>
        </div>

        <div class="css-beautifier-result">
            <h3 class="css-beautifier-result-title">Formatted CSS:</h3>
            <div class="css-beautifier-output" id="cssBeautifierOutput">
                Your formatted CSS will appear here...
            </div>
        </div>

        <div class="css-beautifier-related-tools">
            <h3 class="css-beautifier-related-tools-title">Related Tools</h3>
            <div class="css-beautifier-related-tools-grid">
                <a href="/p/css-minifier.html" class="css-beautifier-related-tool-item" rel="noopener">
                    <div class="css-beautifier-related-tool-icon">
                        <i class="fas fa-compress"></i>
                    </div>
                    <div class="css-beautifier-related-tool-name">CSS Minifier</div>
                </a>
                <a href="/p/html-beautifier.html" class="css-beautifier-related-tool-item" rel="noopener">
                    <div class="css-beautifier-related-tool-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="css-beautifier-related-tool-name">HTML Beautifier</div>
                </a>
                <a href="/p/javascript-beautifier.html" class="css-beautifier-related-tool-item" rel="noopener">
                    <div class="css-beautifier-related-tool-icon">
                        <i class="fab fa-js-square"></i>
                    </div>
                    <div class="css-beautifier-related-tool-name">JavaScript Beautifier</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Format Your Code with Our Online CSS Beautifier</h2>
            <p>Tired of untangling messy, unformatted CSS? Our online <strong>CSS Beautifier</strong> is the perfect tool for developers, designers, and students who want to transform chaotic stylesheets into clean, readable, and well-structured code. Beautifying your CSS makes it significantly easier to read, debug, and maintain. With just one click, you can format your code according to standard best practices, improving collaboration and saving valuable development time.</p>
            
            <h3>How to Use the CSS Formatter</h3>
            <ol>
                <li><strong>Paste Your Code:</strong> Copy your disorganized CSS code and paste it into the input field above.</li>
                <li><strong>Choose Options:</strong> Select your preferred indentation level (e.g., 2 or 4 spaces) to customize the output format.</li>
                <li><strong>Beautify & Copy:</strong> Click the "Beautify CSS" button. Your code will be instantly formatted in the output box, ready for you to copy and use in your projects.</li>
            </ol>
        
            <h3>Frequently Asked Questions About CSS Beautifier</h3>
            
            <h4>How do you make CSS code look pretty?</h4>
            <p>You can make CSS code look pretty by formatting it with consistent indentation, spacing, and line breaks. A CSS Beautifier tool automates this process by taking raw or minified CSS and applying formatting rules, such as adding two or four spaces for indentation, placing each declaration on a new line, and ensuring proper spacing around selectors and braces.</p>
            
            <h4>What is the best CSS formatter?</h4>
            <p>The "best" CSS formatter often depends on personal or team preference. Prettier is a widely adopted, opinionated code formatter that is considered a standard in the industry for its consistent output. Many online tools, like this CSS Beautifier, and IDE extensions (for VS Code, etc.) use Prettier or similar logic to provide high-quality formatting.</p>
            
            <h4>How do I format a CSS file in VS Code?</h4>
            <p>To format a CSS file in Visual Studio Code, you can use the built-in formatter or install an extension like Prettier - Code formatter. Once installed, you can format your code by right-clicking in the file and selecting 'Format Document' or by using the shortcut Shift + Alt + F (on Windows) or Shift + Option + F (on Mac).</p>
            
            <h4>What does it mean to beautify code?</h4>
            <p>Beautifying code, also known as pretty-printing, is the process of reformatting source code to make it more readable for humans. This involves adding indentation, line breaks, and consistent spacing without changing the code's functionality. The goal is to improve code maintenance, debugging, and collaboration.</p>
            
            <h4>Is there a Prettier for CSS online?</h4>
            <p>Yes, there are many online tools that use the principles or libraries of Prettier to format CSS. This CSS Beautifier is an example of such a tool. It provides a web-based interface where you can paste your CSS and get a 'prettified' output instantly, without needing to install any software or command-line tools.</p>
        </div>

        <div class="css-beautifier-features">
            <h3 class="css-beautifier-features-title">Key Features:</h3>
            <ul class="css-beautifier-features-list">
                <li class="css-beautifier-features-item">Customizable indentation (spaces/tabs)</li>
                <li class="css-beautifier-features-item">Instant, in-browser formatting</li>
                <li class="css-beautifier-features-item">Improves code readability</li>
                <li class="css-beautifier-features-item">Simplifies debugging</li>
                <li class="css-beautifier-features-item">Handles messy & minified code</li>
                <li class="css-beautifier-features-item">One-click copy to clipboard</li>
                <li class="css-beautifier-features-item">Promotes team coding standards</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="css-beautifier-notification" id="cssBeautifierNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // CSS Beautifier
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('cssBeautifierInput'),
                output: () => document.getElementById('cssBeautifierOutput'),
                notification: () => document.getElementById('cssBeautifierNotification'),
                indentSize: () => document.getElementById('cssIndentSize')
            };

            window.CssBeautifier = {
                beautify() {
                    const input = elements.input();
                    const output = elements.output();
                    const cssText = input.value;

                    if (!cssText.trim()) {
                        output.textContent = 'Please enter CSS code to beautify.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        indent: elements.indentSize().value
                    };

                    try {
                        const result = this.processCss(cssText, options);
                        output.textContent = result || 'Could not format the provided CSS.';
                    } catch (error) {
                        output.textContent = `Error: ${error.message}`;
                        output.style.color = '#dc2626';
                    }
                },

                processCss(text, options) {
                    let indentLevel = 0;
                    let indentString = '    '; // Default to 4 spaces
                    if (options.indent === '2') {
                        indentString = '  ';
                    } else if (options.indent === 'tab') {
                        indentString = '\t';
                    }
                    
                    let result = '';
                    let inComment = false;
                    let inString = false;
                    let stringChar = '';

                    // Pre-process: clean up excessive whitespace and ensure spaces around operators
                    text = text.replace(/\s*([{};:,])\s*/g, ' $1 ').replace(/\s\s+/g, ' ').trim();

                    for (let i = 0; i < text.length; i++) {
                        const char = text[i];
                        const nextChar = text[i + 1] || '';

                        if (inComment) {
                            result += char;
                            if (char === '*' && nextChar === '/') {
                                result += '/';
                                i++;
                                inComment = false;
                                if (!/^\s*$/.test(text.substring(i + 1))) {
                                    result += '\n' + indentString.repeat(indentLevel);
                                }
                            }
                            continue;
                        }
                        
                        if (inString) {
                            result += char;
                            if (char === '\\' && nextChar) {
                                result += nextChar;
                                i++;
                            } else if (char === stringChar) {
                                inString = false;
                            }
                            continue;
                        }

                        if (char === "'" || char === '"') {
                            inString = true;
                            stringChar = char;
                            result += char;
                            continue;
                        }

                        if (char === '/' && nextChar === '*') {
                            inComment = true;
                            if (result.trim().length > 0 && result.slice(-1) !== '\n') {
                                result = result.trimEnd() + '\n' + indentString.repeat(indentLevel);
                            }
                            result += '/*';
                            i++;
                            continue;
                        }

                        if (char === '{') {
                            result = result.trim() + ' {\n';
                            indentLevel++;
                            result += indentString.repeat(indentLevel);
                        } else if (char === '}') {
                            result = result.trim();
                            indentLevel = Math.max(0, indentLevel - 1);
                            result += '\n' + indentString.repeat(indentLevel) + '}\n' + indentString.repeat(indentLevel);
                        } else if (char === ';') {
                            result = result.trim() + ';\n';
                            result += indentString.repeat(indentLevel);
                        } else if (char === ',') {
                            result = result.trim() + ',\n' + indentString.repeat(indentLevel);
                        } else if (/\s/.test(char)) {
                            if (result.length > 0 && !/\s/.test(result.slice(-1))) {
                                result += ' ';
                            }
                        } else {
                            result += char;
                        }
                    }

                    // Final cleanup
                    return result.replace(/(\n\s*){3,}/g, '\n\n').replace(/}\n\s*}/g, '}\n}').trim();
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your formatted CSS will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your formatted CSS will appear here...', 'Please enter CSS code to beautify.', 'Could not format the provided CSS.'].includes(text) || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        CssBeautifier.beautify();
                    }
                });
            });
        })();
    </script>
</body>
</html>