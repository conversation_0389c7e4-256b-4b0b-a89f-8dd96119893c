<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Charge Converter - Convert Coulombs, Amp-hours (Ah), mAh</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Charge Converter - Convert Coulombs, Amp-hours (Ah), mAh",
        "description": "Instantly convert between electric charge units like coulombs (C), ampere-hours (Ah), milliampere-hours (mAh), and more. Free online tool for electronics, battery analysis, and engineering.",
        "url": "https://www.webtoolskit.org/p/charge-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-02",
        "dateModified": "2025-06-02",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Charge Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Charge Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a charge converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A 'charge converter' can refer to two things. Physically, it can be an electronic circuit like a charge pump that converts voltage. However, this online tool is a unit converter. Its purpose is to mathematically convert a measurement of electric charge from one unit (like milliampere-hours) to another (like coulombs) for calculations and comparisons."
          }
        },
        {
          "@type": "Question",
          "name": "How do you calculate electric charge?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Electric charge (Q) can be calculated if you know the constant current (I) and the time (t) it flows for. The formula is Q = I × t. For example, if a current of 2 amperes flows for 10 seconds, the total charge that has passed is 2 A × 10 s = 20 Coulombs."
          }
        },
        {
          "@type": "Question",
          "name": "Can I use a buck converter to charge a battery?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "While a buck converter can step down voltage to the correct level for a battery, using it alone is not recommended. A proper battery charger is a smart system that manages the charging process, typically using CC/CV (Constant Current/Constant Voltage) stages, monitoring temperature, and terminating the charge when full. A simple buck converter lacks this crucial safety and management logic."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between a DC converter and a DC charger?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A DC-DC converter is a basic electronic circuit that simply changes one DC voltage level to another (e.g., 12V to 5V). A DC charger is a more complex system built around a converter. It includes control circuitry to safely manage the battery charging process, adjusting voltage and current based on the battery's state and terminating the charge when complete to prevent overcharging."
          }
        },
        {
          "@type": "Question",
          "name": "How do I know if my converter is charging my battery?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "There are several ways to check. Many chargers have an indicator light (e.g., red for charging, green for full). You can also use a multimeter to measure the battery's voltage; it should be slowly rising during the charge. A more direct method is to use an ammeter or a USB power meter connected in series to measure the current flowing into the battery. A positive current reading indicates it is charging."
          }
        }
      ]
    }
    </script>

    <style>
        /* Charge Converter Widget - Simplified & Template Compatible */
        .charge-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .charge-converter-widget-container * { box-sizing: border-box; }

        .charge-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .charge-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .charge-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .charge-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .charge-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .charge-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .charge-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .charge-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .charge-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .charge-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .charge-converter-btn:hover { transform: translateY(-2px); }

        .charge-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .charge-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .charge-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .charge-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .charge-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .charge-converter-btn-success:hover {
            background-color: #059669;
        }

        .charge-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .charge-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .charge-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .charge-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .charge-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .charge-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .charge-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .charge-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .charge-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .charge-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .charge-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="current-converter"] .charge-converter-related-tool-icon { background: linear-gradient(145deg, #3B82F6, #2563EB); }
        a[href*="voltage-converter"] .charge-converter-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="power-converter"] .charge-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }

        .charge-converter-related-tool-item:hover .charge-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="current-converter"]:hover .charge-converter-related-tool-icon { background: linear-gradient(145deg, #60a5fa, #3b82f6); }
        a[href*="voltage-converter"]:hover .charge-converter-related-tool-icon { background: linear-gradient(145deg, #fbbd24, #f59e0b); }
        a[href*="power-converter"]:hover .charge-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        
        .charge-converter-related-tool-item { box-shadow: none; border: none; }
        .charge-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .charge-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .charge-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .charge-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .charge-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .charge-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .charge-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .charge-converter-related-tool-item:hover .charge-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .charge-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .charge-converter-widget-title { font-size: 1.875rem; }
            .charge-converter-buttons { flex-direction: column; }
            .charge-converter-btn { flex: none; }
            .charge-converter-input-group { grid-template-columns: 1fr; }
            .charge-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .charge-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .charge-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .charge-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .charge-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .charge-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .charge-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .charge-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .charge-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .charge-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .charge-converter-output::selection { background-color: var(--primary-color); color: white; }
        .charge-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .charge-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="charge-converter-widget-container">
        <h1 class="charge-converter-widget-title">Charge Converter</h1>
        <p class="charge-converter-widget-description">
            Easily convert between electric charge units like Coulombs (C), Ampere-hours (Ah), and Milliampere-hours (mAh) for your electronics projects.
        </p>
        
        <div class="charge-converter-input-group">
            <label for="chargeFromInput" class="charge-converter-label">From:</label>
            <input 
                type="number" 
                id="chargeFromInput" 
                class="charge-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="chargeFromUnit" class="charge-converter-select">
                <option value="c" selected>Coulomb (C)</option>
                <option value="mc">Millicoulomb (mC)</option>
                <option value="uc">Microcoulomb (μC)</option>
                <option value="ah">Ampere-hour (Ah)</option>
                <option value="mah">Milliampere-hour (mAh)</option>
            </select>
        </div>

        <div class="charge-converter-input-group">
            <label for="chargeToInput" class="charge-converter-label">To:</label>
            <input 
                type="number" 
                id="chargeToInput" 
                class="charge-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="chargeToUnit" class="charge-converter-select">
                <option value="c">Coulomb (C)</option>
                <option value="mc">Millicoulomb (mC)</option>
                <option value="uc">Microcoulomb (μC)</option>
                <option value="ah">Ampere-hour (Ah)</option>
                <option value="mah" selected>Milliampere-hour (mAh)</option>
            </select>
        </div>

        <div class="charge-converter-buttons">
            <button class="charge-converter-btn charge-converter-btn-primary" onclick="ChargeConverter.convert()">
                Convert Charge
            </button>
            <button class="charge-converter-btn charge-converter-btn-secondary" onclick="ChargeConverter.clear()">
                Clear All
            </button>
            <button class="charge-converter-btn charge-converter-btn-success" onclick="ChargeConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="charge-converter-result">
            <h3 class="charge-converter-result-title">Conversion Result:</h3>
            <div class="charge-converter-output" id="chargeConverterOutput">
                Your converted charge will appear here...
            </div>
        </div>

        <div class="charge-converter-related-tools">
            <h3 class="charge-converter-related-tools-title">Related Tools</h3>
            <div class="charge-converter-related-tools-grid">
                <a href="/p/current-converter.html" class="charge-converter-related-tool-item" rel="noopener">
                    <div class="charge-converter-related-tool-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="charge-converter-related-tool-name">Current Converter</div>
                </a>
                <a href="/p/voltage-converter.html" class="charge-converter-related-tool-item" rel="noopener">
                    <div class="charge-converter-related-tool-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <div class="charge-converter-related-tool-name">Voltage Converter</div>
                </a>
                <a href="/p/power-converter.html" class="charge-converter-related-tool-item" rel="noopener">
                    <div class="charge-converter-related-tool-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="charge-converter-related-tool-name">Power Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Instant Electric Charge Unit Conversion</h2>
            <p>Electric charge is the fundamental property of matter that causes it to experience a force when placed in an electromagnetic field. In practical electronics, it's a key measure of a battery's capacity. Whether you're working with the SI unit of Coulombs (C) or the more common battery ratings of Milliampere-hours (mAh) and Ampere-hours (Ah), you need to be able to switch between them. Our free <strong>Charge Converter</strong> is a simple tool to make these conversions instant and accurate.</p>
            <p>This utility is perfect for electronics hobbyists, engineers, and students who are calculating battery life, specifying components, or working on physics problems. Eliminate the chance of manual error and streamline your calculations with this easy-to-use converter.</p>

            <h3>How to Use the Charge Converter</h3>
            <ol>
                <li><strong>Enter Your Value:</strong> Type the numeric value of the charge you want to convert.</li>
                <li><strong>Select Units:</strong> Choose your starting unit (e.g., Ampere-hour) and your target unit (e.g., Coulomb) from the dropdown lists.</li>
                <li><strong>Convert:</strong> Click the "Convert Charge" button for an immediate, precise calculation.</li>
                <li><strong>Copy Result:</strong> Use the "Copy Result" button to easily copy the converted value for use in your work.</li>
            </ol>

            <h3>Frequently Asked Questions About Charge Conversion</h3>
            
            <h4>What is a charge converter?</h4>
            <p>A 'charge converter' can refer to two things. Physically, it can be an electronic circuit like a charge pump that converts voltage. However, this online tool is a unit converter. Its purpose is to mathematically convert a measurement of electric charge from one unit (like milliampere-hours) to another (like coulombs) for calculations and comparisons.</p>

            <h4>How do you calculate electric charge?</h4>
            <p>Electric charge (Q) can be calculated if you know the constant current (I) and the time (t) it flows for. The formula is Q = I × t. For example, if a current of 2 amperes flows for 10 seconds, the total charge that has passed is 2 A × 10 s = 20 Coulombs.</p>

            <h4>Can I use a buck converter to charge a battery?</h4>
            <p>While a buck converter can step down voltage to the correct level for a battery, using it alone is not recommended. A proper battery charger is a smart system that manages the charging process, typically using CC/CV (Constant Current/Constant Voltage) stages, monitoring temperature, and terminating the charge when full. A simple buck converter lacks this crucial safety and management logic.</p>

            <h4>What is the difference between a DC converter and a DC charger?</h4>
            <p>A DC-DC converter is a basic electronic circuit that simply changes one DC voltage level to another (e.g., 12V to 5V). A DC charger is a more complex system built around a converter. It includes control circuitry to safely manage the battery charging process, adjusting voltage and current based on the battery's state and terminating the charge when complete to prevent overcharging.</p>

            <h4>How do I know if my converter is charging my battery?</h4>
            <p>There are several ways to check. Many chargers have an indicator light (e.g., red for charging, green for full). You can also use a multimeter to measure the battery's voltage; it should be slowly rising during the charge. A more direct method is to use an ammeter or a USB power meter connected in series to measure the current flowing into the battery. A positive current reading indicates it is charging.</p>
        </div>

        <div class="charge-converter-features">
            <h3 class="charge-converter-features-title">Key Features:</h3>
            <ul class="charge-converter-features-list">
                <li class="charge-converter-features-item" style="margin-bottom: 0.3em;">Converts C, Ah, mAh & more</li>
                <li class="charge-converter-features-item" style="margin-bottom: 0.3em;">Ideal for battery capacity math</li>
                <li class="charge-converter-features-item" style="margin-bottom: 0.3em;">High-precision calculations</li>
                <li class="charge-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="charge-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side processing</li>
                <li class="charge-converter-features-item" style="margin-bottom: 0.3em;">Responsive on all devices</li>
                <li class="charge-converter-features-item">100% free and private</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="charge-converter-notification" id="chargeConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Charge Converter
        (function() {
            'use strict';

            // Conversion factors to Coulombs (C)
            const conversionFactors = {
                'c': 1,
                'mc': 0.001,
                'uc': 1e-6,
                'ah': 3600,
                'mah': 3.6
            };

            const elements = {
                fromInput: () => document.getElementById('chargeFromInput'),
                toInput: () => document.getElementById('chargeToInput'),
                fromUnit: () => document.getElementById('chargeFromUnit'),
                toUnit: () => document.getElementById('chargeToUnit'),
                output: () => document.getElementById('chargeConverterOutput'),
                notification: () => document.getElementById('chargeConverterNotification')
            };

            window.ChargeConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to base unit (Coulombs) first, then to target unit
                    const valueInCoulombs = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInCoulombs / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-9 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(12)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'c': 'C', 'mc': 'mC', 'uc': 'μC', 'ah': 'Ah', 'mah': 'mAh' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted charge will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        ChargeConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>