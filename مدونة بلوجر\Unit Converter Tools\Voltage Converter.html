<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voltage Converter - Convert Volts, Millivolts, Kilovolts & More</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Voltage Converter - Convert Volts, Millivolts, Kilovolts & More",
        "description": "Instantly convert between electrical voltage units like volts (V), millivolts (mV), kilovolts (kV), and others. Free online tool for engineers, students, and electronics enthusiasts.",
        "url": "https://www.webtoolskit.org/p/voltage-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-19",
        "dateModified": "2025-06-19",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Voltage Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Voltage Units" },
            { "@type": "CopyAction", "name": "Copy Converted Result" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Do I need a voltage converter for Europe?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "It depends on your device. North America uses ~120V, while Europe uses ~230V. Check the power label on your device's charger or plug. If it says 'INPUT: 100-240V', it is dual-voltage and you only need a simple plug adapter to fit the European outlet. If it only says 'INPUT: 120V', you will need a voltage converter to step down the voltage and prevent damage to your device."
          }
        },
        {
          "@type": "Question",
          "name": "What is a voltage converter used for?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A physical voltage converter is a device that changes the voltage of an electrical power source. Its primary use is to allow a device designed for one voltage standard (e.g., 120V in the US) to be safely used with another (e.g., 230V in Europe). It either 'steps down' (reduces) or 'steps up' (increases) the voltage to match the device's requirement, protecting it from electrical damage."
          }
        },
        {
          "@type": "Question",
          "name": "What happens if I don't use a voltage converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using an electronic device without the correct voltage converter can have serious consequences. Plugging a 120V device into a 230V outlet will send too much power to it, which can instantly overheat and destroy its internal components, making it unusable. Plugging a 230V device into a 120V outlet will underpower it, causing it to not turn on or function very poorly."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between a travel adapter and a voltage converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A travel adapter and a voltage converter serve two different functions. A travel adapter simply changes the physical shape of your device's plug to fit a foreign wall outlet; it does not change the electrical voltage. A voltage converter is an electrical device that actually changes the voltage. You need an adapter if your device is dual-voltage, but you need a converter (and possibly an adapter) if it is not."
          }
        },
        {
          "@type": "Question",
          "name": "How do I tell if I need a voltage converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Look for the power specification label, usually found on the device's power brick, plug, or back panel. Find the 'INPUT' rating. If the range listed is '100-240V' and '50/60Hz', your device is dual-voltage and you do not need a voltage converter, only a plug adapter. If it lists a single voltage, like '120V', then you will need a voltage converter when traveling to a country with a different standard."
          }
        }
      ]
    }
    </script>

    <style>
        /* Voltage Converter Widget - Simplified & Template Compatible */
        .voltage-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .voltage-converter-widget-container * { box-sizing: border-box; }

        .voltage-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .voltage-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .voltage-converter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .voltage-converter-input-group {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .voltage-converter-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .voltage-converter-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .voltage-converter-select {
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .voltage-converter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .voltage-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .voltage-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .voltage-converter-btn:hover { transform: translateY(-2px); }

        .voltage-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .voltage-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .voltage-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .voltage-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .voltage-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .voltage-converter-btn-success:hover {
            background-color: #059669;
        }

        .voltage-converter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .voltage-converter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .voltage-converter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .voltage-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .voltage-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .voltage-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .voltage-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .voltage-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .voltage-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .voltage-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .voltage-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="current-converter"] .voltage-converter-related-tool-icon { background: linear-gradient(145deg, #3B82F6, #2563EB); }
        a[href*="power-converter"] .voltage-converter-related-tool-icon { background: linear-gradient(145deg, #EF4444, #DC2626); }
        a[href*="charge-converter"] .voltage-converter-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }

        .voltage-converter-related-tool-item:hover .voltage-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="current-converter"]:hover .voltage-converter-related-tool-icon { background: linear-gradient(145deg, #60a5fa, #3b82f6); }
        a[href*="power-converter"]:hover .voltage-converter-related-tool-icon { background: linear-gradient(145deg, #f87171, #ef4444); }
        a[href*="charge-converter"]:hover .voltage-converter-related-tool-icon { background: linear-gradient(145deg, #f06bb3, #e91e63); }
        
        .voltage-converter-related-tool-item { box-shadow: none; border: none; }
        .voltage-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .voltage-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .voltage-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .voltage-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .voltage-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .voltage-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .voltage-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .voltage-converter-related-tool-item:hover .voltage-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .voltage-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .voltage-converter-widget-title { font-size: 1.875rem; }
            .voltage-converter-buttons { flex-direction: column; }
            .voltage-converter-btn { flex: none; }
            .voltage-converter-input-group { grid-template-columns: 1fr; }
            .voltage-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .voltage-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .voltage-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .voltage-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .voltage-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .voltage-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .voltage-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .voltage-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .voltage-converter-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .voltage-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .voltage-converter-output::selection { background-color: var(--primary-color); color: white; }
        .voltage-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .voltage-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="voltage-converter-widget-container">
        <h1 class="voltage-converter-widget-title">Voltage Converter</h1>
        <p class="voltage-converter-widget-description">
            Convert electrical voltage units with ease. Instantly switch between Volts, Millivolts, Kilovolts, and more for your calculations.
        </p>
        
        <div class="voltage-converter-input-group">
            <label for="voltageFromInput" class="voltage-converter-label">From:</label>
            <input 
                type="number" 
                id="voltageFromInput" 
                class="voltage-converter-input"
                placeholder="Enter value to convert..."
                step="any"
            />
            <select id="voltageFromUnit" class="voltage-converter-select">
                <option value="v" selected>Volt (V)</option>
                <option value="mv">Millivolt (mV)</option>
                <option value="kv">Kilovolt (kV)</option>
                <option value="uv">Microvolt (μV)</option>
                <option value="Mv">Megavolt (MV)</option>
            </select>
        </div>

        <div class="voltage-converter-input-group">
            <label for="voltageToInput" class="voltage-converter-label">To:</label>
            <input 
                type="number" 
                id="voltageToInput" 
                class="voltage-converter-input"
                placeholder="Converted value will appear here..."
                readonly
            />
            <select id="voltageToUnit" class="voltage-converter-select">
                <option value="v">Volt (V)</option>
                <option value="mv" selected>Millivolt (mV)</option>
                <option value="kv">Kilovolt (kV)</option>
                <option value="uv">Microvolt (μV)</option>
                <option value="Mv">Megavolt (MV)</option>
            </select>
        </div>

        <div class="voltage-converter-buttons">
            <button class="voltage-converter-btn voltage-converter-btn-primary" onclick="VoltageConverter.convert()">
                Convert Voltage
            </button>
            <button class="voltage-converter-btn voltage-converter-btn-secondary" onclick="VoltageConverter.clear()">
                Clear All
            </button>
            <button class="voltage-converter-btn voltage-converter-btn-success" onclick="VoltageConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="voltage-converter-result">
            <h3 class="voltage-converter-result-title">Conversion Result:</h3>
            <div class="voltage-converter-output" id="voltageConverterOutput">
                Your converted voltage will appear here...
            </div>
        </div>

        <div class="voltage-converter-related-tools">
            <h3 class="voltage-converter-related-tools-title">Related Tools</h3>
            <div class="voltage-converter-related-tools-grid">
                <a href="/p/current-converter.html" class="voltage-converter-related-tool-item" rel="noopener">
                    <div class="voltage-converter-related-tool-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="voltage-converter-related-tool-name">Current Converter</div>
                </a>
                <a href="/p/power-converter.html" class="voltage-converter-related-tool-item" rel="noopener">
                    <div class="voltage-converter-related-tool-icon">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="voltage-converter-related-tool-name">Power Converter</div>
                </a>
                <a href="/p/charge-converter.html" class="voltage-converter-related-tool-item" rel="noopener">
                    <div class="voltage-converter-related-tool-icon">
                        <i class="fas fa-battery-half"></i>
                    </div>
                    <div class="voltage-converter-related-tool-name">Charge Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Easy and Accurate Voltage Unit Conversion</h2>
            <p>In the world of electronics and electrical engineering, voltage is a fundamental concept. Whether you're analyzing a circuit, reading a component datasheet, or studying electrical theory, you'll encounter voltage values in various scales. Our free <strong>Voltage Converter</strong> is a handy tool that simplifies the process of converting between these units. It allows for quick and precise conversions between Volts (V), Millivolts (mV), Kilovolts (kV), and more, saving you time and preventing calculation errors.</p>
            <p>This tool is indispensable for students, hobbyists, and professionals who need to work with different voltage magnitudes, from the tiny signals in a sensor (microvolts) to the massive potential in power transmission lines (kilovolts). Ensure your calculations are always accurate with this easy-to-use converter.</p>

            <h3>How to Use the Voltage Converter</h3>
            <ol>
                <li><strong>Enter Your Value:</strong> Type the numeric voltage value you want to convert into the "From" input field.</li>
                <li><strong>Select Your Units:</strong> Choose your starting unit (e.g., Volts) from the first dropdown and your target unit (e.g., Millivolts) from the second.</li>
                <li><strong>Convert:</strong> Click the "Convert Voltage" button to get an instant result.</li>
                <li><strong>Copy Result:</strong> Click the "Copy Result" button to easily copy the converted value for your notes, reports, or schematics.</li>
            </ol>

            <h3>Frequently Asked Questions About Voltage Conversion</h3>

            <h4>Do I need a voltage converter for Europe?</h4>
            <p>It depends on your device. North America uses ~120V, while Europe uses ~230V. Check the power label on your device's charger or plug. If it says 'INPUT: 100-240V', it is dual-voltage and you only need a simple plug adapter to fit the European outlet. If it only says 'INPUT: 120V', you will need a voltage converter to step down the voltage and prevent damage to your device.</p>
            
            <h4>What is a voltage converter used for?</h4>
            <p>A physical voltage converter is a device that changes the voltage of an electrical power source. Its primary use is to allow a device designed for one voltage standard (e.g., 120V in the US) to be safely used with another (e.g., 230V in Europe). It either 'steps down' (reduces) or 'steps up' (increases) the voltage to match the device's requirement, protecting it from electrical damage.</p>

            <h4>What happens if I don't use a voltage converter?</h4>
            <p>Using an electronic device without the correct voltage converter can have serious consequences. Plugging a 120V device into a 230V outlet will send too much power to it, which can instantly overheat and destroy its internal components, making it unusable. Plugging a 230V device into a 120V outlet will underpower it, causing it to not turn on or function very poorly.</p>

            <h4>What is the difference between a travel adapter and a voltage converter?</h4>
            <p>A travel adapter and a voltage converter serve two different functions. A travel adapter simply changes the physical shape of your device's plug to fit a foreign wall outlet; it does not change the electrical voltage. A voltage converter is an electrical device that actually changes the voltage. You need an adapter if your device is dual-voltage, but you need a converter (and possibly an adapter) if it is not.</p>

            <h4>How do I tell if I need a voltage converter?</h4>
            <p>Look for the power specification label, usually found on the device's power brick, plug, or back panel. Find the 'INPUT' rating. If the range listed is '100-240V' and '50/60Hz', your device is dual-voltage and you do not need a voltage converter, only a plug adapter. If it lists a single voltage, like '120V', then you will need a voltage converter when traveling to a country with a different standard.</p>
        </div>

        <div class="voltage-converter-features">
            <h3 class="voltage-converter-features-title">Key Features:</h3>
            <ul class="voltage-converter-features-list">
                <li class="voltage-converter-features-item" style="margin-bottom: 0.3em;">Converts V, mV, kV, µV, MV</li>
                <li class="voltage-converter-features-item" style="margin-bottom: 0.3em;">High-precision results</li>
                <li class="voltage-converter-features-item" style="margin-bottom: 0.3em;">Ideal for electrical calculations</li>
                <li class="voltage-converter-features-item" style="margin-bottom: 0.3em;">One-click copy function</li>
                <li class="voltage-converter-features-item" style="margin-bottom: 0.3em;">Fast, client-side conversions</li>
                <li class="voltage-converter-features-item" style="margin-bottom: 0.3em;">Responsive on all devices</li>
                <li class="voltage-converter-features-item">100% free and private to use</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="voltage-converter-notification" id="voltageConverterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Voltage Converter
        (function() {
            'use strict';

            // Conversion factors to Volts (V)
            const conversionFactors = {
                'v': 1,
                'mv': 0.001,
                'kv': 1000,
                'uv': 1e-6,
                'Mv': 1e6
            };

            const elements = {
                fromInput: () => document.getElementById('voltageFromInput'),
                toInput: () => document.getElementById('voltageToInput'),
                fromUnit: () => document.getElementById('voltageFromUnit'),
                toUnit: () => document.getElementById('voltageToUnit'),
                output: () => document.getElementById('voltageConverterOutput'),
                notification: () => document.getElementById('voltageConverterNotification')
            };

            window.VoltageConverter = {
                convert() {
                    const fromInput = elements.fromInput();
                    const toInput = elements.toInput();
                    const fromUnit = elements.fromUnit();
                    const toUnit = elements.toUnit();
                    const output = elements.output();

                    const value = parseFloat(fromInput.value);

                    if (isNaN(value) || fromInput.value.trim() === '') {
                        output.textContent = 'Please enter a valid number to convert.';
                        output.style.color = '#dc2626';
                        toInput.value = '';
                        return;
                    }

                    output.style.color = '';

                    // Convert to Volts first, then to target unit
                    const valueInVolts = value * conversionFactors[fromUnit.value];
                    const convertedValue = valueInVolts / conversionFactors[toUnit.value];

                    // Format the result
                    const formattedResult = this.formatResult(convertedValue);

                    toInput.value = formattedResult;
                    output.textContent = `${value} ${this.getUnitName(fromUnit.value)} = ${formattedResult} ${this.getUnitName(toUnit.value)}`;
                },

                formatResult(value) {
                    if (value === 0) return '0';
                    if (Math.abs(value) >= 1e9 || (Math.abs(value) < 1e-9 && value !== 0)) {
                        return value.toExponential(6);
                    } else {
                        return parseFloat(value.toPrecision(12)).toString();
                    }
                },

                getUnitName(unit) {
                    const unitNames = { 'v': 'V', 'mv': 'mV', 'kv': 'kV', 'uv': 'µV', 'Mv': 'MV' };
                    return unitNames[unit] || unit;
                },

                clear() {
                    elements.fromInput().value = '';
                    elements.toInput().value = '';
                    elements.output().textContent = 'Your converted voltage will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.toInput().value;
                    if (!text || text === '') return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const fromInput = elements.fromInput();

                // Handle Enter key
                fromInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        VoltageConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>